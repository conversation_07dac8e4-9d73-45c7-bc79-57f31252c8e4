The transition from legacy ASP.NET applications to modern Django solutions offers significant advantages, including improved maintainability, scalability, and enhanced user experiences through technologies like HTMX and Alpine.js. This modernization plan outlines the automated conversion of your ASP.NET Customer Enquiry module, focusing on a "fat model, thin view" Django architecture and a highly interactive, dynamic frontend without traditional JavaScript frameworks.

By leveraging AI-assisted automation, we aim to streamline the migration, minimize manual coding, and ensure the new system adheres to best practices. This approach translates directly into business benefits:
- **Reduced Development Time:** Automated conversion accelerates the migration process, allowing quicker deployment of the modernized application.
- **Lower Maintenance Costs:** Django's clean architecture and Python's readability make the application easier to understand, debug, and maintain.
- **Enhanced User Experience:** HTMX and Alpine.js provide a snappy, reactive interface without full page reloads, improving user satisfaction and productivity.
- **Improved Scalability and Performance:** Django's robust design and efficient ORM, coupled with optimized database interactions, ensure the application can handle increased loads.
- **Future-Proofing:** Adopting modern, widely-used technologies positions your application for long-term support and easier integration with new tools.

This plan details the necessary Django application files, including models, forms, views, templates, URLs, and comprehensive tests, all designed to be seamlessly integrated and managed.

---

## ASP.NET to Django Conversion Script: Customer Enquiry Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database tables:
- `SD_Cust_Enquiry_Master`: Stores the main customer enquiry details.
- `tblFile_Attachment`: A temporary staging table for file uploads before they are associated with an enquiry.
- `SD_Cust_Enquiry_Attach_Master`: Stores attachments permanently linked to a customer enquiry.
- `SD_Cust_Master`: Contains details of existing customers, used for populating the form.
- `tblCountry`, `tblState`, `tblCity`: Used for cascading dropdowns for addresses.

**Extracted Schemas:**

**`SD_Cust_Enquiry_Master`**
- `EnqId` (PK, int, auto-increment)
- `SysDate` (date)
- `SysTime` (time)
- `SessionId` (string)
- `CompId` (int)
- `FinYearId` (int)
- `CustomerId` (string, FK to `SD_Cust_Master`, nullable)
- `CustomerName` (string)
- `RegdAddress` (string)
- `RegdCountry` (string, FK to `tblCountry`)
- `RegdState` (string, FK to `tblState`)
- `RegdCity` (string, FK to `tblCity`)
- `RegdPinNo` (string)
- `RegdContactNo` (string)
- `RegdFaxNo` (string)
- `WorkAddress` (string)
- `WorkCountry` (string, FK to `tblCountry`)
- `WorkState` (string, FK to `tblState`)
- `WorkCity` (string, FK to `tblCity`)
- `WorkPinNo` (string)
- `WorkContactNo` (string)
- `WorkFaxNo` (string)
- `MaterialDelAddress` (string)
- `MaterialDelCountry` (string, FK to `tblCountry`)
- `MaterialDelState` (string, FK to `tblState`)
- `MaterialDelCity` (string, FK to `tblCity`)
- `MaterialDelPinNo` (string)
- `MaterialDelContactNo` (string)
- `MaterialDelFaxNo` (string)
- `ContactPerson` (string)
- `JuridictionCode` (string)
- `Commissionurate` (string)
- `TinVatNo` (string)
- `Email` (string)
- `EccNo` (string)
- `Divn` (string)
- `TinCstNo` (string)
- `ContactNo` (string)
- `Range` (string)
- `PanNo` (string)
- `TDSCode` (string)
- `Remark` (string, nullable)
- `EnquiryFor` (string)
- `Flag` (int, 0 for New Customer, 1 for Existing Customer)

**`tblFile_Attachment`** (Temporary Attachments)
- `Id` (PK, int)
- `FileName` (string)
- `FileSize` (double)
- `ContentType` (string)
- `FileData` (binary)
- `CompId` (int)
- `FinYearId` (int)
- `SessionId` (string)

**`SD_Cust_Enquiry_Attach_Master`** (Permanent Attachments)
- `EnqId` (FK to `SD_Cust_Enquiry_Master`)
- `CompId` (int)
- `SessionId` (string)
- `FinYearId` (int)
- `FileName` (string)
- `FileSize` (double)
- `ContentType` (string)
- `FileData` (binary)

**`SD_Cust_Master`** (Existing Customers)
- `CustomerId` (PK, string)
- `CustomerName` (string)
- (Assumed additional address, contact, and tax fields similar to `SD_Cust_Enquiry_Master` for `btnView_Click` functionality)
- `CompId` (int)

**`tblCountry`, `tblState`, `tblCity`** (Lookup Tables)
- `tblCountry`: `CId` (PK, string), `CountryName` (string)
- `tblState`: `SId` (PK, string), `CId` (FK, string), `StateName` (string)
- `tblCity`: `CityId` (PK, string), `SId` (FK, string), `CityName` (string)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Identified Functionality:**
- **Create (Customer Enquiry):**
    - `Submit_Click`: Handles both "New" and "Existing" customer enquiry creation.
    - Captures numerous form fields.
    - Performs extensive server-side validation.
    - Associates temporary attachments from `tblFile_Attachment` to the newly created enquiry in `SD_Cust_Enquiry_Attach_Master`.
    - Clears temporary attachments after successful association.
- **Read (Customer Enquiry/Attachments):**
    - `Page_Load`: Populates initial dropdowns (Country, State, City).
    - `btnView_Click`: Retrieves and populates existing customer details into the form.
    - `GridView1` with `SqlDataSource1`: Displays temporary attachments.
    - `sql` WebMethod: Provides customer data for autocomplete.
- **Update (Attachments):**
    - `SqlDataSource1` has an `UpdateCommand`, though not explicitly used for the main enquiry form. It is used for `tblFile_Attachment`.
    - Deleting an attachment from `GridView1` (via `SqlDataSource1` `DeleteCommand`) is effectively an update to the temporary attachment list.
- **Delete (Attachments):**
    - `GridView1` `CommandField` with `ShowDeleteButton="True"`: Deletes temporary attachments.
- **Dynamic Interactions:**
    - `RadioBtnNew_CheckedChanged`, `RadioBtnExisting_CheckedChanged`: Toggles form field visibility.
    - `DDListNewRegdCountry_SelectedIndexChanged`, `DDListNewRegdState_SelectedIndexChanged`, etc.: Cascading dropdowns (Country -> State -> City).
    - File upload (`Button1_Click`).
    - Client-side confirmations for upload and delete (`confirmationUpload()`, `confirmationDelete()`, `confirmation()`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Inferred UI Components:**
- **Form Layout:** A single form with two main sections (New/Existing Customer) controlled by radio buttons. Tabs (`TabContainer1`) separate "Details" and "Attachments".
- **Input Fields:** Numerous `asp:TextBox` (single-line, multi-line), `asp:DropDownList` (Country, State, City), `asp:RadioButton` (New/Existing Customer), `asp:FileUpload`.
- **Validation:** `asp:RequiredFieldValidator` and `asp:RegularExpressionValidator` are used for client-side and server-side validation.
- **Action Buttons:** `asp:Button` for Submit, Cancel, Search, Upload.
- **Autocomplete:** `cc1:AutoCompleteExtender` for existing customer names.
- **Data Display:** `asp:GridView` for temporary file attachments.
- **Status Message:** `asp:Label` for displaying messages (`lblmsg`).

### Step 4: Generate Django Code

We will create a new Django app named `sales_distribution`.

#### 4.1 Models (`sales_distribution/models.py`)

We'll define models for Customer Enquiry, Temporary Attachments, and permanent Enquiry Attachments. We will also include assumed models for `Country`, `State`, `City`, and `Customer` to demonstrate relationships, assuming they are managed by the database and not by Django migrations.

```python
from django.db import models
from django.utils import timezone
import os

# --- Assumed Lookup Models (managed=False) ---
# These models represent existing tables like Country, State, City, Customer
# They are assumed to be managed by the existing database system.

class Country(models.Model):
    country_id = models.CharField(max_length=10, primary_key=True, db_column='CId')
    name = models.CharField(max_length=100, db_column='CountryName')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    state_id = models.CharField(max_length=10, primary_key=True, db_column='SId')
    country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='CId')
    name = models.CharField(max_length=100, db_column='StateName')

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    city_id = models.CharField(max_length=10, primary_key=True, db_column='CityId')
    state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='SId')
    name = models.CharField(max_length=100, db_column='CityName')

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class Customer(models.Model):
    customer_id = models.CharField(max_length=50, primary_key=True, db_column='CustomerId')
    customer_name = models.CharField(max_length=255, db_column='CustomerName')
    regd_address = models.TextField(db_column='RegdAddress', blank=True, null=True)
    regd_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='RegdCountry', related_name='regd_customers', blank=True, null=True)
    regd_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='RegdState', related_name='regd_customers', blank=True, null=True)
    regd_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='RegdCity', related_name='regd_customers', blank=True, null=True)
    regd_pin_no = models.CharField(max_length=20, db_column='RegdPinNo', blank=True, null=True)
    regd_contact_no = models.CharField(max_length=50, db_column='RegdContactNo', blank=True, null=True)
    regd_fax_no = models.CharField(max_length=50, db_column='RegdFaxNo', blank=True, null=True)
    
    work_address = models.TextField(db_column='WorkAddress', blank=True, null=True)
    work_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='WorkCountry', related_name='work_customers', blank=True, null=True)
    work_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='WorkState', related_name='work_customers', blank=True, null=True)
    work_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='WorkCity', related_name='work_customers', blank=True, null=True)
    work_pin_no = models.CharField(max_length=20, db_column='WorkPinNo', blank=True, null=True)
    work_contact_no = models.CharField(max_length=50, db_column='WorkContactNo', blank=True, null=True)
    work_fax_no = models.CharField(max_length=50, db_column='WorkFaxNo', blank=True, null=True)

    material_del_address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    material_del_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='MaterialDelCountry', related_name='mat_del_customers', blank=True, null=True)
    material_del_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='MaterialDelState', related_name='mat_del_customers', blank=True, null=True)
    material_del_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='MaterialDelCity', related_name='mat_del_customers', blank=True, null=True)
    material_del_pin_no = models.CharField(max_length=20, db_column='MaterialDelPinNo', blank=True, null=True)
    material_del_contact_no = models.CharField(max_length=50, db_column='MaterialDelContactNo', blank=True, null=True)
    material_del_fax_no = models.CharField(max_length=50, db_column='MaterialDelFaxNo', blank=True, null=True)

    contact_person = models.CharField(max_length=255, db_column='ContactPerson', blank=True, null=True)
    juridiction_code = models.CharField(max_length=50, db_column='JuridictionCode', blank=True, null=True)
    commissionurate = models.CharField(max_length=100, db_column='Commissionurate', blank=True, null=True)
    tin_vat_no = models.CharField(max_length=50, db_column='TinVatNo', blank=True, null=True)
    email = models.EmailField(max_length=255, db_column='Email', blank=True, null=True)
    ecc_no = models.CharField(max_length=50, db_column='EccNo', blank=True, null=True)
    divn = models.CharField(max_length=50, db_column='Divn', blank=True, null=True)
    tin_cst_no = models.CharField(max_length=50, db_column='TinCstNo', blank=True, null=True)
    contact_no = models.CharField(max_length=50, db_column='ContactNo', blank=True, null=True)
    range_field = models.CharField(max_length=50, db_column='Range', blank=True, null=True) # Renamed 'range' to 'range_field' due to Python keyword
    pan_no = models.CharField(max_length=50, db_column='PanNo', blank=True, null=True)
    tds_code = models.CharField(max_length=50, db_column='TDSCode', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId is part of Customer table

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

# --- Main Application Models (managed=False) ---

class CustomerEnquiry(models.Model):
    enq_id = models.AutoField(primary_key=True, db_column='EnqId')
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(max_length=255, db_column='SessionId') # From ASP.NET Session["username"]
    comp_id = models.IntegerField(db_column='CompId') # From ASP.NET Session["compid"]
    fin_year_id = models.IntegerField(db_column='FinYearId') # From ASP.NET Session["finyear"]
    
    # For Existing Customer (Flag=1)
    customer = models.ForeignKey(Customer, on_delete=models.PROTECT, db_column='CustomerId', blank=True, null=True)
    
    # For New Customer (Flag=0) or duplicated for existing
    customer_name = models.CharField(max_length=255, db_column='CustomerName')

    regd_address = models.TextField(db_column='RegdAddress')
    regd_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='RegdCountry', related_name='enquiry_regd_countries')
    regd_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='RegdState', related_name='enquiry_regd_states')
    regd_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='RegdCity', related_name='enquiry_regd_cities')
    regd_pin_no = models.CharField(max_length=20, db_column='RegdPinNo')
    regd_contact_no = models.CharField(max_length=50, db_column='RegdContactNo')
    regd_fax_no = models.CharField(max_length=50, db_column='RegdFaxNo')

    work_address = models.TextField(db_column='WorkAddress')
    work_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='WorkCountry', related_name='enquiry_work_countries')
    work_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='WorkState', related_name='enquiry_work_states')
    work_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='WorkCity', related_name='enquiry_work_cities')
    work_pin_no = models.CharField(max_length=20, db_column='WorkPinNo')
    work_contact_no = models.CharField(max_length=50, db_column='WorkContactNo')
    work_fax_no = models.CharField(max_length=50, db_column='WorkFaxNo')

    material_del_address = models.TextField(db_column='MaterialDelAddress')
    material_del_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='MaterialDelCountry', related_name='enquiry_mat_del_countries')
    material_del_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='MaterialDelState', related_name='enquiry_mat_del_states')
    material_del_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='MaterialDelCity', related_name='enquiry_mat_del_cities')
    material_del_pin_no = models.CharField(max_length=20, db_column='MaterialDelPinNo')
    material_del_contact_no = models.CharField(max_length=50, db_column='MaterialDelContactNo')
    material_del_fax_no = models.CharField(max_length=50, db_column='MaterialDelFaxNo')

    contact_person = models.CharField(max_length=255, db_column='ContactPerson')
    juridiction_code = models.CharField(max_length=50, db_column='JuridictionCode')
    commissionurate = models.CharField(max_length=100, db_column='Commissionurate')
    tin_vat_no = models.CharField(max_length=50, db_column='TinVatNo')
    email = models.EmailField(max_length=255, db_column='Email')
    ecc_no = models.CharField(max_length=50, db_column='EccNo')
    divn = models.CharField(max_length=50, db_column='Divn')
    tin_cst_no = models.CharField(max_length=50, db_column='TinCstNo')
    contact_no = models.CharField(max_length=50, db_column='ContactNo')
    range_field = models.CharField(max_length=50, db_column='Range') # Renamed 'range' to 'range_field'
    pan_no = models.CharField(max_length=50, db_column='PanNo')
    tds_code = models.CharField(max_length=50, db_column='TDSCode')
    remark = models.TextField(db_column='Remark', blank=True, null=True)
    enquiry_for = models.TextField(db_column='EnquiryFor')
    flag = models.IntegerField(db_column='Flag') # 0 for New, 1 for Existing

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"Enquiry {self.enq_id} for {self.customer_name}"

    @classmethod
    def create_enquiry_with_attachments(cls, form_data, session_key, comp_id, fin_year_id):
        """
        Creates a new customer enquiry and moves temporary attachments.
        This method encapsulates the complex logic from ASP.NET Submit_Click.
        """
        # Extract customer type and name
        is_new_customer = form_data.pop('is_new_customer')
        customer_name_input = form_data.pop('new_customer_name_input', '').upper()
        existing_customer_input = form_data.pop('existing_customer_name_input', '')
        
        customer_obj = None
        final_customer_name = ""
        enquiry_flag = 0

        if is_new_customer:
            final_customer_name = customer_name_input
            enquiry_flag = 0
        else:
            # Parse existing customer input (e.g., "Customer A [CUST001]")
            try:
                customer_id_str = existing_customer_input.split('[')[-1].replace(']', '').strip()
                customer_obj = Customer.objects.get(customer_id=customer_id_str, comp_id=comp_id)
                final_customer_name = existing_customer_input.split('[')[0].strip()
                enquiry_flag = 1
            except (IndexError, Customer.DoesNotExist):
                raise ValueError("Invalid existing customer selected.")
                
            # If existing customer, pre-fill form data from Customer master
            # (This is handled by btnView_Click in ASP.NET, in Django, it's part of initial load or HTMX response)
            # For saving, we just need the Customer object reference and its name.
            
            # The ASP.NET code duplicates many fields from SD_Cust_Master to SD_Cust_Enquiry_Master
            # when using an existing customer. We need to populate these if they are empty
            # in the form or if the form is for existing customer and not all fields were submitted.
            # For simplicity, we assume form_data already contains valid values from manual entry or pre-fill.
            # If fields like address are empty for an existing customer, the form validation will catch it.
            # The fat model approach here is about handling the save and attachment logic.
            # The population of form fields is a view/template concern.

        enquiry = cls(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id=session_key,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            customer=customer_obj,
            customer_name=final_customer_name,
            flag=enquiry_flag,
            **form_data # Remaining cleaned data from form
        )
        enquiry.save()

        # Move temporary attachments to permanent storage
        temp_attachments = TemporaryAttachment.objects.filter(
            session_id=session_key,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        for temp_att in temp_attachments:
            CustomerEnquiryAttachment.objects.create(
                enquiry=enquiry,
                comp_id=temp_att.comp_id,
                session_id=temp_att.session_id, # This seems redundant for permanent record
                fin_year_id=temp_att.fin_year_id,
                file_name=temp_att.file_name,
                file_size=temp_att.file_size,
                content_type=temp_att.content_type,
                file_data=temp_att.file_data,
            )
            temp_att.delete() # Remove from temporary table

        return enquiry
    
    def get_full_address(self, address_type='regd'):
        """Helper to get formatted address for display."""
        if address_type == 'regd':
            country = self.regd_country.name if self.regd_country else ''
            state = self.regd_state.name if self.regd_state else ''
            city = self.regd_city.name if self.regd_city else ''
            address_parts = [self.regd_address, city, state, country, self.regd_pin_no]
        elif address_type == 'work':
            country = self.work_country.name if self.work_country else ''
            state = self.work_state.name if self.work_state else ''
            city = self.work_city.name if self.work_city else ''
            address_parts = [self.work_address, city, state, country, self.work_pin_no]
        elif address_type == 'material_del':
            country = self.material_del_country.name if self.material_del_country else ''
            state = self.material_del_state.name if self.material_del_state else ''
            city = self.material_del_city.name if self.material_del_city else ''
            address_parts = [self.material_del_address, city, state, country, self.material_del_pin_no]
        else:
            return ""
        
        return ", ".join(filter(None, address_parts))


class TemporaryAttachment(models.Model):
    id = models.AutoField(primary_key=True, db_column='Id')
    file_name = models.CharField(max_length=255, db_column='FileName')
    file_size = models.FloatField(db_column='FileSize')
    content_type = models.CharField(max_length=100, db_column='ContentType')
    file_data = models.BinaryField(db_column='FileData') # Stores file as binary data
    session_id = models.CharField(max_length=255, db_column='SessionId')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblFile_Attachment'
        verbose_name = 'Temporary Attachment'
        verbose_name_plural = 'Temporary Attachments'

    def __str__(self):
        return self.file_name

    def delete_attachment(self):
        """Business logic for deleting a temporary attachment."""
        self.delete()


class CustomerEnquiryAttachment(models.Model):
    # This table uses a composite primary key derived from EnqId and an implicit attachment ID
    # For Django, we'll use an AutoField as primary key if the actual table has one implicitly,
    # or define a unique_together constraint if it's (EnqId, FileName) or similar.
    # Assuming a simple auto-increment PK like the ASP.NET SqlDataSource's `Id` for delete.
    id = models.AutoField(primary_key=True) # Assuming an implicit PK column
    enquiry = models.ForeignKey(CustomerEnquiry, on_delete=models.CASCADE, db_column='EnqId')
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    file_name = models.CharField(max_length=255, db_column='FileName')
    file_size = models.FloatField(db_column='FileSize')
    content_type = models.CharField(max_length=100, db_column='ContentType')
    file_data = models.BinaryField(db_column='FileData')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Attach_Master'
        verbose_name = 'Customer Enquiry Attachment'
        verbose_name_plural = 'Customer Enquiry Attachments'

    def __str__(self):
        return f"Attachment {self.file_name} for Enquiry {self.enquiry.enq_id}"

    def download_url(self):
        """Provides a URL for downloading the attachment."""
        # This will map to a view that streams the file_data
        return f"/sales_distribution/enquiry-attachments/{self.pk}/download/"

```

#### 4.2 Forms (`sales_distribution/forms.py`)

```python
from django import forms
from django.core.validators import EmailValidator
from django.forms import ValidationError
from .models import CustomerEnquiry, TemporaryAttachment, Customer, Country, State, City

class CustomerEnquiryForm(forms.ModelForm):
    # Hidden fields for managing customer type toggle and search input
    is_new_customer = forms.BooleanField(required=False, initial=True, widget=forms.HiddenInput())
    new_customer_name_input = forms.CharField(
        max_length=255, 
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500', 'placeholder': 'Enter New Customer Name'})
    )
    existing_customer_name_input = forms.CharField(
        max_length=255, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500', 
            'placeholder': 'Search Existing Customer (e.g., "Acme Inc. [CUST001]")',
            'hx-get': '/sales_distribution/customer-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'hx-swap': 'innerHTML'
        })
    )

    # Re-declare foreign key fields as CharField for dropdown selection from IDs
    regd_country = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales_distribution/get-states/',
            'hx-target': '#id_regd_state',
            'hx-include': 'this', # Send current country ID
            'hx-swap': 'innerHTML'
        })
    )
    regd_state = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales_distribution/get-cities/',
            'hx-target': '#id_regd_city',
            'hx-include': 'this', # Send current state ID
            'hx-swap': 'innerHTML'
        })
    )
    regd_city = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )

    work_country = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales_distribution/get-states/',
            'hx-target': '#id_work_state',
            'hx-include': 'this',
            'hx-swap': 'innerHTML'
        })
    )
    work_state = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales_distribution/get-cities/',
            'hx-target': '#id_work_city',
            'hx-include': 'this',
            'hx-swap': 'innerHTML'
        })
    )
    work_city = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )

    material_del_country = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales_distribution/get-states/',
            'hx-target': '#id_material_del_state',
            'hx-include': 'this',
            'hx-swap': 'innerHTML'
        })
    )
    material_del_state = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/sales_distribution/get-cities/',
            'hx-target': '#id_material_del_city',
            'hx-include': 'this',
            'hx-swap': 'innerHTML'
        })
    )
    material_del_city = forms.CharField(
        max_length=10, 
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )

    class Meta:
        model = CustomerEnquiry
        fields = [
            'is_new_customer', 'new_customer_name_input', 'existing_customer_name_input',
            'customer_name', # This field will be populated internally or from new_customer_name_input
            'regd_address', 'regd_country', 'regd_state', 'regd_city', 'regd_pin_no', 'regd_contact_no', 'regd_fax_no',
            'work_address', 'work_country', 'work_state', 'work_city', 'work_pin_no', 'work_contact_no', 'work_fax_no',
            'material_del_address', 'material_del_country', 'material_del_state', 'material_del_city', 'material_del_pin_no', 'material_del_contact_no', 'material_del_fax_no',
            'contact_person', 'juridiction_code', 'commissionurate', 'tin_vat_no', 'email', 'ecc_no', 'divn',
            'tin_cst_no', 'contact_no', 'range_field', 'pan_no', 'tds_code', 'remark', 'enquiry_for'
        ]
        widgets = {
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'regd_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-20'}),
            'regd_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'regd_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'regd_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),

            'work_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-20'}),
            'work_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'work_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'work_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),

            'material_del_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-20'}),
            'material_del_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'material_del_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'material_del_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),

            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'juridiction_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'commissionurate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'tin_vat_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'ecc_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'divn': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'tin_cst_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'range_field': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'tds_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'}),
            'enquiry_for': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-20'}),
            'remark': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 h-20'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate country dropdowns
        countries = Country.objects.all().order_by('name')
        country_choices = [('', 'Select')] + [(c.country_id, c.name) for c in countries]

        self.fields['regd_country'].choices = country_choices
        self.fields['work_country'].choices = country_choices
        self.fields['material_del_country'].choices = country_choices

        # Populate initial states/cities if instance exists (for Update/Edit)
        if self.instance.pk:
            if self.instance.regd_country_id:
                self.fields['regd_state'].choices = [('', 'Select')] + [(s.state_id, s.name) for s in State.objects.filter(country_id=self.instance.regd_country_id).order_by('name')]
            if self.instance.regd_state_id:
                self.fields['regd_city'].choices = [('', 'Select')] + [(c.city_id, c.name) for c in City.objects.filter(state_id=self.instance.regd_state_id).order_by('name')]

            if self.instance.work_country_id:
                self.fields['work_state'].choices = [('', 'Select')] + [(s.state_id, s.name) for s in State.objects.filter(country_id=self.instance.work_country_id).order_by('name')]
            if self.instance.work_state_id:
                self.fields['work_city'].choices = [('', 'Select')] + [(c.city_id, c.name) for c in City.objects.filter(state_id=self.instance.work_state_id).order_by('name')]

            if self.instance.material_del_country_id:
                self.fields['material_del_state'].choices = [('', 'Select')] + [(s.state_id, s.name) for s in State.objects.filter(country_id=self.instance.material_del_country_id).order_by('name')]
            if self.instance.material_del_state_id:
                self.fields['material_del_city'].choices = [('', 'Select')] + [(c.city_id, c.name) for c in City.objects.filter(state_id=self.instance.material_del_state_id).order_by('name')]
            
            # Set initial values for the proxy fields if instance is existing
            if self.instance.flag == 0: # New customer
                self.initial['is_new_customer'] = True
                self.initial['new_customer_name_input'] = self.instance.customer_name
            elif self.instance.flag == 1 and self.instance.customer: # Existing customer
                self.initial['is_new_customer'] = False
                self.initial['existing_customer_name_input'] = str(self.instance.customer)

        # Ensure that if no instance, states/cities are empty initially
        for field_name in ['regd_state', 'regd_city', 'work_state', 'work_city', 'material_del_state', 'material_del_city']:
            if not self.fields[field_name].choices:
                self.fields[field_name].choices = [('', 'Select')]

        # Mark non-model fields as not required by default for validation flow
        self.fields['new_customer_name_input'].required = False
        self.fields['existing_customer_name_input'].required = False

    def clean(self):
        cleaned_data = super().clean()
        is_new_customer = cleaned_data.get('is_new_customer')
        new_customer_name_input = cleaned_data.get('new_customer_name_input')
        existing_customer_name_input = cleaned_data.get('existing_customer_name_input')

        # Custom validation based on new/existing customer radio selection
        if is_new_customer:
            if not new_customer_name_input:
                self.add_error('new_customer_name_input', 'New customer name is required.')
            # Clear existing_customer_name_input as it's not relevant
            cleaned_data['existing_customer_name_input'] = ''
        else: # Existing customer
            if not existing_customer_name_input:
                self.add_error('existing_customer_name_input', 'Existing customer selection is required.')
            else:
                # Validate and get Customer object
                try:
                    customer_id_str = existing_customer_name_input.split('[')[-1].replace(']', '').strip()
                    customer = Customer.objects.get(customer_id=customer_id_str)
                    cleaned_data['customer'] = customer # Attach Customer object
                    cleaned_data['customer_name'] = existing_customer_name_input.split('[')[0].strip() # Use parsed name
                except (IndexError, Customer.DoesNotExist):
                    self.add_error('existing_customer_name_input', 'Invalid existing customer selected.')
            # Clear new_customer_name_input as it's not relevant
            cleaned_data['new_customer_name_input'] = ''

        # Ensure proper FK assignment for Country, State, City
        for addr_type in ['regd', 'work', 'material_del']:
            country_id = cleaned_data.get(f'{addr_type}_country')
            state_id = cleaned_data.get(f'{addr_type}_state')
            city_id = cleaned_data.get(f'{addr_type}_city')

            if country_id:
                try:
                    cleaned_data[f'{addr_type}_country'] = Country.objects.get(country_id=country_id)
                except Country.DoesNotExist:
                    self.add_error(f'{addr_type}_country', 'Invalid country selected.')
            else:
                 self.add_error(f'{addr_type}_country', 'Country is required.')

            if state_id:
                try:
                    cleaned_data[f'{addr_type}_state'] = State.objects.get(state_id=state_id, country_id=country_id)
                except State.DoesNotExist:
                    self.add_error(f'{addr_type}_state', 'Invalid state selected for the chosen country.')
            else:
                 self.add_error(f'{addr_type}_state', 'State is required.')

            if city_id:
                try:
                    cleaned_data[f'{addr_type}_city'] = City.objects.get(city_id=city_id, state_id=state_id)
                except City.DoesNotExist:
                    self.add_error(f'{addr_type}_city', 'Invalid city selected for the chosen state.')
            else:
                 self.add_error(f'{addr_type}_city', 'City is required.')
        
        # Required fields check (ASP.NET validators were extensive)
        required_fields = [
            'regd_address', 'regd_pin_no', 'regd_contact_no', 'regd_fax_no',
            'work_address', 'work_pin_no', 'work_contact_no', 'work_fax_no',
            'material_del_address', 'material_del_pin_no', 'material_del_contact_no', 'material_del_fax_no',
            'contact_person', 'juridiction_code', 'commissionurate', 'tin_vat_no', 'email', 'ecc_no', 'divn',
            'tin_cst_no', 'contact_no', 'range_field', 'pan_no', 'tds_code', 'enquiry_for'
        ]
        for field_name in required_fields:
            if not cleaned_data.get(field_name):
                self.add_error(field_name, 'This field is required.')

        return cleaned_data

class TemporaryAttachmentUploadForm(forms.ModelForm):
    file_data = forms.FileField(
        required=True,
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    class Meta:
        model = TemporaryAttachment
        fields = ['file_data']

```

#### 4.3 Views (`sales_distribution/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404, render
from django.db import transaction
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.text import slugify

from .models import CustomerEnquiry, TemporaryAttachment, Customer, Country, State, City, CustomerEnquiryAttachment
from .forms import CustomerEnquiryForm, TemporaryAttachmentUploadForm

# Assume CompId and FinYearId are retrieved from user session or profile
# For demonstration, let's use dummy values or assume they are passed in request attributes
# In a real system, you'd likely have a custom middleware or User model extension
# to access request.user.comp_id and request.user.fin_year_id

# Helper function to get common session/company data
def get_user_context_data(request):
    return {
        'comp_id': request.session.get('compid', 1),  # Dummy CompId
        'fin_year_id': request.session.get('finyear', 1), # Dummy FinYearId
        'session_key': request.session.session_key # Used for temporary attachments
    }

class CustomerEnquiryListView(ListView):
    model = CustomerEnquiry
    template_name = 'sales_distribution/customer_enquiry/customer_enquiry_list.html'
    context_object_name = 'enquiries'

    def get_queryset(self):
        # Filter by company/financial year if applicable
        context_data = get_user_context_data(self.request)
        return CustomerEnquiry.objects.filter(
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        ).order_by('-enq_id')

class CustomerEnquiryTablePartialView(ListView):
    """
    HTMX partial view to render the DataTables content.
    """
    model = CustomerEnquiry
    template_name = 'sales_distribution/customer_enquiry/_customer_enquiry_table.html'
    context_object_name = 'enquiries'

    def get_queryset(self):
        context_data = get_user_context_data(self.request)
        return CustomerEnquiry.objects.filter(
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        ).order_by('-enq_id')

class CustomerEnquiryCreateUpdateView(View):
    template_name = 'sales_distribution/customer_enquiry/customer_enquiry_form.html'
    success_url = reverse_lazy('customer_enquiry_list') # HTMX will handle redirection

    def get(self, request, pk=None):
        context_data = get_user_context_data(request)
        enquiry = None
        if pk:
            enquiry = get_object_or_404(CustomerEnquiry, pk=pk, comp_id=context_data['comp_id'])
            form = CustomerEnquiryForm(instance=enquiry)
        else:
            form = CustomerEnquiryForm()
            # Clear temporary attachments for new entry
            TemporaryAttachment.objects.filter(
                session_id=context_data['session_key'],
                comp_id=context_data['comp_id'],
                fin_year_id=context_data['fin_year_id']
            ).delete()

        temp_attachments = TemporaryAttachment.objects.filter(
            session_id=context_data['session_key'],
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        )
        return render(request, self.template_name, {
            'form': form,
            'is_edit': bool(pk),
            'temporary_attachments': temp_attachments,
            'comp_id': context_data['comp_id'], # Pass these for potential future use in client-side
            'fin_year_id': context_data['fin_year_id'],
            'session_key': context_data['session_key']
        })

    def post(self, request, pk=None):
        context_data = get_user_context_data(request)
        enquiry = None
        if pk:
            enquiry = get_object_or_404(CustomerEnquiry, pk=pk, comp_id=context_data['comp_id'])
            form = CustomerEnquiryForm(request.POST, instance=enquiry)
        else:
            form = CustomerEnquiryForm(request.POST)

        if form.is_valid():
            try:
                with transaction.atomic():
                    if pk:
                        # For updates, we just save the form data.
                        # Attachment handling should be separate if updates to attachments are allowed.
                        # ASP.NET original only shows attachment upload/delete on the temporary grid
                        # and then moves them on main form submit for NEW entries.
                        # For existing entries, it seems attachments are final.
                        # If attachment modification for existing entries is needed, extend this logic.
                        form.save(commit=False)
                        form.instance.save()
                        # No temporary attachment moving for update, as per ASP.NET logic
                        messages.success(request, 'Customer Enquiry updated successfully.')
                    else:
                        CustomerEnquiry.create_enquiry_with_attachments(
                            form.cleaned_data,
                            context_data['session_key'],
                            context_data['comp_id'],
                            context_data['fin_year_id']
                        )
                        messages.success(request, 'Customer Enquiry generated successfully.')

                # HTMX response for success
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        status=204, # No content, tells HTMX nothing to swap
                        headers={
                            'HX-Trigger': 'refreshCustomerEnquiryList, closeCustomerEnquiryModal',
                            'HX-Redirect': self.success_url # Redirect after modal close if not already on list
                        }
                    )
                return redirect(self.success_url)
            except ValueError as e:
                messages.error(request, f"Error: {e}")
                # Fall through to re-render form with errors
        
        # If form is invalid or an error occurred, re-render the form
        temp_attachments = TemporaryAttachment.objects.filter(
            session_id=context_data['session_key'],
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        )
        return render(request, self.template_name, {
            'form': form,
            'is_edit': bool(pk),
            'temporary_attachments': temp_attachments,
            'comp_id': context_data['comp_id'],
            'fin_year_id': context_data['fin_year_id'],
            'session_key': context_data['session_key']
        })

class CustomerEnquiryDeleteView(DeleteView):
    model = CustomerEnquiry
    template_name = 'sales_distribution/customer_enquiry/customer_enquiry_confirm_delete.html'
    success_url = reverse_lazy('customer_enquiry_list')

    def get_queryset(self):
        context_data = get_user_context_data(self.request)
        return CustomerEnquiry.objects.filter(
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        )

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Enquiry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList, closeCustomerEnquiryModal'
                }
            )
        return response

# --- HTMX Endpoints for Dynamic Interactions ---

class CustomerAutocompleteView(View):
    """
    Provides customer suggestions for autocomplete.
    Maps to ASP.NET sql WebMethod.
    """
    def get(self, request):
        query = request.GET.get('q', '')
        comp_id = get_user_context_data(request)['comp_id']
        
        customers = Customer.objects.filter(
            customer_name__icontains=query,
            comp_id=comp_id
        ).values_list('customer_name', 'customer_id')[:10] # Limit to 10 suggestions

        suggestions = [f"{name} [{cust_id}]" for name, cust_id in customers]
        return HttpResponse(
            render_to_string('sales_distribution/customer_enquiry/_autocomplete_results.html', {'suggestions': suggestions})
        )

class CustomerDataLoadView(View):
    """
    Loads existing customer data into form fields.
    Maps to ASP.NET btnView_Click.
    """
    def get(self, request):
        customer_full_name = request.GET.get('customer_name', '')
        comp_id = get_user_context_data(request)['comp_id']
        
        try:
            customer_id = customer_full_name.split('[')[-1].replace(']', '').strip()
            customer = Customer.objects.get(customer_id=customer_id, comp_id=comp_id)
            
            # Return fields as JSON for Alpine.js to update form, or as HTML partial
            # For simplicity and HTMX philosophy, returning JSON.
            # Alternatively, re-render form section with pre-filled data.
            data = {
                'regd_address': customer.regd_address,
                'regd_country': customer.regd_country.country_id if customer.regd_country else '',
                'regd_state': customer.regd_state.state_id if customer.regd_state else '',
                'regd_city': customer.regd_city.city_id if customer.regd_city else '',
                'regd_pin_no': customer.regd_pin_no,
                'regd_contact_no': customer.regd_contact_no,
                'regd_fax_no': customer.regd_fax_no,
                'work_address': customer.work_address,
                'work_country': customer.work_country.country_id if customer.work_country else '',
                'work_state': customer.work_state.state_id if customer.work_state else '',
                'work_city': customer.work_city.city_id if customer.work_city else '',
                'work_pin_no': customer.work_pin_no,
                'work_contact_no': customer.work_contact_no,
                'work_fax_no': customer.work_fax_no,
                'material_del_address': customer.material_del_address,
                'material_del_country': customer.material_del_country.country_id if customer.material_del_country else '',
                'material_del_state': customer.material_del_state.state_id if customer.material_del_state else '',
                'material_del_city': customer.material_del_city.city_id if customer.material_del_city else '',
                'material_del_pin_no': customer.material_del_pin_no,
                'material_del_contact_no': customer.material_del_contact_no,
                'material_del_fax_no': customer.material_del_fax_no,
                'contact_person': customer.contact_person,
                'juridiction_code': customer.juridiction_code,
                'commissionurate': customer.commissionurate,
                'tin_vat_no': customer.tin_vat_no,
                'email': customer.email,
                'ecc_no': customer.ecc_no,
                'divn': customer.divn,
                'tin_cst_no': customer.tin_cst_no,
                'contact_no': customer.contact_no,
                'range_field': customer.range_field,
                'pan_no': customer.pan_no,
                'tds_code': customer.tds_code,
                # 'remark': customer.remark, # Remark and EnquiryFor are not typically pulled from master
                # 'enquiry_for': customer.enquiry_for
            }
            return JsonResponse(data)
        except (IndexError, Customer.DoesNotExist):
            return JsonResponse({'error': 'Customer not found.'}, status=404)

class GetStatesView(View):
    """
    HTMX endpoint to get states for a selected country.
    Maps to ASP.NET DDListNewRegdCountry_SelectedIndexChanged, etc.
    """
    def get(self, request):
        country_id = request.GET.get('regd_country') or request.GET.get('work_country') or request.GET.get('material_del_country')
        states = State.objects.filter(country_id=country_id).order_by('name') if country_id else []
        return render(request, 'sales_distribution/customer_enquiry/_dropdown_options.html', {'options': states, 'value_field': 'state_id', 'display_field': 'name'})

class GetCitiesView(View):
    """
    HTMX endpoint to get cities for a selected state.
    Maps to ASP.NET DDListNewRegdState_SelectedIndexChanged, etc.
    """
    def get(self, request):
        state_id = request.GET.get('regd_state') or request.GET.get('work_state') or request.GET.get('material_del_state')
        cities = City.objects.filter(state_id=state_id).order_by('name') if state_id else []
        return render(request, 'sales_distribution/customer_enquiry/_dropdown_options.html', {'options': cities, 'value_field': 'city_id', 'display_field': 'name'})

class TemporaryAttachmentUploadView(View):
    """
    Handles file uploads to the temporary table.
    Maps to ASP.NET Button1_Click.
    """
    def post(self, request):
        context_data = get_user_context_data(request)
        form = TemporaryAttachmentUploadForm(request.POST, request.FILES)
        
        if form.is_valid():
            uploaded_file = form.cleaned_data['file_data']
            TemporaryAttachment.objects.create(
                file_name=uploaded_file.name,
                file_size=uploaded_file.size,
                content_type=uploaded_file.content_type,
                file_data=uploaded_file.read(),
                session_id=context_data['session_key'],
                comp_id=context_data['comp_id'],
                fin_year_id=context_data['fin_year_id']
            )
            messages.success(request, f"File '{uploaded_file.name}' uploaded temporarily.")
            # Trigger refresh of temporary attachments table
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshTemporaryAttachmentsTable'}
            )
        else:
            messages.error(request, f"Error uploading file: {form.errors.as_text()}")
            # Re-render form with errors or just return 200 with error
            return HttpResponse(status=400, content=f"Error: {form.errors.as_text()}")

class TemporaryAttachmentDeleteView(DeleteView):
    model = TemporaryAttachment
    
    def get_queryset(self):
        context_data = get_user_context_data(self.request)
        return TemporaryAttachment.objects.filter(
            session_id=context_data['session_key'],
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        )

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        file_name = obj.file_name
        obj.delete()
        messages.success(self.request, f"File '{file_name}' deleted.")
        return HttpResponse(
            status=204,
            headers={'HX-Trigger': 'refreshTemporaryAttachmentsTable'}
        )

class TemporaryAttachmentTablePartialView(View):
    """
    HTMX partial view to render the temporary attachments table.
    """
    def get(self, request):
        context_data = get_user_context_data(request)
        temporary_attachments = TemporaryAttachment.objects.filter(
            session_id=context_data['session_key'],
            comp_id=context_data['comp_id'],
            fin_year_id=context_data['fin_year_id']
        )
        return render(request, 'sales_distribution/customer_enquiry/_temporary_attachments_table.html', {
            'temporary_attachments': temporary_attachments,
            'comp_id': context_data['comp_id'],
            'fin_year_id': context_data['fin_year_id'],
            'session_key': context_data['session_key']
        })

class CustomerEnquiryAttachmentDownloadView(View):
    """
    Streams the file data for download.
    Maps to ASP.NET DownloadFile.aspx.
    """
    def get(self, request, pk):
        attachment = get_object_or_404(CustomerEnquiryAttachment, pk=pk)
        response = HttpResponse(attachment.file_data, content_type=attachment.content_type)
        response['Content-Disposition'] = f'attachment; filename="{slugify(attachment.file_name)}"'
        return response

```

#### 4.4 Templates

The templates are structured for reusability and HTMX partial loading.

**`sales_distribution/customer_enquiry/customer_enquiry_list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Enquiries</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300"
            hx-get="{% url 'customer_enquiry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Enquiry
        </button>
    </div>
    
    <div id="customerEnquiryTable-container"
         hx-trigger="load, refreshCustomerEnquiryList from:body"
         hx-get="{% url 'customer_enquiry_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading enquiries...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on closeModal add .hidden to #modal then remove .is-active from #modal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
             _="on htmx:afterSwap.modalContent if not event.detail.xhr.status == 204 add .is-active to #modal">
            <!-- Content loaded via HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for modal state
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });

    // Initialize DataTables after HTMX loads the table content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'customerEnquiryTable-container') {
            $('#customerEnquiryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
            });
        }
    });

    // Close modal on specific HTMX triggers
    document.body.addEventListener('closeCustomerEnquiryModal', function() {
        document.getElementById('modal').classList.add('hidden');
    });

</script>
{% endblock %}
```

**`sales_distribution/customer_enquiry/_customer_enquiry_table.html`** (HTMX partial for list)

```html
<table id="customerEnquiryTable" class="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
    <thead class="bg-gray-50 border-b border-gray-200">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry For</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Regd. Address</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for enquiry in enquiries %}
        <tr class="hover:bg-gray-100">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ enquiry.customer_name }}</td>
            <td class="py-3 px-4 text-sm text-gray-800 max-w-xs overflow-hidden text-ellipsis">{{ enquiry.enquiry_for }}</td>
            <td class="py-3 px-4 text-sm text-gray-800 max-w-xs overflow-hidden text-ellipsis">{{ enquiry.get_full_address('regd') }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ enquiry.contact_person }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ enquiry.email }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300"
                    hx-get="{% url 'customer_enquiry_edit' enquiry.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md shadow-sm transition duration-300"
                    hx-get="{% url 'customer_enquiry_delete' enquiry.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-6 text-center text-gray-600">No customer enquiries to display.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTables initialization script is in the main list.html, triggered by htmx:afterSwap -->
```

**`sales_distribution/customer_enquiry/customer_enquiry_form.html`** (Main form for add/edit)

```html
<div class="p-6" x-data="{ isNewCustomer: {{ form.is_new_customer.value|default:'true' }} }">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Enquiry</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <input type="hidden" name="is_new_customer" x-bind:value="isNewCustomer ? 'True' : 'False'">
        
        <!-- Customer Type Selection -->
        <div class="flex items-center space-x-6 mb-6">
            <label class="inline-flex items-center">
                <input type="radio" class="form-radio text-blue-600" name="customer_type" value="new" x-model="isNewCustomer">
                <span class="ml-2 text-gray-700">New Customer</span>
            </label>
            <label class="inline-flex items-center">
                <input type="radio" class="form-radio text-blue-600" name="customer_type" value="existing" x-model="isNewCustomer" x-on:change="if (!isNewCustomer) $nextTick(() => document.getElementById('id_existing_customer_name_input').focus())">
                <span class="ml-2 text-gray-700">Existing Customer</span>
            </label>
        </div>

        <!-- New Customer Name Input -->
        <div x-show="isNewCustomer" class="transition-all duration-300 ease-in-out">
            <label for="{{ form.new_customer_name_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                Customer's Name <span class="text-red-500">*</span>
            </label>
            {{ form.new_customer_name_input }}
            {% if form.new_customer_name_input.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.new_customer_name_input.errors }}</p>
            {% endif %}
        </div>

        <!-- Existing Customer Name Input with Autocomplete & Search Button -->
        <div x-show="!isNewCustomer" class="transition-all duration-300 ease-in-out">
            <label for="{{ form.existing_customer_name_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                Customer's Name <span class="text-red-500">*</span>
            </label>
            <div class="flex space-x-2">
                <div class="relative flex-grow">
                    {{ form.existing_customer_name_input }}
                    <div id="customer-autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto"></div>
                </div>
                <button type="button"
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
                    hx-get="{% url 'customer_data_load' %}"
                    hx-include="#id_existing_customer_name_input"
                    hx-trigger="click"
                    hx-target="body"
                    hx-swap="none"
                    _="on htmx:afterRequest(evt) from the closest <button/> then if evt.detail.xhr.status == 200 then set form elements based on evt.detail.xhr.response as json
                       set #id_regd_address.value to evt.detail.xhr.response.regd_address
                       set #id_regd_country.value to evt.detail.xhr.response.regd_country
                       send hx-trigger to #id_regd_country with new country selected
                       set #id_regd_state.value to evt.detail.xhr.response.regd_state
                       send hx-trigger to #id_regd_state with new state selected
                       set #id_regd_city.value to evt.detail.xhr.response.regd_city

                       set #id_regd_pin_no.value to evt.detail.xhr.response.regd_pin_no
                       set #id_regd_contact_no.value to evt.detail.xhr.response.regd_contact_no
                       set #id_regd_fax_no.value to evt.detail.xhr.response.regd_fax_no

                       set #id_work_address.value to evt.detail.xhr.response.work_address
                       set #id_work_country.value to evt.detail.xhr.response.work_country
                       send hx-trigger to #id_work_country with new country selected
                       set #id_work_state.value to evt.detail.xhr.response.work_state
                       send hx-trigger to #id_work_state with new state selected
                       set #id_work_city.value to evt.detail.xhr.response.work_city

                       set #id_work_pin_no.value to evt.detail.xhr.response.work_pin_no
                       set #id_work_contact_no.value to evt.detail.xhr.response.work_contact_no
                       set #id_work_fax_no.value to evt.detail.xhr.response.work_fax_no

                       set #id_material_del_address.value to evt.detail.xhr.response.material_del_address
                       set #id_material_del_country.value to evt.detail.xhr.response.material_del_country
                       send hx-trigger to #id_material_del_country with new country selected
                       set #id_material_del_state.value to evt.detail.xhr.response.material_del_state
                       send hx-trigger to #id_material_del_state with new state selected
                       set #id_material_del_city.value to evt.detail.xhr.response.material_del_city

                       set #id_material_del_pin_no.value to evt.detail.xhr.response.material_del_pin_no
                       set #id_material_del_contact_no.value to evt.detail.xhr.response.material_del_contact_no
                       set #id_material_del_fax_no.value to evt.detail.xhr.response.material_del_fax_no

                       set #id_contact_person.value to evt.detail.xhr.response.contact_person
                       set #id_juridiction_code.value to evt.detail.xhr.response.juridiction_code
                       set #id_commissionurate.value to evt.detail.xhr.response.commissionurate
                       set #id_tin_vat_no.value to evt.detail.xhr.response.tin_vat_no
                       set #id_email.value to evt.detail.xhr.response.email
                       set #id_ecc_no.value to evt.detail.xhr.response.ecc_no
                       set #id_divn.value to evt.detail.xhr.response.divn
                       set #id_tin_cst_no.value to evt.detail.xhr.response.tin_cst_no
                       set #id_contact_no.value to evt.detail.xhr.response.contact_no
                       set #id_range_field.value to evt.detail.xhr.response.range_field
                       set #id_pan_no.value to evt.detail.xhr.response.pan_no
                       set #id_tds_code.value to evt.detail.xhr.response.tds_code
                       else alert('Customer not found or error loading data.')"
                >
                    Search
                </button>
            </div>
            {% if form.existing_customer_name_input.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.existing_customer_name_input.errors }}</p>
            {% endif %}
        </div>

        <!-- Tab Container (simulated with Alpine.js) -->
        <div x-data="{ activeTab: 'details' }" class="mt-8 border border-gray-200 rounded-md">
            <div class="flex border-b border-gray-200 bg-gray-50">
                <button type="button" @click="activeTab = 'details'"
                        :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'details', 'text-gray-700': activeTab !== 'details'}"
                        class="px-6 py-3 text-sm font-medium focus:outline-none transition-colors duration-200 ease-in-out">
                    Details
                </button>
                <button type="button" @click="activeTab = 'attachments'"
                        :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'attachments', 'text-gray-700': activeTab !== 'attachments'}"
                        class="px-6 py-3 text-sm font-medium focus:outline-none transition-colors duration-200 ease-in-out">
                    Attachments
                </button>
            </div>

            <!-- Details Tab Content -->
            <div x-show="activeTab === 'details'" class="p-6 space-y-6">
                {% include 'sales_distribution/customer_enquiry/_address_section.html' %}
                {% include 'sales_distribution/customer_enquiry/_contact_tax_section.html' %}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.enquiry_for.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Enquiry For <span class="text-red-500">*</span>
                        </label>
                        {{ form.enquiry_for }}
                        {% if form.enquiry_for.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.enquiry_for.errors }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Remarks
                        </label>
                        {{ form.remark }}
                        {% if form.remark.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.remark.errors }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Attachments Tab Content -->
            <div x-show="activeTab === 'attachments'" class="p-6 space-y-6">
                {% include 'sales_distribution/customer_enquiry/_attachment_section.html' with temporary_attachments=temporary_attachments comp_id=comp_id fin_year_id=fin_year_id session_key=session_key %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
                _="on click trigger closeModal from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
                Submit
            </button>
        </div>
    </form>
</div>
```

**`sales_distribution/customer_enquiry/_autocomplete_results.html`**

```html
{% for suggestion in suggestions %}
    <div class="px-4 py-2 hover:bg-blue-100 cursor-pointer text-gray-800"
         hx-on:click="document.getElementById('id_existing_customer_name_input').value = this.innerText; document.getElementById('customer-autocomplete-results').innerHTML = '';">
        {{ suggestion }}
    </div>
{% endfor %}
```

**`sales_distribution/customer_enquiry/_address_section.html`**

```html
<div class="border p-4 rounded-md bg-gray-50">
    <h4 class="text-md font-semibold text-gray-700 mb-4">Address Details</h4>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Registered Office -->
        <div>
            <h5 class="font-medium text-gray-600 mb-2">Registered Office</h5>
            <div class="space-y-4">
                <div>
                    <label for="{{ form.regd_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address <span class="text-red-500">*</span></label>
                    {{ form.regd_address }}
                    {% if form.regd_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.regd_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country <span class="text-red-500">*</span></label>
                    {{ form.regd_country }}
                    {% if form.regd_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_country.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.regd_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State <span class="text-red-500">*</span></label>
                    {{ form.regd_state }}
                    {% if form.regd_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_state.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.regd_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City <span class="text-red-500">*</span></label>
                    {{ form.regd_city }}
                    {% if form.regd_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_city.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.regd_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PIN No. <span class="text-red-500">*</span></label>
                    {{ form.regd_pin_no }}
                    {% if form.regd_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_pin_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.regd_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No. <span class="text-red-500">*</span></label>
                    {{ form.regd_contact_no }}
                    {% if form.regd_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.regd_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No. <span class="text-red-500">*</span></label>
                    {{ form.regd_fax_no }}
                    {% if form.regd_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_fax_no.errors }}</p>{% endif %}
                </div>
            </div>
        </div>

        <!-- Works/Factory -->
        <div>
            <h5 class="font-medium text-gray-600 mb-2">Works/Factory</h5>
            <div class="space-y-4">
                <div>
                    <label for="{{ form.work_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address <span class="text-red-500">*</span></label>
                    {{ form.work_address }}
                    {% if form.work_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.work_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country <span class="text-red-500">*</span></label>
                    {{ form.work_country }}
                    {% if form.work_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_country.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.work_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State <span class="text-red-500">*</span></label>
                    {{ form.work_state }}
                    {% if form.work_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_state.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.work_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City <span class="text-red-500">*</span></label>
                    {{ form.work_city }}
                    {% if form.work_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_city.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.work_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PIN No. <span class="text-red-500">*</span></label>
                    {{ form.work_pin_no }}
                    {% if form.work_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_pin_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.work_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No. <span class="text-red-500">*</span></label>
                    {{ form.work_contact_no }}
                    {% if form.work_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_contact_no.errors %}</div>
                </div>
                <div>
                    <label for="{{ form.work_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No. <span class="text-red-500">*</span></label>
                    {{ form.work_fax_no }}
                    {% if form.work_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_fax_no.errors }}</p>{% endif %}
                </div>
            </div>
        </div>

        <!-- Material Delivery -->
        <div>
            <h5 class="font-medium text-gray-600 mb-2">Material Delivery</h5>
            <div class="space-y-4">
                <div>
                    <label for="{{ form.material_del_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address <span class="text-red-500">*</span></label>
                    {{ form.material_del_address }}
                    {% if form.material_del_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_address.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.material_del_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country <span class="text-red-500">*</span></label>
                    {{ form.material_del_country }}
                    {% if form.material_del_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_country.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.material_del_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State <span class="text-red-500">*</span></label>
                    {{ form.material_del_state }}
                    {% if form.material_del_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_state.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.material_del_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City <span class="text-red-500">*</span></label>
                    {{ form.material_del_city }}
                    {% if form.material_del_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_city.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.material_del_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PIN No. <span class="text-red-500">*</span></label>
                    {{ form.material_del_pin_no }}
                    {% if form.material_del_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_pin_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.material_del_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No. <span class="text-red-500">*</span></label>
                    {{ form.material_del_contact_no }}
                    {% if form.material_del_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.material_del_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No. <span class="text-red-500">*</span></label>
                    {{ form.material_del_fax_no }}
                    {% if form.material_del_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_fax_no.errors }}</p>{% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
```

**`sales_distribution/customer_enquiry/_contact_tax_section.html`**

```html
<div class="border p-4 rounded-md bg-gray-50">
    <h4 class="text-md font-semibold text-gray-700 mb-4">Contact & Statutory Details</h4>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
            <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person <span class="text-red-500">*</span></label>
            {{ form.contact_person }}
            {% if form.contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail <span class="text-red-500">*</span></label>
            {{ form.email }}
            {% if form.email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No. <span class="text-red-500">*</span></label>
            {{ form.contact_no }}
            {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.juridiction_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Juridiction Code <span class="text-red-500">*</span></label>
            {{ form.juridiction_code }}
            {% if form.juridiction_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.juridiction_code.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">ECC.No. <span class="text-red-500">*</span></label>
            {{ form.ecc_no }}
            {% if form.ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ecc_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.range_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Range <span class="text-red-500">*</span></label>
            {{ form.range_field }}
            {% if form.range_field.errors %}<p class="text-red-500 text-xs mt-1">{{ form.range_field.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.commissionurate.id_for_label }}" class="block text-sm font-medium text-gray-700">Commissionurate <span class="text-red-500">*</span></label>
            {{ form.commissionurate }}
            {% if form.commissionurate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.commissionurate.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.divn.id_for_label }}" class="block text-sm font-medium text-gray-700">Divn <span class="text-red-500">*</span></label>
            {{ form.divn }}
            {% if form.divn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.divn.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PAN No. <span class="text-red-500">*</span></label>
            {{ form.pan_no }}
            {% if form.pan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pan_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/VAT No. <span class="text-red-500">*</span></label>
            {{ form.tin_vat_no }}
            {% if form.tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_vat_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/CST No. <span class="text-red-500">*</span></label>
            {{ form.tin_cst_no }}
            {% if form.tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_cst_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.tds_code.id_for_label }}" class="block text-sm font-medium text-gray-700">TDS Code. <span class="text-red-500">*</span></label>
            {{ form.tds_code }}
            {% if form.tds_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tds_code.errors }}</p>{% endif %}
        </div>
    </div>
</div>
```

**`sales_distribution/customer_enquiry/_attachment_section.html`**

```html
<div class="border p-4 rounded-md bg-gray-50">
    <h4 class="text-md font-semibold text-gray-700 mb-4">Attachments</h4>
    <form hx-post="{% url 'temp_attachment_upload' %}" hx-encoding="multipart/form-data" hx-target="body" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center space-x-4 mb-6">
            <label class="block text-sm font-medium text-gray-700 w-24">Attachment</label>
            {{ attachment_form.file_data }}
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300">
                Upload
            </button>
        </div>
        {% if attachment_form.file_data.errors %}
            <p class="text-red-500 text-xs mt-1">{{ attachment_form.file_data.errors }}</p>
        {% endif %}
    </form>

    <div id="temporary-attachments-table-container"
         hx-trigger="load, refreshTemporaryAttachmentsTable from:body"
         hx-get="{% url 'temp_attachment_list' %}"
         hx-target="this"
         hx-swap="innerHTML">
        <!-- Temporary attachments table loaded here via HTMX -->
        <div class="text-center p-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading attachments...</p>
        </div>
    </div>
</div>
```

**`sales_distribution/customer_enquiry/_temporary_attachments_table.html`**

```html
<table class="min-w-full bg-white shadow-md rounded-lg overflow-hidden mt-4">
    <thead class="bg-gray-50 border-b border-gray-200">
        <tr>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Size (Bytes)</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for attachment in temporary_attachments %}
        <tr class="hover:bg-gray-100">
            <td class="py-2 px-4 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 text-sm text-gray-800">{{ attachment.file_name }}</td>
            <td class="py-2 px-4 text-sm text-gray-800">{{ attachment.file_size }}</td>
            <td class="py-2 px-4 text-sm font-medium">
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md shadow-sm transition duration-300"
                    hx-delete="{% url 'temp_attachment_delete' attachment.pk %}"
                    hx-confirm="Are you sure you want to delete this attachment?"
                    hx-target="body"
                    hx-swap="none">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-6 text-center text-gray-600">No attachments uploaded temporarily.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`sales_distribution/customer_enquiry/customer_enquiry_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the enquiry for "<span class="font-medium">{{ object.customer_name }}</span>" (ID: {{ object.enq_id }})?</p>
    <form hx-delete="{% url 'customer_enquiry_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button type="button"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
                    _="on click trigger closeModal from #modal">
                Cancel
            </button>
            <button type="submit"
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`sales_distribution/customer_enquiry/_dropdown_options.html`** (Generic for cascading dropdowns)

```html
<option value="">Select</option>
{% for option in options %}
    <option value="{{ getattr(option, value_field) }}">{{ getattr(option, display_field) }}</option>
{% endfor %}
```

#### 4.5 URLs (`sales_distribution/urls.py`)

```python
from django.urls import path
from .views import (
    CustomerEnquiryListView, CustomerEnquiryTablePartialView,
    CustomerEnquiryCreateUpdateView, CustomerEnquiryDeleteView,
    CustomerAutocompleteView, CustomerDataLoadView,
    GetStatesView, GetCitiesView,
    TemporaryAttachmentUploadView, TemporaryAttachmentDeleteView,
    TemporaryAttachmentTablePartialView, CustomerEnquiryAttachmentDownloadView
)

urlpatterns = [
    # Main Customer Enquiry CRUD
    path('customer-enquiries/', CustomerEnquiryListView.as_view(), name='customer_enquiry_list'),
    path('customer-enquiries/add/', CustomerEnquiryCreateUpdateView.as_view(), name='customer_enquiry_add'),
    path('customer-enquiries/edit/<int:pk>/', CustomerEnquiryCreateUpdateView.as_view(), name='customer_enquiry_edit'),
    path('customer-enquiries/delete/<int:pk>/', CustomerEnquiryDeleteView.as_view(), name='customer_enquiry_delete'),

    # HTMX partials and API endpoints
    path('customer-enquiries/table/', CustomerEnquiryTablePartialView.as_view(), name='customer_enquiry_table'),
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('customer-data-load/', CustomerDataLoadView.as_view(), name='customer_data_load'),

    # Dynamic Dropdowns
    path('get-states/', GetStatesView.as_view(), name='get_states'),
    path('get-cities/', GetCitiesView.as_view(), name='get_cities'),

    # Temporary Attachments
    path('temp-attachments/upload/', TemporaryAttachmentUploadView.as_view(), name='temp_attachment_upload'),
    path('temp-attachments/delete/<int:pk>/', TemporaryAttachmentDeleteView.as_view(), name='temp_attachment_delete'),
    path('temp-attachments/table/', TemporaryAttachmentTablePartialView.as_view(), name='temp_attachment_list'),

    # Permanent Enquiry Attachments Download
    path('enquiry-attachments/<int:pk>/download/', CustomerEnquiryAttachmentDownloadView.as_view(), name='enquiry_attachment_download'),
]

```

#### 4.6 Tests (`sales_distribution/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from unittest.mock import patch, MagicMock
from io import BytesIO

from .models import CustomerEnquiry, TemporaryAttachment, Customer, Country, State, City, CustomerEnquiryAttachment

# Helper to create mock objects for lookup tables
def create_mock_lookup_data():
    Country.objects.create(country_id='C001', name='India')
    Country.objects.create(country_id='C002', name='USA')
    State.objects.create(state_id='S001', country_id='C001', name='Maharashtra')
    State.objects.create(state_id='S002', country_id='C001', name='Gujarat')
    State.objects.create(state_id='S003', country_id='C002', name='California')
    City.objects.create(city_id='CT001', state_id='S001', name='Mumbai')
    City.objects.create(city_id='CT002', state_id='S001', name='Pune')
    City.objects.create(city_id='CT003', state_id='S003', name='Los Angeles')
    Customer.objects.create(
        customer_id='CUST001', customer_name='Acme Corp', comp_id=1,
        regd_address='123 Main St', regd_country_id='C001', regd_state_id='S001', regd_city_id='CT001',
        regd_pin_no='400001', regd_contact_no='12345', regd_fax_no='12346',
        work_address='456 Work St', work_country_id='C001', work_state_id='S001', work_city_id='CT001',
        work_pin_no='400002', work_contact_no='67890', work_fax_no='67891',
        material_del_address='789 Del St', material_del_country_id='C001', material_del_state_id='S001', material_del_city_id='CT001',
        material_del_pin_no='400003', material_del_contact_no='11223', material_del_fax_no='11224',
        contact_person='John Doe', juridiction_code='JC001', commissionurate='CM001',
        tin_vat_no='TVN001', email='<EMAIL>', ecc_no='ECC001', divn='DIV001',
        tin_cst_no='TCN001', contact_no='555-1234', range_field='R001', pan_no='PAN001', tds_code='TDS001'
    )
    Customer.objects.create(customer_id='CUST002', customer_name='Beta Ltd', comp_id=1)


class CustomerEnquiryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        create_mock_lookup_data()
        cls.country = Country.objects.get(country_id='C001')
        cls.state = State.objects.get(state_id='S001')
        cls.city = City.objects.get(city_id='CT001')
        cls.customer = Customer.objects.get(customer_id='CUST001')

        # Create a new enquiry instance for testing
        cls.enquiry = CustomerEnquiry.objects.create(
            session_id='testsession123',
            comp_id=1,
            fin_year_id=1,
            customer=cls.customer,
            customer_name='Acme Corp',
            regd_address='123 Main St', regd_country=cls.country, regd_state=cls.state, regd_city=cls.city,
            regd_pin_no='400001', regd_contact_no='12345', regd_fax_no='12346',
            work_address='456 Work St', work_country=cls.country, work_state=cls.state, work_city=cls.city,
            work_pin_no='400002', work_contact_no='67890', work_fax_no='67891',
            material_del_address='789 Del St', material_del_country=cls.country, material_del_state=cls.state, material_del_city=cls.city,
            material_del_pin_no='400003', material_del_contact_no='11223', material_del_fax_no='11224',
            contact_person='John Doe', juridiction_code='JC001', commissionurate='CM001',
            tin_vat_no='TVN001', email='<EMAIL>', ecc_no='ECC001', divn='DIV001',
            tin_cst_no='TCN001', contact_no='555-1234', range_field='R001', pan_no='PAN001', tds_code='TDS001',
            remark='Test remark', enquiry_for='Product X', flag=1
        )
    
    def test_customer_enquiry_creation(self):
        self.assertEqual(CustomerEnquiry.objects.count(), 1)
        enquiry = CustomerEnquiry.objects.get(enq_id=self.enquiry.enq_id)
        self.assertEqual(enquiry.customer_name, 'Acme Corp')
        self.assertEqual(enquiry.regd_country.name, 'India')
        self.assertEqual(enquiry.customer.customer_id, 'CUST001')

    def test_get_full_address_method(self):
        regd_address = self.enquiry.get_full_address('regd')
        self.assertIn('123 Main St', regd_address)
        self.assertIn('Mumbai', regd_address)
        self.assertIn('Maharashtra', regd_address)
        self.assertIn('India', regd_address)
        self.assertIn('400001', regd_address)
        
        work_address = self.enquiry.get_full_address('work')
        self.assertIn('456 Work St', work_address)

        mat_del_address = self.enquiry.get_full_address('material_del')
        self.assertIn('789 Del St', mat_del_address)

    def test_create_enquiry_with_attachments_new_customer(self):
        temp_file_data = b'test file content'
        temp_file_name = 'test_file.txt'
        TemporaryAttachment.objects.create(
            file_name=temp_file_name, file_size=len(temp_file_data),
            content_type='text/plain', file_data=temp_file_data,
            session_id='new_session', comp_id=1, fin_year_id=1
        )

        form_data = {
            'is_new_customer': True,
            'new_customer_name_input': 'New Customer Inc',
            'regd_address': 'New Address', 'regd_country': 'C001', 'regd_state': 'S001', 'regd_city': 'CT001',
            'regd_pin_no': '100001', 'regd_contact_no': '98765', 'regd_fax_no': '98766',
            'work_address': 'New Work Add', 'work_country': 'C001', 'work_state': 'S001', 'work_city': 'CT001',
            'work_pin_no': '100002', 'work_contact_no': '98767', 'work_fax_no': '98768',
            'material_del_address': 'New Mat Del Add', 'material_del_country': 'C001', 'material_del_state': 'S001', 'material_del_city': 'CT001',
            'material_del_pin_no': '100003', 'material_del_contact_no': '98769', 'material_del_fax_no': '98770',
            'contact_person': 'Jane Doe', 'juridiction_code': 'JC002', 'commissionurate': 'CM002',
            'tin_vat_no': 'TVN002', 'email': '<EMAIL>', 'ecc_no': 'ECC002', 'divn': 'DIV002',
            'tin_cst_no': 'TCN002', 'contact_no': '555-5678', 'range_field': 'R002', 'pan_no': 'PAN002', 'tds_code': 'TDS002',
            'enquiry_for': 'New Product', 'remark': 'New remark'
        }
        
        with patch('sales_distribution.models.timezone.now', return_value=timezone.datetime(2023, 1, 1, 10, 0, 0)):
            new_enquiry = CustomerEnquiry.create_enquiry_with_attachments(
                form_data, 'new_session', 1, 1
            )
        
        self.assertEqual(CustomerEnquiry.objects.count(), 2) # Original + New
        self.assertEqual(new_enquiry.customer_name, 'NEW CUSTOMER INC') # Should be uppercase
        self.assertEqual(new_enquiry.flag, 0)
        self.assertIsNone(new_enquiry.customer)
        self.assertEqual(TemporaryAttachment.objects.count(), 0) # Temp attachment should be moved
        self.assertEqual(CustomerEnquiryAttachment.objects.count(), 1)
        self.assertEqual(CustomerEnquiryAttachment.objects.first().file_name, temp_file_name)


class TemporaryAttachmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        TemporaryAttachment.objects.create(
            file_name='test.jpg', file_size=1024, content_type='image/jpeg',
            file_data=b'dummy_image_data', session_id='s1', comp_id=1, fin_year_id=1
        )

    def test_attachment_creation(self):
        att = TemporaryAttachment.objects.get(file_name='test.jpg')
        self.assertEqual(att.file_size, 1024)
        self.assertEqual(att.content_type, 'image/jpeg')

    def test_delete_attachment(self):
        att = TemporaryAttachment.objects.get(file_name='test.jpg')
        att.delete_attachment()
        self.assertEqual(TemporaryAttachment.objects.count(), 0)


class CustomerEnquiryAttachmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        create_mock_lookup_data()
        cls.country = Country.objects.get(country_id='C001')
        cls.state = State.objects.get(state_id='S001')
        cls.city = City.objects.get(city_id='CT001')
        cls.customer = Customer.objects.get(customer_id='CUST001')

        cls.enquiry = CustomerEnquiry.objects.create(
            session_id='testsession123', comp_id=1, fin_year_id=1,
            customer=cls.customer, customer_name='Acme Corp',
            regd_address='123 Main St', regd_country=cls.country, regd_state=cls.state, regd_city=cls.city,
            regd_pin_no='400001', regd_contact_no='12345', regd_fax_no='12346',
            work_address='456 Work St', work_country=cls.country, work_state=cls.state, work_city=cls.city,
            work_pin_no='400002', work_contact_no='67890', work_fax_no='67891',
            material_del_address='789 Del St', material_del_country=cls.country, material_del_state=cls.state, material_del_city=cls.city,
            material_del_pin_no='400003', material_del_contact_no='11223', material_del_fax_no='11224',
            contact_person='John Doe', juridiction_code='JC001', commissionurate='CM001',
            tin_vat_no='TVN001', email='<EMAIL>', ecc_no='ECC001', divn='DIV001',
            tin_cst_no='TCN001', contact_no='555-1234', range_field='R001', pan_no='PAN001', tds_code='TDS001',
            remark='Test remark', enquiry_for='Product X', flag=1
        )
        cls.attachment = CustomerEnquiryAttachment.objects.create(
            enquiry=cls.enquiry, file_name='report.pdf', file_size=2048,
            content_type='application/pdf', file_data=b'dummy_pdf_data',
            session_id='s1', comp_id=1, fin_year_id=1
        )

    def test_enquiry_attachment_creation(self):
        self.assertEqual(CustomerEnquiryAttachment.objects.count(), 1)
        att = CustomerEnquiryAttachment.objects.get(id=self.attachment.id)
        self.assertEqual(att.file_name, 'report.pdf')
        self.assertEqual(att.enquiry.enq_id, self.enquiry.enq_id)

    def test_download_url_method(self):
        url = self.attachment.download_url()
        self.assertIn(f'/sales_distribution/enquiry-attachments/{self.attachment.pk}/download/', url)


class CustomerEnquiryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        create_mock_lookup_data()
        cls.country = Country.objects.get(country_id='C001')
        cls.state = State.objects.get(state_id='S001')
        cls.city = City.objects.get(city_id='CT001')
        cls.customer_existing = Customer.objects.get(customer_id='CUST001')
        cls.customer_new = Customer.objects.get(customer_id='CUST002') # Using this as a dummy new customer
        
        # Create an existing enquiry
        cls.enquiry_existing = CustomerEnquiry.objects.create(
            session_id='testsession_exist',
            comp_id=1,
            fin_year_id=1,
            customer=cls.customer_existing,
            customer_name='Acme Corp',
            regd_address='123 Main St', regd_country=cls.country, regd_state=cls.state, regd_city=cls.city,
            regd_pin_no='400001', regd_contact_no='12345', regd_fax_no='12346',
            work_address='456 Work St', work_country=cls.country, work_state=cls.state, work_city=cls.city,
            work_pin_no='400002', work_contact_no='67890', work_fax_no='67891',
            material_del_address='789 Del St', material_del_country=cls.country, material_del_state=cls.state, material_del_city=cls.city,
            material_del_pin_no='400003', material_del_contact_no='11223', material_del_fax_no='11224',
            contact_person='John Doe', juridiction_code='JC001', commissionurate='CM001',
            tin_vat_no='TVN001', email='<EMAIL>', ecc_no='ECC001', divn='DIV001',
            tin_cst_no='TCN001', contact_no='555-1234', range_field='R001', pan_no='PAN001', tds_code='TDS001',
            remark='Test remark', enquiry_for='Product X', flag=1
        )

    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session['compid'] = 1
        self.session['finyear'] = 1
        self.session.save()

        # Mock get_user_context_data to return fixed values for tests
        patcher = patch('sales_distribution.views.get_user_context_data', return_value={
            'comp_id': 1,
            'fin_year_id': 1,
            'session_key': self.client.session.session_key
        })
        self.mock_get_user_context_data = patcher.start()
        self.addCleanup(patcher.stop)

    def tearDown(self):
        # Clean up temporary attachments if any
        TemporaryAttachment.objects.all().delete()

    def _create_form_data(self, is_new_customer=True, customer_name="Test Customer"):
        data = {
            'regd_address': 'Regd Address', 'regd_country': 'C001', 'regd_state': 'S001', 'regd_city': 'CT001',
            'regd_pin_no': '111111', 'regd_contact_no': '1111111111', 'regd_fax_no': '111111111',
            'work_address': 'Work Address', 'work_country': 'C001', 'work_state': 'S001', 'work_city': 'CT001',
            'work_pin_no': '222222', 'work_contact_no': '2222222222', 'work_fax_no': '222222222',
            'material_del_address': 'Mat Del Address', 'material_del_country': 'C001', 'material_del_state': 'S001', 'material_del_city': 'CT001',
            'material_del_pin_no': '333333', 'material_del_contact_no': '3333333333', 'material_del_fax_no': '333333333',
            'contact_person': 'Test Contact', 'juridiction_code': 'JC000', 'commissionurate': 'CM000',
            'tin_vat_no': 'TVN000', 'email': '<EMAIL>', 'ecc_no': 'ECC000', 'divn': 'DIV000',
            'tin_cst_no': 'TCN000', 'contact_no': '0000000000', 'range_field': 'R000', 'pan_no': 'PAN000', 'tds_code': 'TDS000',
            'enquiry_for': 'General Inquiry', 'remark': 'No remarks'
        }
        if is_new_customer:
            data['is_new_customer'] = 'True'
            data['new_customer_name_input'] = customer_name
        else:
            data['is_new_customer'] = 'False'
            data['existing_customer_name_input'] = customer_name
        return data

    # Test ListView
    def test_list_view_get(self):
        response = self.client.get(reverse('customer_enquiry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/customer_enquiry_list.html')
        self.assertIn('enquiries', response.context)
        self.assertContains(response, self.enquiry_existing.customer_name)

    def test_list_view_htmx_table_partial(self):
        response = self.client.get(reverse('customer_enquiry_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_customer_enquiry_table.html')
        self.assertContains(response, self.enquiry_existing.customer_name)

    # Test CreateView
    def test_create_view_get(self):
        response = self.client.get(reverse('customer_enquiry_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/customer_enquiry_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['is_edit'])

    @patch('sales_distribution.models.CustomerEnquiry.create_enquiry_with_attachments', autospec=True)
    def test_create_view_post_new_customer_success(self, mock_create_enquiry):
        mock_enquiry_instance = MagicMock()
        mock_enquiry_instance.enq_id = 99
        mock_create_enquiry.return_value = mock_enquiry_instance

        initial_count = CustomerEnquiry.objects.count()
        form_data = self._create_form_data(is_new_customer=True, customer_name="New Test Customer")
        
        response = self.client.post(reverse('customer_enquiry_add'), form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList, closeCustomerEnquiryModal')
        self.assertEqual(response.headers['HX-Redirect'], reverse('customer_enquiry_list'))
        self.assertEqual(CustomerEnquiry.objects.count(), initial_count) # create_enquiry_with_attachments mocks DB write
        mock_create_enquiry.assert_called_once()
        self.assertContains(self.client.get(reverse('customer_enquiry_list')), "Customer Enquiry generated successfully.")

    def test_create_view_post_existing_customer_success(self):
        initial_count = CustomerEnquiry.objects.count()
        form_data = self._create_form_data(is_new_customer=False, customer_name=str(self.customer_existing))
        
        response = self.client.post(reverse('customer_enquiry_add'), form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList, closeCustomerEnquiryModal')
        self.assertEqual(response.headers['HX-Redirect'], reverse('customer_enquiry_list'))
        self.assertEqual(CustomerEnquiry.objects.count(), initial_count + 1) # Should add one
        self.assertTrue(CustomerEnquiry.objects.filter(customer_name='Acme Corp').exists())
        self.assertContains(self.client.get(reverse('customer_enquiry_list')), "Customer Enquiry generated successfully.")

    def test_create_view_post_validation_error(self):
        form_data = self._create_form_data(is_new_customer=True)
        form_data['regd_address'] = '' # Make it invalid
        response = self.client.post(reverse('customer_enquiry_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/customer_enquiry_form.html')
        self.assertContains(response, 'This field is required.')


    # Test UpdateView
    def test_update_view_get(self):
        response = self.client.get(reverse('customer_enquiry_edit', args=[self.enquiry_existing.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/customer_enquiry_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['is_edit'])
        self.assertEqual(response.context['form'].instance.pk, self.enquiry_existing.pk)

    def test_update_view_post_success(self):
        updated_remark = "Updated remark for test"
        form_data = self._create_form_data(is_new_customer=False, customer_name=str(self.customer_existing))
        form_data['remark'] = updated_remark
        form_data['existing_customer_name_input'] = str(self.customer_existing) # Ensure this field is set for existing customer

        response = self.client.post(reverse('customer_enquiry_edit', args=[self.enquiry_existing.pk]), form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList, closeCustomerEnquiryModal')
        self.assertEqual(response.headers['HX-Redirect'], reverse('customer_enquiry_list'))
        
        self.enquiry_existing.refresh_from_db()
        self.assertEqual(self.enquiry_existing.remark, updated_remark)
        self.assertContains(self.client.get(reverse('customer_enquiry_list')), "Customer Enquiry updated successfully.")

    def test_update_view_post_validation_error(self):
        form_data = self._create_form_data(is_new_customer=False, customer_name=str(self.customer_existing))
        form_data['regd_address'] = '' # Make invalid
        response = self.client.post(reverse('customer_enquiry_edit', args=[self.enquiry_existing.pk]), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/customer_enquiry_form.html')
        self.assertContains(response, 'This field is required.')

    # Test DeleteView
    def test_delete_view_get(self):
        response = self.client.get(reverse('customer_enquiry_delete', args=[self.enquiry_existing.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/customer_enquiry_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].pk, self.enquiry_existing.pk)

    def test_delete_view_post_success(self):
        initial_count = CustomerEnquiry.objects.count()
        response = self.client.delete(reverse('customer_enquiry_delete', args=[self.enquiry_existing.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList, closeCustomerEnquiryModal')
        self.assertEqual(CustomerEnquiry.objects.count(), initial_count - 1)
        self.assertFalse(CustomerEnquiry.objects.filter(pk=self.enquiry_existing.pk).exists())
        self.assertContains(self.client.get(reverse('customer_enquiry_list')), "Customer Enquiry deleted successfully.")
    
    # Test HTMX Autocomplete
    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'q': 'acme'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Acme Corp [CUST001]')
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_autocomplete_results.html')

    # Test Customer Data Load (btnView_Click equivalent)
    def test_customer_data_load_view(self):
        response = self.client.get(reverse('customer_data_load'), {'customer_name': 'Acme Corp [CUST001]'})
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertEqual(json_response['regd_address'], '123 Main St')
        self.assertEqual(json_response['email'], '<EMAIL>')

    def test_customer_data_load_view_not_found(self):
        response = self.client.get(reverse('customer_data_load'), {'customer_name': 'NonExistent [NON001]'})
        self.assertEqual(response.status_code, 404)
        self.assertIn('Customer not found', response.json()['error'])

    # Test Cascading Dropdowns
    def test_get_states_view(self):
        response = self.client.get(reverse('get_states'), {'regd_country': 'C001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="S001">Maharashtra</option>')
        self.assertContains(response, '<option value="S002">Gujarat</option>')
        self.assertNotContains(response, '<option value="S003">California</option>')

    def test_get_cities_view(self):
        response = self.client.get(reverse('get_cities'), {'regd_state': 'S001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="CT001">Mumbai</option>')
        self.assertContains(response, '<option value="CT002">Pune</option>')
        self.assertNotContains(response, '<option value="CT003">Los Angeles</option>')

    # Test Temporary Attachment Upload/Delete
    def test_temp_attachment_upload_view(self):
        test_file = SimpleUploadedFile("file.txt", b"file_content", content_type="text/plain")
        response = self.client.post(reverse('temp_attachment_upload'), {'file_data': test_file}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTemporaryAttachmentsTable')
        self.assertEqual(TemporaryAttachment.objects.count(), 1)
        self.assertEqual(TemporaryAttachment.objects.first().file_name, 'file.txt')

    def test_temp_attachment_delete_view(self):
        temp_att = TemporaryAttachment.objects.create(
            file_name='temp_to_delete.pdf', file_size=100, content_type='application/pdf',
            file_data=b'pdf_content', session_id=self.client.session.session_key, comp_id=1, fin_year_id=1
        )
        self.assertEqual(TemporaryAttachment.objects.count(), 1)
        response = self.client.delete(reverse('temp_attachment_delete', args=[temp_att.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTemporaryAttachmentsTable')
        self.assertEqual(TemporaryAttachment.objects.count(), 0)

    def test_temp_attachment_list_partial_view(self):
        TemporaryAttachment.objects.create(
            file_name='list_test.pdf', file_size=100, content_type='application/pdf',
            file_data=b'pdf_content', session_id=self.client.session.session_key, comp_id=1, fin_year_id=1
        )
        response = self.client.get(reverse('temp_attachment_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customer_enquiry/_temporary_attachments_table.html')
        self.assertContains(response, 'list_test.pdf')

    # Test Enquiry Attachment Download
    def test_enquiry_attachment_download_view(self):
        attachment_data = b'This is some test attachment content.'
        attachment_name = 'download_me.txt'
        enquiry_attachment = CustomerEnquiryAttachment.objects.create(
            enquiry=self.enquiry_existing, file_name=attachment_name, file_size=len(attachment_data),
            content_type='text/plain', file_data=attachment_data,
            session_id='dummy_session', comp_id=1, fin_year_id=1
        )
        response = self.client.get(reverse('enquiry_attachment_download', args=[enquiry_attachment.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/plain')
        self.assertEqual(response['Content-Disposition'], f'attachment; filename="download-me.txt"')
        self.assertEqual(response.content, attachment_data)

    def test_enquiry_attachment_download_view_not_found(self):
        response = self.client.get(reverse('enquiry_attachment_download', args=[9999]))
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Updates:**
    -   **Form Submission:** All `POST` requests from the main form (Submit, Upload) are handled via `hx-post` with `hx-swap="none"` and `HX-Trigger` headers to refresh the list or attachments table.
    -   **Autocomplete:** `hx-get` on `keyup changed delay:500ms` for `id_existing_customer_name_input` targeting `customer-autocomplete-results` div.
    -   **"Search" Button (`btnView`):** `hx-get` on click, includes the existing customer input, and uses `_` (Hyperscript) to parse the JSON response and update form fields. This demonstrates a powerful way to update multiple fields without manual JS.
    -   **Cascading Dropdowns:** `hx-get` on `change` events for Country and State dropdowns, targeting subsequent dropdowns.
    -   **Temporary Attachments:** `hx-post` for upload, `hx-delete` for deleting, both triggering a `hx-get` to refresh the `_temporary_attachments_table.html` partial.
    -   **DataTables Reload:** `hx-trigger="refreshCustomerEnquiryList from:body"` is set on the main `customerEnquiryTable-container` to automatically reload the table content after successful form submissions or deletions.
    -   **Modals:** HTMX loads form/delete confirmation content into `#modalContent`, and Alpine.js manages the `hidden` class of the main `#modal` div.

-   **Alpine.js for UI State Management:**
    -   **Radio Button Toggle:** The main form `x-data="{ isNewCustomer: ... }"` component uses `x-show` directives to conditionally display "New Customer" or "Existing Customer" sections. `x-model` binds radio button selection to `isNewCustomer`.
    -   **Modal Control:** The `modal` div uses `x-data="{ isOpen: false }"` and `x-show="isOpen"` to control visibility. Buttons inside the modal trigger `open()` or `close()` methods. `_="on click add .is-active to #modal"` is used for HTMX to activate the modal on button click. A custom event `closeCustomerEnquiryModal` triggered by HTMX on form success or delete closes the modal.

-   **DataTables for List Views:**
    -   The `customer_enquiry_list.html` includes a placeholder div (`customerEnquiryTable-container`) that `hx-get`s the `_customer_enquiry_table.html` partial.
    -   A JavaScript snippet in `extra_js` block (inside `customer_enquiry_list.html`) listens for the `htmx:afterSwap` event specifically for the table container. Once the table HTML is loaded by HTMX, it initializes DataTables on `$('#customerEnquiryTable')`. This ensures DataTables is correctly applied to dynamically loaded content.

-   **No Additional JavaScript Frameworks:**
    -   The solution exclusively relies on HTMX for server-side generated UI interactions and Alpine.js for minimal client-side reactive state, avoiding heavy JavaScript frameworks like React, Vue, or Angular.

-   **DRY Template Inheritance:**
    -   All main templates (`customer_enquiry_list.html`) extend `core/base.html` to inherit common structure, CDN links (for HTMX, Alpine.js, DataTables, Tailwind CSS), and other global components. Partial templates (e.g., `_customer_enquiry_table.html`, `_address_section.html`) are used for reusable components that are either included or loaded via HTMX.

---