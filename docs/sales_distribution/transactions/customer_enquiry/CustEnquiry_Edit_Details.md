Here's a comprehensive Django modernization plan for your ASP.NET application, designed for AI-assisted automation and focused on business value. This plan breaks down the migration into actionable steps, focusing on modern Django patterns, HTMX, Alpine.js, and DataTables for a highly interactive user experience without traditional JavaScript frameworks.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with at least two primary tables: `SD_Cust_Enquiry_Master` and `SD_Cust_Enquiry_Attach_Master`. It also implicitly relies on lookup tables for `Country`, `State`, and `City`.

-   **Main Enquiry Table:** `SD_Cust_Enquiry_Master`
    -   Columns inferred from `Update_Click` and `Page_Load`:
        -   `EnqId` (likely an integer primary key)
        -   `CustomerId` (integer, likely a foreign key to a Customer table)
        -   `CustomerName` (string)
        -   `SysDate`, `SysTime`, `SessionId` (for auditing/tracking)
        -   `RegdAddress`, `RegdCountry`, `RegdState`, `RegdCity`, `RegdPinNo`, `RegdContactNo`, `RegdFaxNo`
        -   `WorkAddress`, `WorkCountry`, `WorkState`, `WorkCity`, `WorkPinNo`, `WorkContactNo`, `WorkFaxNo`
        -   `MaterialDelAddress`, `MaterialDelCountry`, `MaterialDelState`, `MaterialDelCity`, `MaterialDelPinNo`, `MaterialDelContactNo`, `MaterialDelFaxNo`
        -   `ContactPerson`, `JuridictionCode`, `Commissionurate`, `TinVatNo`, `Email`, `EccNo`, `Divn`, `TinCstNo`, `ContactNo` (general), `Range`, `PanNo`, `TDSCode`, `Remark`, `EnquiryFor`

-   **Attachment Table:** `SD_Cust_Enquiry_Attach_Master`
    -   Columns from `SqlDataSource1`:
        -   `Id` (integer primary key)
        -   `EnqId` (integer, foreign key to `SD_Cust_Enquiry_Master`)
        -   `CompId`, `FinYearId`, `SessionId` (for multi-tenancy/auditing)
        -   `FileName` (string)
        -   `FileSize` (double/float)
        -   `ContentType` (string)
        -   `FileData` (binary/object)

-   **Lookup Tables (Inferred):**
    -   `Country` (with a `CId` and `CountryName` or similar)
    -   `State` (with an `SId`, `StateName`, and a `CId` foreign key)
    -   `City` (with a `CityId`, `CityName`, and an `SId` foreign key)

### Step 2: Identify Backend Functionality

The ASP.NET page handles the editing of customer enquiry details and managing associated attachments.

-   **Customer Enquiry Details (SD_Cust_Enquiry_Master):**
    -   **Read (R):** On `Page_Load`, existing enquiry data is fetched and displayed in various input controls.
    -   **Update (U):** The `Update_Click` event handler performs a comprehensive update operation, saving all form fields back to the `SD_Cust_Enquiry_Master` table. It includes extensive server-side validation.

-   **Customer Enquiry Attachments (SD_Cust_Enquiry_Attach_Master):**
    -   **Create (C):** The `Button1_Click` event handles uploading a new file, inserting its details and binary data into the `SD_Cust_Enquiry_Attach_Master` table.
    -   **Read (R):** The `SqlDataSource1` and `GridView1` display a list of existing attachments for the current enquiry.
    -   **Delete (D):** The `GridView1` includes a `CommandField` for deletion, triggered by `GridView1_RowDataBound` and `SqlDataSource1`'s `DeleteCommand`.
    -   **Download:** A `HyperLinkField` is used to download attachments, pointing to a `DownloadFile.aspx` handler.

-   **Dropdown Chaining:** `DDListEditRegdCountry_SelectedIndexChanged`, `DDListEditWorkCountry_SelectedIndexChanged`, `DDListEditMaterialDelCountry_SelectedIndexChanged` trigger updates to corresponding State dropdowns. Similarly, State dropdown changes update City dropdowns. This requires dynamic loading of options.

-   **Validation:** Extensive client-side (`RequiredFieldValidator`, `RegularExpressionValidator`) and server-side validation is present in the ASP.NET code, particularly in the `Update_Click` method.

### Step 3: Infer UI Components

The page is a detailed form with a nested table for attachments.

-   **Main Form:**
    -   Labels (`hfEnqId`, `lblCustName`, `hfCustId`) for non-editable display.
    -   Multiple `TextBox` controls for text input (e.g., Address, PIN, Contact, various codes). Many are `TextMode="MultiLine"`.
    -   Multiple `DropDownList` controls for Country, State, City selections.
    -   `Button` controls for `Update` and `Cancel`.
    -   `FileUpload` control for attachment upload.
-   **Attachments List (`GridView1`):**
    -   A tabular display with columns for "SN", "FileName", "FileSize", "ContentType", and "Actions" (Delete, Download).
    -   Pagination is enabled (`AllowPaging="True"`).
    -   Client-side confirmation for Delete and Download.

### Step 4: Generate Django Code

We will create a new Django application, perhaps named `sales` or `enquiries`, to house these components.

#### 4.1 Models

We will define models for `CustomerEnquiry`, `CustomerEnquiryAttachment`, and simplified `Country`, `State`, `City` models, assuming they are part of a larger ERP system and might exist as shared lookup tables. For this exercise, we'll place them in the `sales` app.

```python
# sales/models.py
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import re

# Assuming Country, State, City exist as lookup tables in the same DB
# Or, if they are part of a 'core' app, they would be imported from there.
# For this example, we define simple placeholder models.

class Country(models.Model):
    # Assuming 'CId' maps to 'id' (Django's default PK) or a custom primary key
    name = models.CharField(db_column='CountryName', max_length=100, unique=True)

    class Meta:
        managed = False  # Set to False if table exists outside Django migrations
        db_table = 'Country' # Replace with actual table name if different
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    # Assuming 'SId' maps to 'id'
    country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='CId')
    name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'State' # Replace with actual table name if different
        verbose_name = 'State'
        verbose_name_plural = 'States'
        unique_together = ('country', 'name') # Example, if unique by country

    def __str__(self):
        return f"{self.name} ({self.country.name})"

class City(models.Model):
    # Assuming 'CityId' maps to 'id'
    state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='SId')
    name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'City' # Replace with actual table name if different
        verbose_name = 'City'
        verbose_name_plural = 'Cities'
        unique_together = ('state', 'name') # Example, if unique by state

    def __str__(self):
        return f"{self.name} ({self.state.name})"


class CustomerEnquiry(models.Model):
    # Mapped from SD_Cust_Enquiry_Master
    # Assuming EnqId is the primary key. If not, Django will auto-create 'id'.
    enq_id = models.AutoField(db_column='EnqId', primary_key=True) # Or models.IntegerField if PK is not auto-incremented by DB

    # Foreign key to Customer (assuming a Customer model exists elsewhere)
    # For this example, we'll just use a char field for customer_id and customer_name
    customer_id = models.IntegerField(db_column='CustomerId', verbose_name="Customer ID")
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer's Name")

    # Audit fields
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)

    # Registered Office Address
    regd_address = models.TextField(db_column='RegdAddress', verbose_name="Address (Regd.)")
    regd_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='RegdCountry', related_name='regd_enquiries', null=True, blank=True)
    regd_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='RegdState', related_name='regd_enquiries', null=True, blank=True)
    regd_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='RegdCity', related_name='regd_enquiries', null=True, blank=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20, verbose_name="PIN No. (Regd.)")
    regd_contact_no = models.CharField(db_column='RegdContactNo', max_length=50, verbose_name="Contact No. (Regd.)")
    regd_fax_no = models.CharField(db_column='RegdFaxNo', max_length=50, verbose_name="Fax No. (Regd.)")

    # Works/Factory Address
    work_address = models.TextField(db_column='WorkAddress', verbose_name="Address (Works/Factory)")
    work_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='WorkCountry', related_name='work_enquiries', null=True, blank=True)
    work_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='WorkState', related_name='work_enquiries', null=True, blank=True)
    work_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='WorkCity', related_name='work_enquiries', null=True, blank=True)
    work_pin_no = models.CharField(db_column='WorkPinNo', max_length=20, verbose_name="PIN No. (Works/Factory)")
    work_contact_no = models.CharField(db_column='WorkContactNo', max_length=50, verbose_name="Contact No. (Works/Factory)")
    work_fax_no = models.CharField(db_column='WorkFaxNo', max_length=50, verbose_name="Fax No. (Works/Factory)")

    # Material Delivery Address
    material_del_address = models.TextField(db_column='MaterialDelAddress', verbose_name="Address (Material Del.)")
    material_del_country = models.ForeignKey(Country, on_delete=models.PROTECT, db_column='MaterialDelCountry', related_name='material_enquiries', null=True, blank=True)
    material_del_state = models.ForeignKey(State, on_delete=models.PROTECT, db_column='MaterialDelState', related_name='material_enquiries', null=True, blank=True)
    material_del_city = models.ForeignKey(City, on_delete=models.PROTECT, db_column='MaterialDelCity', related_name='material_enquiries', null=True, blank=True)
    material_del_pin_no = models.CharField(db_column='MaterialDelPinNo', max_length=20, verbose_name="PIN No. (Material Del.)")
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, verbose_name="Contact No. (Material Del.)")
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, verbose_name="Fax No. (Material Del.)")

    # Other details
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, verbose_name="Contact Person")
    juridiction_code = models.CharField(db_column='JuridictionCode', max_length=50, verbose_name="Juridiction Code")
    commissionurate = models.CharField(db_column='Commissionurate', max_length=100, verbose_name="Commissionurate")
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, verbose_name="TIN/VAT No.")
    email = models.EmailField(db_column='Email', max_length=100, verbose_name="E-mail")
    ecc_no = models.CharField(db_column='EccNo', max_length=50, verbose_name="ECC.No.")
    divn = models.CharField(db_column='Divn', max_length=50, verbose_name="Divn")
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, verbose_name="TIN/CST No.")
    contact_no = models.CharField(db_column='ContactNo', max_length=50, verbose_name="Contact No.") # General contact no
    range_field = models.CharField(db_column='Range', max_length=50, verbose_name="Range") # Renamed 'Range' to 'range_field' to avoid keyword conflict
    pan_no = models.CharField(db_column='PanNo', max_length=50, verbose_name="PAN No.")
    tds_code = models.CharField(db_column='TDSCode', max_length=50, verbose_name="TDS Code")
    remark = models.TextField(db_column='Remark', null=True, blank=True, verbose_name="Remarks")
    enquiry_for = models.TextField(db_column='EnquiryFor', verbose_name="Enquiry For")

    class Meta:
        managed = False  # Set to False if table exists outside Django migrations
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"Enquiry {self.enq_id} for {self.customer_name}"

    def update_enquiry_details(self, data):
        """
        Business logic to update multiple fields of the enquiry.
        This consolidates the massive update logic from the C# code-behind.
        """
        for field, value in data.items():
            setattr(self, field, value)
        self.save()
        return self

class CustomerEnquiryAttachment(models.Model):
    # Mapped from SD_Cust_Enquiry_Attach_Master
    # Assuming Id is the primary key
    id = models.AutoField(db_column='Id', primary_key=True)
    enquiry = models.ForeignKey(CustomerEnquiry, on_delete=models.CASCADE, db_column='EnqId', related_name='attachments')

    # Audit fields
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)

    file_name = models.CharField(db_column='FileName', max_length=255)
    file_size = models.BigIntegerField(db_column='FileSize') # Changed to BigIntegerField for file size
    content_type = models.CharField(db_column='ContentType', max_length=100)
    file_data = models.BinaryField(db_column='FileData') # For storing binary file data

    class Meta:
        managed = False  # Set to False if table exists outside Django migrations
        db_table = 'SD_Cust_Enquiry_Attach_Master'
        verbose_name = 'Customer Enquiry Attachment'
        verbose_name_plural = 'Customer Enquiry Attachments'

    def __str__(self):
        return self.file_name

    def download_url(self):
        """Generates the URL for downloading this attachment."""
        from django.urls import reverse
        return reverse('sales:enquiry_attachment_download', args=[self.pk])

    def delete_attachment(self):
        """Business logic for deleting an attachment."""
        self.delete()
        return True

```

#### 4.2 Forms

We will create a `ModelForm` for the `CustomerEnquiry` to handle its fields and a separate form for `CustomerEnquiryAttachment` for file uploads. The dropdowns will be handled dynamically.

```python
# sales/forms.py
from django import forms
from .models import CustomerEnquiry, CustomerEnquiryAttachment, Country, State, City

# Helper for common widget attributes
COMMON_WIDGET_ATTRS = {
    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
    'x-model': 'formData.[field_name]' # For Alpine.js integration
}

class CustomerEnquiryForm(forms.ModelForm):
    # Fields that need specific setup or initial population
    regd_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        label="Country",
        widget=forms.Select(attrs={
            **COMMON_WIDGET_ATTRS,
            'hx-get': "{% url 'sales:get_states_by_country' %}",
            'hx-target': '#regd_state_container',
            'hx-trigger': 'change',
            'name': 'regd_country' # Ensure name is correct for hx-post
        }),
        required=False, # Make required in clean method for better validation
        empty_label="Select"
    )
    regd_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Will be populated dynamically
        label="State",
        widget=forms.Select(attrs={
            **COMMON_WIDGET_ATTRS,
            'hx-get': "{% url 'sales:get_cities_by_state' %}",
            'hx-target': '#regd_city_container',
            'hx-trigger': 'change',
            'name': 'regd_state'
        }),
        required=False,
        empty_label="Select"
    )
    regd_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Will be populated dynamically
        label="City",
        widget=forms.Select(attrs={
            **COMMON_WIDGET_ATTRS,
            'name': 'regd_city'
        }),
        required=False,
        empty_label="Select"
    )

    work_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="Country", widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'hx-get': "{% url 'sales:get_states_by_country' %}", 'hx-target': '#work_state_container', 'hx-trigger': 'change', 'name': 'work_country'}), required=False, empty_label="Select")
    work_state = forms.ModelChoiceField(queryset=State.objects.none(), label="State", widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'hx-get': "{% url 'sales:get_cities_by_state' %}", 'hx-target': '#work_city_container', 'hx-trigger': 'change', 'name': 'work_state'}), required=False, empty_label="Select")
    work_city = forms.ModelChoiceField(queryset=City.objects.none(), label="City", widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'name': 'work_city'}), required=False, empty_label="Select")

    material_del_country = forms.ModelChoiceField(queryset=Country.objects.all(), label="Country", widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'hx-get': "{% url 'sales:get_states_by_country' %}", 'hx-target': '#material_del_state_container', 'hx-trigger': 'change', 'name': 'material_del_country'}), required=False, empty_label="Select")
    material_del_state = forms.ModelChoiceField(queryset=State.objects.none(), label="State", widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'hx-get': "{% url 'sales:get_cities_by_state' %}", 'hx-target': '#material_del_city_container', 'hx-trigger': 'change', 'name': 'material_del_state'}), required=False, empty_label="Select")
    material_del_city = forms.ModelChoiceField(queryset=City.objects.none(), label="City", widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'name': 'material_del_city'}), required=False, empty_label="Select")


    class Meta:
        model = CustomerEnquiry
        # Exclude read-only fields like enq_id, customer_id, customer_name, audit fields
        exclude = ['enq_id', 'customer_id', 'customer_name', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id']
        widgets = {
            'regd_address': forms.Textarea(attrs={**COMMON_WIDGET_ATTRS, 'rows': 3}),
            'regd_pin_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'regd_contact_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'regd_fax_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),

            'work_address': forms.Textarea(attrs={**COMMON_WIDGET_ATTRS, 'rows': 3}),
            'work_pin_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'work_contact_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'work_fax_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),

            'material_del_address': forms.Textarea(attrs={**COMMON_WIDGET_ATTRS, 'rows': 3}),
            'material_del_pin_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'material_del_contact_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'material_del_fax_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),

            'contact_person': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'juridiction_code': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'commissionurate': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'tin_vat_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'email': forms.EmailInput(attrs=COMMON_WIDGET_ATTRS),
            'ecc_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'divn': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'tin_cst_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'contact_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'range_field': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'pan_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'tds_code': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'remark': forms.Textarea(attrs={**COMMON_WIDGET_ATTRS, 'rows': 3}),
            'enquiry_for': forms.Textarea(attrs={**COMMON_WIDGET_ATTRS, 'rows': 5}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize dropdowns based on existing instance data if available
        if self.instance.pk:
            if self.instance.regd_country:
                self.fields['regd_state'].queryset = State.objects.filter(country=self.instance.regd_country)
            if self.instance.regd_state:
                self.fields['regd_city'].queryset = City.objects.filter(state=self.instance.regd_state)

            if self.instance.work_country:
                self.fields['work_state'].queryset = State.objects.filter(country=self.instance.work_country)
            if self.instance.work_state:
                self.fields['work_city'].queryset = City.objects.filter(state=self.instance.work_state)

            if self.instance.material_del_country:
                self.fields['material_del_state'].queryset = State.objects.filter(country=self.instance.material_del_country)
            if self.instance.material_del_state:
                self.fields['material_del_city'].queryset = City.objects.filter(state=self.instance.material_del_state)

    def clean(self):
        cleaned_data = super().clean()

        # Replicate ASP.NET's server-side validation for required fields
        required_fields = [
            'regd_address', 'regd_country', 'regd_state', 'regd_city', 'regd_pin_no', 'regd_contact_no', 'regd_fax_no',
            'work_address', 'work_country', 'work_state', 'work_city', 'work_pin_no', 'work_contact_no', 'work_fax_no',
            'material_del_address', 'material_del_country', 'material_del_state', 'material_del_city', 'material_del_pin_no', 'material_del_contact_no', 'material_del_fax_no',
            'contact_person', 'juridiction_code', 'commissionurate', 'tin_vat_no', 'email', 'ecc_no', 'divn', 'tin_cst_no',
            'contact_no', 'range_field', 'pan_no', 'tds_code', 'enquiry_for'
        ]

        for field_name in required_fields:
            if not cleaned_data.get(field_name):
                self.add_error(field_name, _("This field is required."))
        
        # Email validation is handled by EmailField, but if it was a regex check
        # if 'email' in cleaned_data and not re.match(r"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*", cleaned_data['email']):
        #     self.add_error('email', _("Enter a valid email address."))

        return cleaned_data


class CustomerEnquiryAttachmentForm(forms.ModelForm):
    # This form is only for file upload
    file = forms.FileField(
        label="Select File",
        widget=forms.FileInput(attrs={
            'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100',
            'hx-onchange': 'this.closest("form").requestSubmit()' # Auto-submit on file selection
        }),
        required=True
    )

    class Meta:
        model = CustomerEnquiryAttachment
        fields = [] # We'll handle data manually in the view after file upload
        # No 'file_data' or 'file_name' in fields directly, as they are populated from 'file' field
        # The actual CustomerEnquiryAttachment model fields are populated from the uploaded file
        # 'file_name', 'file_size', 'content_type', 'file_data'

    def clean_file(self):
        uploaded_file = self.cleaned_data.get('file')
        if not uploaded_file:
            raise forms.ValidationError("No file was uploaded.")
        # Add any specific file type or size validation here if needed
        # For example, to limit file size:
        # MAX_UPLOAD_SIZE = 10 * 1024 * 1024 # 10 MB
        # if uploaded_file.size > MAX_UPLOAD_SIZE:
        #     raise forms.ValidationError(f"File size cannot exceed {MAX_UPLOAD_SIZE / (1024*1024):.0f} MB.")
        return uploaded_file

```

#### 4.3 Views

We'll use a `DetailView` for the main enquiry page (acting as an edit page) and combine the attachment list and upload logic within it, leveraging HTMX for dynamic updates. Separate views will handle dropdown cascading and attachment CRUD actions.

```python
# sales/views.py
from django.views.generic import DetailView, UpdateView, DeleteView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404, redirect
from django.utils import timezone
import mimetypes
from django.db.models import Q # For filtering attachments

from .models import CustomerEnquiry, CustomerEnquiryAttachment, Country, State, City
from .forms import CustomerEnquiryForm, CustomerEnquiryAttachmentForm

# Assuming base context like compid, finyear, username come from middleware or session.
# For simplicity, we'll hardcode or mock them for now.
# In a real ERP system, this would be part of a UserProfile or Company context.
MOCK_COMP_ID = 1
MOCK_FIN_YEAR_ID = 2023
MOCK_SESSION_ID = "admin_user"

class CustomerEnquiryEditView(UpdateView):
    """
    Handles displaying and updating Customer Enquiry details.
    Combines the 'read' and 'update' functionality from the ASP.NET page.
    """
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales/customerenquiry/edit.html'
    context_object_name = 'enquiry'
    pk_url_kwarg = 'enq_id' # Match URL parameter name
    success_url = reverse_lazy('sales:customer_enquiry_list') # Redirect to a list page after successful update

    def get_object(self, queryset=None):
        """
        Retrieves the CustomerEnquiry object.
        Matches the ASP.NET logic of fetching by EnqId and CustomerId (hfCustId.Text).
        """
        enq_id = self.kwargs.get(self.pk_url_kwarg)
        customer_id = self.request.GET.get('CustomerId') # From Request.QueryString["CustomerId"]

        if not enq_id or not customer_id:
            raise Http404("Enquiry ID and Customer ID are required.")

        # In a real scenario, also filter by CompId and FinYearId from session
        try:
            return CustomerEnquiry.objects.get(
                enq_id=enq_id,
                customer_id=customer_id,
                comp_id=MOCK_COMP_ID # Assuming session value
            )
        except CustomerEnquiry.DoesNotExist:
            raise Http404("Customer Enquiry not found.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the attachment form for the upload section
        context['attachment_form'] = CustomerEnquiryAttachmentForm()
        # Pass attachments for the current enquiry
        context['attachments'] = self.object.attachments.filter(
            comp_id=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID
        ).order_by('file_name')
        return context

    def form_valid(self, form):
        """
        Handles valid form submission.
        Moves logic to model's update_enquiry_details method.
        """
        enquiry = form.instance
        enquiry.sys_date = timezone.now().date()
        enquiry.sys_time = timezone.now().strftime('%H:%M:%S')
        enquiry.session_id = MOCK_SESSION_ID # From Session["username"]
        enquiry.comp_id = MOCK_COMP_ID # From Session["compid"]

        # Call the fat model method to update details
        updated_enquiry = enquiry.update_enquiry_details(form.cleaned_data)

        messages.success(self.request, 'Customer Enquiry updated successfully.')

        if self.request.headers.get('HX-Request'):
            # If HTMX request, return 204 No Content and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEnquiryDetails'
                }
            )
        return super().form_valid(form) # Redirect on full page request

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors below.')
        if self.request.headers.get('HX-Request'):
            # If HTMX request, render the form again with errors
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class CustomerEnquiryAttachmentUploadView(View):
    """
    Handles file uploads for Customer Enquiry Attachments via HTMX.
    This replaces Button1_Click logic.
    """
    def post(self, request, enq_id):
        enquiry = get_object_or_404(CustomerEnquiry, enq_id=enq_id, comp_id=MOCK_COMP_ID)
        form = CustomerEnquiryAttachmentForm(request.POST, request.FILES)

        if form.is_valid():
            uploaded_file = form.cleaned_data['file']

            CustomerEnquiryAttachment.objects.create(
                enquiry=enquiry,
                comp_id=MOCK_COMP_ID,
                session_id=MOCK_SESSION_ID,
                fin_year_id=MOCK_FIN_YEAR_ID,
                file_name=uploaded_file.name,
                file_size=uploaded_file.size,
                content_type=uploaded_file.content_type if uploaded_file.content_type else 'application/octet-stream',
                file_data=uploaded_file.read() # Read file data
            )
            messages.success(request, f"File '{uploaded_file.name}' uploaded successfully.")
            # Trigger a refresh of the attachment list
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshAttachmentList'}
            )
        else:
            messages.error(request, 'Error uploading file: ' + form.errors.as_text())
            # If HTMX, return a partial with errors or just trigger client-side message
            return HttpResponse(
                status=400, # Bad request
                headers={'HX-Trigger': '{"showToast": "Error uploading file."}'} # Example custom trigger
            )

class CustomerEnquiryAttachmentDeleteView(DeleteView):
    """
    Handles deleting Customer Enquiry Attachments via HTMX.
    This replaces the GridView delete command.
    """
    model = CustomerEnquiryAttachment
    pk_url_kwarg = 'pk'
    template_name = 'sales/customerenquiry/_confirm_delete_attachment.html' # Partial for modal

    def get_object(self, queryset=None):
        # Ensure the attachment belongs to the correct enquiry and company
        obj = super().get_object(queryset)
        if obj.enquiry.enq_id != self.kwargs['enq_id'] or obj.comp_id != MOCK_COMP_ID:
            raise Http404("Attachment not found or not authorized.")
        return obj

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to handle HTMX response and messages.
        """
        self.object = self.get_object()
        success_message = f"Attachment '{self.object.file_name}' deleted successfully."
        self.object.delete_attachment() # Call fat model method
        messages.success(request, success_message)

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content, successful deletion
                headers={'HX-Trigger': 'refreshAttachmentList'} # Trigger attachment list refresh
            )
        return super().delete(request, *args, **kwargs)

class CustomerEnquiryAttachmentDownloadView(View):
    """
    Handles downloading Customer Enquiry Attachments.
    This replaces DownloadFile.aspx.
    """
    def get(self, request, pk, enq_id):
        # Ensure the attachment belongs to the correct enquiry and company
        attachment = get_object_or_404(CustomerEnquiryAttachment, pk=pk, enquiry__enq_id=enq_id, comp_id=MOCK_COMP_ID)

        response = HttpResponse(attachment.file_data, content_type=attachment.content_type)
        response['Content-Disposition'] = f'attachment; filename="{attachment.file_name}"'
        return response

class GetStatesByCountryView(View):
    """
    HTMX endpoint to get states for a selected country.
    """
    def get(self, request):
        country_id = request.GET.get('country_id')
        states = State.objects.none()
        if country_id:
            states = State.objects.filter(country_id=country_id).order_by('name')
        
        # Render a partial template containing just the <option> tags for the states
        # The partial should be minimal, only rendering what's needed for the hx-target
        return self.render_to_response({'states': states})

    def render_to_response(self, context):
        states = context['states']
        options_html = '<option value="">Select</option>'
        for state in states:
            options_html += f'<option value="{state.pk}">{state.name}</option>'
        return HttpResponse(options_html)

class GetCitiesByStateView(View):
    """
    HTMX endpoint to get cities for a selected state.
    """
    def get(self, request):
        state_id = request.GET.get('state_id')
        cities = City.objects.none()
        if state_id:
            cities = City.objects.filter(state_id=state_id).order_by('name')

        # Render a partial template containing just the <option> tags for the cities
        return self.render_to_response({'cities': cities})

    def render_to_response(self, context):
        cities = context['cities']
        options_html = '<option value="">Select</option>'
        for city in cities:
            options_html += f'<option value="{city.pk}">{city.name}</option>'
        return HttpResponse(options_html)

# This is a placeholder for a list view if you want to navigate from one.
# The original page was an edit detail, not a list.
class CustomerEnquiryListView(View):
    def get(self, request):
        # In a real app, you'd fetch a list of enquiries here.
        # For this context, this is a placeholder redirect target.
        return HttpResponse("Placeholder for Customer Enquiry List Page. Navigation would lead here after save/cancel.")
```

#### 4.4 Templates

Templates will be split into a main page and several partials for HTMX-driven updates.

```html
<!-- sales/customerenquiry/edit.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Customer Enquiry - Edit</h2>

    <!-- Display Customer Information -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <strong class="text-gray-700">Enquiry ID:</strong>
                <span class="text-blue-600 font-semibold">{{ enquiry.enq_id }}</span>
            </div>
            <div>
                <strong class="text-gray-700">Customer's Name:</strong>
                <span class="text-gray-900">{{ enquiry.customer_name }} [<span class="text-gray-600">{{ enquiry.customer_id }}</span>]</span>
            </div>
        </div>
    </div>

    <!-- Main Enquiry Form (HTMX Target for updates) -->
    <div id="enquiry-form-container"
         hx-trigger="refreshEnquiryDetails from:body"
         hx-get="{% url 'sales:customer_enquiry_edit_partial' enquiry.enq_id %}?CustomerId={{ enquiry.customer_id }}"
         hx-swap="innerHTML">
        {% include 'sales/customerenquiry/_form_fields.html' %}
    </div>

    <!-- Attachment Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mt-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Attachment</h3>
        <div class="flex items-center space-x-4 mb-4">
            <form hx-post="{% url 'sales:enquiry_attachment_upload' enquiry.enq_id %}"
                  hx-encoding="multipart/form-data"
                  hx-swap="none"
                  hx-indicator="#upload-spinner">
                {% csrf_token %}
                {{ attachment_form.file.label_tag }}
                {{ attachment_form.file }}
                {% if attachment_form.file.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ attachment_form.file.errors }}</p>
                {% endif %}
                <button type="submit" class="ml-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Upload
                </button>
                <div id="upload-spinner" class="htmx-indicator ml-2 inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 hidden"></div>
            </form>
        </div>

        <div id="attachment-table-container"
             hx-trigger="load, refreshAttachmentList from:body"
             hx-get="{% url 'sales:enquiry_attachment_list_partial' enquiry.enq_id %}?CustomerId={{ enquiry.customer_id }}"
             hx-swap="innerHTML">
            <!-- Attachments table will be loaded here via HTMX -->
            <div class="text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading attachments...</p>
            </div>
        </div>
    </div>
</div>

<!-- Global Modal for Delete Confirmation -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('customerEnquiryForm', () => ({
            formData: {}, // Alpine.js state for form fields
            init() {
                // Initialize formData with current values
                this.formData = Object.fromEntries(new FormData(this.$refs.mainForm).entries());
            },
            // Any other Alpine logic for UI state
        }));
    });

    // Handle toast messages from HX-Trigger
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.xhr.getResponseHeader('HX-Trigger')) {
            try {
                const trigger = JSON.parse(evt.detail.xhr.getResponseHeader('HX-Trigger'));
                if (trigger.showToast) {
                    // Implement your toast notification logic here
                    // E.g., `alert(trigger.showToast);` or using a dedicated toast library
                    console.log('Toast:', trigger.showToast);
                }
            } catch (e) {
                // Not a JSON trigger, ignore or log
            }
        }
    });

    // DataTables initialization handler (called after HTMX swap)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'attachment-table-container') {
            $('#attachmentTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing table if it exists before re-initializing
            });
        }
    });
</script>
{% endblock %}
```

```html
<!-- sales/customerenquiry/_form_fields.html (Partial for main enquiry form) -->
<div x-data="customerEnquiryForm" class="bg-white shadow-md rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Address/Details</h3>
    <form hx-post="{% url 'sales:customer_enquiry_edit' enquiry.enq_id %}?CustomerId={{ enquiry.customer_id }}" 
          hx-swap="outerHTML" 
          hx-target="#enquiry-form-container"
          hx-indicator="#form-spinner"
          x-ref="mainForm">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="col-span-1 md:col-span-1 flex items-center bg-gray-100 p-2 rounded">
                <span class="font-medium text-gray-700 w-full text-center">Address/Details</span>
            </div>
            <div class="col-span-1 md:col-span-1 flex items-center bg-gray-100 p-2 rounded">
                <span class="font-medium text-gray-700 w-full text-center">REGD. OFFICE</span>
            </div>
            <div class="col-span-1 md:col-span-1 flex items-center bg-gray-100 p-2 rounded">
                <span class="font-medium text-gray-700 w-full text-center">WORKS/FACTORY</span>
            </div>
            <div class="col-span-1 md:col-span-1 flex items-center bg-gray-100 p-2 rounded">
                <span class="font-medium text-gray-700 w-full text-center">MATERIAL DELIVERY</span>
            </div>
        </div>

        <!-- Address Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">Address</label>
            <div>
                {{ form.regd_address }}
                {% if form.regd_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_address.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.work_address }}
                {% if form.work_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_address.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.material_del_address }}
                {% if form.material_del_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_address.errors }}</p>{% endif %}
            </div>
        </div>

        <!-- Country Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">Country</label>
            <div>
                {{ form.regd_country }}
                {% if form.regd_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_country.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.work_country }}
                {% if form.work_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_country.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.material_del_country }}
                {% if form.material_del_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_country.errors }}</p>{% endif %}
            </div>
        </div>

        <!-- State Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">State</label>
            <div id="regd_state_container">
                {{ form.regd_state }}
                {% if form.regd_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_state.errors }}</p>{% endif %}
            </div>
            <div id="work_state_container">
                {{ form.work_state }}
                {% if form.work_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_state.errors }}</p>{% endif %}
            </div>
            <div id="material_del_state_container">
                {{ form.material_del_state }}
                {% if form.material_del_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_state.errors }}</p>{% endif %}
            </div>
        </div>

        <!-- City Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">City</label>
            <div id="regd_city_container">
                {{ form.regd_city }}
                {% if form.regd_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_city.errors }}</p>{% endif %}
            </div>
            <div id="work_city_container">
                {{ form.work_city }}
                {% if form.work_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_city.errors }}</p>{% endif %}
            </div>
            <div id="material_del_city_container">
                {{ form.material_del_city }}
                {% if form.material_del_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_city.errors }}</p>{% endif %}
            </div>
        </div>

        <!-- PIN No. Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">PIN No.</label>
            <div>
                {{ form.regd_pin_no }}
                {% if form.regd_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.work_pin_no }}
                {% if form.work_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.material_del_pin_no }}
                {% if form.material_del_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_pin_no.errors }}</p>{% endif %}
            </div>
        </div>

        <!-- Contact No. Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">Contact No.</label>
            <div>
                {{ form.regd_contact_no }}
                {% if form.regd_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.work_contact_no }}
                {% if form.work_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.material_del_contact_no }}
                {% if form.material_del_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_contact_no.errors }}</p>{% endif %}
            </div>
        </div>

        <!-- Fax No. Row -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <label class="font-medium text-gray-700 py-2 px-3">Fax No.</label>
            <div>
                {{ form.regd_fax_no }}
                {% if form.regd_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_fax_no.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.work_fax_no }}
                {% if form.work_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_fax_no.errors }}</p>{% endif %}
            </div>
            <div>
                {{ form.material_del_fax_no }}
                {% if form.material_del_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_fax_no.errors }}</p>{% endif %}
            </div>
        </div>

        <hr class="my-6 border-gray-200">

        <!-- Other Details -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person</label>
                {{ form.contact_person }}
                {% if form.contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
                {{ form.email }}
                {% if form.email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No. (General)</label>
                {{ form.contact_no }}
                {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.juridiction_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Juridiction Code</label>
                {{ form.juridiction_code }}
                {% if form.juridiction_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.juridiction_code.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">ECC.No.</label>
                {{ form.ecc_no }}
                {% if form.ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ecc_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.range_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Range</label>
                {{ form.range_field }}
                {% if form.range_field.errors %}<p class="text-red-500 text-xs mt-1">{{ form.range_field.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.commissionurate.id_for_label }}" class="block text-sm font-medium text-gray-700">Commissionurate</label>
                {{ form.commissionurate }}
                {% if form.commissionurate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.commissionurate.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.divn.id_for_label }}" class="block text-sm font-medium text-gray-700">Divn</label>
                {{ form.divn }}
                {% if form.divn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.divn.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PAN No.</label>
                {{ form.pan_no }}
                {% if form.pan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pan_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/VAT No.</label>
                {{ form.tin_vat_no }}
                {% if form.tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_vat_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/CST No.</label>
                {{ form.tin_cst_no }}
                {% if form.tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_cst_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tds_code.id_for_label }}" class="block text-sm font-medium text-gray-700">TDS Code</label>
                {{ form.tds_code }}
                {% if form.tds_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tds_code.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="mb-4">
            <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
            {{ form.remark }}
            {% if form.remark.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remark.errors }}</p>{% endif %}
        </div>

        <div class="mb-6">
            <label for="{{ form.enquiry_for.id_for_label }}" class="block text-sm font-medium text-gray-700">Enquiry For</label>
            {{ form.enquiry_for }}
            {% if form.enquiry_for.errors %}<p class="text-red-500 text-xs mt-1">{{ form.enquiry_for.errors }}</p>{% endif %}
        </div>

        <div class="flex justify-end space-x-4">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm">
                Update
            </button>
            <a href="{% url 'sales:customer_enquiry_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-sm">
                Cancel
            </a>
            <div id="form-spinner" class="htmx-indicator ml-2 inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 hidden"></div>
        </div>
    </form>
</div>
```

```html
<!-- sales/customerenquiry/_attachment_table.html (Partial for attachments list) -->
<div class="overflow-x-auto">
    <table id="attachmentTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Size (Bytes)</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Content Type</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for attachment in attachments %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                <td class="py-2 px-4 border-b text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-900">{{ attachment.file_name }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-900">{{ attachment.file_size }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-900">{{ attachment.content_type }}</td>
                <td class="py-2 px-4 border-b text-sm">
                    <a href="{{ attachment.download_url }}" class="text-blue-600 hover:underline mr-3">Download</a>
                    <button
                        class="text-red-600 hover:underline"
                        hx-get="{% url 'sales:enquiry_attachment_delete' enquiry.enq_id attachment.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">No attachments to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables init will be called by main template's htmx:afterSwap listener
    // This script block should ideally be removed if the main template handles it via htmx:afterSwap.
    // For clarity, keeping it here but noting the ideal single-point init.
</script>
```

```html
<!-- sales/customerenquiry/_confirm_delete_attachment.html (Partial for delete modal) -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete the attachment "<strong class="font-semibold">{{ object.file_name }}</strong>"?</p>
    <form hx-delete="{% url 'sales:enquiry_attachment_delete' object.enquiry.enq_id object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

```html
<!-- sales/customerenquiry/_state_options.html (Partial for state dropdown) -->
<!-- This file will be generated by GetStatesByCountryView -->
<!-- It should contain only <option> tags based on the selected country_id -->
<!-- Example: -->
<option value="">Select</option>
{% for state in states %}
<option value="{{ state.pk }}">{{ state.name }}</option>
{% endfor %}
```

```html
<!-- sales/customerenquiry/_city_options.html (Partial for city dropdown) -->
<!-- This file will be generated by GetCitiesByStateView -->
<!-- It should contain only <option> tags based on the selected state_id -->
<!-- Example: -->
<option value="">Select</option>
{% for city in cities %}
<option value="{{ city.pk }}">{{ city.name }}</option>
{% endfor %}
```

#### 4.5 URLs

URL patterns for the `sales` application.

```python
# sales/urls.py
from django.urls import path
from .views import (
    CustomerEnquiryEditView, CustomerEnquiryAttachmentUploadView,
    CustomerEnquiryAttachmentDeleteView, CustomerEnquiryAttachmentDownloadView,
    GetStatesByCountryView, GetCitiesByStateView, CustomerEnquiryListView
)

app_name = 'sales' # Namespace for URLs

urlpatterns = [
    # Main Customer Enquiry Edit Page
    # URL structure aligns with original ASP.NET query params for EnqId and CustomerId
    # For a Django app, consider making this more RESTful if possible in new development
    path('customer-enquiry/edit/<int:enq_id>/', CustomerEnquiryEditView.as_view(), name='customer_enquiry_edit'),
    # HTMX partial for the main form fields (for re-swapping after update)
    path('customer-enquiry/edit-partial/<int:enq_id>/', CustomerEnquiryEditView.as_view(template_name='sales/customerenquiry/_form_fields.html'), name='customer_enquiry_edit_partial'),

    # Attachment related URLs
    path('customer-enquiry/<int:enq_id>/attachments/upload/', CustomerEnquiryAttachmentUploadView.as_view(), name='enquiry_attachment_upload'),
    path('customer-enquiry/<int:enq_id>/attachments/<int:pk>/delete/', CustomerEnquiryAttachmentDeleteView.as_view(), name='enquiry_attachment_delete'),
    path('customer-enquiry/<int:enq_id>/attachments/<int:pk>/download/', CustomerEnquiryAttachmentDownloadView.as_view(), name='enquiry_attachment_download'),
    # HTMX partial for the attachment list table
    path('customer-enquiry/<int:enq_id>/attachments/list-partial/', CustomerEnquiryAttachmentUploadView.as_view(template_name='sales/customerenquiry/_attachment_table.html'), name='enquiry_attachment_list_partial'),


    # HTMX endpoints for dropdown cascading
    path('ajax/get_states_by_country/', GetStatesByCountryView.as_view(), name='get_states_by_country'),
    path('ajax/get_cities_by_state/', GetCitiesByStateView.as_view(), name='get_cities_by_state'),

    # Placeholder for the redirect target after update/cancel
    path('customer-enquiry/', CustomerEnquiryListView.as_view(), name='customer_enquiry_list'),
]

```

#### 4.6 Tests

Comprehensive tests for models and views to ensure functionality and data integrity.

```python
# sales/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.http import Http404
from django.forms.models import model_to_dict # Helper for form data
from unittest.mock import patch, MagicMock

from .models import CustomerEnquiry, CustomerEnquiryAttachment, Country, State, City
from .views import MOCK_COMP_ID, MOCK_FIN_YEAR_ID, MOCK_SESSION_ID

class CoreLookupModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country1 = Country.objects.create(name='India')
        cls.state1 = State.objects.create(country=cls.country1, name='Maharashtra')
        cls.city1 = City.objects.create(state=cls.state1, name='Mumbai')

    def test_country_creation(self):
        self.assertEqual(self.country1.name, 'India')

    def test_state_creation(self):
        self.assertEqual(self.state1.name, 'Maharashtra')
        self.assertEqual(self.state1.country, self.country1)

    def test_city_creation(self):
        self.assertEqual(self.city1.name, 'Mumbai')
        self.assertEqual(self.city1.state, self.state1)

class CustomerEnquiryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(name='TestCountry')
        cls.state = State.objects.create(country=cls.country, name='TestState')
        cls.city = City.objects.create(state=cls.state, name='TestCity')
        
        cls.enquiry = CustomerEnquiry.objects.create(
            enq_id=1,
            customer_id=101,
            customer_name='Test Customer A',
            comp_id=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID,
            regd_address='123 Test St',
            regd_country=cls.country,
            regd_state=cls.state,
            regd_city=cls.city,
            regd_pin_no='123456',
            regd_contact_no='9876543210',
            regd_fax_no='12345',
            work_address='456 Work Rd',
            work_country=cls.country,
            work_state=cls.state,
            work_city=cls.city,
            work_pin_no='654321',
            work_contact_no='0123456789',
            work_fax_no='54321',
            material_del_address='789 Del Ave',
            material_del_country=cls.country,
            material_del_state=cls.state,
            material_del_city=cls.city,
            material_del_pin_no='112233',
            material_del_contact_no='9988776655',
            material_del_fax_no='67890',
            contact_person='John Doe',
            juridiction_code='JCODE1',
            commissionurate='COMM1',
            tin_vat_no='TINVAT1',
            email='<EMAIL>',
            ecc_no='ECC1',
            divn='DIVN1',
            tin_cst_no='TINCST1',
            contact_no='1122334455',
            range_field='RANGE1',
            pan_no='PAN1',
            tds_code='TDS1',
            remark='Initial remarks',
            enquiry_for='Enquiry for product X'
        )
    
    def test_enquiry_creation(self):
        self.assertEqual(self.enquiry.customer_name, 'Test Customer A')
        self.assertEqual(self.enquiry.regd_address, '123 Test St')

    def test_update_enquiry_details(self):
        new_data = {
            'regd_address': 'New Regd Address',
            'email': '<EMAIL>',
            'remark': 'Updated remarks'
        }
        updated_enquiry = self.enquiry.update_enquiry_details(new_data)
        self.assertEqual(updated_enquiry.regd_address, 'New Regd Address')
        self.assertEqual(updated_enquiry.email, '<EMAIL>')
        self.assertEqual(updated_enquiry.remark, 'Updated remarks')
        self.enquiry.refresh_from_db() # Reload to ensure changes are persisted
        self.assertEqual(self.enquiry.regd_address, 'New Regd Address')

class CustomerEnquiryAttachmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(name='TestCountry')
        cls.state = State.objects.create(country=cls.country, name='TestState')
        cls.city = City.objects.create(state=cls.state, name='TestCity')
        cls.enquiry = CustomerEnquiry.objects.create(
            enq_id=2, customer_id=102, customer_name='Test Customer B',
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID,
            regd_address='addr', regd_pin_no='pin', regd_contact_no='cont', regd_fax_no='fax',
            work_address='addr', work_pin_no='pin', work_contact_no='cont', work_fax_no='fax',
            material_del_address='addr', material_del_pin_no='pin', material_del_contact_no='cont', material_del_fax_no='fax',
            contact_person='cp', juridiction_code='jc', commissionurate='com', tin_vat_no='tvn',
            email='<EMAIL>', ecc_no='eccn', divn='dvn', tin_cst_no='tcn', contact_no='cn',
            range_field='rf', pan_no='pn', tds_code='tdc', enquiry_for='ef',
            regd_country=cls.country, regd_state=cls.state, regd_city=cls.city,
            work_country=cls.country, work_state=cls.state, work_city=cls.city,
            material_del_country=cls.country, material_del_state=cls.state, material_del_city=cls.city
        )
        cls.attachment = CustomerEnquiryAttachment.objects.create(
            enquiry=cls.enquiry,
            comp_id=MOCK_COMP_ID,
            session_id=MOCK_SESSION_ID,
            fin_year_id=MOCK_FIN_YEAR_ID,
            file_name='test_document.pdf',
            file_size=1024,
            content_type='application/pdf',
            file_data=b'PDF content'
        )

    def test_attachment_creation(self):
        self.assertEqual(self.attachment.file_name, 'test_document.pdf')
        self.assertEqual(self.attachment.enquiry, self.enquiry)

    def test_download_url(self):
        expected_url = reverse('sales:enquiry_attachment_download', args=[self.enquiry.enq_id, self.attachment.pk])
        self.assertEqual(self.attachment.download_url(), expected_url)

    def test_delete_attachment(self):
        self.assertTrue(CustomerEnquiryAttachment.objects.filter(pk=self.attachment.pk).exists())
        self.attachment.delete_attachment()
        self.assertFalse(CustomerEnquiryAttachment.objects.filter(pk=self.attachment.pk).exists())

class CustomerEnquiryViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(name='TestCountry')
        cls.state = State.objects.create(country=cls.country, name='TestState')
        cls.city = City.objects.create(state=cls.state, name='TestCity')
        cls.enquiry = CustomerEnquiry.objects.create(
            enq_id=3,
            customer_id=103,
            customer_name='Test Customer C',
            comp_id=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID,
            regd_address='Old Regd Address',
            regd_country=cls.country,
            regd_state=cls.state,
            regd_city=cls.city,
            regd_pin_no='123456',
            regd_contact_no='9876543210',
            regd_fax_no='12345',
            work_address='Old Work Address',
            work_country=cls.country,
            work_state=cls.state,
            work_city=cls.city,
            work_pin_no='654321',
            work_contact_no='0123456789',
            work_fax_no='54321',
            material_del_address='Old Mat Del Address',
            material_del_country=cls.country,
            material_del_state=cls.state,
            material_del_city=cls.city,
            material_del_pin_no='112233',
            material_del_contact_no='9988776655',
            material_del_fax_no='67890',
            contact_person='Old Contact Person',
            juridiction_code='OLD_JCODE',
            commissionurate='OLD_COMM',
            tin_vat_no='OLD_TINVAT',
            email='<EMAIL>',
            ecc_no='OLD_ECC',
            divn='OLD_DIVN',
            tin_cst_no='OLD_TINCST',
            contact_no='OLD_CONT',
            range_field='OLD_RANGE',
            pan_no='OLD_PAN',
            tds_code='OLD_TDS',
            remark='Old remarks',
            enquiry_for='Old enquiry purpose'
        )
        cls.attachment1 = CustomerEnquiryAttachment.objects.create(
            enquiry=cls.enquiry,
            comp_id=MOCK_COMP_ID,
            session_id=MOCK_SESSION_ID,
            fin_year_id=MOCK_FIN_YEAR_ID,
            file_name='doc1.txt',
            file_size=500,
            content_type='text/plain',
            file_data=b'content of doc1'
        )

    def test_customer_enquiry_edit_view_get(self):
        url = reverse('sales:customer_enquiry_edit', args=[self.enquiry.enq_id]) + f'?CustomerId={self.enquiry.customer_id}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/edit.html')
        self.assertContains(response, 'Old Regd Address')
        self.assertContains(response, 'doc1.txt')

    def test_customer_enquiry_edit_view_post_success(self):
        url = reverse('sales:customer_enquiry_edit', args=[self.enquiry.enq_id]) + f'?CustomerId={self.enquiry.customer_id}'
        data = model_to_dict(self.enquiry, exclude=['enq_id', 'customer_id', 'customer_name', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id'])
        
        # Update some fields
        data['regd_address'] = 'Updated Regd Address'
        data['email'] = '<EMAIL>'
        data['remark'] = 'Updated remarks via form'

        # Since dropdowns are FKs, we need to pass their PKs
        data['regd_country'] = self.country.pk
        data['regd_state'] = self.state.pk
        data['regd_city'] = self.city.pk
        data['work_country'] = self.country.pk
        data['work_state'] = self.state.pk
        data['work_city'] = self.city.pk
        data['material_del_country'] = self.country.pk
        data['material_del_state'] = self.state.pk
        data['material_del_city'] = self.city.pk

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302) # Redirects on successful POST (non-HTMX)
        self.enquiry.refresh_from_db()
        self.assertEqual(self.enquiry.regd_address, 'Updated Regd Address')
        self.assertEqual(self.enquiry.email, '<EMAIL>')
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer Enquiry updated successfully.')

    def test_customer_enquiry_edit_view_post_htmx_success(self):
        url = reverse('sales:customer_enquiry_edit', args=[self.enquiry.enq_id]) + f'?CustomerId={self.enquiry.customer_id}'
        data = model_to_dict(self.enquiry, exclude=['enq_id', 'customer_id', 'customer_name', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id'])
        data['regd_address'] = 'HTMX Updated Address'
        # Include FKs
        data['regd_country'] = self.country.pk
        data['regd_state'] = self.state.pk
        data['regd_city'] = self.city.pk
        data['work_country'] = self.country.pk
        data['work_state'] = self.state.pk
        data['work_city'] = self.city.pk
        data['material_del_country'] = self.country.pk
        data['material_del_state'] = self.state.pk
        data['material_del_city'] = self.city.pk

        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEnquiryDetails')
        self.enquiry.refresh_from_db()
        self.assertEqual(self.enquiry.regd_address, 'HTMX Updated Address')

    def test_customer_enquiry_edit_view_post_invalid(self):
        url = reverse('sales:customer_enquiry_edit', args=[self.enquiry.enq_id]) + f'?CustomerId={self.enquiry.customer_id}'
        data = model_to_dict(self.enquiry, exclude=['enq_id', 'customer_id', 'customer_name', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id'])
        data['regd_address'] = '' # Make a required field empty
        # Include FKs (even if empty, validation will catch)
        data['regd_country'] = ''
        data['regd_state'] = ''
        data['regd_city'] = ''
        data['work_country'] = ''
        data['work_state'] = ''
        data['work_city'] = ''
        data['material_del_country'] = ''
        data['material_del_state'] = ''
        data['material_del_city'] = ''

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertFormError(response, 'form', 'regd_address', 'This field is required.')
        messages = list(response.context['messages'])
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_customer_enquiry_edit_view_post_htmx_invalid(self):
        url = reverse('sales:customer_enquiry_edit', args=[self.enquiry.enq_id]) + f'?CustomerId={self.enquiry.customer_id}'
        data = model_to_dict(self.enquiry, exclude=['enq_id', 'customer_id', 'customer_name', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id'])
        data['email'] = 'invalid-email' # Invalid email
        # Include FKs as valid or empty
        data['regd_country'] = self.country.pk
        data['regd_state'] = self.state.pk
        data['regd_city'] = self.city.pk
        data['work_country'] = self.country.pk
        data['work_state'] = self.state.pk
        data['work_city'] = self.city.pk
        data['material_del_country'] = self.country.pk
        data['material_del_state'] = self.state.pk
        data['material_del_city'] = self.city.pk

        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors for HTMX
        self.assertFormError(response, 'form', 'email', 'Enter a valid email address.')
        # Check for messages (if passed via HX-Trigger)
        messages = list(response.context['messages'])
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')


    def test_enquiry_attachment_upload_view_post(self):
        url = reverse('sales:enquiry_attachment_upload', args=[self.enquiry.enq_id])
        file_content = b"This is a test file content."
        uploaded_file = SimpleUploadedFile("test_file.txt", file_content, content_type="text/plain")
        
        initial_attachment_count = CustomerEnquiryAttachment.objects.count()

        response = self.client.post(url, {'file': uploaded_file}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAttachmentList')
        self.assertEqual(CustomerEnquiryAttachment.objects.count(), initial_attachment_count + 1)
        new_attachment = CustomerEnquiryAttachment.objects.get(file_name='test_file.txt')
        self.assertEqual(new_attachment.file_size, len(file_content))
        self.assertEqual(new_attachment.file_data, file_content)
        self.assertEqual(new_attachment.enquiry, self.enquiry)

    def test_enquiry_attachment_delete_view_get(self):
        url = reverse('sales:enquiry_attachment_delete', args=[self.enquiry.enq_id, self.attachment1.pk])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_confirm_delete_attachment.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.attachment1.file_name)

    def test_enquiry_attachment_delete_view_delete(self):
        url = reverse('sales:enquiry_attachment_delete', args=[self.enquiry.enq_id, self.attachment1.pk])
        response = self.client.delete(url, HTTP_HX_REQUEST='true') # Use DELETE method for Django DeleteView
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAttachmentList')
        self.assertFalse(CustomerEnquiryAttachment.objects.filter(pk=self.attachment1.pk).exists())
        messages = list(response.context['messages']) if response.context else []
        self.assertIn('Attachment \'doc1.txt\' deleted successfully.', [str(m) for m in messages])

    def test_enquiry_attachment_download_view(self):
        url = reverse('sales:enquiry_attachment_download', args=[self.enquiry.enq_id, self.attachment1.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], self.attachment1.content_type)
        self.assertEqual(response['Content-Disposition'], f'attachment; filename="{self.attachment1.file_name}"')
        self.assertEqual(response.content, self.attachment1.file_data)

    def test_get_states_by_country_view(self):
        country2 = Country.objects.create(name='Country B')
        state2a = State.objects.create(country=country2, name='State B1')
        state2b = State.objects.create(country=country2, name='State B2')

        url = reverse('sales:get_states_by_country') + f'?country_id={country2.pk}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{state2a.pk}">{state2a.name}</option>', response.content.decode())
        self.assertIn(f'<option value="{state2b.pk}">{state2b.name}</option>', response.content.decode())
        self.assertIn('<option value="">Select</option>', response.content.decode())

    def test_get_cities_by_state_view(self):
        state3 = State.objects.create(country=self.country, name='State C')
        city3a = City.objects.create(state=state3, name='City C1')
        city3b = City.objects.create(state=state3, name='City C2')

        url = reverse('sales:get_cities_by_state') + f'?state_id={state3.pk}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{city3a.pk}">{city3a.name}</option>', response.content.decode())
        self.assertIn(f'<option value="{city3b.pk}">{city3b.name}</option>', response.content.decode())
        self.assertIn('<option value="">Select</option>', response.content.decode())

```

### Step 5: HTMX and Alpine.js Integration

This plan heavily leverages HTMX for all dynamic interactions:

-   **Form Submission:** The main enquiry form (`_form_fields.html`) uses `hx-post`, `hx-swap="outerHTML"`, and `hx-target="#enquiry-form-container"` to replace the entire form section, including updated error messages, if validation fails. On success, `HX-Trigger` `refreshEnquiryDetails` is sent, triggering a re-render of the form to show saved data and clear form state.
-   **Dropdown Chaining:** Country dropdowns use `hx-get` to `GetStatesByCountryView` targeting specific `div` containers (e.g., `#regd_state_container`). These views return only the `<option>` tags, which HTMX swaps into the `<select>` element. The same pattern applies to State-to-City.
-   **File Upload:** The attachment form uses `hx-post` with `hx-encoding="multipart/form-data"`. On successful upload, a `HX-Trigger` `refreshAttachmentList` is sent.
-   **Attachment List:** The attachment table (`_attachment_table.html`) is a partial template loaded via `hx-get` on page load and `refreshAttachmentList` event, ensuring the table always shows the latest data.
-   **Delete Confirmation:** Delete buttons on attachments trigger an `hx-get` to `CustomerEnquiryAttachmentDeleteView`, which loads a confirmation modal (`_confirm_delete_attachment.html`). The modal's form uses `hx-delete` (or `hx-post` with method override) to perform the actual deletion, triggering `refreshAttachmentList` on success.
-   **DataTables:** The `attachmentTable` uses jQuery DataTables for client-side sorting, searching, and pagination. Its initialization is tied to the `htmx:afterSwap` event on the `attachment-table-container` to re-initialize after HTMX loads new content.
-   **Alpine.js:** `Alpine.js` is used for client-side UI state management, particularly within the main form (`_form_fields.html`) for reactive updates or managing local component state (e.g., loading indicators or showing/hiding elements). It can manage `formData` for two-way binding with `x-model` directives, although for direct form submissions, HTMX handles data submission, and Alpine is more for immediate UI feedback.

All interactions are designed to avoid full page reloads, providing a fast and responsive user experience typical of single-page applications, but with minimal JavaScript overhead.

---

## Final Notes

This comprehensive plan transforms the legacy ASP.NET application into a modern Django solution.

-   **Business Value:** This migration reduces technical debt, improves application performance, enhances user experience through dynamic interactions, and provides a scalable, maintainable codebase on a modern framework. The use of Python and Django opens up opportunities for easier integration with AI/ML services and a wider talent pool.
-   **Automation Focus:** This plan provides the structure and code templates necessary for an AI-assisted automation tool to generate much of the boilerplate code (models, forms, views, URLs, basic templates) by extracting patterns from the ASP.NET source. The logical separation into specific files and the adherence to Django best practices simplify the automation task.
-   **Placeholders:** Remember to replace `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, etc., with actual values derived from your specific ASP.NET application. For the dropdowns, ensure your `Country`, `State`, and `City` tables are correctly mapped to Django models and their `id` and `name` fields are accurate.
-   **Environment:** Ensure your Django project is set up to connect to the existing SQL Server database (or migrate data to PostgreSQL/MySQL) and that `settings.py` includes the `sales` app and `django.contrib.messages`. Tailwind CSS and required CDNs (HTMX, Alpine.js, jQuery, DataTables) should be included in your `core/base.html` template.