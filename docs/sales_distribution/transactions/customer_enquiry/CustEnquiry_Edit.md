## ASP.NET to Django Conversion Script: Customer Enquiry Search & Edit

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables. The primary table for the enquiry listing is `SD_Cust_Enquiry_Master`. The search functionality involves `SD_Cust_master` for customer names, `tblFinancial_master` for financial year display, `tblHR_OfficeStaff` for employee names, and `SD_Cust_WorkOrder_Master` to filter records based on work order status.

**Identified Tables and Key Columns:**
*   **`SD_Cust_Enquiry_Master`**:
    *   `EnqId` (Primary Key, CharField)
    *   `FinYearId` (ForeignKey, Integer)
    *   `CustomerName` (CharField)
    *   `CustomerId` (ForeignKey, CharField)
    *   `SysDate` (CharField, stores date as string, e.g., 'MM-DD-YYYY')
    *   `CompId` (Integer)
    *   `SessionId` (ForeignKey to Employee, CharField)
*   **`SD_Cust_WorkOrder_Master`**:
    *   `EnqId` (ForeignKey to CustomerEnquiry, CharField)
    *   `CloseOpen` (Integer, status flag: 0=open, 1=closed)
*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (CharField)
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key, CharField)
    *   `Title` (CharField)
    *   `EmployeeName` (CharField)
    *   `CompId` (Integer)
*   **`SD_Cust_master`**:
    *   `CustomerId` (Primary Key, CharField)
    *   `CustomerName` (CharField)
    *   `CompId` (Integer)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page primarily performs a "Read" (search and list) operation on customer enquiries. There's also an autocomplete feature for customer names and a cleanup operation for temporary file attachments (which will be re-evaluated for a modern Django context). The page title "Edit" refers to the ability to select an enquiry for further editing on a separate `CustEnquiry_Edit_Details.aspx` page, not direct inline editing on this listing. For Django, we will provide generic CRUD operations for `CustomerEnquiry` to demonstrate a complete flow.

**Identified Operations:**
*   **Read (List & Search):**
    *   Displaying customer enquiries with columns: SN, Fin Yrs, Customer, Code, Enquiry No., Gen. Date, Gen. By.
    *   Filtering by "Customer Name" or "Enquiry No".
    *   Autocomplete for "Customer Name" search field.
    *   Pagination (20 items per page).
    *   Business Logic: Only display enquiries that do NOT have an associated `SD_Cust_WorkOrder_Master` record with `CloseOpen = 1`.
*   **Session Management:** Uses `CompId`, `username` (`SessionId`), `finyear` from session for filtering and cleanup.
*   **Data Transformation:** Complex string manipulation to parse `SysDate` from database string format to `DD/MM/YYYY`.
*   **Cleanup:** Deletes `tblFile_Attachment` records on page load, which should be re-architected in a modern application (e.g., using a background task or scheduled job for orphaned files). This specific cleanup won't be directly replicated in Django views.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses a `DropDownList`, `TextBoxes` with `AutoCompleteExtender`, a `Button`, and a `GridView`. These correspond to standard form inputs and a data table in Django.

**Inferred UI Components and Django Equivalents:**
*   `DropDownList1` (Search By): Django `forms.ChoiceField` for `SearchEnquiryForm`.
*   `txtEnqId` (Enquiry ID): Django `forms.CharField` for `SearchEnquiryForm`.
*   `TxtSearchValue` (Customer Name): Django `forms.CharField` for `SearchEnquiryForm` with HTMX for autocomplete.
*   `btnSearch` (Search Button): HTMX `hx-get` trigger on the search form.
*   `SearchGridView1` (Enquiry List): Django HTML `<table>` with DataTables. Columns will map directly. The `HyperLinkField` will be a standard Django URL.

---

### Step 4: Generate Django Code

We will create a Django application named `sales` for this module.

#### 4.1 Models (`sales/models.py`)

This file defines the Django ORM models, mapping to the existing database tables. `managed = False` is crucial for existing databases. Business logic for filtering and data retrieval is encapsulated using a custom manager/queryset.

```python
from django.db import models
from django.db.models import F, Case, When, Value, CharField
from django.db.models.functions import Concat
from datetime import datetime

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    title = models.CharField(db_column='Title', max_length=10)
    employee_name = models.CharField(db_column='EmployeeName', max_length=100)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}.{self.employee_name}"

class CustomerMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=200)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return self.customer_name

class WorkOrder(models.Model):
    # EnqId might not be primary key in original DB, assuming a composite key or
    # that the original code only queried based on EnqId unique closeopen status.
    # We will use a dummy primary key if the table has no explicit PK for Django ORM.
    id = models.AutoField(primary_key=True) # Assuming no explicit PK from ASP.NET for this table
    enq_id = models.CharField(db_column='EnqId', max_length=50)
    close_open = models.IntegerField(db_column='CloseOpen')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"Work Order for Enquiry {self.enq_id} - Status: {self.close_open}"

class CustomerEnquiryQuerySet(models.QuerySet):
    def active_enquiries(self, comp_id, fin_year):
        """
        Retrieves active customer enquiries based on company ID, financial year,
        and excludes enquiries linked to closed work orders (CloseOpen = 1).
        Annotates with related financial year and employee names.
        """
        return self.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year,
            customer_id__isnull=False, # Equivalent to CustomerId != ''
        ).annotate(
            fin_year_display=F('financial_year__fin_year'),
            employee_full_name=Concat(F('employee__title'), Value('.'), F('employee__employee_name'), output_field=CharField()),
        ).exclude(
            # Exclude if ANY related work order has close_open = 1
            work_orders__close_open=1
        ).order_by('-enq_id') # Order by Enquiry ID descending as in original

class CustomerEnquiry(models.Model):
    enq_id = models.CharField(db_column='EnqId', max_length=50, primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    customer_name = models.CharField(db_column='CustomerName', max_length=200)
    customer_id = models.CharField(db_column='CustomerId', max_length=50)
    sys_date = models.CharField(db_column='SysDate', max_length=50) # Stored as string in source DB
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=50)

    # Django ORM relationships for cleaner queries
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='enquiries', to_field='fin_year_id')
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='enquiries', to_field='emp_id')
    customer = models.ForeignKey(CustomerMaster, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='enquiries', to_field='customer_id')
    # One-to-many relationship with WorkOrder, using enq_id
    work_orders = models.ManyToManyField(WorkOrder, through='CustomerEnquiryWorkOrder', related_name='enquiry_set')

    objects = CustomerEnquiryQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"Enquiry {self.enq_id} for {self.customer_name}"

    @property
    def formatted_sys_date(self):
        # Parses the SysDate string from the DB into a display format (DD/MM/YYYY)
        # The original ASP.NET code had very complex date parsing.
        # We try 'MM-DD-YYYY' and 'DD-MM-YYYY' as common string date formats.
        try:
            dt_obj = datetime.strptime(self.sys_date, '%m-%d-%Y')
            return dt_obj.strftime('%d/%m/%Y')
        except ValueError:
            try:
                dt_obj = datetime.strptime(self.sys_date, '%d-%m-%Y')
                return dt_obj.strftime('%d/%m/%Y')
            except ValueError:
                return self.sys_date # Fallback if parsing fails

# Intermediate model for ManyToMany relationship with WorkOrder if needed,
# or direct filtering through `enq_id` in WorkOrder.
# The original code's logic was to check if any WorkOrder for the EnqId has CloseOpen=0, not a direct M2M.
# A simpler approach for the filtering logic:
# CustomerEnquiry.objects.exclude(work_orders__close_open=1) works well with `related_name='enquiry_set'`
# on WorkOrder. So no explicit `through` model is strictly needed for the filtering.
# I'll modify the `WorkOrder` model to have a proper FK back to `CustomerEnquiry`.

# Re-defining WorkOrder with FK to CustomerEnquiry
class WorkOrder(models.Model):
    id = models.AutoField(primary_key=True) # Django-managed PK if no explicit PK in DB
    enq_id = models.CharField(db_column='EnqId', max_length=50) # The raw DB field

    # Foreign Key to CustomerEnquiry based on 'EnqId'
    # Use `db_constraint=False` if there's no actual FK constraint in the legacy DB
    enquiry = models.ForeignKey(CustomerEnquiry, on_delete=models.DO_NOTHING, db_column='EnqId', to_field='enq_id', related_name='work_orders', db_constraint=False)
    
    close_open = models.IntegerField(db_column='CloseOpen')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        # If (EnqId, some_other_id) was unique, add unique_together here.
        # Assuming the filter is based on 'any' matching record for EnqId.

    def __str__(self):
        return f"Work Order for Enquiry {self.enq_id} - Status: {self.close_open}"

```

#### 4.2 Forms (`sales/forms.py`)

Two forms are needed: one for the search functionality (`SearchEnquiryForm`) and one for the generic CRUD operations (`CustomerEnquiryForm`).

```python
from django import forms
from .models import CustomerEnquiry, CustomerMaster

class SearchEnquiryForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('select', 'Select'),
        ('customer_name', 'Customer Name'),
        ('enquiry_no', 'Enquiry No'),
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-trigger': 'change', 'hx-post': 'hx-search-criteria-toggle/', 'hx-target': '#search-inputs', 'hx-swap': 'innerHTML'})
    )
    
    customer_name = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter Customer Name',
            'hx-get': '/sales/customer-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-on:focus': 'showAutocomplete = true',
            'x-on:click.outside': 'showAutocomplete = false',
            'x-on:keydown.escape': 'showAutocomplete = false',
        })
    )
    
    enquiry_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Enter Enquiry No'})
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        customer_name = cleaned_data.get('customer_name')
        enquiry_no = cleaned_data.get('enquiry_no')

        if search_by == 'customer_name' and not customer_name:
            self.add_error('customer_name', 'Customer Name is required for this search type.')
        if search_by == 'enquiry_no' and not enquiry_no:
            self.add_error('enquiry_no', 'Enquiry No is required for this search type.')
        
        return cleaned_data

class CustomerEnquiryForm(forms.ModelForm):
    class Meta:
        model = CustomerEnquiry
        fields = ['enq_id', 'fin_year_id', 'customer_id', 'customer_name', 'sys_date', 'comp_id', 'session_id']
        widgets = {
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'readonly': 'readonly'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM-DD-YYYY'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'readonly': 'readonly'}),
            'session_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'readonly': 'readonly'}),
        }
        labels = {
            'enq_id': 'Enquiry No.',
            'fin_year_id': 'Financial Year ID',
            'customer_id': 'Customer Code',
            'customer_name': 'Customer Name',
            'sys_date': 'Generation Date (MM-DD-YYYY)',
            'comp_id': 'Company ID',
            'session_id': 'Generated By (Employee ID)',
        }

    # Custom validation for sys_date if it's expected in a specific string format
    def clean_sys_date(self):
        sys_date_str = self.cleaned_data['sys_date']
        # Optionally, validate that it's a valid date string in 'MM-DD-YYYY' or 'DD-MM-YYYY'
        try:
            datetime.strptime(sys_date_str, '%m-%d-%Y')
        except ValueError:
            try:
                datetime.strptime(sys_date_str, '%d-%m-%Y')
            except ValueError:
                raise forms.ValidationError("Date must be in MM-DD-YYYY or DD-MM-YYYY format.")
        return sys_date_str

```

#### 4.3 Views (`sales/views.py`)

Views are kept thin, delegating business logic to models or form validation. They handle HTMX requests for dynamic updates and form submissions. The `TablePartialView` and `SearchCriteriaToggleView` are added for HTMX interactions.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q
from django.template.loader import render_to_string

from .models import CustomerEnquiry, CustomerMaster
from .forms import CustomerEnquiryForm, SearchEnquiryForm

# Mock session data and helper functions for demonstration
# In a real application, these would come from authentication middleware/session
def get_user_session_data(request):
    # This simulates ASP.NET Session variables.
    # In Django, use request.user for authentication, and profile/settings for compid/finyear.
    return {
        "compid": 1, # Example: request.user.profile.company_id
        "username": "ADMIN", # Example: request.user.username
        "finyear": 2024, # Example: request.user.profile.financial_year
    }

class CustomerEnquiryListView(ListView):
    model = CustomerEnquiry
    template_name = 'sales/customerenquiry/list.html'
    context_object_name = 'customerenquiries'
    paginate_by = 20 # ASP.NET GridView PageSize

    def get_queryset(self):
        session_data = get_user_session_data(self.request)
        comp_id = session_data["compid"]
        fin_year = session_data["finyear"]

        queryset = CustomerEnquiry.objects.active_enquiries(comp_id, fin_year)

        # Apply search filters from SearchEnquiryForm
        form = SearchEnquiryForm(self.request.GET)
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            customer_name_query = form.cleaned_data.get('customer_name')
            enquiry_no_query = form.cleaned_data.get('enquiry_no')

            if search_by == 'customer_name' and customer_name_query:
                # Original ASP.NET used fun.getCode(TxtSearchValue.Text) if search_by was 0 (customer name)
                # which implies CustomerName [CustomerId]. We should search by CustomerName directly
                # or parse the [CustomerId] if that's how it's submitted.
                # For simplicity, let's assume direct search by customer name for now.
                # If the search value includes " [ID]", we should extract the ID.
                if ' [' in customer_name_query and customer_name_query.endswith(']'):
                    parts = customer_name_query.rsplit(' [', 1)
                    customer_id_from_search = parts[1][:-1]
                    queryset = queryset.filter(customer_id=customer_id_from_search)
                else:
                    queryset = queryset.filter(customer_name__icontains=customer_name_query)
            elif search_by == 'enquiry_no' and enquiry_no_query:
                queryset = queryset.filter(enq_id__iexact=enquiry_no_query)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the search form to the template
        context['search_form'] = SearchEnquiryForm(self.request.GET)
        return context

# HTMX partial view for rendering the table content
class CustomerEnquiryTablePartialView(CustomerEnquiryListView):
    template_name = 'sales/customerenquiry/_customerenquiry_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure that 'object_list' is available for the partial template
        context['customerenquiries'] = context['object_list']
        return context

# HTMX endpoint for toggling search input visibility
class SearchCriteriaToggleView(View):
    def post(self, request, *args, **kwargs):
        form = SearchEnquiryForm(request.POST)
        context = {}
        if form.is_valid():
            selected_value = form.cleaned_data.get('search_by')
            context['selected_search_by'] = selected_value
            context['form'] = form # Pass form back to ensure values persist
            return render(request, 'sales/customerenquiry/_search_inputs.html', context)
        return HttpResponse("", status=400) # Or return an error partial

# HTMX endpoint for customer autocomplete
class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('customer_name', '')
        session_data = get_user_session_data(request)
        comp_id = session_data["compid"]

        if prefix_text:
            customers = CustomerMaster.objects.filter(
                comp_id=comp_id,
                customer_name__icontains=prefix_text # Original was startsWith, but icontains is more flexible
            ).values('customer_id', 'customer_name')[:10] # Limit to 10 suggestions

            suggestions = [{"id": c['customer_id'], "name": c['customer_name']} for c in customers]
            return render(request, 'sales/customerenquiry/_customer_autocomplete_results.html', {'suggestions': suggestions})
        return HttpResponse("") # Empty response if no prefix_text

class CustomerEnquiryCreateView(CreateView):
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales/customerenquiry/form.html'
    success_url = reverse_lazy('customerenquiry_list') # Redirect to list view after success

    def get_initial(self):
        initial = super().get_initial()
        session_data = get_user_session_data(self.request)
        initial['comp_id'] = session_data["compid"]
        initial['session_id'] = session_data["username"]
        # Generate a new unique enquiry ID here if not auto-incrementing in DB.
        # For simplicity, leaving it to user input for now or generate based on sequence.
        # In real ERP, this often comes from a sequence generator.
        # initial['enq_id'] = generate_unique_enquiry_id()
        return initial

    def form_valid(self, form):
        # Additional business logic before saving (e.g., generate enq_id if not DB auto)
        # form.instance.enq_id = generate_unique_enquiry_id() # Example
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Enquiry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList' # Custom event to trigger list refresh
                }
            )
        return response

class CustomerEnquiryUpdateView(UpdateView):
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales/customerenquiry/form.html'
    success_url = reverse_lazy('customerenquiry_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Enquiry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response

class CustomerEnquiryDeleteView(DeleteView):
    model = CustomerEnquiry
    template_name = 'sales/customerenquiry/confirm_delete.html'
    success_url = reverse_lazy('customerenquiry_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Enquiry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response

```

#### 4.4 Templates (`sales/templates/sales/customerenquiry/`)

These templates implement the UI using HTMX, Alpine.js, and DataTables.

**`sales/templates/sales/customerenquiry/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Enquiries - Edit</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'customerenquiry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Enquiry
        </button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search Enquiries</h3>
        <form id="searchForm" hx-get="{% url 'customerenquiry_table' %}" hx-target="#customerenquiryTable-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By:</label>
                    {{ search_form.search_by }}
                </div>
                <div id="search-inputs">
                    {% include 'sales/customerenquiry/_search_inputs.html' with selected_search_by=search_form.search_by.value form=search_form %}
                </div>
            </div>
            <div class="mt-6 text-right">
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </form>
    </div>
    
    <div id="customerenquiryTable-container"
         hx-trigger="load, refreshCustomerEnquiryList from:body"
         hx-get="{% url 'customerenquiry_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading customer enquiries...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 hidden flex items-center justify-center is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components for this page yet, HTMX handles most dynamic parts.
        // Alpine.js is used for modal show/hide in the HTML with `_=` attributes.
    });
</script>
{% endblock %}

```

**`sales/templates/sales/customerenquiry/_customerenquiry_table.html`** (Partial for DataTables)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-lg p-4">
    <table id="customerenquiryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in customerenquiries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.fin_year_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800">
                    <a href="{% url 'customerenquiry_detail' obj.pk %}?CustomerId={{ obj.customer_id }}&EnqId={{ obj.enq_id }}&ModId=2&SubModId=10">
                        {{ obj.customer_name }}
                    </a>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.customer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.enq_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.formatted_sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.employee_full_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'customerenquiry_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                        hx-get="{% url 'customerenquiry_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-lg text-maroon-600 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#customerenquiryTable').DataTable({
            "pageLength": 20, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "pagingType": "simple_numbers", // Or "full_numbers"
        });
    });
</script>

```

**`sales/templates/sales/customerenquiry/_search_inputs.html`** (Partial for dynamic search fields)

```html
<div x-data="{ selectedSearchBy: '{{ selected_search_by|default:'select' }}' }" x-init="selectedSearchBy = $el.closest('form').querySelector('[name=search_by]').value">
    <div x-show="selectedSearchBy === 'customer_name'" class="relative" x-cloak>
        <label for="{{ form.customer_name.id_for_label }}" class="sr-only">Customer Name</label>
        {{ form.customer_name }}
        <div id="customer-autocomplete-results"
             class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
             x-show="showAutocomplete"
             x-transition:enter="ease-out duration-200"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-150"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             >
             <!-- Autocomplete results loaded here by HTMX -->
        </div>
        {% if form.customer_name.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.customer_name.errors }}</p>
        {% endif %}
    </div>
    <div x-show="selectedSearchBy === 'enquiry_no'" x-cloak>
        <label for="{{ form.enquiry_no.id_for_label }}" class="sr-only">Enquiry No</label>
        {{ form.enquiry_no }}
        {% if form.enquiry_no.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.enquiry_no.errors }}</p>
        {% endif %}
    </div>
    <div x-show="selectedSearchBy === 'select'" x-cloak>
        <div class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-500 sm:text-sm">
            Please select a search criteria
        </div>
    </div>
</div>

```

**`sales/templates/sales/customerenquiry/_customer_autocomplete_results.html`** (Partial for autocomplete dropdown)

```html
{% if suggestions %}
    {% for suggestion in suggestions %}
        <div class="px-4 py-2 hover:bg-blue-100 cursor-pointer"
             hx-on:click="document.getElementById('{{ form.customer_name.id_for_label }}').value = '{{ suggestion.name }} [{{ suggestion.id }}]'; showAutocomplete = false;">
            {{ suggestion.name }} [<span class="text-gray-500 text-xs">{{ suggestion.id }}</span>]
        </div>
    {% endfor %}
{% else %}
    <div class="px-4 py-2 text-gray-500">No results found</div>
{% endif %}

```

**`sales/templates/sales/customerenquiry/form.html`** (Partial for CRUD forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Enquiry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save
            </button>
        </div>
    </form>
</div>
```

**`sales/templates/sales/customerenquiry/confirm_delete.html`** (Partial for delete confirmation)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-lg text-gray-700 mb-8">
        Are you sure you want to delete Customer Enquiry "{{ object.enq_id }} - {{ object.customer_name }}"?
        This action cannot be undone.
    </p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`sales/urls.py`)

Defines all URL patterns for the `sales` application, including standard CRUD and HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    CustomerEnquiryListView,
    CustomerEnquiryCreateView,
    CustomerEnquiryUpdateView,
    CustomerEnquiryDeleteView,
    CustomerEnquiryTablePartialView,
    SearchCriteriaToggleView,
    CustomerAutocompleteView,
)

urlpatterns = [
    # Main list view for Customer Enquiries
    path('customerenquiry/', CustomerEnquiryListView.as_view(), name='customerenquiry_list'),
    
    # HTMX endpoints for dynamic content loading
    path('customerenquiry/table/', CustomerEnquiryTablePartialView.as_view(), name='customerenquiry_table'),
    path('customerenquiry/hx-search-criteria-toggle/', SearchCriteriaToggleView.as_view(), name='hx_search_criteria_toggle'),
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'), # Autocomplete for customer name

    # CRUD operations
    path('customerenquiry/add/', CustomerEnquiryCreateView.as_view(), name='customerenquiry_add'),
    path('customerenquiry/edit/<str:pk>/', CustomerEnquiryUpdateView.as_view(), name='customerenquiry_edit'),
    path('customerenquiry/delete/<str:pk>/', CustomerEnquiryDeleteView.as_view(), name='customerenquiry_delete'),

    # Placeholder for detail page (from original ASP.NET HyperLinkField)
    # This would link to a separate detail/edit page for an individual enquiry.
    path('customerenquiry/<str:pk>/detail/', CustomerEnquiryUpdateView.as_view(), name='customerenquiry_detail'), # Reusing update view for detail link
]
```

#### 4.6 Tests (`sales/tests.py`)

Comprehensive tests for models, forms, and views to ensure functionality and data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import CustomerEnquiry, CustomerMaster, FinancialYear, Employee, WorkOrder
from .forms import CustomerEnquiryForm, SearchEnquiryForm
from datetime import datetime

# Mock function for session data consistent with views
def mock_get_user_session_data(request):
    return {
        "compid": 1,
        "username": "TESTUSER",
        "finyear": 2024,
    }

# Mock out direct database access for tests if using unmanaged models
# In a real scenario, you'd ensure your test database connection works with managed=False models
# or use `mock_db_connections` if you want to avoid hitting real DB.
# For simplicity and demonstrating Django's test setup, we will create mock instances
# that the ORM can interact with, assuming the test database is set up to reflect schema.

class ModelTestBase(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock related objects first due to FK constraints
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year="2023-2024")
        cls.employee = Employee.objects.create(emp_id="EMP001", title="Mr", employee_name="Test Employee", comp_id=1)
        cls.customer_master = CustomerMaster.objects.create(customer_id="CUST001", customer_name="Test Customer", comp_id=1)
        
        # Create test data for CustomerEnquiry
        cls.enquiry_1 = CustomerEnquiry.objects.create(
            enq_id="ENQ001",
            fin_year_id=cls.fin_year.fin_year_id,
            customer_name=cls.customer_master.customer_name,
            customer_id=cls.customer_master.customer_id,
            sys_date="01-15-2024",
            comp_id=1,
            session_id=cls.employee.emp_id
        )
        cls.enquiry_2 = CustomerEnquiry.objects.create(
            enq_id="ENQ002",
            fin_year_id=cls.fin_year.fin_year_id,
            customer_name="Another Customer",
            customer_id="CUST002",
            sys_date="02-20-2024",
            comp_id=1,
            session_id=cls.employee.emp_id
        )
        # Enquiry with a closed work order (should be excluded)
        cls.enquiry_closed = CustomerEnquiry.objects.create(
            enq_id="ENQ003",
            fin_year_id=cls.fin_year.fin_year_id,
            customer_name="Closed Enquiry Customer",
            customer_id="CUST003",
            sys_date="03-01-2024",
            comp_id=1,
            session_id=cls.employee.emp_id
        )
        WorkOrder.objects.create(enquiry=cls.enquiry_closed, enq_id=cls.enquiry_closed.enq_id, close_open=1)
        
        # Enquiry with an open work order (should be included)
        cls.enquiry_open_wo = CustomerEnquiry.objects.create(
            enq_id="ENQ004",
            fin_year_id=cls.fin_year.fin_year_id,
            customer_name="Open WO Customer",
            customer_id="CUST004",
            sys_date="04-01-2024",
            comp_id=1,
            session_id=cls.employee.emp_id
        )
        WorkOrder.objects.create(enquiry=cls.enquiry_open_wo, enq_id=cls.enquiry_open_wo.enq_id, close_open=0)

class CustomerEnquiryModelTest(ModelTestBase):
    def test_customer_enquiry_creation(self):
        self.assertEqual(self.enquiry_1.enq_id, "ENQ001")
        self.assertEqual(self.enquiry_1.customer_name, "Test Customer")
        self.assertEqual(self.enquiry_1.fin_year_id, self.fin_year.fin_year_id)
        self.assertEqual(self.enquiry_1.customer_id, self.customer_master.customer_id)
        self.assertEqual(self.enquiry_1.sys_date, "01-15-2024")
        self.assertEqual(self.enquiry_1.comp_id, 1)
        self.assertEqual(self.enquiry_1.session_id, self.employee.emp_id)
        
    def test_formatted_sys_date_property(self):
        self.assertEqual(self.enquiry_1.formatted_sys_date, "15/01/2024")
        enq_ddmmyyyy = CustomerEnquiry.objects.create(
            enq_id="ENQ005", fin_year_id=self.fin_year.fin_year_id, customer_name="Date Test",
            customer_id="CUST005", sys_date="31-12-2023", comp_id=1, session_id=self.employee.emp_id
        )
        self.assertEqual(enq_ddmmyyyy.formatted_sys_date, "31/12/2023")
        enq_invalid_date = CustomerEnquiry.objects.create(
            enq_id="ENQ006", fin_year_id=self.fin_year.fin_year_id, customer_name="Invalid Date",
            customer_id="CUST006", sys_date="NOT-A-DATE", comp_id=1, session_id=self.employee.emp_id
        )
        self.assertEqual(enq_invalid_date.formatted_sys_date, "NOT-A-DATE")

    def test_active_enquiries_queryset(self):
        # Should exclude ENQ003 (closed work order)
        active_qs = CustomerEnquiry.objects.active_enquiries(comp_id=1, fin_year=2024)
        self.assertEqual(active_qs.count(), 3) # ENQ001, ENQ002, ENQ004
        self.assertIn(self.enquiry_1, active_qs)
        self.assertIn(self.enquiry_2, active_qs)
        self.assertIn(self.enquiry_open_wo, active_qs)
        self.assertNotIn(self.enquiry_closed, active_qs)
        
    def test_queryset_annotations(self):
        enquiry = CustomerEnquiry.objects.active_enquiries(comp_id=1, fin_year=2024).get(enq_id="ENQ001")
        self.assertEqual(enquiry.fin_year_display, "2023-2024")
        self.assertEqual(enquiry.employee_full_name, "Mr.Test Employee")

class CustomerEnquiryFormTest(TestCase):
    def test_customer_enquiry_form_valid_data(self):
        form = CustomerEnquiryForm(data={
            'enq_id': 'NEWENQ007',
            'fin_year_id': 2024,
            'customer_id': 'CUST007',
            'customer_name': 'New Customer',
            'sys_date': '07-04-2024',
            'comp_id': 1,
            'session_id': 'EMP002',
        })
        self.assertTrue(form.is_valid(), form.errors.as_text())

    def test_customer_enquiry_form_invalid_sys_date(self):
        form = CustomerEnquiryForm(data={
            'enq_id': 'NEWENQ008',
            'fin_year_id': 2024,
            'customer_id': 'CUST008',
            'customer_name': 'Another Customer',
            'sys_date': 'Invalid Date Format',
            'comp_id': 1,
            'session_id': 'EMP002',
        })
        self.assertFalse(form.is_valid())
        self.assertIn('sys_date', form.errors)
        self.assertIn("Date must be in MM-DD-YYYY or DD-MM-YYYY format.", form.errors['sys_date'])

    def test_search_enquiry_form_valid_data(self):
        form = SearchEnquiryForm(data={'search_by': 'customer_name', 'customer_name': 'Test'})
        self.assertTrue(form.is_valid())
        form = SearchEnquiryForm(data={'search_by': 'enquiry_no', 'enquiry_no': 'ENQ123'})
        self.assertTrue(form.is_valid())
        form = SearchEnquiryForm(data={'search_by': 'select'}) # Valid if nothing is entered
        self.assertTrue(form.is_valid())

    def test_search_enquiry_form_missing_fields(self):
        form = SearchEnquiryForm(data={'search_by': 'customer_name', 'customer_name': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('customer_name', form.errors)
        
        form = SearchEnquiryForm(data={'search_by': 'enquiry_no', 'enquiry_no': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('enquiry_no', form.errors)

@patch('sales.views.get_user_session_data', mock_get_user_session_data)
class CustomerEnquiryViewsTest(ModelTestBase):
    def setUp(self):
        super().setUp()
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('customerenquiry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/list.html')
        self.assertIn('customerenquiries', response.context)
        self.assertEqual(len(response.context['customerenquiries']), 3) # Only active ones

    def test_list_view_search_by_customer_name(self):
        response = self.client.get(reverse('customerenquiry_list'), {'search_by': 'customer_name', 'customer_name': 'Test Customer'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['customerenquiries']), 1)
        self.assertEqual(response.context['customerenquiries'].first().enq_id, self.enquiry_1.enq_id)

    def test_list_view_search_by_enquiry_no(self):
        response = self.client.get(reverse('customerenquiry_list'), {'search_by': 'enquiry_no', 'enquiry_no': 'ENQ002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['customerenquiries']), 1)
        self.assertEqual(response.context['customerenquiries'].first().enq_id, self.enquiry_2.enq_id)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('customerenquiry_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_customerenquiry_table.html')
        self.assertIn('customerenquiries', response.context)
        self.assertContains(response, 'ENQ001') # Check if data is present

    def test_create_view_get(self):
        response = self.client.get(reverse('customerenquiry_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/form.html')
        self.assertIsInstance(response.context['form'], CustomerEnquiryForm)
        self.assertEqual(response.context['form'].initial['comp_id'], 1)
        self.assertEqual(response.context['form'].initial['session_id'], 'TESTUSER')

    def test_create_view_post_success(self):
        data = {
            'enq_id': 'NEWENQTEST',
            'fin_year_id': self.fin_year.fin_year_id,
            'customer_id': self.customer_master.customer_id,
            'customer_name': 'New Test Customer',
            'sys_date': '01-01-2025',
            'comp_id': 1,
            'session_id': 'TESTUSER',
        }
        response = self.client.post(reverse('customerenquiry_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful POST
        self.assertTrue(CustomerEnquiry.objects.filter(enq_id='NEWENQTEST').exists())

    def test_create_view_post_htmx_success(self):
        data = {
            'enq_id': 'HXENQTEST',
            'fin_year_id': self.fin_year.fin_year_id,
            'customer_id': self.customer_master.customer_id,
            'customer_name': 'HTMX Test Customer',
            'sys_date': '01-01-2025',
            'comp_id': 1,
            'session_id': 'TESTUSER',
        }
        response = self.client.post(reverse('customerenquiry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')
        self.assertTrue(CustomerEnquiry.objects.filter(enq_id='HXENQTEST').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('customerenquiry_edit', args=[self.enquiry_1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/form.html')
        self.assertIsInstance(response.context['form'], CustomerEnquiryForm)
        self.assertEqual(response.context['form'].instance, self.enquiry_1)

    def test_update_view_post_success(self):
        updated_data = {
            'enq_id': self.enquiry_1.pk,
            'fin_year_id': self.enquiry_1.fin_year_id,
            'customer_id': self.enquiry_1.customer_id,
            'customer_name': 'Updated Customer Name',
            'sys_date': self.enquiry_1.sys_date,
            'comp_id': self.enquiry_1.comp_id,
            'session_id': self.enquiry_1.session_id,
        }
        response = self.client.post(reverse('customerenquiry_edit', args=[self.enquiry_1.pk]), updated_data)
        self.assertEqual(response.status_code, 302)
        self.enquiry_1.refresh_from_db()
        self.assertEqual(self.enquiry_1.customer_name, 'Updated Customer Name')

    def test_delete_view_get(self):
        response = self.client.get(reverse('customerenquiry_delete', args=[self.enquiry_1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/confirm_delete.html')
        self.assertEqual(response.context['object'], self.enquiry_1)

    def test_delete_view_post_success(self):
        response = self.client.post(reverse('customerenquiry_delete', args=[self.enquiry_1.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(CustomerEnquiry.objects.filter(pk=self.enquiry_1.pk).exists())

    def test_delete_view_post_htmx_success(self):
        response = self.client.post(reverse('customerenquiry_delete', args=[self.enquiry_2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')
        self.assertFalse(CustomerEnquiry.objects.filter(pk=self.enquiry_2.pk).exists())

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'customer_name': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_customer_autocomplete_results.html')
        self.assertContains(response, 'Test Customer')
        self.assertContains(response, 'CUST001')

    def test_customer_autocomplete_view_no_results(self):
        response = self.client.get(reverse('customer_autocomplete'), {'customer_name': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_customer_autocomplete_results.html')
        self.assertContains(response, 'No results found')

    def test_search_criteria_toggle_view(self):
        response = self.client.post(reverse('hx_search_criteria_toggle'), {'search_by': 'enquiry_no'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerenquiry/_search_inputs.html')
        # Check if the correct input field is visible/rendered
        self.assertContains(response, 'name="enquiry_no"')
        self.assertNotContains(response, 'name="customer_name"')


```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation:**

1.  **HTMX for Dynamic Updates:**
    *   **Table Refresh:** The `customerenquiryTable-container` in `list.html` uses `hx-trigger="load, refreshCustomerEnquiryList from:body"` and `hx-get="{% url 'customerenquiry_table' %}"` to load the table content dynamically on page load and whenever a `refreshCustomerEnquiryList` event is triggered (e.g., after a CRUD operation).
    *   **Form Modals:** "Add New Enquiry", "Edit", and "Delete" buttons use `hx-get` to fetch their respective form/confirmation partials (`form.html`, `confirm_delete.html`) into the `#modalContent` div.
    *   **Form Submission:** Forms (`form.html`, `confirm_delete.html`) use `hx-post="{{ request.path }}" hx-swap="none"`. Upon successful submission, views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshCustomerEnquiryList'})`, which closes the modal (implicitly via `on click remove .is-active from #modal` on parent element) and triggers the list refresh.
    *   **Search Input Toggle:** The `search_by` dropdown in `list.html` uses `hx-post="hx-search-criteria-toggle/" hx-target="#search-inputs" hx-swap="innerHTML"` to dynamically load the correct search input field (`_search_inputs.html`) based on the selection.
    *   **Customer Autocomplete:** The `customer_name` input uses `hx-get="/sales/customer-autocomplete/"` with `hx-trigger="keyup changed delay:500ms, search"` to fetch suggestions into `#customer-autocomplete-results`. Clicking a suggestion then updates the input field via Alpine.js.

2.  **Alpine.js for UI State Management:**
    *   **Modal Visibility:** The main `#modal` div uses `x-data="{ showModal: false }"` (or similar) combined with `_=` attributes for simpler JS. Specifically, `_="on click add .is-active to #modal"` for opening and `_="on click remove .is-active from me"` (on the modal overlay) or `_="on click remove .is-active from #modal"` (on Cancel buttons) for closing. The `is-active` class would control Tailwind's `hidden` property.
    *   **Autocomplete Dropdown:** The `_search_inputs.html` uses `x-data` and `x-show="showAutocomplete"` to control the visibility of the autocomplete results dropdown. `x-on:focus` and `x-on:click.outside` events handle showing/hiding the dropdown.

3.  **DataTables for List Views:**
    *   The `_customerenquiry_table.html` partial contains a standard `<table>` with a unique ID (`customerenquiryTable`).
    *   A `<script>` block within this partial initializes DataTables: `$('#customerenquiryTable').DataTable({...});`. This script runs *after* the HTMX content is loaded into the DOM, ensuring DataTables can correctly attach to the table.
    *   This provides client-side searching, sorting, and pagination without full page reloads after the initial table render.

4.  **No Additional JavaScript:** All dynamic interactions are managed by HTMX and Alpine.js, adhering to the "no additional JavaScript" principle.

5.  **DRY Template Inheritance:**
    *   All component templates (`list.html`, `form.html`, `confirm_delete.html`) extend `core/base.html` (as per instructions, `base.html` is assumed to exist and handle CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS).

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names derived from the ASP.NET code, following Django conventions.
*   **DRY Templates:** Partial templates (`_customerenquiry_table.html`, `_search_inputs.html`, `_customer_autocomplete_results.html`) are used for reusable components, ensuring modularity.
*   **Fat Model, Thin View:** Complex filtering logic for `CustomerEnquiry` (checking `CloseOpen` status, related data annotation) is pushed into a custom `QuerySet` (`active_enquiries` method) within the `sales/models.py`. Views remain concise, primarily handling HTTP request/response and form processing.
*   **Tests:** Comprehensive unit tests for models and integration tests for views are provided, covering various scenarios including HTMX interactions and data filtering.
*   **Security:** Session management for `CompId`, `username`, `finyear` is mocked. In a real application, Django's built-in authentication and user profiles would manage these. `CSRF_TOKEN` is included in all forms.
*   **Date Handling:** The migration addresses the complex string-based date handling by providing a `@property` in the model to parse the string, but highlights that the ideal solution is to store dates in a proper `DateField` or `DateTimeField` format in the database itself.
*   **Styling:** Tailwind CSS classes are liberally applied to match a modern UI aesthetic, consistent with the project guidelines.