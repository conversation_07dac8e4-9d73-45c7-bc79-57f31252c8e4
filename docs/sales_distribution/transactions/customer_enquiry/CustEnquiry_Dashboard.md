## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the provided ASP.NET code, the explicit database interaction is a `DELETE` operation on `tblFile_Attachment`. The `CustEnquiry_Dashboard.aspx` page's name suggests a primary entity related to "Customer Enquiry." We will infer a model for `CustomerEnquiry` as the main subject of this module for comprehensive CRUD operations, while `FileAttachment` will handle the specific cleanup logic from the `Page_Load` event.

-   **`tblFile_Attachment`**:
    *   **Table Name**: `tblFile_Attachment`
    *   **Inferred Columns**:
        *   `Id` (Primary Key, Integer)
        *   `CompId` (Integer) - Corresponds to `Session["compid"]`
        *   `FinYearId` (Integer) - Corresponds to `Session["finyear"]`
        *   `SessionId` (String) - Corresponds to `Session["username"]`
        *   `FileName` (String) - Inferred for file attachments
        *   `FilePath` (String) - Inferred for file attachments
        *   `UploadDate` (DateTime) - Inferred for file attachments

-   **`CustomerEnquiry`**: (Inferred as the primary business entity for this module, as its details are not directly provided in the current ASP.NET snippet. This model will be used to demonstrate full CRUD functionality as required.)
    *   **Table Name**: `tblCustomerEnquiry` (Hypothetical, common naming convention)
    *   **Inferred Columns**:
        *   `EnquiryId` (Primary Key, Integer)
        *   `EnquiryNumber` (String)
        *   `CustomerName` (String)
        *   `EnquiryDate` (Date)
        *   `Status` (String)
        *   `Remarks` (Text)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

-   **`tblFile_Attachment`**:
    *   **Delete**: An explicit `DELETE` operation is performed on `tblFile_Attachment` in the `Page_Load` event of `CustEnquiry_Dashboard.aspx.cs`. This cleanup targets records associated with the current `CompId`, `FinYearId`, and `SessionId`.

-   **`CustomerEnquiry`**: (Inferred)
    *   **Create**: Implicitly expected for a customer enquiry module (e.g., adding a new enquiry).
    *   **Read**: Implicitly expected (e.g., viewing a list of enquiries or a single enquiry's details).
    *   **Update**: Implicitly expected (e.g., modifying an existing enquiry).
    *   **Delete**: Implicitly expected (e.g., removing an enquiry).
    *   **Validation Logic**: Not explicit in the provided ASP.NET snippet, but expected for any form submissions.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The `CustEnquiry_Dashboard.aspx` primarily serves as a container for an iframe pointing to `CustEnquiry_Convert.aspx`. This implies the dashboard itself has minimal direct UI elements. The `Page_Load` logic is a background operation.

-   **`CustEnquiry_Dashboard.aspx`**:
    *   **`iframe`**: The dominant element, embedding `CustEnquiry_Convert.aspx`. This indicates that the core interaction logic for "Customer Enquiry Conversion" resides in the separate page. In Django, this implies either embedding another Django view's content or a direct navigation/link. For this migration, the dashboard will simply be a landing page that performs the cleanup, and links to the main `CustomerEnquiry` CRUD section.

-   **`CustEnquiry_Convert.aspx` (Inferred)**:
    *   Would likely contain data entry forms (e.g., `TextBox` for customer name, `DropDownList` for status), data display (e.g., `GridView` for listing enquiries), and action buttons (`Button` for save, delete, etc.).
    *   We will simulate these UI components for the `CustomerEnquiry` model using Django forms, views, and HTMX-driven DataTables.

## Step 4: Generate Django Code

The Django application will be named `sales_enquiry` to reflect the module `Module_SalesDistribution_Transactions_CustEnquiry`.

### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

We will define two models: `FileAttachment` to handle the cleanup logic, and `CustomerEnquiry` as the primary business entity for the module, on which we will demonstrate the full CRUD functionality.

```python
# sales_enquiry/models.py
from django.db import models
from django.utils import timezone
from django.db.models import Manager # Import Manager to inherit from

class FileAttachmentManager(Manager):
    """
    Custom manager for FileAttachment to encapsulate cleanup logic.
    """
    def cleanup_attachments_for_session(self, comp_id, fin_year_id, session_key):
        """
        Deletes temporary file attachments linked to a specific company, financial year,
        and user session. This mimics the Page_Load cleanup in the ASP.NET code.
        """
        try:
            # For demonstration, we'll assume a user is logged in and their session_key is used
            # along with comp_id and fin_year_id from their profile/context.
            # In a real app, comp_id and fin_year_id would come from the authenticated user's profile.
            self.filter(
                comp_id=comp_id,
                fin_year_id=fin_year_id,
                session_id=session_key
            ).delete()
            # No return value needed, as this is a side effect.
            return True
        except Exception as e:
            # Log the exception in a real application
            print(f"Error during file attachment cleanup: {e}")
            return False

class FileAttachment(models.Model):
    """
    Represents temporary file attachments.
    Corresponds to 'tblFile_Attachment' in the ASP.NET code.
    Used primarily for cleanup on page load.
    """
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    fin_year_id = models.IntegerField(db_column='FinYearId', verbose_name="Financial Year ID")
    session_id = models.CharField(max_length=255, db_column='SessionId', verbose_name="Session ID")
    file_name = models.CharField(max_length=255, db_column='FileName', blank=True, null=True, verbose_name="File Name")
    file_path = models.CharField(max_length=500, db_column='FilePath', blank=True, null=True, verbose_name="File Path")
    upload_date = models.DateTimeField(db_column='UploadDate', default=timezone.now, verbose_name="Upload Date")

    objects = FileAttachmentManager() # Assign the custom manager

    class Meta:
        managed = False  # Set to True if Django manages the table creation/migrations
        db_table = 'tblFile_Attachment'
        verbose_name = 'File Attachment'
        verbose_name_plural = 'File Attachments'

    def __str__(self):
        return f"{self.file_name or 'No Name'} (Session: {self.session_id})"

class CustomerEnquiry(models.Model):
    """
    Represents a customer enquiry.
    Corresponds conceptually to the 'CustEnquiry' module.
    """
    enquiry_number = models.CharField(max_length=100, unique=True, verbose_name="Enquiry Number")
    customer_name = models.CharField(max_length=255, verbose_name="Customer Name")
    enquiry_date = models.DateField(default=timezone.now, verbose_name="Enquiry Date")
    status = models.CharField(max_length=50, default='Pending', verbose_name="Status")
    remarks = models.TextField(blank=True, null=True, verbose_name="Remarks")

    class Meta:
        managed = False  # Set to True if Django manages the table creation/migrations
        db_table = 'tblCustomerEnquiry' # Hypothetical table name
        verbose_name = 'Customer Enquiry'
        verbose_name_plural = 'Customer Enquiries'

    def __str__(self):
        return f"{self.enquiry_number} - {self.customer_name}"

    def update_status(self, new_status):
        """Business logic: Update enquiry status."""
        valid_statuses = ['Pending', 'In Progress', 'Converted', 'Closed', 'Rejected']
        if new_status not in valid_statuses:
            raise ValueError(f"Invalid status: {new_status}. Must be one of {valid_statuses}")
        self.status = new_status
        self.save()
        return True

    def is_editable(self):
        """Business logic: Determine if an enquiry can be edited."""
        return self.status not in ['Converted', 'Closed', 'Rejected']

```

### 4.2 Forms

Task: Define a Django form for user input for `CustomerEnquiry`.

## Instructions:

A `ModelForm` will be created for the `CustomerEnquiry` model, incorporating basic Tailwind CSS classes for styling.

```python
# sales_enquiry/forms.py
from django import forms
from .models import CustomerEnquiry

class CustomerEnquiryForm(forms.ModelForm):
    class Meta:
        model = CustomerEnquiry
        fields = ['enquiry_number', 'customer_name', 'enquiry_date', 'status', 'remarks']
        widgets = {
            'enquiry_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enquiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[('Pending', 'Pending'), ('In Progress', 'In Progress'), ('Converted', 'Converted'), ('Closed', 'Closed'), ('Rejected', 'Rejected')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'rows': 4, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_enquiry_number(self):
        enquiry_number = self.cleaned_data.get('enquiry_number')
        # Check for uniqueness only for new objects or if the enquiry number is changed
        if self.instance.pk is None or self.instance.enquiry_number != enquiry_number:
            if CustomerEnquiry.objects.filter(enquiry_number=enquiry_number).exists():
                raise forms.ValidationError("This enquiry number already exists.")
        return enquiry_number

```

### 4.3 Views

Task: Implement CRUD operations using CBVs for `CustomerEnquiry` and a dashboard view for `FileAttachment` cleanup.

## Instructions:

The `CustEnquiryDashboardView` will handle the `Page_Load` cleanup. The standard `ListView`, `CreateView`, `UpdateView`, `DeleteView` and a partial view for `CustomerEnquiry` will implement the CRUD operations.

```python
# sales_enquiry/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import CustomerEnquiry, FileAttachment
from .forms import CustomerEnquiryForm
from django.shortcuts import render

# --- Dashboard View (mimicking CustEnquiry_Dashboard.aspx behavior) ---
class CustEnquiryDashboardView(TemplateView):
    """
    This view mimics the behavior of CustEnquiry_Dashboard.aspx.cs's Page_Load,
    performing cleanup of temporary file attachments, and then rendering a dashboard.
    """
    template_name = 'sales_enquiry/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Assuming current user for session ID, and company/financial year from user profile/context.
        # In a real app, comp_id and fin_year_id would be dynamically fetched from the user's session
        # or profile, e.g., self.request.user.user_profile.comp_id
        
        # For demonstration, hardcode or get from temporary session/config if not tied to user.
        # Here, we'll use a placeholder for demo purposes.
        # In a real scenario, this would come from the authenticated user's context.
        # Example: user_comp_id = self.request.user.profile.comp_id if self.request.user.is_authenticated else 0
        # user_fin_year = self.request.user.profile.fin_year if self.request.user.is_authenticated else 0
        # session_id_for_cleanup = self.request.session.session_key if self.request.session.session_key else 'default_session'
        
        # Placeholder values for demonstration (replace with actual session/user data)
        user_comp_id = 1 
        user_fin_year = 2024
        # Using a dummy session ID for cleanup if user is not authenticated or a real session_key is needed
        session_id_for_cleanup = self.request.session.session_key or 'anonymous_session_dummy'
        
        cleanup_success = FileAttachment.objects.cleanup_attachments_for_session(
            comp_id=user_comp_id,
            fin_year_id=user_fin_year,
            session_key=session_id_for_cleanup
        )
        if cleanup_success:
            messages.info(self.request, "Temporary file attachments cleaned up.")
        else:
            messages.error(self.request, "Failed to clean up temporary file attachments.")

        return context

# --- Customer Enquiry CRUD Views ---
class CustomerEnquiryListView(ListView):
    model = CustomerEnquiry
    template_name = 'sales_enquiry/customerenquiry/list.html'
    context_object_name = 'customer_enquiries'

class CustomerEnquiryTablePartialView(ListView):
    """
    Renders only the table portion for HTMX partial updates.
    """
    model = CustomerEnquiry
    template_name = 'sales_enquiry/customerenquiry/_customerenquiry_table.html'
    context_object_name = 'customer_enquiries'

class CustomerEnquiryCreateView(CreateView):
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales_enquiry/customerenquiry/_customerenquiry_form.html' # Use partial template
    success_url = reverse_lazy('customerenquiry_list') # Redundant for HTMX, but good fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Enquiry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing but listen for triggers
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList' # Custom HTMX event to trigger list refresh
                }
            )
        return response

class CustomerEnquiryUpdateView(UpdateView):
    model = CustomerEnquiry
    form_class = CustomerEnquiryForm
    template_name = 'sales_enquiry/customerenquiry/_customerenquiry_form.html' # Use partial template
    context_object_name = 'customer_enquiry'
    success_url = reverse_lazy('customerenquiry_list') # Redundant for HTMX, but good fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Enquiry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response

class CustomerEnquiryDeleteView(DeleteView):
    model = CustomerEnquiry
    template_name = 'sales_enquiry/customerenquiry/confirm_delete.html' # Use partial template
    context_object_name = 'customer_enquiry'
    success_url = reverse_lazy('customerenquiry_list') # Redundant for HTMX, but good fallback

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Enquiry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerEnquiryList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will extend `core/base.html` (not included here) and use HTMX for dynamic interactions, Alpine.js for modal behavior, and DataTables for list presentation.

```html
{# sales_enquiry/dashboard.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer Enquiry Dashboard</h2>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <p class="text-gray-700">Welcome to the Customer Enquiry Dashboard. This page performs necessary background cleanup operations.</p>
        <p class="mt-4">You can manage customer enquiries from here:</p>
        <a href="{% url 'customerenquiry_list' %}" 
           class="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
           Go to Customer Enquiries
        </a>
    </div>

    <!-- Messages display area (often in base.html, but included here for completeness) -->
    {% if messages %}
        <div class="mt-6">
            {% for message in messages %}
                <div class="p-4 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for dashboard specific interactivity
    });
</script>
{% endblock %}

```

```html
{# sales_enquiry/customerenquiry/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer Enquiries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'customerenquiry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Enquiry
        </button>
    </div>
    
    <div id="customerenquiryTable-container"
         hx-trigger="load, refreshCustomerEnquiryList from:body" {# Listen for the custom event to refresh #}
         hx-get="{% url 'customerenquiry_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading customer enquiries...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }" x-show="showModal"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             _="on htmx:afterOnLoad add .is-active to #modal">
             <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            open: false,
            // You can add more modal state here if needed, though HTMX directly controls its visibility.
        }));
    });
</script>
{% endblock %}

```

```html
{# sales_enquiry/customerenquiry/_customerenquiry_table.html #}
<table id="customerenquiryTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry Number</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in customer_enquiries %}
        <tr hx-target="this" hx-swap="outerHTML settle:500ms"> {# For potential row-level updates #}
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.enquiry_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.enquiry_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.status }}</td>
            <td class="py-2 px-4 border-b border-gray-200 flex space-x-2">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'customerenquiry_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'customerenquiry_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Ensure DataTable is re-initialized after HTMX swap
$(document).ready(function() {
    $('#customerenquiryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "destroy": true // Important: Destroy existing DataTable instance before re-initializing
    });
});
</script>
```

```html
{# sales_enquiry/customerenquiry/_customerenquiry_form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Enquiry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) {
            htmx.removeClass(document.getElementById('modal'), 'is-active');
            htmx.trigger(document.body, 'refreshCustomerEnquiryList');
          } else {
            console.error('Form submission failed');
            // Optionally, re-render form with errors if backend returns form data
            htmx.swap(event.detail.xhr.responseText, 'innerHTML');
          }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# sales_enquiry/customerenquiry/confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the enquiry "<strong>{{ customer_enquiry.enquiry_number }} - {{ customer_enquiry.customer_name }}</strong>"? This action cannot be undone.</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'customerenquiry_delete' customer_enquiry.pk %}"
            hx-swap="none"
            hx-on::after-request="if(event.detail.successful) {
                htmx.removeClass(document.getElementById('modal'), 'is-active');
                htmx.trigger(document.body, 'refreshCustomerEnquiryList');
            }"
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create `sales_enquiry/urls.py` with paths for the dashboard, and `CustomerEnquiry` CRUD operations, including the HTMX partial table view.

```python
# sales_enquiry/urls.py
from django.urls import path
from .views import (
    CustEnquiryDashboardView,
    CustomerEnquiryListView,
    CustomerEnquiryCreateView,
    CustomerEnquiryUpdateView,
    CustomerEnquiryDeleteView,
    CustomerEnquiryTablePartialView,
)

urlpatterns = [
    # Dashboard URL (mimicking original ASP.NET page)
    path('dashboard/', CustEnquiryDashboardView.as_view(), name='enquiry_dashboard'),

    # Customer Enquiry URLs
    path('customer-enquiries/', CustomerEnquiryListView.as_view(), name='customerenquiry_list'),
    path('customer-enquiries/table/', CustomerEnquiryTablePartialView.as_view(), name='customerenquiry_table'),
    path('customer-enquiries/add/', CustomerEnquiryCreateView.as_view(), name='customerenquiry_add'),
    path('customer-enquiries/edit/<int:pk>/', CustomerEnquiryUpdateView.as_view(), name='customerenquiry_edit'),
    path('customer-enquiries/delete/<int:pk>/', CustomerEnquiryDeleteView.as_view(), name='customerenquiry_delete'),
]

```

### 4.6 Tests

Task: Write tests for the models and views.

## Instructions:

Include comprehensive unit tests for model methods and integration tests for all views.

```python
# sales_enquiry/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import FileAttachment, CustomerEnquiry

class FileAttachmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.comp_id = 1
        cls.fin_year_id = 2024
        cls.session_key = 'testsession123'
        
        FileAttachment.objects.create(
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id,
            session_id=cls.session_key,
            file_name='temp_file_1.pdf',
            file_path='/path/to/temp_file_1.pdf'
        )
        FileAttachment.objects.create(
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id,
            session_id='anothersession',
            file_name='temp_file_2.docx',
            file_path='/path/to/temp_file_2.docx'
        )
        # File for different company/fin_year
        FileAttachment.objects.create(
            comp_id=2,
            fin_year_id=2023,
            session_id=cls.session_key,
            file_name='temp_file_3.txt',
            file_path='/path/to/temp_file_3.txt'
        )
  
    def test_file_attachment_creation(self):
        obj = FileAttachment.objects.get(session_id=self.session_key, file_name='temp_file_1.pdf')
        self.assertEqual(obj.comp_id, self.comp_id)
        self.assertEqual(obj.fin_year_id, self.fin_year_id)
        self.assertEqual(obj.file_name, 'temp_file_1.pdf')
        
    def test_verbose_name_plural(self):
        self.assertEqual(str(FileAttachment._meta.verbose_name_plural), 'File Attachments')

    def test_cleanup_attachments_for_session(self):
        initial_count = FileAttachment.objects.count()
        # Clean up files for the specific session, comp_id, fin_year_id
        FileAttachment.objects.cleanup_attachments_for_session(self.comp_id, self.fin_year_id, self.session_key)
        
        # Verify that only the relevant file was deleted
        self.assertEqual(FileAttachment.objects.count(), initial_count - 1)
        self.assertFalse(FileAttachment.objects.filter(session_id=self.session_key, file_name='temp_file_1.pdf').exists())
        self.assertTrue(FileAttachment.objects.filter(session_id='anothersession').exists())
        self.assertTrue(FileAttachment.objects.filter(comp_id=2).exists())


class CustomerEnquiryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.enquiry1 = CustomerEnquiry.objects.create(
            enquiry_number='ENQ001', 
            customer_name='Alice Smith', 
            enquiry_date=timezone.now().date(), 
            status='Pending'
        )
        cls.enquiry2 = CustomerEnquiry.objects.create(
            enquiry_number='ENQ002', 
            customer_name='Bob Johnson', 
            enquiry_date=timezone.now().date(), 
            status='In Progress'
        )
    
    def test_customer_enquiry_creation(self):
        self.assertEqual(self.enquiry1.enquiry_number, 'ENQ001')
        self.assertEqual(self.enquiry1.customer_name, 'Alice Smith')
        self.assertEqual(self.enquiry1.status, 'Pending')
        
    def test_update_status_method(self):
        self.enquiry1.update_status('Converted')
        self.enquiry1.refresh_from_db()
        self.assertEqual(self.enquiry1.status, 'Converted')
        
        with self.assertRaises(ValueError):
            self.enquiry1.update_status('Invalid Status')

    def test_is_editable_method(self):
        self.assertTrue(self.enquiry1.is_editable())
        self.enquiry1.status = 'Converted'
        self.assertFalse(self.enquiry1.is_editable())
        self.enquiry1.status = 'Closed'
        self.assertFalse(self.enquiry1.is_editable())
        self.enquiry1.status = 'Rejected'
        self.assertFalse(self.enquiry1.is_editable())


class SalesEnquiryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.enquiry1 = CustomerEnquiry.objects.create(
            enquiry_number='ENQ001', 
            customer_name='Alice Smith', 
            enquiry_date=timezone.now().date(), 
            status='Pending'
        )
        cls.enquiry2 = CustomerEnquiry.objects.create(
            enquiry_number='ENQ002', 
            customer_name='Bob Johnson', 
            enquiry_date=timezone.now().date(), 
            status='In Progress'
        )
    
    def setUp(self):
        self.client = Client()
        # Mocking session values or user authentication if needed for FileAttachment cleanup
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()
        # Create a dummy FileAttachment to be cleaned up
        FileAttachment.objects.create(
            comp_id=1,
            fin_year_id=2024,
            session_id=self.client.session.session_key,
            file_name='test_cleanup.txt'
        )
    
    def test_dashboard_view_get(self):
        response = self.client.get(reverse('enquiry_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_enquiry/dashboard.html')
        # Check if cleanup was attempted (assuming cleanup message in content)
        self.assertContains(response, "Temporary file attachments cleaned up.")
        # Verify file attachment was deleted for this session
        self.assertFalse(FileAttachment.objects.filter(session_id=self.client.session.session_key).exists())
        
    def test_customerenquiry_list_view(self):
        response = self.client.get(reverse('customerenquiry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_enquiry/customerenquiry/list.html')
        self.assertContains(response, 'ENQ001')
        self.assertContains(response, 'ENQ002')
        self.assertTrue('customer_enquiries' in response.context)
        self.assertEqual(len(response.context['customer_enquiries']), 2)

    def test_customerenquiry_table_partial_view(self):
        response = self.client.get(reverse('customerenquiry_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_enquiry/customerenquiry/_customerenquiry_table.html')
        self.assertContains(response, 'ENQ001')
        self.assertContains(response, 'ENQ002')

    def test_customerenquiry_create_view_get(self):
        response = self.client.get(reverse('customerenquiry_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_enquiry/customerenquiry/_customerenquiry_form.html')
        self.assertTrue('form' in response.context)
        
    def test_customerenquiry_create_view_post_htmx(self):
        data = {
            'enquiry_number': 'ENQ003',
            'customer_name': 'Charlie Brown',
            'enquiry_date': timezone.now().date(),
            'status': 'Pending',
            'remarks': 'New enquiry from HTMX'
        }
        response = self.client.post(reverse('customerenquiry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')
        self.assertTrue(CustomerEnquiry.objects.filter(enquiry_number='ENQ003').exists())
        
    def test_customerenquiry_create_view_post_validation_error(self):
        data = {
            'enquiry_number': 'ENQ001', # Duplicate
            'customer_name': 'Charlie Brown',
            'enquiry_date': timezone.now().date(),
            'status': 'Pending',
            'remarks': 'New enquiry from HTMX'
        }
        response = self.client.post(reverse('customerenquiry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'sales_enquiry/customerenquiry/_customerenquiry_form.html')
        self.assertContains(response, 'This enquiry number already exists.')
        
    def test_customerenquiry_update_view_get(self):
        response = self.client.get(reverse('customerenquiry_edit', args=[self.enquiry1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_enquiry/customerenquiry/_customerenquiry_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.pk, self.enquiry1.pk)
        
    def test_customerenquiry_update_view_post_htmx(self):
        updated_name = 'Alice Smith-Updated'
        data = {
            'enquiry_number': self.enquiry1.enquiry_number, # Must be included even if unchanged
            'customer_name': updated_name,
            'enquiry_date': self.enquiry1.enquiry_date,
            'status': 'Converted',
            'remarks': self.enquiry1.remarks
        }
        response = self.client.post(reverse('customerenquiry_edit', args=[self.enquiry1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')
        self.enquiry1.refresh_from_db()
        self.assertEqual(self.enquiry1.customer_name, updated_name)

    def test_customerenquiry_delete_view_get(self):
        response = self.client.get(reverse('customerenquiry_delete', args=[self.enquiry1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_enquiry/customerenquiry/confirm_delete.html')
        self.assertTrue('customer_enquiry' in response.context)

    def test_customerenquiry_delete_view_post_htmx(self):
        initial_count = CustomerEnquiry.objects.count()
        response = self.client.post(reverse('customerenquiry_delete', args=[self.enquiry1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerEnquiryList')
        self.assertEqual(CustomerEnquiry.objects.count(), initial_count - 1)
        self.assertFalse(CustomerEnquiry.objects.filter(pk=self.enquiry1.pk).exists())

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   HTMX is used extensively for all dynamic interactions:
    -   Modal loading for Add/Edit/Delete forms: `hx-get` on buttons targeting `#modalContent`.
    -   Form submissions: `hx-post` on forms, with `hx-swap="none"` and `hx-on::after-request` to handle modal dismissal and list refresh.
    -   List refreshing: The `customerenquiryTable-container` div uses `hx-trigger="load, refreshCustomerEnquiryList from:body"` to load the table on page load and refresh it upon the custom `refreshCustomerEnquiryList` event (triggered by successful CRUD operations).
    -   `HX-Trigger` headers are sent back from views (status 204) to inform the frontend about successful operations and trigger the list refresh.
-   Alpine.js (combined with simple `_=` attributes from `hyperscript.org` often used with HTMX) is used for UI state management, specifically for the modal:
    -   `id="modal"` element with `x-data="{ showModal: false }"` and `x-show="showModal"` (though in this example, HTMX directly adds/removes `is-active` class, Alpine can manage more complex states).
    -   `_=` attributes like `on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me` directly manage the modal's visibility.
-   DataTables is integrated into `_customerenquiry_table.html`:
    -   `$('#customerenquiryTable').DataTable(...)` initializes the table with client-side searching, sorting, and pagination.
    -   `"destroy": true` is crucial for HTMX, ensuring that DataTables can be re-initialized when the table content is swapped.
-   All interactions are designed to work without full page reloads, providing a smooth user experience.

## Final Notes

-   **Placeholders**: Replace `tblCustomerEnquiry` with the actual table name for customer enquiries in your database if different. `CompId`, `FinYearId`, and `SessionId` in `FileAttachment` should be dynamically obtained from the authenticated user's session or profile in a production environment, not hardcoded as in some test/demonstration scenarios.
-   **DRY Templates**: Templates are designed to be DRY, with `list.html` loading the `_customerenquiry_table.html` partial, and `_customerenquiry_form.html` serving for both create and update.
-   **Fat Model, Thin View**: Business logic for `FileAttachment` cleanup is in its custom manager method. `CustomerEnquiry` has `update_status` and `is_editable` methods. Views remain concise, focusing on handling HTTP requests and delegating business rules.
-   **Comprehensive Tests**: Unit tests cover model methods and properties. Integration tests cover all view functionalities, including HTMX specific interactions (status codes, headers).
-   **Tailwind CSS**: Assumed to be configured in your `core/base.html` and `tailwind.config.js`. The provided HTML includes Tailwind utility classes.