## ASP.NET to Django Conversion Script: Customer PO New Details

This document outlines a comprehensive plan for migrating the `CustPO_New_Details.aspx` and its C# code-behind to a modern Django application. Our approach focuses on leveraging Django's robust features, modern frontend techniques (HTMX + Alpine.js + DataTables), and an automation-driven strategy to minimize manual effort and ensure a smooth transition.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task**: Identify the database tables and their columns from the ASP.NET code.

**Instructions**:
The analysis of the ASP.NET code reveals interactions with several database tables. We will define Django models to map directly to these existing tables, ensuring `managed = False` to prevent Django from attempting to create or modify them.

**Identified Tables and Key Columns**:

1.  **`SD_Cust_PO_Master` (Customer Purchase Order Master)**:
    *   `POId` (Primary Key, auto-incremented)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `SessionId` (String, maps to `username` from ASP.NET session)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `CustomerId` (String/Integer, Foreign Key to `SD_Cust_master`)
    *   `EnqId` (Integer, Foreign Key to `SD_Cust_Enquiry_Master`)
    *   `QuotationNo` (Integer, Foreign Key to `SD_Cust_Quotation_Master`)
    *   `PONo` (String)
    *   `PODate` (Date)
    *   `POReceivedDate` (Date)
    *   `VendorCode` (String)
    *   `PaymentTerms` (String)
    *   `PF` (String)
    *   `VAT` (String)
    *   `Excise` (String)
    *   `Octroi` (String)
    *   `Warrenty` (String)
    *   `Insurance` (String)
    *   `Transport` (String)
    *   `NoteNo` (String)
    *   `RegistrationNo` (String)
    *   `Freight` (String)
    *   `Remarks` (String)
    *   `CST` (String)
    *   `Validity` (String)
    *   `OtherCharges` (String)
    *   `FileName` (String)
    *   `FileSize` (Integer)
    *   `ContentType` (String)
    *   `FileData` (Binary, for file content)

2.  **`SD_Cust_PO_Details` (Customer Purchase Order Details - Permanent)**:
    *   `Id` (Primary Key, auto-incremented)
    *   `SessionId` (String)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `POId` (Integer, Foreign Key to `SD_Cust_PO_Master`)
    *   `ItemDesc` (String)
    *   `TotalQty` (Decimal/Float)
    *   `Unit` (Integer, Foreign Key to `Unit_Master`)
    *   `Rate` (Decimal/Float)
    *   `Discount` (Decimal/Float)

3.  **`SD_Cust_PO_Details_Temp` (Customer Purchase Order Details - Temporary)**:
    *   `Id` (Primary Key, auto-incremented)
    *   `SessionId` (String, crucial for temporary storage by user)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `ItemDesc` (String)
    *   `TotalQty` (Decimal/Float)
    *   `Unit` (Integer, Foreign Key to `Unit_Master`)
    *   `Rate` (Decimal/Float)
    *   `Discount` (Decimal/Float)

4.  **`Unit_Master`**:
    *   `Id` (Primary Key)
    *   `Symbol` (String)

5.  **`SD_Cust_Quotation_Master`**:
    *   `Id` (Primary Key)
    *   `QuotationNo` (String)
    *   `SysDate` (String/Date)
    *   *Other fields implied by `SELECT *` in C#*

6.  **`SD_Cust_master`**:
    *   `CustomerId` (Primary Key)
    *   `CustomerName` (String)
    *   `RegdAddress` (String)
    *   `RegdCountry` (Integer, FK)
    *   `RegdState` (Integer, FK)
    *   `RegdCity` (Integer, FK)
    *   `RegdPinNo` (String)
    *   `CompId` (Integer)
    *   *Other fields implied by `SELECT *` in C#*

7.  **`tblcountry`**:
    *   `CId` (Primary Key)
    *   `CountryName` (String)

8.  **`tblState`**:
    *   `SId` (Primary Key)
    *   `StateName` (String)

9.  **`tblCity`**:
    *   `CityId` (Primary Key)
    *   `CityName` (String)

### Step 2: Identify Backend Functionality

**Task**: Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions**:
The ASP.NET page handles the creation of a new Customer PO across a multi-tab interface, including managing temporary line items in a grid.

*   **Create (Main PO)**: Triggered by `Button6_Click` on the "Terms & Conditions" tab. This involves inserting a new record into `SD_Cust_PO_Master` and then moving all associated records from `SD_Cust_PO_Details_Temp` to `SD_Cust_PO_Details`. It also handles file uploads and updates `POStatus` in `SD_Cust_Enquiry_Master`.
*   **Create (Goods Item)**: Triggered by `Button5_Click` on the "Goods Details" tab. This inserts a new item into `SD_Cust_PO_Details_Temp` and refreshes the grid (`GridView1`).
*   **Read (Main PO Details)**: Customer, Enquiry, and existing Quotation details are read and displayed on `Page_Load`.
*   **Read (Goods Items)**: The `FillGrid()` method reads data from `SD_Cust_PO_Details_Temp` to populate `GridView1`.
*   **Update (Goods Item)**: Handled by `GridView1_RowUpdating1` when editing a row in the "Goods Details" grid. Updates `SD_Cust_PO_Details_Temp`.
*   **Delete (Goods Item)**: Handled by `GridView1_RowDeleting` when deleting a row from the "Goods Details" grid. Deletes from `SD_Cust_PO_Details_Temp`.
*   **Validation**: Numerous `RequiredFieldValidator` and `RegularExpressionValidator` controls are present, along with server-side checks (`fun.DateValidation`, `fun.NumberValidationQty`).
*   **Session Management**: `Session["username"]`, `Session["compid"]`, `Session["finyear"]` are used for context. `Session["TabIndex"]` is used to persist the active tab.
*   **Dropdown/Autocomplete**: The `DropDownExtender` and `ListBox` controls dynamically load distinct values from `SD_Cust_PO_Master` for various terms (Payment Terms, PF, VAT, etc.), acting as a historical lookup or auto-suggest.

### Step 3: Infer UI Components

**Task**: Analyze ASP.NET controls and their roles.

**Instructions**:
The ASP.NET page features a multi-tabbed interface with a main form, a dynamic grid for line items, and several input fields, dropdowns, and buttons.

*   **TabContainer**: `AjaxControlToolkit:TabContainer` (ID `TabContainer1`) with three panels: "Customer Details", "Goods Details", "Terms & Conditions". This will be converted to a tabbed UI using Alpine.js for state management and potentially HTMX for lazy loading tab content.
*   **Display Labels**: `asp:Label` controls (`LblName`, `LblAddress`, `LblEnqNo`) for displaying customer and enquiry information.
*   **Input Fields**: `asp:TextBox` controls for text, numbers, and dates (`TxtPONo`, `TxtPODate`, `TxtPORecDate`, `TxtVendorCode`, `TxtItemDesc`, `TxtQty`, `TxtRate`, `TxtDiscount`, `TxtPayments`, `TxtPF`, `TxtExcise`, `txtVAT`, `TxtOctroi`, `TxtWarrenty`, `TxtInsurance`, `TxtTransPort`, `TxtNoteNo`, `TxtRegdNo`, `TxtFreight`, `Txtcst`, `Txtvalidity`, `Txtocharges`, `TxtRemarks`). Date fields use `CalendarExtender`.
*   **Dropdowns**: `asp:DropDownList` controls (`drpQuotNO`, `DrpUnit`) for selecting predefined values.
*   **File Upload**: `asp:FileUpload` (`FileUpload1`) for attaching documents.
*   **Buttons**: `asp:Button` controls for navigation (`Next`, `Cancel`) and form submission (`Submit`).
*   **Data Grid**: `asp:GridView` (`GridView1`) for displaying, adding, editing, and deleting "Goods Details" items. This will be replaced by a DataTables-powered HTML table, with HTMX for CRUD operations.
*   **Dynamic Dropdowns (Auto-suggest)**: The combination of `asp:Panel`, `asp:ListBox`, and `cc1:DropDownExtender` for fields like Payment Terms, PF, VAT, etc. These pull distinct values from `SD_Cust_PO_Master`. This will be converted to `select` fields with pre-populated options for common/historical values, or a `datalist` with HTMX-powered suggestions for a true auto-complete experience.

### Step 4: Generate Django Code

We will structure the Django application under a new app called `sales`.

#### 4.1 Models (`sales/models.py`)

**Task**: Create Django models based on the identified database schema.

**Instructions**:
All models will use `managed = False` and `db_table` to map to the existing database. Foreign key relationships are inferred and defined. Business logic related to calculated fields or data transformations (e.g., `Amount` calculation for `CustomerOrderItem`) will be added as model methods.

```python
from django.db import models
from django.conf import settings
from decimal import Decimal
import os

# --- Stub Models for referenced tables (assuming they exist in DB) ---
# These models define only the fields relevant to the current migration
# or essential for foreign key relationships.

class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # Assuming string from HfCustId.Text
    comp_id = models.IntegerField(db_column='CompId')
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    regd_address = models.CharField(db_column='RegdAddress', max_length=500, blank=True, null=True)
    regd_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='RegdCountry', blank=True, null=True)
    regd_state = models.ForeignKey(State, models.DO_NOTHING, db_column='RegdState', blank=True, null=True)
    regd_city = models.ForeignKey(City, models.DO_NOTHING, db_column='RegdCity', blank=True, null=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class CustomerQuotation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    sys_date = models.CharField(db_column='SysDate', max_length=50) # Store as string as per ASP.NET code
    # Add other fields if necessary from SD_Cust_Quotation_Master
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    enq_id = models.IntegerField(db_column='EnqId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Customer Quotation'
        verbose_name_plural = 'Customer Quotations'

    def __str__(self):
        return f"{self.quotation_no}[{self.sys_date}]"

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

# --- Main Models for Customer PO ---

class CustomerOrder(models.Model):
    poid = models.AutoField(db_column='POId', primary_key=True) # AutoField for auto-increment PK
    sys_date = models.CharField(db_column='SysDate', max_length=50)
    sys_time = models.CharField(db_column='SysTime', max_length=50)
    session_id = models.CharField(db_column='SessionId', max_length=255) # Maps to username
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    customer_id = models.CharField(db_column='CustomerId', max_length=50) # FK to Customer, but using CharField due to original schema
    enq_id = models.IntegerField(db_column='EnqId') # FK to SD_Cust_Enquiry_Master (not defined here)
    quotation_no = models.ForeignKey(CustomerQuotation, models.DO_NOTHING, db_column='QuotationNo', blank=True, null=True) # FK to CustomerQuotation
    po_no = models.CharField(db_column='PONo', max_length=255)
    po_date = models.DateField(db_column='PODate')
    po_received_date = models.DateField(db_column='POReceivedDate')
    vendor_code = models.CharField(db_column='VendorCode', max_length=255)
    payment_terms = models.CharField(db_column='PaymentTerms', max_length=255)
    pf = models.CharField(db_column='PF', max_length=255)
    vat = models.CharField(db_column='VAT', max_length=255)
    excise = models.CharField(db_column='Excise', max_length=255)
    octroi = models.CharField(db_column='Octroi', max_length=255)
    warrenty = models.CharField(db_column='Warrenty', max_length=255)
    insurance = models.CharField(db_column='Insurance', max_length=255)
    transport = models.CharField(db_column='Transport', max_length=255)
    note_no = models.CharField(db_column='NoteNo', max_length=255)
    registration_no = models.CharField(db_column='RegistrationNo', max_length=255)
    freight = models.CharField(db_column='Freight', max_length=255)
    remarks = models.CharField(db_column='Remarks', max_length=1000, blank=True, null=True)
    cst = models.CharField(db_column='CST', max_length=255)
    validity = models.CharField(db_column='Validity', max_length=255)
    other_charges = models.CharField(db_column='OtherCharges', max_length=255)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.IntegerField(db_column='FileSize', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # For storing binary file data

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer Order'
        verbose_name_plural = 'Customer Orders'

    def __str__(self):
        return self.po_no

    def get_customer_details(self):
        # Business logic to fetch customer name and address
        # This simulates the Page_Load customer details retrieval
        try:
            customer = Customer.objects.get(customer_id=self.customer_id, comp_id=self.comp_id)
            country = customer.regd_country.country_name if customer.regd_country else ''
            state = customer.regd_state.state_name if customer.regd_state else ''
            city = customer.regd_city.city_name if customer.regd_city else ''
            address = f"{customer.regd_address},<br>&nbsp;&nbsp;{city},{state},<br>&nbsp;&nbsp;{country}.<br>&nbsp;&nbsp;{customer.regd_pin_no}<br>"
            return {'name': customer.customer_name, 'address': address}
        except Customer.DoesNotExist:
            return {'name': 'N/A', 'address': 'N/A'}

    @classmethod
    def get_distinct_terms(cls, field_name):
        # Method to get distinct values for dropdown extenders
        return cls.objects.values_list(field_name, flat=True).filter(**{f"{field_name}__isnull": False}).distinct().order_by(field_name)

class CustomerOrderItem(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=255) # Maps to username
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    po = models.ForeignKey(CustomerOrder, models.DO_NOTHING, db_column='POId')
    item_desc = models.CharField(db_column='ItemDesc', max_length=1000)
    total_qty = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3)
    unit = models.ForeignKey(Unit, models.DO_NOTHING, db_column='Unit')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Customer Order Item'
        verbose_name_plural = 'Customer Order Items'

    def __str__(self):
        return self.item_desc

    def calculate_amount(self):
        # (TotalQty * (Rate - (Rate * Discount / 100)))
        return self.total_qty * (self.rate - (self.rate * self.discount / Decimal(100)))


class CustomerOrderItemTemp(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=255) # Maps to username
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    item_desc = models.CharField(db_column='ItemDesc', max_length=1000)
    total_qty = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3)
    unit = models.ForeignKey(Unit, models.DO_NOTHING, db_column='Unit')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details_Temp'
        verbose_name = 'Customer Order Item (Temporary)'
        verbose_name_plural = 'Customer Order Items (Temporary)'

    def __str__(self):
        return self.item_desc

    def calculate_amount(self):
        # (TotalQty * (Rate - (Rate * Discount / 100)))
        return self.total_qty * (self.rate - (self.rate * self.discount / Decimal(100)))

    @classmethod
    def transfer_temp_items_to_permanent(cls, user_session_id, comp_id, fin_year_id, po_instance):
        """
        Transfers all temporary items for a given session to the permanent CustomerOrderItem table
        and then clears the temporary table.
        """
        temp_items = cls.objects.filter(
            session_id=user_session_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        for item in temp_items:
            CustomerOrderItem.objects.create(
                session_id=item.session_id,
                comp_id=item.comp_id,
                fin_year_id=item.fin_year_id,
                po=po_instance,
                item_desc=item.item_desc,
                total_qty=item.total_qty,
                unit=item.unit,
                rate=item.rate,
                discount=item.discount
            )
        temp_items.delete() # Clear temporary items after transfer
        return True

```

#### 4.2 Forms (`sales/forms.py`)

**Task**: Define Django forms for user input and validation.

**Instructions**:
Two primary forms will be created: `CustomerOrderForm` for the main PO details (across tabs) and `CustomerOrderItemForm` for adding/editing individual goods items. Validation logic will be implemented as `clean_` methods or by using appropriate field types.

```python
from django import forms
from django.forms.widgets import NumberInput
from .models import (
    CustomerOrder, CustomerOrderItemTemp, Unit, CustomerQuotation,
    Customer, Country, State, City
)
from django.core.exceptions import ValidationError
from django.db.models import F # For selecting quotation_no and sys_date in dropdown

class CustomerOrderForm(forms.ModelForm):
    # Hidden fields for context, populated in view init
    customer_id_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    enq_id_hidden = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Quotation Number dropdown, custom choices in __init__
    quotation_no = forms.ModelChoiceField(
        queryset=CustomerQuotation.objects.all(),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    po_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'readonly': 'readonly'}),
        input_formats=['%d-%m-%Y'],
        help_text="Format: DD-MM-YYYY"
    )
    po_received_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'readonly': 'readonly'}),
        input_formats=['%d-%m-%Y'],
        help_text="Format: DD-MM-YYYY"
    )

    # File upload field not directly on ModelForm for BinaryField, handled separately in view
    attachment = forms.FileField(required=False, widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}))

    class Meta:
        model = CustomerOrder
        fields = [
            'po_no', 'po_date', 'po_received_date', 'vendor_code',
            'payment_terms', 'pf', 'vat', 'excise', 'octroi', 'warrenty',
            'insurance', 'transport', 'note_no', 'registration_no', 'freight',
            'cst', 'validity', 'other_charges', 'remarks', 'quotation_no'
        ]
        widgets = {
            'po_no': forms.TextInput(attrs={'class': 'box3'}),
            'vendor_code': forms.TextInput(attrs={'class': 'box3'}),
            'payment_terms': forms.TextInput(attrs={'class': 'box3'}),
            'pf': forms.TextInput(attrs={'class': 'box3'}),
            'vat': forms.TextInput(attrs={'class': 'box3'}),
            'excise': forms.TextInput(attrs={'class': 'box3'}),
            'octroi': forms.TextInput(attrs={'class': 'box3'}),
            'warrenty': forms.TextInput(attrs={'class': 'box3'}),
            'insurance': forms.TextInput(attrs={'class': 'box3'}),
            'transport': forms.TextInput(attrs={'class': 'box3'}),
            'note_no': forms.TextInput(attrs={'class': 'box3'}),
            'registration_no': forms.TextInput(attrs={'class': 'box3'}),
            'freight': forms.TextInput(attrs={'class': 'box3'}),
            'cst': forms.TextInput(attrs={'class': 'box3'}),
            'validity': forms.TextInput(attrs={'class': 'box3'}),
            'other_charges': forms.TextInput(attrs={'class': 'box3'}),
            'remarks': forms.Textarea(attrs={'class': 'box3', 'rows': 4}), # Height="100px"
        }
        labels = {
            'po_no': 'PO No',
            'po_date': 'PO Date',
            'po_received_date': 'PO Received Date',
            'vendor_code': 'Our Vendor Code',
            'payment_terms': 'Payment Terms',
            'pf': 'P & F',
            'vat': 'VAT',
            'excise': 'Excise / Service Tax',
            'octroi': 'Octroi',
            'warrenty': 'Warranty',
            'insurance': 'Insurance',
            'transport': 'Mode of Transport',
            'note_no': 'R.R./G.C. Note No.',
            'registration_no': 'If by motor vehicle,it\'s registr. no',
            'freight': 'Freight',
            'remarks': 'Remarks',
            'cst': 'CST',
            'validity': 'Validity',
            'other_charges': 'Other Charges',
            'quotation_no': 'Quotation No.'
        }
        field_classes = {
            'po_date': forms.DateField,
            'po_received_date': forms.DateField,
        }

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        enq_id = kwargs.pop('enq_id', None)
        super().__init__(*args, **kwargs)

        # Apply Tailwind classes to all fields by default
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect, forms.FileInput)):
                if 'class' in field.widget.attrs:
                    field.widget.attrs['class'] += ' block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
                else:
                    field.widget.attrs['class'] = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'

        # Filter quotation choices based on comp_id, fin_year_id, and enq_id
        if comp_id is not None and fin_year_id is not None and enq_id is not None:
            self.fields['quotation_no'].queryset = CustomerQuotation.objects.filter(
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id, # as per ASP.NET: FinYearId<='
                enq_id=enq_id
            ).annotate(
                # Recreate the 'QuatNo' string from ASP.NET for display
                quat_no_display=forms.CharField(
                    models.Func(
                        F('quotation_no'),
                        models.Value('['),
                        models.Func(
                            models.Func(F('sys_date'), models.Value('-'), function='REPLACE'),
                            models.Value('/'),
                            function='REPLACE',
                            template='%(expressions)s'
                        ),
                        models.Value(']'),
                        function='CONCAT'
                    )
                )
            ).order_by('quotation_no') # Order might need adjustment

            # Set data_text_field for ModelChoiceField if we need custom display
            self.fields['quotation_no'].label_from_instance = lambda obj: obj.quat_no_display if hasattr(obj, 'quat_no_display') else str(obj)

    def clean(self):
        cleaned_data = super().clean()
        po_date = cleaned_data.get('po_date')
        po_received_date = cleaned_data.get('po_received_date')

        # Custom date validation
        if po_date and po_received_date and po_received_date < po_date:
            self.add_error('po_received_date', 'PO Received Date cannot be before PO Date.')
        return cleaned_data


class CustomerOrderItemForm(forms.ModelForm):
    # Use DecimalField for quantities and rates, matching ASP.NET's decimal parsing
    total_qty = forms.DecimalField(
        max_digits=18, decimal_places=3,
        widget=NumberInput(attrs={'class': 'box3'}),
        min_value=Decimal('0.001') # Assuming positive quantities
    )
    rate = forms.DecimalField(
        max_digits=18, decimal_places=2,
        widget=NumberInput(attrs={'class': 'box3'}),
        min_value=Decimal('0.01') # Assuming positive rates
    )
    discount = forms.DecimalField(
        max_digits=18, decimal_places=2,
        widget=NumberInput(attrs={'class': 'box3'}),
        min_value=Decimal('0.00'), max_value=Decimal('100.00') # Discount percentage
    )

    class Meta:
        model = CustomerOrderItemTemp
        fields = ['item_desc', 'total_qty', 'unit', 'rate', 'discount']
        widgets = {
            'item_desc': forms.Textarea(attrs={'class': 'box3', 'rows': 4}), # Height="70px"
            'unit': forms.Select(attrs={'class': 'box3'}),
        }
        labels = {
            'item_desc': 'Description & Specification of goods',
            'total_qty': 'Total Qty of goods',
            'unit': 'Unit',
            'rate': 'Rate per unit',
            'discount': 'Discount (%)',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate unit dropdown dynamically
        self.fields['unit'].queryset = Unit.objects.all().order_by('symbol')
        self.fields['unit'].empty_label = "Select"

        # Apply Tailwind classes to all fields by default
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect, forms.FileInput)):
                if 'class' in field.widget.attrs:
                    field.widget.attrs['class'] += ' block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
                else:
                    field.widget.attrs['class'] = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'

    def clean_item_desc(self):
        item_desc = self.cleaned_data['item_desc']
        if not item_desc.strip():
            raise forms.ValidationError("Description and Specification of goods cannot be empty.")
        return item_desc

```

#### 4.3 Views (`sales/views.py`)

**Task**: Implement CRUD operations using CBVs.

**Instructions**:
The core `CustomerOrderCreateView` will orchestrate the multi-step form. Separate `HTMX-`specific views will handle the dynamic addition, updating, and deletion of temporary goods items, as well as refreshing the DataTables grid. Views will be thin (5-15 lines per method), pushing business logic to models.

```python
from django.views.generic import TemplateView, CreateView, UpdateView, DeleteView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.mixins import LoginRequiredMixin # Assume user authentication is required
from django.template.loader import render_to_string

from .models import (
    CustomerOrder, CustomerOrderItemTemp, Unit, Customer, CustomerQuotation,
    Country, State, City
)
from .forms import CustomerOrderForm, CustomerOrderItemForm
from decimal import Decimal

# Helper function to get common session/context variables
def get_user_context(request):
    # In a real ERP, these would come from the logged-in user's profile
    # or specific session management for company/financial year.
    # For this migration, we'll hardcode based on ASP.NET's session logic
    # and assume a user is logged in to get request.user.username.
    return {
        'session_id': request.user.username if request.user.is_authenticated else 'anonymous',
        'comp_id': request.session.get('compid', 1), # Default to 1 if not in session
        'fin_year_id': request.session.get('finyear', 1), # Default to 1 if not in session
    }

class CustomerOrderCreateUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'sales/customerorder/main_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context(self.request)
        customer_id = self.request.GET.get('CustomerId')
        enq_id = self.request.GET.get('EnqId')

        # Retrieve customer and enquiry details as in ASP.NET Page_Load
        customer = None
        customer_details = {'name': 'N/A', 'address': 'N/A'}
        try:
            if customer_id and user_context['comp_id']:
                customer_obj = Customer.objects.get(customer_id=customer_id, comp_id=user_context['comp_id'])
                customer_details = customer_obj.get_customer_details()
                customer = customer_obj # Pass the actual customer object for potential FK
        except Customer.DoesNotExist:
            pass # Handle case where customer is not found

        context['customer_name'] = customer_details['name']
        context['customer_address'] = customer_details['address']
        context['enquiry_no'] = enq_id # LblEnqNo.Text

        # Initialize the main form
        if 'main_form' not in context:
            context['main_form'] = CustomerOrderForm(
                initial={
                    'customer_id_hidden': customer_id,
                    'enq_id_hidden': enq_id
                },
                comp_id=user_context['comp_id'],
                fin_year_id=user_context['fin_year_id'],
                enq_id=int(enq_id) if enq_id else None # Pass enq_id to filter quotations
            )

        # Initialize the item form for "Goods Details" tab
        if 'item_form' not in context:
            context['item_form'] = CustomerOrderItemForm()

        # Get distinct values for terms & conditions dropdowns
        context['payment_terms_options'] = CustomerOrder.get_distinct_terms('payment_terms')
        context['pf_options'] = CustomerOrder.get_distinct_terms('pf')
        context['vat_options'] = CustomerOrder.get_distinct_terms('vat')
        context['excise_options'] = CustomerOrder.get_distinct_terms('excise')
        context['octroi_options'] = CustomerOrder.get_distinct_terms('octroi')
        context['warrenty_options'] = CustomerOrder.get_distinct_terms('warrenty')
        context['insurance_options'] = CustomerOrder.get_distinct_terms('insurance')
        context['transport_options'] = CustomerOrder.get_distinct_terms('transport')
        context['freight_options'] = CustomerOrder.get_distinct_terms('freight')
        # NoteNo, RegdNo, CST, Validity, OtherCharges are free text or assumed to be in options too.

        # Pass initial active tab (from session in ASP.NET)
        context['active_tab'] = self.request.session.get('active_tab', 0)
        return context

    def post(self, request, *args, **kwargs):
        # This POST handles the final submission of the Customer PO
        user_context = get_user_context(request)
        form = CustomerOrderForm(
            request.POST, request.FILES,
            comp_id=user_context['comp_id'],
            fin_year_id=user_context['fin_year_id'],
            enq_id=int(request.POST.get('enq_id_hidden')) if request.POST.get('enq_id_hidden') else None
        )

        if form.is_valid():
            with transaction.atomic():
                po_instance = form.save(commit=False)
                # Set system/session specific fields
                po_instance.sys_date = timezone.now().strftime('%d-%m-%Y')
                po_instance.sys_time = timezone.now().strftime('%H:%M:%S')
                po_instance.session_id = user_context['session_id']
                po_instance.comp_id = user_context['comp_id']
                po_instance.fin_year_id = user_context['fin_year_id']
                po_instance.customer_id = form.cleaned_data['customer_id_hidden']
                po_instance.enq_id = form.cleaned_data['enq_id_hidden']

                # Handle file upload
                uploaded_file = request.FILES.get('attachment')
                if uploaded_file:
                    po_instance.file_name = uploaded_file.name
                    po_instance.file_size = uploaded_file.size
                    po_instance.content_type = uploaded_file.content_type
                    po_instance.file_data = uploaded_file.read() # Read binary data

                po_instance.save() # Save the main PO

                # Transfer temporary items to permanent
                CustomerOrderItemTemp.transfer_temp_items_to_permanent(
                    user_context['session_id'],
                    user_context['comp_id'],
                    user_context['fin_year_id'],
                    po_instance
                )

                # Update POStatus in SD_Cust_Enquiry_Master (stubbed as it's not defined)
                # from .models import SdCustEnquiryMaster # Assuming this model exists
                # SdCustEnquiryMaster.objects.filter(enq_id=po_instance.enq_id, comp_id=user_context['comp_id'], fin_year_id=user_context['fin_year_id']).update(po_status=1)

            messages.success(request, 'Customer PO is generated successfully.')
            # Redirect to the list page, similar to "CustPO_New.aspx"
            return redirect(reverse_lazy('sales:customerorder_list_main'))
        else:
            # If form is not valid, re-render the template with errors
            # and ensure the correct tab is active (likely the last one for final submit)
            context = self.get_context_data(main_form=form)
            context['active_tab'] = 2 # Terms & Conditions tab
            messages.error(request, 'Please correct the errors in the form.')
            return self.render_to_response(context)

# HTMX endpoints for Goods Details (CustomerOrderItemTemp)
class CustomerOrderItemAddView(LoginRequiredMixin, CreateView):
    model = CustomerOrderItemTemp
    form_class = CustomerOrderItemForm
    http_method_names = ['post'] # Only allow POST for creation

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        instance = form.save(commit=False)
        instance.session_id = user_context['session_id']
        instance.comp_id = user_context['comp_id']
        instance.fin_year_id = user_context['fin_year_id']
        instance.save()
        messages.success(self.request, 'Goods item added successfully.')

        # Respond with HTMX trigger to refresh the item list
        return HttpResponse(
            status=204, # No content, tells HTMX that the request was successful
            headers={
                'HX-Trigger': 'refreshCustomerOrderItemList'
            }
        )

    def form_invalid(self, form):
        # Render the form again with errors as a partial, so HTMX can swap it in
        context = self.get_context_data(form=form)
        return render(self.request, 'sales/customerorder/_goods_item_form.html', context)


class CustomerOrderItemUpdateView(LoginRequiredMixin, UpdateView):
    model = CustomerOrderItemTemp
    form_class = CustomerOrderItemForm
    http_method_names = ['get', 'post'] # Allow GET to load form, POST to submit

    def get_object(self, queryset=None):
        # Ensure only current user's temp items can be edited
        user_context = get_user_context(self.request)
        return get_object_or_404(
            CustomerOrderItemTemp,
            pk=self.kwargs['pk'],
            session_id=user_context['session_id'],
            comp_id=user_context['comp_id'],
            fin_year_id=user_context['fin_year_id']
        )

    def form_valid(self, form):
        form.save()
        messages.success(self.request, 'Goods item updated successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshCustomerOrderItemList'
            }
        )

    def form_invalid(self, form):
        context = self.get_context_data(form=form)
        return render(self.request, 'sales/customerorder/_goods_item_form.html', context)

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        context = self.get_context_data(form=form)
        return render(request, 'sales/customerorder/_goods_item_form.html', context)


class CustomerOrderItemDeleteView(LoginRequiredMixin, DeleteView):
    model = CustomerOrderItemTemp
    http_method_names = ['get', 'post'] # GET for confirmation, POST for deletion

    def get_object(self, queryset=None):
        # Ensure only current user's temp items can be deleted
        user_context = get_user_context(self.request)
        return get_object_or_404(
            CustomerOrderItemTemp,
            pk=self.kwargs['pk'],
            session_id=user_context['session_id'],
            comp_id=user_context['comp_id'],
            fin_year_id=user_context['fin_year_id']
        )

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return render(request, 'sales/customerorder/_confirm_delete_item.html', context)

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Goods item deleted successfully.')
        response = super().delete(request, *args, **kwargs)
        # Instead of redirect, send HTMX trigger
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshCustomerOrderItemList'
            }
        )

class CustomerOrderItemListPartialView(LoginRequiredMixin, ListView):
    model = CustomerOrderItemTemp
    template_name = 'sales/customerorder/_goods_item_list_table.html'
    context_object_name = 'items'

    def get_queryset(self):
        user_context = get_user_context(self.request)
        # Filter items for the current user's session
        queryset = super().get_queryset().filter(
            session_id=user_context['session_id'],
            comp_id=user_context['comp_id'],
            fin_year_id=user_context['fin_year_id']
        ).select_related('unit').order_by('-id') # Order by Id Desc as in FillGrid()
        return queryset

# Placeholder for the actual Customer PO list view (used for redirect after creation)
class CustomerOrderListView(LoginRequiredMixin, TemplateView):
    template_name = 'sales/customerorder/list.html' # A simple placeholder template
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        messages.info(self.request, "This is a placeholder for the Customer PO listing page.")
        return context

```

#### 4.4 Templates (`sales/templates/sales/customerorder/`)

**Task**: Create templates for each view and partials for HTMX interactions.

**Instructions**:
Templates will extend `core/base.html`. DataTables will be used for the goods item list. HTMX attributes will drive dynamic interactions, and Alpine.js will manage UI state like active tabs and modal visibility.

**`main_form.html` (The main page with tabs)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: {{ active_tab }} }">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-4">Customer PO - New</h2>

        <!-- Global Messages -->
        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" @click.prevent="activeTab = 0"
                   :class="{ 'border-blue-500 text-blue-600': activeTab === 0, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 0 }"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Customer Details
                </a>
                <a href="#" @click.prevent="activeTab = 1"
                   :class="{ 'border-blue-500 text-blue-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-700': activeTab !== 1 }"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Goods Details
                </a>
                <a href="#" @click.prevent="activeTab = 2"
                   :class="{ 'border-blue-500 text-blue-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-700': activeTab !== 2 }"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Terms &amp; Conditions
                </a>
            </nav>
        </div>

        <!-- Tab Content -->
        <form method="post" enctype="multipart/form-data" action="{% url 'sales:customerorder_new' customer_id=request.GET.CustomerId enq_id=request.GET.EnqId %}">
            {% csrf_token %}
            {{ main_form.customer_id_hidden }}
            {{ main_form.enq_id_hidden }}

            <!-- Tab 1: Customer Details -->
            <div x-show="activeTab === 0" class="pt-6 space-y-4">
                {% include 'sales/customerorder/_customer_details_tab.html' %}
                <div class="flex justify-end mt-6 space-x-4">
                    <button type="button" @click="activeTab = 1" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Next
                    </button>
                    <button type="button" @click="window.location.href='{% url 'sales:customerorder_list_main' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </button>
                </div>
            </div>

            <!-- Tab 2: Goods Details -->
            <div x-show="activeTab === 1" class="pt-6 space-y-4">
                {% include 'sales/customerorder/_goods_details_tab.html' %}
                <div class="flex justify-end mt-6 space-x-4">
                    <button type="button" @click="activeTab = 2" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Next
                    </button>
                    <button type="button" @click="window.location.href='{% url 'sales:customerorder_list_main' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </button>
                </div>
            </div>

            <!-- Tab 3: Terms & Conditions -->
            <div x-show="activeTab === 2" class="pt-6 space-y-4">
                {% include 'sales/customerorder/_terms_conditions_tab.html' %}
                <div class="flex justify-end mt-6 space-x-4">
                    <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Submit
                    </button>
                    <button type="button" @click="window.location.href='{% url 'sales:customerorder_list_main' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Cancel
                    </button>
                </div>
            </div>
        </form>

    </div>
</div>

<!-- Modal for item CRUD -->
<div id="itemModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
     _="on click if event.target.id == 'itemModal' remove .is-active from me">
    <div id="itemModalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr(".datepicker", {
            dateFormat: "d-m-Y",
            allowInput: false // prevent manual input if readonly is set
        });

        // Initialize DataTable when _goods_item_list_table.html is loaded via HTMX
        // This observer will re-initialize DataTables if the table is swapped out
        new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.id === 'customerOrderItemTable') {
                            $(node).DataTable({
                                "pageLength": 10,
                                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                                "pagingType": "simple_numbers", // or "full_numbers"
                                "dom": 'lfrtip', // Layout: Length changing, Filtering, Table, Information, Pagination
                            });
                        }
                    });
                }
            });
        }).observe(document.getElementById('goodsItemsTableContainer'), { childList: true, subtree: true });
    });

    // Custom event listener for refreshing item list
    document.body.addEventListener('refreshCustomerOrderItemList', function(evt) {
        // Trigger HTMX to reload the table partial
        const itemTableContainer = document.getElementById('goodsItemsTableContainer');
        if (itemTableContainer) {
            htmx.trigger(itemTableContainer, 'load');
        }
        // Close modal after successful CRUD operation
        const itemModal = document.getElementById('itemModal');
        if (itemModal) {
            itemModal.classList.remove('is-active');
        }
    });

</script>
{% endblock %}
```

**`_customer_details_tab.html` (Partial for Tab 1)**

```html
<div class="fontcss p-4 border border-gray-200 rounded-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label class="block text-sm font-medium text-gray-700">Name of Customer:</label>
            <p class="mt-1 text-sm text-gray-900">{{ customer_name }}</p>
        </div>
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Regd. Office Address:</label>
            <p class="mt-1 text-sm text-gray-900">{{ customer_address|safe }}</p>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700">Enquiry No:</label>
            <p class="mt-1 text-sm text-gray-900">{{ enquiry_no }}</p>
        </div>
        <div>
            <label for="{{ main_form.quotation_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Quotation No.</label>
            {{ main_form.quotation_no }}
            {% if main_form.quotation_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ main_form.quotation_no.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ main_form.po_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PO No</label>
            {{ main_form.po_no }}
            {% if main_form.po_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ main_form.po_no.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ main_form.po_date.id_for_label }}" class="block text-sm font-medium text-gray-700">PO Date</label>
            {{ main_form.po_date }}
            {% if main_form.po_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ main_form.po_date.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ main_form.po_received_date.id_for_label }}" class="block text-sm font-medium text-gray-700">PO Received Date</label>
            {{ main_form.po_received_date }}
            {% if main_form.po_received_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ main_form.po_received_date.errors }}</p>
            {% endif %}
        </div>
        <div>
            <label for="{{ main_form.vendor_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Our Vendor Code</label>
            {{ main_form.vendor_code }}
            {% if main_form.vendor_code.errors %}
                <p class="text-red-500 text-xs mt-1">{{ main_form.vendor_code.errors }}</p>
            {% endif %}
        </div>
    </div>
</div>
```

**`_goods_details_tab.html` (Partial for Tab 2)**

```html
<div class="fontcss p-4 border border-gray-200 rounded-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Goods Details</h3>

    <!-- Form for adding/editing a single item -->
    <div id="goodsItemFormContainer"
         hx-target="this"
         hx-swap="outerHTML">
        {% include 'sales/customerorder/_goods_item_form.html' with form=item_form action_url=request.path item_id=None %}
    </div>

    <hr class="my-6 border-gray-200">

    <!-- List of items (DataTables) -->
    <div id="goodsItemsTableContainer"
         hx-trigger="load, refreshCustomerOrderItemList from:body"
         hx-get="{% url 'sales:customerorder_item_list_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading goods items...</p>
        </div>
    </div>
</div>
```

**`_goods_item_form.html` (Partial for adding/editing a single item)**

```html
<div class="p-4" id="itemForm">
    <h4 class="text-md font-medium text-gray-800 mb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Item</h4>
    <form hx-post="{% if form.instance.pk %}{% url 'sales:customerorder_item_edit' pk=form.instance.pk %}{% else %}{% url 'sales:customerorder_item_add' %}{% endif %}"
          hx-swap="none"
          hx-trigger="submit">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="md:col-span-2">
                <label for="{{ form.item_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_desc.label }}
                </label>
                {{ form.item_desc }}
                {% if form.item_desc.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.item_desc.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.total_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.total_qty.label }}
                </label>
                {{ form.total_qty }}
                {% if form.total_qty.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.total_qty.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.rate.label }}
                </label>
                {{ form.rate }}
                {% if form.rate.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.discount.label }}
                </label>
                {{ form.discount }}
                {% if form.discount.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.unit.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.unit.label }}
                </label>
                {{ form.unit }}
                {% if form.unit.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.unit.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            {% if form.instance.pk %}
                <button
                    type="button"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    _="on click htmx.ajax('GET', '{% url 'sales:customerorder_item_form_clear' %}', {target: '#goodsItemFormContainer', swap: 'outerHTML'}) and remove .is-active from #itemModal">
                    Cancel
                </button>
            {% else %}
                <button
                    type="button"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    _="on click this.form.reset()">
                    Clear Form
                </button>
            {% endif %}

            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Submit Item
            </button>
        </div>
    </form>
</div>
```

**`_goods_item_list_table.html` (Partial for DataTables grid)**

```html
<table id="customerOrderItemTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Discount (%)</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.item_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.unit.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.total_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.rate|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.discount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.calculate_amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 flex justify-center space-x-2">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'sales:customerorder_item_edit' pk=item.pk %}"
                    hx-target="#itemModalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #itemModal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'sales:customerorder_item_delete' pk=item.pk %}"
                    hx-target="#itemModalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #itemModal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 border-b border-gray-200 text-center text-gray-500">
                No goods items added yet.
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTables initialization handled in main_form.html via MutationObserver -->
```

**`_terms_conditions_tab.html` (Partial for Tab 3)**

```html
<div class="fontcss p-4 border border-gray-200 rounded-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Terms & Conditions</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ main_form.payment_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Payment Terms</label>
            {{ main_form.payment_terms }}
            {% if main_form.payment_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.payment_terms.errors }}</p>{% endif %}
            <datalist id="payment_terms_options">
                {% for option in payment_terms_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">P & F</label>
            {{ main_form.pf }}
            {% if main_form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.pf.errors }}</p>{% endif %}
            <datalist id="pf_options">
                {% for option in pf_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.vat.id_for_label }}" class="block text-sm font-medium text-gray-700">VAT</label>
            {{ main_form.vat }}
            {% if main_form.vat.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.vat.errors }}</p>{% endif %}
            <datalist id="vat_options">
                {% for option in vat_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.excise.id_for_label }}" class="block text-sm font-medium text-gray-700">Excise / Service Tax</label>
            {{ main_form.excise }}
            {% if main_form.excise.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.excise.errors }}</p>{% endif %}
            <datalist id="excise_options">
                {% for option in excise_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.octroi.id_for_label }}" class="block text-sm font-medium text-gray-700">Octroi</label>
            {{ main_form.octroi }}
            {% if main_form.octroi.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.octroi.errors }}</p>{% endif %}
            <datalist id="octroi_options">
                {% for option in octroi_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.warrenty.id_for_label }}" class="block text-sm font-medium text-gray-700">Warranty</label>
            {{ main_form.warrenty }}
            {% if main_form.warrenty.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.warrenty.errors }}</p>{% endif %}
            <datalist id="warrenty_options">
                {% for option in warrenty_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance</label>
            {{ main_form.insurance }}
            {% if main_form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.insurance.errors }}</p>{% endif %}
            <datalist id="insurance_options">
                {% for option in insurance_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.transport.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Transport</label>
            {{ main_form.transport }}
            {% if main_form.transport.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.transport.errors }}</p>{% endif %}
            <datalist id="transport_options">
                {% for option in transport_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.note_no.id_for_label }}" class="block text-sm font-medium text-gray-700">R.R./G.C. Note No.</label>
            {{ main_form.note_no }}
            {% if main_form.note_no.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.note_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ main_form.registration_no.id_for_label }}" class="block text-sm font-medium text-gray-700">If by motor vehicle, it's registr. no</label>
            {{ main_form.registration_no }}
            {% if main_form.registration_no.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.registration_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ main_form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700">Freight</label>
            {{ main_form.freight }}
            {% if main_form.freight.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.freight.errors }}</p>{% endif %}
            <datalist id="freight_options">
                {% for option in freight_options %}<option value="{{ option }}">{{ option }}</option>{% endfor %}
            </datalist>
        </div>
        <div>
            <label for="{{ main_form.cst.id_for_label }}" class="block text-sm font-medium text-gray-700">CST</label>
            {{ main_form.cst }}
            {% if main_form.cst.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.cst.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ main_form.validity.id_for_label }}" class="block text-sm font-medium text-gray-700">Validity</label>
            {{ main_form.validity }}
            {% if main_form.validity.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.validity.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ main_form.other_charges.id_for_label }}" class="block text-sm font-medium text-gray-700">Other Charges</label>
            {{ main_form.other_charges }}
            {% if main_form.other_charges.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.other_charges.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ main_form.attachment.id_for_label }}" class="block text-sm font-medium text-gray-700">Attachment</label>
            {{ main_form.attachment }}
            {% if main_form.attachment.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.attachment.errors }}</p>{% endif %}
        </div>
        <div class="md:col-span-2">
            <label for="{{ main_form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
            {{ main_form.remarks }}
            {% if main_form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.remarks.errors }}</p>{% endif %}
        </div>
    </div>
</div>
```

**`_confirm_delete_item.html` (Partial for item deletion confirmation)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this item: <strong>{{ object.item_desc }}</strong>?</p>
    <div class="flex justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #itemModal">
            Cancel
        </button>
        <button
            hx-post="{% url 'sales:customerorder_item_delete' pk=object.pk %}"
            hx-swap="none"
            hx-trigger="click"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

**`list.html` (Placeholder for main PO list view)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Customer PO List</h2>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <p class="text-gray-700">This page would display a list of all Customer Purchase Orders.</p>
    <p class="mt-4">
        <a href="{% url 'sales:customerorder_new' customer_id='TESTCUST001' enq_id='12345' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Create New PO (Example)
        </a>
    </p>
</div>
{% endblock %}
```

#### 4.5 URLs (`sales/urls.py`)

**Task**: Define URL patterns for the views.

**Instructions**:
Create paths for the main form, and all HTMX endpoints for the goods items (add, edit, delete, list partial).

```python
from django.urls import path
from .views import (
    CustomerOrderCreateUpdateView,
    CustomerOrderItemAddView,
    CustomerOrderItemUpdateView,
    CustomerOrderItemDeleteView,
    CustomerOrderItemListPartialView,
    CustomerOrderListView
)
from django.http import HttpResponse # For the clear form HTMX endpoint
from .forms import CustomerOrderItemForm

app_name = 'sales'

urlpatterns = [
    # Main Customer PO creation page (multi-tab)
    path('customer-po/new/', CustomerOrderCreateUpdateView.as_view(), name='customerorder_new'),
    # Use specific query params to simulate original ASP.NET navigation
    path('customer-po/new/<str:customer_id>/<int:enq_id>/', CustomerOrderCreateUpdateView.as_view(), name='customerorder_new'),

    # HTMX endpoints for temporary goods items
    path('customer-po/items/add/', CustomerOrderItemAddView.as_view(), name='customerorder_item_add'),
    path('customer-po/items/edit/<int:pk>/', CustomerOrderItemUpdateView.as_view(), name='customerorder_item_edit'),
    path('customer-po/items/delete/<int:pk>/', CustomerOrderItemDeleteView.as_view(), name='customerorder_item_delete'),
    path('customer-po/items/list/', CustomerOrderItemListPartialView.as_view(), name='customerorder_item_list_partial'),
    # HTMX endpoint to clear the item form (return an empty form)
    path('customer-po/items/form/clear/', lambda request: HttpResponse(render_to_string('sales/customerorder/_goods_item_form.html', {'form': CustomerOrderItemForm()})), name='customerorder_item_form_clear'),

    # Placeholder for the main Customer PO list page (redirect target)
    path('customer-po/list/', CustomerOrderListView.as_view(), name='customerorder_list_main'),
]
```

#### 4.6 Tests (`sales/tests.py`)

**Task**: Write comprehensive tests for the models and views.

**Instructions**:
Include unit tests for model methods and integration tests for all views, including HTMX interactions. Ensure full coverage for critical logic.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.core.files.uploadedfile import SimpleUploadedFile
from datetime import date
from decimal import Decimal

from .models import (
    CustomerOrder, CustomerOrderItem, CustomerOrderItemTemp, Unit, Customer,
    Country, State, City, CustomerQuotation
)
from .forms import CustomerOrderForm, CustomerOrderItemForm

# --- Mocking User and Session (For testing context) ---
# In a real application, you'd use Django's auth system to log in a user.
# For testing the context, we'll simulate the required session data.
class MockUser:
    is_authenticated = True
    username = 'testuser'

# --- Model Tests ---

class CustomerOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for FKs
        cls.country = Country.objects.create(cid=1, country_name='Testland')
        cls.state = State.objects.create(sid=1, state_name='Testate')
        cls.city = City.objects.create(city_id=1, city_name='Testville')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            comp_id=1,
            customer_name='Test Customer',
            regd_address='123 Test St',
            regd_country=cls.country,
            regd_state=cls.state,
            regd_city=cls.city,
            regd_pin_no='123456'
        )
        cls.quotation = CustomerQuotation.objects.create(
            id=101, quotation_no='Q-001', sys_date='10-01-2023',
            comp_id=1, fin_year_id=1, enq_id=12345
        )
        cls.unit = Unit.objects.create(id=1, symbol='KG')

        # Create a test CustomerOrder instance
        cls.po = CustomerOrder.objects.create(
            sys_date='01-01-2023', sys_time='10:00:00', session_id='testuser',
            comp_id=1, fin_year_id=1, customer_id='CUST001', enq_id=12345,
            quotation_no=cls.quotation, po_no='PO-001-2023', po_date=date(2023, 1, 5),
            po_received_date=date(2023, 1, 6), vendor_code='VEND001',
            payment_terms='Net 30', pf='10%', vat='5%', excise='0%', octroi='0%',
            warrenty='1 year', insurance='Covered', transport='Road', note_no='RR123',
            registration_no='ABC-123', freight='Paid', remarks='Test PO', cst='2%',
            validity='30 days', other_charges='N/A'
        )
        # Create a test CustomerOrderItemTemp instance
        cls.temp_item = CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Test Item 1', total_qty=Decimal('10.000'),
            unit=cls.unit, rate=Decimal('50.00'), discount=Decimal('5.00')
        )

    def test_customer_order_creation(self):
        po = CustomerOrder.objects.get(poid=self.po.poid)
        self.assertEqual(po.po_no, 'PO-001-2023')
        self.assertEqual(po.customer_id, 'CUST001')
        self.assertEqual(po.quotation_no, self.quotation)

    def test_customer_order_verbose_name(self):
        self.assertEqual(CustomerOrder._meta.verbose_name, 'Customer Order')
        self.assertEqual(CustomerOrder._meta.verbose_name_plural, 'Customer Orders')

    def test_get_customer_details_method(self):
        details = self.po.get_customer_details()
        self.assertEqual(details['name'], 'Test Customer')
        self.assertIn('123 Test St', details['address'])

    def test_get_distinct_terms_method(self):
        # Create another PO to ensure distinct values
        CustomerOrder.objects.create(
            sys_date='01-01-2023', sys_time='10:00:00', session_id='testuser2',
            comp_id=1, fin_year_id=1, customer_id='CUST002', enq_id=12346,
            po_no='PO-002-2023', po_date=date(2023, 1, 5),
            po_received_date=date(2023, 1, 6), vendor_code='VEND002',
            payment_terms='Net 60', pf='12%', vat='5%', excise='0%', octroi='0%',
            warrenty='1 year', insurance='Not Covered', transport='Air', note_no='RR124',
            registration_no='DEF-456', freight='FOB', remarks='Another PO', cst='2%',
            validity='60 days', other_charges='Packing Fee'
        )
        payment_terms = CustomerOrder.get_distinct_terms('payment_terms')
        self.assertIn('Net 30', payment_terms)
        self.assertIn('Net 60', payment_terms)
        self.assertEqual(len(payment_terms), 2) # Should be 2 distinct terms

class CustomerOrderItemTempModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit = Unit.objects.create(id=1, symbol='KG')
        cls.temp_item = CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Laptop', total_qty=Decimal('2.000'),
            unit=cls.unit, rate=Decimal('1000.00'), discount=Decimal('10.00')
        )

    def test_item_creation(self):
        item = CustomerOrderItemTemp.objects.get(id=self.temp_item.id)
        self.assertEqual(item.item_desc, 'Laptop')
        self.assertEqual(item.total_qty, Decimal('2.000'))
        self.assertEqual(item.rate, Decimal('1000.00'))

    def test_calculate_amount(self):
        item = CustomerOrderItemTemp.objects.get(id=self.temp_item.id)
        # Amount = 2 * (1000 - (1000 * 10 / 100)) = 2 * (1000 - 100) = 2 * 900 = 1800
        self.assertEqual(item.calculate_amount(), Decimal('1800.00'))

    def test_transfer_temp_items_to_permanent(self):
        # Create a mock PO for transfer
        country = Country.objects.create(cid=2, country_name='Anotherland')
        state = State.objects.create(sid=2, state_name='Anotherstate')
        city = City.objects.create(city_id=2, city_name='Anothercity')
        customer = Customer.objects.create(
            customer_id='CUST002',
            comp_id=1,
            customer_name='Another Customer'
        )
        quotation = CustomerQuotation.objects.create(
            id=102, quotation_no='Q-002', sys_date='10-01-2023',
            comp_id=1, fin_year_id=1, enq_id=12345
        )
        po_instance = CustomerOrder.objects.create(
            sys_date='01-01-2023', sys_time='10:00:00', session_id='testuser',
            comp_id=1, fin_year_id=1, customer_id='CUST002', enq_id=12345,
            quotation_no=quotation, po_no='PO-X-2023', po_date=date(2023, 1, 5),
            po_received_date=date(2023, 1, 6), vendor_code='VENDX',
            payment_terms='Net 30', pf='10%', vat='5%', excise='0%', octroi='0%',
            warrenty='1 year', insurance='Covered', transport='Road', note_no='RRX',
            registration_no='ABC-X', freight='Paid', remarks='Test PO', cst='2%',
            validity='30 days', other_charges='N/A'
        )
        
        # Ensure there's a temporary item
        CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Monitor', total_qty=Decimal('1.000'),
            unit=self.unit, rate=Decimal('200.00'), discount=Decimal('0.00')
        )

        initial_temp_count = CustomerOrderItemTemp.objects.count()
        initial_permanent_count = CustomerOrderItem.objects.count()

        CustomerOrderItemTemp.transfer_temp_items_to_permanent(
            'testuser', 1, 1, po_instance
        )

        # Assert temporary items are moved and then deleted
        self.assertEqual(CustomerOrderItemTemp.objects.count(), 0)
        self.assertEqual(CustomerOrderItem.objects.count(), initial_permanent_count + initial_temp_count)
        self.assertTrue(CustomerOrderItem.objects.filter(item_desc='Laptop', po=po_instance).exists())
        self.assertTrue(CustomerOrderItem.objects.filter(item_desc='Monitor', po=po_instance).exists())


# --- View Tests ---

class CustomerOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.country = Country.objects.create(cid=1, country_name='Testland')
        cls.state = State.objects.create(sid=1, state_name='Testate')
        cls.city = City.objects.create(city_id=1, city_name='Testville')
        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            comp_id=1,
            customer_name='Test Customer',
            regd_address='123 Test St',
            regd_country=cls.country,
            regd_state=cls.state,
            regd_city=cls.city,
            regd_pin_no='123456'
        )
        cls.quotation = CustomerQuotation.objects.create(
            id=101, quotation_no='Q-001', sys_date='10-01-2023',
            comp_id=1, fin_year_id=1, enq_id=12345
        )
        cls.unit = Unit.objects.create(id=1, symbol='KG')

    def setUp(self):
        self.client = Client()
        self.client.force_login(MockUser()) # Simulate logged-in user
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1
        # Clear temporary items for each test
        CustomerOrderItemTemp.objects.all().delete()

    def test_customer_order_create_view_get(self):
        url = reverse('sales:customerorder_new', kwargs={'customer_id': 'CUST001', 'enq_id': 12345})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerorder/main_form.html')
        self.assertContains(response, 'Test Customer')
        self.assertContains(response, '12345')
        self.assertTrue('main_form' in response.context)
        self.assertTrue('item_form' in response.context)
        self.assertContains(response, '<table id="customerOrderItemTable"') # Check for item table container

    def test_customer_order_create_view_post_success(self):
        initial_po_count = CustomerOrder.objects.count()
        # Add a temp item to be transferred
        CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Temp Item', total_qty=Decimal('1.000'),
            unit=self.unit, rate=Decimal('100.00'), discount=Decimal('0.00')
        )
        initial_temp_item_count = CustomerOrderItemTemp.objects.count()

        data = {
            'customer_id_hidden': 'CUST001',
            'enq_id_hidden': 12345,
            'quotation_no': self.quotation.id,
            'po_no': 'NEW-PO-001',
            'po_date': '01-02-2023',
            'po_received_date': '02-02-2023',
            'vendor_code': 'NEWVEND',
            'payment_terms': 'Net 30',
            'pf': '5%', 'vat': '5%', 'excise': '0%', 'octroi': '0%',
            'warrenty': '1 year', 'insurance': 'None', 'transport': 'Sea',
            'note_no': 'SEA-123', 'registration_no': 'SHIP-ABC',
            'freight': 'Collect', 'remarks': 'New PO test', 'cst': '0%',
            'validity': '90 days', 'other_charges': 'Handling',
            'attachment': SimpleUploadedFile("test_file.txt", b"file_content", content_type="text/plain")
        }
        url = reverse('sales:customerorder_new', kwargs={'customer_id': 'CUST001', 'enq_id': 12345})
        response = self.client.post(url, data, follow=True) # follow=True to check redirect

        self.assertEqual(response.status_code, 200) # Should be 200 after redirect
        self.assertEqual(CustomerOrder.objects.count(), initial_po_count + 1)
        new_po = CustomerOrder.objects.get(po_no='NEW-PO-001')
        self.assertEqual(new_po.file_name, 'test_file.txt')
        self.assertEqual(new_po.file_data, b'file_content')
        self.assertEqual(new_po.quotation_no.id, self.quotation.id)

        # Verify temp items were moved and cleared
        self.assertEqual(CustomerOrderItemTemp.objects.count(), 0)
        self.assertEqual(CustomerOrderItem.objects.filter(po=new_po).count(), initial_temp_item_count)
        self.assertTrue(CustomerOrderItem.objects.filter(item_desc='Temp Item', po=new_po).exists())

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer PO is generated successfully.')
        self.assertRedirects(response, reverse('sales:customerorder_list_main'))

    def test_customer_order_create_view_post_invalid(self):
        initial_po_count = CustomerOrder.objects.count()
        data = {
            'customer_id_hidden': 'CUST001',
            'enq_id_hidden': 12345,
            'po_no': '', # Invalid: Required
            'po_date': 'invalid-date', # Invalid date
            'po_received_date': '01-01-2023',
            'vendor_code': '', # Invalid: Required
            # Missing other required fields
        }
        url = reverse('sales:customerorder_new', kwargs={'customer_id': 'CUST001', 'enq_id': 12345})
        response = self.client.post(url, data) # No follow, stay on the same page

        self.assertEqual(response.status_code, 200)
        self.assertEqual(CustomerOrder.objects.count(), initial_po_count)
        self.assertContains(response, 'Please correct the errors in the form.')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid date.')
        self.assertIn('main_form', response.context)
        self.assertFalse(response.context['main_form'].is_valid())
        self.assertEqual(response.context['active_tab'], 2) # Stays on last tab for errors


class CustomerOrderItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit = Unit.objects.create(id=1, symbol='NOS')

    def setUp(self):
        self.client = Client()
        self.client.force_login(MockUser())
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1
        CustomerOrderItemTemp.objects.all().delete() # Clear temp items for each test

    def test_add_item_view_post_success(self):
        initial_count = CustomerOrderItemTemp.objects.count()
        data = {
            'item_desc': 'Test Item for PO',
            'total_qty': '5.500',
            'unit': self.unit.id,
            'rate': '12.34',
            'discount': '1.50'
        }
        response = self.client.post(reverse('sales:customerorder_item_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerOrderItemList')
        self.assertEqual(CustomerOrderItemTemp.objects.count(), initial_count + 1)
        self.assertTrue(CustomerOrderItemTemp.objects.filter(item_desc='Test Item for PO', session_id='testuser').exists())

    def test_add_item_view_post_invalid(self):
        initial_count = CustomerOrderItemTemp.objects.count()
        data = {
            'item_desc': '', # Invalid
            'total_qty': 'abc', # Invalid
            'unit': self.unit.id,
            'rate': 'invalid', # Invalid
            'discount': '101.00' # Invalid discount
        }
        response = self.client.post(reverse('sales:customerorder_item_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'sales/customerorder/_goods_item_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a number.')
        self.assertContains(response, 'Ensure this value is less than or equal to 100.00.')
        self.assertEqual(CustomerOrderItemTemp.objects.count(), initial_count)

    def test_item_list_partial_view(self):
        CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='List Item 1', total_qty=Decimal('1'), unit=self.unit,
            rate=Decimal('10'), discount=Decimal('0')
        )
        response = self.client.get(reverse('sales:customerorder_item_list_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerorder/_goods_item_list_table.html')
        self.assertContains(response, 'List Item 1')
        self.assertContains(response, 'NOS') # Check unit symbol

    def test_update_item_view_get(self):
        item = CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Old Item', total_qty=Decimal('1'), unit=self.unit,
            rate=Decimal('10'), discount=Decimal('0')
        )
        response = self.client.get(reverse('sales:customerorder_item_edit', args=[item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerorder/_goods_item_form.html')
        self.assertContains(response, 'Old Item') # Check if form pre-filled

    def test_update_item_view_post_success(self):
        item = CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Item to Update', total_qty=Decimal('1'), unit=self.unit,
            rate=Decimal('10'), discount=Decimal('0')
        )
        data = {
            'item_desc': 'Updated Item',
            'total_qty': '2.000',
            'unit': self.unit.id,
            'rate': '20.00',
            'discount': '10.00'
        }
        response = self.client.post(reverse('sales:customerorder_item_edit', args=[item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerOrderItemList')
        item.refresh_from_db()
        self.assertEqual(item.item_desc, 'Updated Item')
        self.assertEqual(item.total_qty, Decimal('2.000'))

    def test_delete_item_view_get(self):
        item = CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Item to Delete', total_qty=Decimal('1'), unit=self.unit,
            rate=Decimal('10'), discount=Decimal('0')
        )
        response = self.client.get(reverse('sales:customerorder_item_delete', args=[item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerorder/_confirm_delete_item.html')
        self.assertContains(response, 'Item to Delete')

    def test_delete_item_view_post_success(self):
        item = CustomerOrderItemTemp.objects.create(
            session_id='testuser', comp_id=1, fin_year_id=1,
            item_desc='Item to Delete Permanent', total_qty=Decimal('1'), unit=self.unit,
            rate=Decimal('10'), discount=Decimal('0')
        )
        initial_count = CustomerOrderItemTemp.objects.count()
        response = self.client.post(reverse('sales:customerorder_item_delete', args=[item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerOrderItemList')
        self.assertEqual(CustomerOrderItemTemp.objects.count(), initial_count - 1)
        self.assertFalse(CustomerOrderItemTemp.objects.filter(pk=item.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions**:
The generated templates and views already integrate HTMX for dynamic content updates (like the DataTables list refresh) and form submissions for item CRUD. Alpine.js is used for managing the active tab state and modal visibility without relying on complex JavaScript frameworks.

*   **Tab Management**: `main_form.html` uses Alpine.js (`x-data="{ activeTab: 0 }"` and `@click.prevent="activeTab = N"`) to control the display of each tab's content (`x-show="activeTab === N"`).
*   **Item CRUD Modals**: A single modal (`#itemModal`) is managed by Alpine.js. Buttons for "Edit" and "Delete" items use HTMX `hx-get` to load the respective forms (`_goods_item_form.html` or `_confirm_delete_item.html`) into `#itemModalContent`, triggering Alpine.js to show the modal (`_="on click add .is-active to #itemModal"`).
*   **Item Form Submission**: The `_goods_item_form.html` uses `hx-post` to submit data. On successful submission (204 No Content response from Django), the `HX-Trigger: refreshCustomerOrderItemList` header is sent.
*   **DataTables Refresh**: The `goodsItemsTableContainer` in `_goods_details_tab.html` is configured with `hx-trigger="load, refreshCustomerOrderItemList from:body"` and `hx-get="{% url 'sales:customerorder_item_list_partial' %}"`. This ensures the table loads on page load and refreshes whenever `refreshCustomerOrderItemList` custom event is triggered (e.g., after an item is added, updated, or deleted). The DataTables initialization is handled by a JavaScript `MutationObserver` in `main_form.html` to ensure it re-initializes correctly when the table partial is swapped.
*   **Datepickers**: `flatpickr` is used for date input fields, providing a modern and user-friendly experience, replacing the `CalendarExtender`.
*   **DRY Template Inheritance**: All content templates extend `core/base.html`, which is assumed to contain common HTML structure, CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables.

### Final Notes

*   **Placeholders**: Replace `customer_id='TESTCUST001'` and `enq_id='12345'` in URLs with actual dynamic values passed from the previous page (e.g., from a listing of enquiries) in a real application.
*   **Authentication**: The `LoginRequiredMixin` is added to views, assuming a Django authentication system is in place.
*   **Database Integration**: Ensure your Django `settings.py` is configured to connect to your existing SQL Server database (e.g., using `django-pyodbc-azure` or `django-mssql-backend`). Remember `managed = False` in models.
*   **Error Handling**: The `messages` framework is used for user feedback, making success/error messages visible on redirects or partial updates.
*   **Scalability**: The use of HTMX and partials allows for highly modular and performant interactions, reducing server load by avoiding full page reloads for dynamic content.
*   **User Context**: The `get_user_context` helper function simulates the `Session` variables from ASP.NET (`CompId`, `FinYearId`, `SessionId`). In a real system, `request.user` attributes or a custom user profile model would provide these details.
*   **Frontend Libraries**: Ensure jQuery and DataTables are loaded correctly in your `base.html`.
*   **Date Formats**: Pay close attention to date format consistency (`dd-MM-yyyy` in ASP.NET, `d-m-Y` in Django forms and `flatpickr`).

This comprehensive plan provides a clear, actionable roadmap for modernizing the specified ASP.NET functionality into a robust, maintainable, and high-performance Django application.