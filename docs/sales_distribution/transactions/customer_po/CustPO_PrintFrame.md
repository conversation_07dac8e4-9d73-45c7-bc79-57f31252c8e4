## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the C# code, we identify the following tables and their key columns:

*   **SD_Cust_PO_Master**: This is the main table for Customer Purchase Orders.
    *   `PONo` (Purchase Order Number)
    *   `EnqId` (Enquiry ID)
    *   `CompId` (Company ID)
    *   `CustomerId` (Customer ID)
    *   `PODate` (Purchase Order Date - appears to be a date string)
    *   `POReceivedDate` (PO Received Date - appears to be a date string)

*   **SD_Cust_master**: This table holds customer details.
    *   `EnqId` (Enquiry ID - links to `SD_Cust_PO_Master`)
    *   `CompId` (Company ID)
    *   `RegdCity` (Registered Address City ID)
    *   `RegdState` (Registered Address State ID)
    *   `RegdCountry` (Registered Address Country ID)
    *   `WorkCity` (Work Address City ID)
    *   `WorkState` (Work Address State ID)
    *   `WorkCountry` (Work Address Country ID)
    *   `MaterialDelCity` (Material Delivery Address City ID)
    *   `MaterialDelState` (Material Delivery Address State ID)
    *   `MaterialDelCountry` (Material Delivery Address Country ID)

*   **tblCity**: Lookup table for city names.
    *   `CityId` (City Identifier)
    *   `CityName` (City Name)

*   **tblState**: Lookup table for state names.
    *   `SId` (State Identifier)
    *   `StateName` (State Name)

*   **tblCountry**: Lookup table for country names.
    *   `CId` (Country Identifier)
    *   `CountryName` (Country Name)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code for `CustPO_PrintFrame.aspx.cs` primarily focuses on **Read** operations:
*   It fetches a single `SD_Cust_PO_Master` record based on `PONo`, `EnqId`, and `CompId`.
*   It fetches associated `SD_Cust_master` details for the given `EnqId` and `CompId`.
*   It performs multiple lookups (`tblCity`, `tblState`, `tblCountry`) to convert geographical IDs into their respective names.
*   It formats date strings.
*   The ultimate goal is to prepare data for a Crystal Report, which is a display/print function.

There are no explicit **Create, Update, or Delete** operations in this specific code-behind. However, given the "DataTables for all list views" and the provided templates for CRUD, we will implement full CRUD functionality for `CustomerPOMaster` in Django, in addition to the detail view that replicates the "print frame" functionality.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET UI (CustPO_PrintFrame.aspx) is minimal:
*   `CR:CrystalReportViewer`: This is the primary control, responsible for rendering and displaying the Crystal Report.
*   `CR:CrystalReportSource`: This control provides the report document to the viewer.
*   `script src="../../../Javascript/loadingNotifier.js"`: Indicates some client-side JavaScript for loading notifications.

In Django, the `CrystalReportViewer` will be replaced by an HTML template that dynamically displays the report data, potentially rendered for printing or as a browsable table. The loading notifier will be handled by HTMX's `hx-indicator` or Alpine.js for more complex loading states. We will replace the Crystal Report output with structured HTML data presentation using a detail view, and a list view leveraging DataTables for browsing multiple POs.

### Step 4: Generate Django Code
For this migration, we will assume a Django application named `sales_distribution`.

## 4.1 Models

Task: Create Django models based on the database schema, including relationships and business logic methods.

## Instructions:

We will define models for `CustomerPOMaster`, `CustomerMaster`, `City`, `State`, and `Country`. All models will be `managed = False` as they map to existing tables. We'll add methods to `CustomerPOMaster` to encapsulate the data retrieval and formatting logic, adhering to the "fat model" principle.

**File: `sales_distribution/models.py`**
```python
from django.db import models
from django.utils import timezone
import datetime

# Helper function for safe ID lookup (if IDs are potentially invalid)
def get_name_from_id(model, id_value, id_field, name_field):
    """Safely retrieves a name from a lookup table given an ID."""
    if not id_value:
        return None
    try:
        obj = model.objects.using('legacy_db').get(**{id_field: id_value})
        return getattr(obj, name_field)
    except model.DoesNotExist:
        return None
    except Exception: # Catch other potential DB errors
        return None

class City(models.Model):
    CityId = models.CharField(db_column='CityId', max_length=50, primary_key=True)
    CityName = models.CharField(db_column='CityName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.CityName or 'Unknown City'

class State(models.Model):
    SId = models.CharField(db_column='SId', max_length=50, primary_key=True)
    StateName = models.CharField(db_column='StateName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.StateName or 'Unknown State'

class Country(models.Model):
    CId = models.CharField(db_column='CId', max_length=50, primary_key=True)
    CountryName = models.CharField(db_column='CountryName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.CountryName or 'Unknown Country'

class CustomerMaster(models.Model):
    # Assuming Composite Primary Key or a unique ID column
    # For managed=False, Django often uses a pseudo 'id' if no PK is explicitly defined
    # We will use EnqId as part of the unique identifier
    enqid = models.CharField(db_column='EnqId', max_length=50, primary_key=True) # Assuming EnqId is unique for customer master
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    # Assuming these fields store IDs and point to City, State, Country tables
    regd_city_id = models.CharField(db_column='RegdCity', max_length=50, blank=True, null=True)
    regd_state_id = models.CharField(db_column='RegdState', max_length=50, blank=True, null=True)
    regd_country_id = models.CharField(db_column='RegdCountry', max_length=50, blank=True, null=True)

    work_city_id = models.CharField(db_column='WorkCity', max_length=50, blank=True, null=True)
    work_state_id = models.CharField(db_column='WorkState', max_length=50, blank=True, null=True)
    work_country_id = models.CharField(db_column='WorkCountry', max_length=50, blank=True, null=True)

    material_del_city_id = models.CharField(db_column='MaterialDelCity', max_length=50, blank=True, null=True)
    material_del_state_id = models.CharField(db_column='MaterialDelState', max_length=50, blank=True, null=True)
    material_del_country_id = models.CharField(db_column='MaterialDelCountry', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'
        # Composite unique constraint if EnqId alone is not unique across companies
        unique_together = (('enqid', 'compid'),) # Add if EnqId is not primary key and needs CompId for uniqueness

    def __str__(self):
        return f"Customer {self.enqid}"

    # Helper methods to get names from IDs
    def get_regd_city_name(self):
        return get_name_from_id(City, self.regd_city_id, 'CityId', 'CityName')

    def get_regd_state_name(self):
        return get_name_from_id(State, self.regd_state_id, 'SId', 'StateName')

    def get_regd_country_name(self):
        return get_name_from_id(Country, self.regd_country_id, 'CId', 'CountryName')

    def get_work_city_name(self):
        return get_name_from_id(City, self.work_city_id, 'CityId', 'CityName')

    def get_work_state_name(self):
        return get_name_from_id(State, self.work_state_id, 'SId', 'StateName')

    def get_work_country_name(self):
        return get_name_from_id(Country, self.work_country_id, 'CId', 'CountryName')

    def get_del_city_name(self):
        return get_name_from_id(City, self.material_del_city_id, 'CityId', 'CityName')

    def get_del_state_name(self):
        return get_name_from_id(State, self.material_del_state_id, 'SId', 'StateName')

    def get_del_country_name(self):
        return get_name_from_id(Country, self.material_del_country_id, 'CId', 'CountryName')


class CustomerPOMaster(models.Model):
    pono = models.CharField(db_column='PONo', max_length=50, primary_key=True) # Assuming PONo is unique for a given company/enquiry
    enqid = models.CharField(db_column='EnqId', max_length=50)
    compid = models.IntegerField(db_column='CompId')
    customerid = models.CharField(db_column='CustomerId', max_length=50, blank=True, null=True)
    # Assuming PODate and POReceivedDate might be stored as VARCHAR in source DB
    # We will use CharField and convert to date in Python for display
    podate_raw = models.CharField(db_column='PODate', max_length=50, blank=True, null=True)
    poreceiveddate_raw = models.CharField(db_column='POReceivedDate', max_length=50, blank=True, null=True)

    # Define a ForeignKey to CustomerMaster to allow ORM joins, even if managed=False
    # The actual column name in SD_Cust_PO_Master for this relationship is EnqId + CompId
    # Since CustomerMaster uses EnqId as PK, we link on that.
    # We'll handle the CompId filtering separately in queries.
    customer_details = models.ForeignKey(
        CustomerMaster,
        on_delete=models.DO_NOTHING,
        db_column='EnqId',  # This means the EnqId in CustPOMaster links to EnqId in CustomerMaster
        related_name='purchase_orders',
        to_field='enqid', # Specifies the field in CustomerMaster that is linked
        null=True, blank=True # Allow null/blank if a PO might not have customer details
    )

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer PO Master'
        verbose_name_plural = 'Customer PO Masters'
        # The original code filters by PONo, EnqId, AND CompId. This implies a composite PK.
        unique_together = (('pono', 'enqid', 'compid'),)

    def __str__(self):
        return f"PO No: {self.pono} (Enq: {self.enqid})"

    def get_formatted_podate(self):
        """Formats the PODate string from 'MM/DD/YYYY' to 'DD-MM-YYYY' or similar."""
        if not self.podate_raw:
            return None
        try:
            # Assuming ASP.NET's FromDateDMY might convert 'MM/DD/YYYY' to 'DD-MM-YYYY'
            # Or parse string date like 'YYYY-MM-DD HH:MM:SS' and format to 'DD-MM-YYYY'
            # Let's try common formats or adjust based on actual DB date string format
            dt_obj = datetime.datetime.strptime(self.podate_raw.split(' ')[0], '%m/%d/%Y')
            return dt_obj.strftime('%d-%m-%Y')
        except ValueError:
            return self.podate_raw # Return original if parsing fails

    def get_formatted_poreceiveddate(self):
        """Formats the POReceivedDate string from 'MM/DD/YYYY' to 'DD-MM-YYYY' or similar."""
        if not self.poreceiveddate_raw:
            return None
        try:
            # Assuming ASP.NET's FromDate might convert to a standard format
            dt_obj = datetime.datetime.strptime(self.poreceiveddate_raw.split(' ')[0], '%m/%d/%Y')
            return dt_obj.strftime('%d-%m-%Y')
        except ValueError:
            return self.poreceiveddate_raw # Return original if parsing fails

    def get_company_name(self):
        """
        Placeholder for fetching company name based on CompId.
        In a real system, this would involve a Company model.
        """
        # Example: Link to a Company model if it exists
        # try:
        #     company = Company.objects.using('legacy_db').get(id=self.compid)
        #     return company.name
        # except Company.DoesNotExist:
        #     return f"Company ID {self.compid}"
        # For now, a simple placeholder:
        return f"Company {self.compid}"

    def get_full_report_data(self):
        """
        Aggregates all data required for the PO 'report',
        including customer details and resolved city/state/country names.
        This encapsulates the complex data retrieval logic from the ASP.NET code-behind.
        """
        data = {
            'pono': self.pono,
            'enqid': self.enqid,
            'compid': self.compid,
            'customer_id': self.customerid,
            'po_date': self.get_formatted_podate(),
            'po_rec_date': self.get_formatted_poreceiveddate(),
            'company_name': self.get_company_name(),
            'customer_master': None,
            'regd_address': {},
            'work_address': {},
            'del_address': {},
        }

        # Fetch customer master details using the foreign key
        # We need to filter by compid as well, as unique_together implies
        # enqid is only unique *within* a compid.
        try:
            customer_master = CustomerMaster.objects.using('legacy_db').get(
                enqid=self.enqid,
                compid=self.compid # Crucial filter
            )
            data['customer_master'] = customer_master
            data['regd_address'] = {
                'city': customer_master.get_regd_city_name(),
                'state': customer_master.get_regd_state_name(),
                'country': customer_master.get_regd_country_name(),
            }
            data['work_address'] = {
                'city': customer_master.get_work_city_name(),
                'state': customer_master.get_work_state_name(),
                'country': customer_master.get_work_country_name(),
            }
            data['del_address'] = {
                'city': customer_master.get_del_city_name(),
                'state': customer_master.get_del_state_name(),
                'country': customer_master.get_del_country_name(),
            }
        except CustomerMaster.DoesNotExist:
            pass # No customer master found for this PO

        return data

```

## 4.2 Forms

Task: Define a Django form for user input for `CustomerPOMaster`.

## Instructions:

Since the original page was for printing, not editing, this form is generated to support the CRUD operations that are part of the target Django architecture (DataTables, add/edit modals).

**File: `sales_distribution/forms.py`**
```python
from django import forms
from .models import CustomerPOMaster, CustomerMaster

class CustomerPOMasterForm(forms.ModelForm):
    # If PODate/POReceivedDate are CharFields in DB, they need to be handled carefully
    # We might convert them to DateField for form input and back to CharField for saving
    # For simplicity, keeping them as CharField for now to match model directly
    # In a real scenario, use DateInput and manage conversion.

    class Meta:
        model = CustomerPOMaster
        fields = [
            'pono', 'enqid', 'compid', 'customerid',
            'podate_raw', 'poreceiveddate_raw',
        ]
        widgets = {
            'pono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enqid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customerid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'podate_raw': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM/DD/YYYY'}),
            'poreceiveddate_raw': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM/DD/YYYY'}),
        }
        labels = {
            'pono': 'PO Number',
            'enqid': 'Enquiry ID',
            'compid': 'Company ID',
            'customerid': 'Customer ID',
            'podate_raw': 'PO Date (MM/DD/YYYY)',
            'poreceiveddate_raw': 'PO Received Date (MM/DD/YYYY)',
        }

    # Add custom validation methods here if needed, e.g., for date format
    def clean_podate_raw(self):
        data = self.cleaned_data['podate_raw']
        # Example validation: check if it's a valid date string
        # if data and not is_valid_date(data):
        #    raise forms.ValidationError("Invalid date format. Use MM/DD/YYYY.")
        return data

```

## 4.3 Views

Task: Implement CRUD operations using CBVs for `CustomerPOMaster`, and a detail view for the "print frame" equivalent.

## Instructions:

We will have views for listing all POs with DataTables, and a detail view for a specific PO (the equivalent of `CustPO_PrintFrame`). We'll also provide the standard CRUD views for `CustomerPOMaster` as required by the structure.

**File: `sales_distribution/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import CustomerPOMaster, CustomerMaster, City, State, Country # Import all relevant models
from .forms import CustomerPOMasterForm
from django.db.models import F # For querying specific columns directly

# Assume 'legacy_db' is configured in settings.py for the old database
# Example: DATABASES = {'default': ..., 'legacy_db': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': 'path_to_legacy.db', 'OPTIONS': {'timeout': 20}, 'ATOMIC_REQUESTS': True}}

class CustomerPOMasterListView(ListView):
    model = CustomerPOMaster
    template_name = 'sales_distribution/customerpomaster/list.html'
    context_object_name = 'customer_po_masters'
    # Use the legacy database
    queryset = CustomerPOMaster.objects.using('legacy_db').all()

class CustomerPOMasterTablePartialView(ListView):
    """
    Renders only the table rows for HTMX updates.
    """
    model = CustomerPOMaster
    template_name = 'sales_distribution/customerpomaster/_customerpomaster_table.html'
    context_object_name = 'customer_po_masters'
    queryset = CustomerPOMaster.objects.using('legacy_db').all() # Use the legacy database

class CustomerPOMasterCreateView(CreateView):
    model = CustomerPOMaster
    form_class = CustomerPOMasterForm
    template_name = 'sales_distribution/customerpomaster/form.html'
    success_url = reverse_lazy('customerpomaster_list') # Redirect to list view

    def form_valid(self, form):
        # In a real scenario, you'd ensure compid is set correctly from session or user profile
        # For demonstration, let's assume a default or get from session if available
        # Example: form.instance.compid = self.request.session.get('compid', 1)
        response = super().form_valid(form)
        messages.success(self.request, 'Customer PO Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX without reloading full page
                headers={
                    'HX-Trigger': 'refreshCustomerPOMasterList' # Custom event to trigger list refresh
                }
            )
        return response
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Ensure that new objects are saved to the legacy database
        kwargs['using'] = 'legacy_db'
        return kwargs

class CustomerPOMasterUpdateView(UpdateView):
    model = CustomerPOMaster
    form_class = CustomerPOMasterForm
    template_name = 'sales_distribution/customerpomaster/form.html'
    success_url = reverse_lazy('customerpomaster_list')

    # Ensure the object is fetched from the legacy database
    def get_object(self, queryset=None):
        pk = self.kwargs.get(self.pk_url_kwarg)
        # Assuming PONo is the PK or a unique identifier that can be used here.
        # If composite PK, need to handle multiple kwargs (e.g., pono, enqid, compid)
        # For this example, if PONo is unique, it's enough. Otherwise, adjust below:
        obj = get_object_or_404(CustomerPOMaster.objects.using('legacy_db'), pk=pk)
        return obj

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer PO Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerPOMasterList'
                }
            )
        return response
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['using'] = 'legacy_db'
        return kwargs

class CustomerPOMasterDeleteView(DeleteView):
    model = CustomerPOMaster
    template_name = 'sales_distribution/customerpomaster/confirm_delete.html'
    success_url = reverse_lazy('customerpomaster_list')

    def get_object(self, queryset=None):
        pk = self.kwargs.get(self.pk_url_kwarg)
        obj = get_object_or_404(CustomerPOMaster.objects.using('legacy_db'), pk=pk)
        return obj

    def delete(self, request, *args, **kwargs):
        # We need to ensure deletion happens on the legacy database
        self.object = self.get_object()
        self.object.delete(using='legacy_db')
        
        messages.success(self.request, 'Customer PO Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerPOMasterList'
                }
            )
        return super().delete(request, *args, **kwargs) # This line might not be reached if HX-Request is true

class CustomerPODetailView(DetailView):
    """
    This view replaces the Crystal Report 'PrintFrame' by displaying
    the detailed data for a specific Customer PO in a web-friendly format,
    suitable for printing from the browser.
    """
    model = CustomerPOMaster
    template_name = 'sales_distribution/customerpomaster/detail.html'
    context_object_name = 'po_data'
    pk_url_kwarg = 'pono' # Use pono as the primary key for URL lookup

    def get_object(self, queryset=None):
        # Get parameters from URL
        pono = self.kwargs.get('pono')
        enqid = self.kwargs.get('enqid')
        # Get company ID from session, default to 1 for testing
        compid = self.request.session.get('compid', 1) 

        if not pono or not enqid:
            raise Http404("PO Number and Enquiry ID are required.")

        try:
            # Fetch the specific PO master object from the legacy database
            # We use 'using' to specify the database connection
            po_master = CustomerPOMaster.objects.using('legacy_db').get(
                pono=pono,
                enqid=enqid,
                compid=compid
            )
            return po_master.get_full_report_data() # Return processed data from model method
        except CustomerPOMaster.DoesNotExist:
            raise Http404("Customer PO not found with the given details.")
        except Exception as e:
            # Log the exception for debugging in a real application
            print(f"Error fetching PO details: {e}")
            raise Http404("An error occurred while retrieving PO details.")

```

## 4.4 Templates

Task: Create templates for each view.

## Instructions:

These templates use HTMX for dynamic content and modals, and DataTables for the list view. They extend `core/base.html` implicitly.

**File: `sales_distribution/templates/sales_distribution/customerpomaster/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Purchase Orders</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'customerpomaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New PO
        </button>
    </div>

    <div id="customerpomasterTable-container"
         hx-trigger="load, refreshCustomerPOMasterList from:body"
         hx-get="{% url 'customerpomaster_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Customer POs...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto my-auto max-h-[90vh] overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**File: `sales_distribution/templates/sales_distribution/customerpomaster/_customerpomaster_table.html`**
```html
<div class="p-4">
    <table id="customerpomasterTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry ID</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer ID</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in customer_po_masters %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.pono }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.enqid }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.customerid }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.get_formatted_podate }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm">
                    <a
                        href="{% url 'customerpomaster_detail' pono=obj.pono enqid=obj.enqid %}"
                        class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-1 px-2 rounded text-xs mr-1 transition duration-300 ease-in-out">
                        View Report
                    </a>
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-1 transition duration-300 ease-in-out"
                        hx-get="{% url 'customerpomaster_edit' obj.pono %}" {# Assuming pono is sufficient PK for edit #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'customerpomaster_delete' obj.pono %}" {# Assuming pono is sufficient PK for delete #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No Customer POs found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Re-initialize DataTable every time the partial is loaded
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#customerpomasterTable')) {
        $('#customerpomasterTable').DataTable().destroy();
    }
    $('#customerpomasterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true
    });
});
</script>
```

**File: `sales_distribution/templates/sales_distribution/customerpomaster/form.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pono|yesno:'Edit,Add' }} Customer PO</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent" hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal; }">
        {% csrf_token %}

        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `sales_distribution/templates/sales_distribution/customerpomaster/confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Customer PO <strong>{{ object.pono }}</strong> (Enquiry ID: {{ object.enqid }})?</p>

    <form hx-post="{% url 'customerpomaster_delete' object.pono %}" hx-swap="none" hx-target="#modalContent" hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal; }">
        {% csrf_token %}
        <input type="hidden" name="pono" value="{{ object.pono }}"> {# Pass necessary identifiers #}
        <input type="hidden" name="enqid" value="{{ object.enqid }}">
        <input type="hidden" name="compid" value="{{ object.compid }}">

        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `sales_distribution/templates/sales_distribution/customerpomaster/detail.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-lg rounded-lg p-8 print:shadow-none print:p-0">
        <div class="flex justify-between items-center mb-6 print:hidden">
            <h2 class="text-3xl font-bold text-gray-800">Customer Purchase Order Details</h2>
            <button onclick="window.print()" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Print Report
            </button>
        </div>

        <div class="mb-8 text-center print:mb-4">
            <h1 class="text-4xl font-extrabold text-blue-700 mb-2">{{ po_data.company_name }}</h1>
            <p class="text-lg text-gray-600">Customer Purchase Order Report</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6 mb-8 border-b pb-6 print:grid-cols-1 print:gap-4">
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3 border-b pb-2">PO Details</h3>
                <p class="text-gray-700"><strong class="font-medium">PO Number:</strong> {{ po_data.pono }}</p>
                <p class="text-gray-700"><strong class="font-medium">Enquiry ID:</strong> {{ po_data.enqid }}</p>
                <p class="text-gray-700"><strong class="font-medium">Customer ID:</strong> {{ po_data.customer_id }}</p>
                <p class="text-gray-700"><strong class="font-medium">PO Date:</strong> {{ po_data.po_date }}</p>
                <p class="text-gray-700"><strong class="font-medium">PO Received Date:</strong> {{ po_data.po_rec_date }}</p>
            </div>
            <div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3 border-b pb-2">Customer Details</h3>
                {% if po_data.customer_master %}
                    <p class="text-gray-700"><strong class="font-medium">Enquiry ID:</strong> {{ po_data.customer_master.enqid }}</p>
                    <p class="text-gray-700"><strong class="font-medium">Company ID:</strong> {{ po_data.customer_master.compid }}</p>
                    {# Add more customer master fields if they exist in the model #}
                {% else %}
                    <p class="text-gray-700">Customer master details not available.</p>
                {% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-6 mb-8 print:grid-cols-1 print:gap-4">
            <div>
                <h4 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Registered Address</h4>
                <p class="text-gray-700"><strong class="font-medium">City:</strong> {{ po_data.regd_address.city|default:"N/A" }}</p>
                <p class="text-gray-700"><strong class="font-medium">State:</strong> {{ po_data.regd_address.state|default:"N/A" }}</p>
                <p class="text-gray-700"><strong class="font-medium">Country:</strong> {{ po_data.regd_address.country|default:"N/A" }}</p>
            </div>
            <div>
                <h4 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Work Address</h4>
                <p class="text-gray-700"><strong class="font-medium">City:</strong> {{ po_data.work_address.city|default:"N/A" }}</p>
                <p class="text-gray-700"><strong class="font-medium">State:</strong> {{ po_data.work_address.state|default:"N/A" }}</p>
                <p class="text-gray-700"><strong class="font-medium">Country:</strong> {{ po_data.work_address.country|default:"N/A" }}</p>
            </div>
            <div>
                <h4 class="text-lg font-semibold text-gray-800 mb-3 border-b pb-2">Material Delivery Address</h4>
                <p class="text-gray-700"><strong class="font-medium">City:</strong> {{ po_data.del_address.city|default:"N/A" }}</p>
                <p class="text-gray-700"><strong class="font-medium">State:</strong> {{ po_data.del_address.state|default:"N/A" }}</p>
                <p class="text-gray-700"><strong class="font-medium">Country:</strong> {{ po_data.del_address.country|default:"N/A" }}</p>
            </div>
        </div>

        <div class="mt-8 pt-4 border-t text-sm text-gray-500 text-center print:mt-4 print:pt-2">
            <p>&copy; {{ "now"|date:"Y" }} AutoERP. All rights reserved.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

## 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

These URL patterns provide access to the list of POs, the detail view (print frame equivalent), and the HTMX endpoints for CRUD modals.

**File: `sales_distribution/urls.py`**
```python
from django.urls import path
from .views import (
    CustomerPOMasterListView,
    CustomerPOMasterCreateView,
    CustomerPOMasterUpdateView,
    CustomerPOMasterDeleteView,
    CustomerPOMasterTablePartialView,
    CustomerPODetailView, # For the print frame equivalent
)

urlpatterns = [
    # List view for all Customer POs (main page with DataTables)
    path('customerpo/', CustomerPOMasterListView.as_view(), name='customerpomaster_list'),

    # HTMX partial for the DataTables content
    path('customerpo/table/', CustomerPOMasterTablePartialView.as_view(), name='customerpomaster_table'),

    # CRUD operations (modal-based with HTMX)
    path('customerpo/add/', CustomerPOMasterCreateView.as_view(), name='customerpomaster_add'),
    path('customerpo/edit/<str:pk>/', CustomerPOMasterUpdateView.as_view(), name='customerpomaster_edit'),
    path('customerpo/delete/<str:pk>/', CustomerPOMasterDeleteView.as_view(), name='customerpomaster_delete'),

    # Detail view (equivalent to the ASP.NET PrintFrame)
    # Uses multiple parameters to uniquely identify the PO as in the original ASP.NET
    path('customerpo/detail/<str:pono>/<str:enqid>/', CustomerPODetailView.as_view(), name='customerpomaster_detail'),
]

```

## 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for model methods and properties, and integration tests for all views. This ensures high test coverage.

**File: `sales_distribution/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import datetime
from .models import CustomerPOMaster, CustomerMaster, City, State, Country

# Mock database connection for tests if not using an actual test DB
# You might need to configure a test database or use sqlite in-memory for `legacy_db`
# in your settings.py for these tests to run.

class CustomerPOMasterModelTest(TestCase):
    # Use a specific database for these tests if not using default
    databases = {'legacy_db'}

    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models in the legacy_db
        cls.city_reg = City.objects.using('legacy_db').create(CityId='C001', CityName='Registered City')
        cls.state_reg = State.objects.using('legacy_db').create(SId='S001', StateName='Registered State')
        cls.country_reg = Country.objects.using('legacy_db').create(CId='CO01', CountryName='Registered Country')
        cls.city_work = City.objects.using('legacy_db').create(CityId='C002', CityName='Work City')
        cls.state_work = State.objects.using('legacy_db').create(SId='S002', StateName='Work State')
        cls.country_work = Country.objects.using('legacy_db').create(CId='CO02', CountryName='Work Country')
        cls.city_del = City.objects.using('legacy_db').create(CityId='C003', CityName='Delivery City')
        cls.state_del = State.objects.using('legacy_db').create(SId='S003', StateName='Delivery State')
        cls.country_del = Country.objects.using('legacy_db').create(CId='CO03', CountryName='Delivery Country')

        cls.customer_master = CustomerMaster.objects.using('legacy_db').create(
            enqid='ENQ123',
            compid=1,
            regd_city_id='C001', regd_state_id='S001', regd_country_id='CO01',
            work_city_id='C002', work_state_id='S002', work_country_id='CO02',
            material_del_city_id='C003', material_del_state_id='S003', material_del_country_id='CO03',
        )

        cls.po_master = CustomerPOMaster.objects.using('legacy_db').create(
            pono='PO001',
            enqid='ENQ123',
            compid=1,
            customerid='CUST456',
            podate_raw='10/25/2023',
            poreceiveddate_raw='10/26/2023',
        )

    def test_customer_po_master_creation(self):
        po = CustomerPOMaster.objects.using('legacy_db').get(pono='PO001')
        self.assertEqual(po.enqid, 'ENQ123')
        self.assertEqual(po.compid, 1)
        self.assertEqual(po.customerid, 'CUST456')

    def test_formatted_podate(self):
        po = CustomerPOMaster.objects.using('legacy_db').get(pono='PO001')
        self.assertEqual(po.get_formatted_podate(), '25-10-2023')
        po.podate_raw = None
        self.assertIsNone(po.get_formatted_podate())
        po.podate_raw = 'Invalid Date'
        self.assertEqual(po.get_formatted_podate(), 'Invalid Date')

    def test_formatted_poreceiveddate(self):
        po = CustomerPOMaster.objects.using('legacy_db').get(pono='PO001')
        self.assertEqual(po.get_formatted_poreceiveddate(), '26-10-2023')
        po.poreceiveddate_raw = None
        self.assertIsNone(po.get_formatted_poreceiveddate())

    def test_get_company_name(self):
        po = CustomerPOMaster.objects.using('legacy_db').get(pono='PO001')
        self.assertEqual(po.get_company_name(), 'Company 1') # Based on placeholder logic

    def test_customer_master_address_lookups(self):
        cust = CustomerMaster.objects.using('legacy_db').get(enqid='ENQ123', compid=1)
        self.assertEqual(cust.get_regd_city_name(), 'Registered City')
        self.assertEqual(cust.get_work_state_name(), 'Work State')
        self.assertEqual(cust.get_del_country_name(), 'Delivery Country')

        # Test with invalid IDs
        cust.regd_city_id = 'C999' # Non-existent city ID
        self.assertIsNone(cust.get_regd_city_name())
        cust.regd_city_id = '' # Empty ID
        self.assertIsNone(cust.get_regd_city_name())


    def test_get_full_report_data(self):
        po = CustomerPOMaster.objects.using('legacy_db').get(pono='PO001')
        report_data = po.get_full_report_data()

        self.assertEqual(report_data['pono'], 'PO001')
        self.assertEqual(report_data['enqid'], 'ENQ123')
        self.assertEqual(report_data['customer_id'], 'CUST456')
        self.assertEqual(report_data['po_date'], '25-10-2023')
        self.assertEqual(report_data['po_rec_date'], '26-10-2023')
        self.assertIsNotNone(report_data['customer_master'])
        self.assertEqual(report_data['regd_address']['city'], 'Registered City')
        self.assertEqual(report_data['work_address']['state'], 'Work State')
        self.assertEqual(report_data['del_address']['country'], 'Delivery Country')

    def test_get_full_report_data_no_customer_master(self):
        # Create a PO without a corresponding CustomerMaster
        po_no_cust = CustomerPOMaster.objects.using('legacy_db').create(
            pono='PO002',
            enqid='NONEXISTENT_ENQ', # This enqid doesn't exist in CustomerMaster
            compid=1,
            customerid='CUST999',
            podate_raw='01/01/2024',
            poreceiveddate_raw='01/02/2024',
        )
        report_data = po_no_cust.get_full_report_data()
        self.assertIsNone(report_data['customer_master'])
        self.assertEqual(report_data['regd_address'], {})


class CustomerPOViewsTest(TestCase):
    databases = {'legacy_db'} # Ensure tests use the legacy_db connection

    @classmethod
    def setUpTestData(cls):
        # Setup minimal data required for views
        cls.customer_master = CustomerMaster.objects.using('legacy_db').create(
            enqid='ENQ456',
            compid=1,
        )
        cls.po_master = CustomerPOMaster.objects.using('legacy_db').create(
            pono='POVIEW001',
            enqid='ENQ456',
            compid=1,
            customerid='CUSTVIEW001',
            podate_raw='11/15/2023',
            poreceiveddate_raw='11/16/2023',
        )
        cls.po_master_2 = CustomerPOMaster.objects.using('legacy_db').create(
            pono='POVIEW002',
            enqid='ENQ456',
            compid=1,
            customerid='CUSTVIEW002',
            podate_raw='11/17/2023',
            poreceiveddate_raw='11/18/2023',
        )

    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session['compid'] = 1 # Mock session compid for detail view
        self.session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('customerpomaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerpomaster/list.html')
        self.assertIn('customer_po_masters', response.context)
        self.assertContains(response, 'POVIEW001') # Check if PO data is present

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('customerpomaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerpomaster/_customerpomaster_table.html')
        self.assertIn('customer_po_masters', response.context)
        self.assertContains(response, 'POVIEW001') # Check if PO data is present
        self.assertContains(response, '11/15/2023') # Original raw date

    def test_create_view_get(self):
        response = self.client.get(reverse('customerpomaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerpomaster/form.html')
        self.assertIn('form', response.context)

    @patch('sales_distribution.models.CustomerPOMaster.objects')
    def test_create_view_post_success(self, mock_objects):
        # Mock the .using('legacy_db').create() call for `managed=False` models
        mock_objects.using.return_value.create.return_value = MagicMock(
            pono='NEWPO', enqid='NEWENQ', compid=1, customerid='NEWCUST'
        )

        data = {
            'pono': 'NEWPO',
            'enqid': 'NEWENQ',
            'compid': 1,
            'customerid': 'NEWCUST',
            'podate_raw': '12/01/2023',
            'poreceiveddate_raw': '12/02/2023',
        }
        response = self.client.post(reverse('customerpomaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerPOMasterList')
        messages = list(response.context['messages']) if response.context else []
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer PO Master added successfully.')
        # Verify that the create method was called on the correct database manager
        mock_objects.using.assert_called_with('legacy_db')
        mock_objects.using.return_value.create.assert_called_once()

    def test_create_view_post_invalid(self):
        data = {
            'pono': '',  # Invalid data
            'enqid': '',
            'compid': '',
            'customerid': '',
            'podate_raw': '',
            'poreceiveddate_raw': '',
        }
        response = self.client.post(reverse('customerpomaster_add'), data)
        self.assertEqual(response.status_code, 200) # Form should re-render with errors
        self.assertFormError(response, 'form', 'pono', ['This field cannot be blank.'])

    def test_update_view_get(self):
        response = self.client.get(reverse('customerpomaster_edit', args=['POVIEW001']))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerpomaster/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.pono, 'POVIEW001')

    @patch('sales_distribution.models.CustomerPOMaster.objects')
    def test_update_view_post_success(self, mock_objects):
        mock_po = MagicMock(
            pono='POVIEW001', enqid='ENQ456', compid=1, customerid='CUSTUPDATED',
            podate_raw='11/15/2023', poreceiveddate_raw='11/16/2023'
        )
        mock_objects.using.return_value.get.return_value = mock_po
        mock_po.save.return_value = None # Mock save method

        data = {
            'pono': 'POVIEW001',
            'enqid': 'ENQ456',
            'compid': 1,
            'customerid': 'CUSTUPDATED',
            'podate_raw': '11/15/2023',
            'poreceiveddate_raw': '11/16/2023',
        }
        response = self.client.post(reverse('customerpomaster_edit', args=['POVIEW001']), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerPOMasterList')
        mock_po.save.assert_called_once_with(using='legacy_db')

    def test_delete_view_get(self):
        response = self.client.get(reverse('customerpomaster_delete', args=['POVIEW001']))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerpomaster/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].pono, 'POVIEW001')

    @patch('sales_distribution.models.CustomerPOMaster.objects')
    def test_delete_view_post_success(self, mock_objects):
        mock_po = MagicMock(
            pono='POVIEW001', enqid='ENQ456', compid=1, customerid='CUSTVIEW001',
            podate_raw='11/15/2023', poreceiveddate_raw='11/16/2023'
        )
        mock_objects.using.return_value.get.return_value = mock_po
        
        # Test deletion via HTTP_HX_REQUEST
        response = self.client.post(reverse('customerpomaster_delete', args=['POVIEW001']), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshCustomerPOMasterList')
        mock_po.delete.assert_called_once_with(using='legacy_db')

        # Test regular (non-HTMX) deletion
        mock_po.delete.reset_mock() # Reset mock for next test
        response_regular = self.client.post(reverse('customerpomaster_delete', args=['POVIEW002']))
        self.assertEqual(response_regular.status_code, 302) # Redirects after success
        self.assertRedirects(response_regular, reverse('customerpomaster_list'))
        mock_po.delete.assert_called_once_with(using='legacy_db')

    def test_detail_view_get_success(self):
        url = reverse('customerpomaster_detail', args=['POVIEW001', 'ENQ456'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/customerpomaster/detail.html')
        self.assertIn('po_data', response.context)
        self.assertEqual(response.context['po_data']['pono'], 'POVIEW001')
        self.assertEqual(response.context['po_data']['enqid'], 'ENQ456')
        self.assertEqual(response.context['po_data']['po_date'], '15-11-2023')

    def test_detail_view_get_not_found(self):
        url = reverse('customerpomaster_detail', args=['NONEXISTENT_PO', 'NONEXISTENT_ENQ'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'Customer PO not found', status_code=404)

    def test_detail_view_get_missing_params(self):
        # Directly test the view function with missing parameters if needed,
        # but URL regex should prevent this for named parameters.
        # This test might be more relevant if the URL regex was optional.
        pass

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided templates and views demonstrate the integration:
-   **HTMX for dynamic updates:**
    -   `list.html` uses `hx-get` to load the table content dynamically from `{% url 'customerpomaster_table' %}` on `load` and on a custom `refreshCustomerPOMasterList` event.
    -   Add/Edit/Delete buttons use `hx-get` to fetch modal content (`_customerpomaster_form.html`, `_customerpomaster_confirm_delete.html`).
    -   Forms in modals (`_customerpomaster_form.html`, `_customerpomaster_confirm_delete.html`) use `hx-post` for submission, `hx-swap="none"` to prevent content replacement, and `hx-on::after-request` to trigger the `HX-Trigger` from the view and close the modal on success.
    -   Views send `HX-Trigger` headers (`refreshCustomerPOMasterList`) after successful CRUD operations to instruct the client to refresh the main list table.
-   **Alpine.js for UI state management:**
    -   The modal (`#modal`) visibility is controlled using Alpine.js's `_` (hyperscript) syntax: `on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me`.
-   **DataTables for list views:**
    -   The `_customerpomaster_table.html` partial includes the `<table id="customerpomasterTable">` element.
    -   A `<script>` block within this partial initializes DataTables on `document.ready()`, ensuring it re-initializes correctly even when loaded via HTMX.
-   **No full page reloads:** All CRUD operations and list refreshes happen dynamically via HTMX.
-   **DRY Templates:** `_customerpomaster_table.html`, `_customerpomaster_form.html`, `_customerpomaster_confirm_delete.html` are partials, promoting reusability.

## Final Notes

-   **Database Configuration:** Ensure you have a `legacy_db` connection configured in your Django `settings.py` pointing to your existing ASP.NET SQL Server database. This is crucial for `managed = False` models.
    ```python
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql', # or 'mysql', 'sqlite3'
            'NAME': 'your_new_django_db',
            # ... other settings
        },
        'legacy_db': {
            'ENGINE': 'mssql', # Requires django-mssql or similar
            'NAME': 'YourASPNETDatabaseName',
            'HOST': 'YourSQLServerInstance',
            'PORT': '',
            'USER': 'YourDbUser',
            'PASSWORD': 'YourDbPassword',
            'OPTIONS': {
                'driver': 'ODBC Driver 17 for SQL Server', # Adjust as per your setup
                'extra_params': 'TrustServerCertificate=yes;' # Potentially needed for local dev
            },
        }
    }

    DATABASE_ROUTERS = ['yourapp.db_routers.LegacyRouter'] # Define a database router
    ```
-   **Database Router (`db_routers.py`):** For `managed=False` models that need to use a specific database, a database router is essential.
    ```python
    # sales_distribution/db_routers.py
    class LegacyRouter:
        """
        A router to control all database operations on models in the
        sales_distribution application.
        """
        route_app_labels = {'sales_distribution'}

        def db_for_read(self, model, **hints):
            if model._meta.app_label in self.route_app_labels:
                return 'legacy_db'
            return None

        def db_for_write(self, model, **hints):
            if model._meta.app_label in self.route_app_labels:
                return 'legacy_db'
            return None

        def allow_relation(self, obj1, obj2, **hints):
            if obj1._meta.app_label in self.route_app_labels or \
               obj2._meta.app_label in self.route_app_labels:
               return True
            return None

        def allow_migrate(self, db, app_label, model_name=None, **hints):
            if app_label in self.route_app_labels:
                return db == 'legacy_db'
            return None
    ```
-   **`pk` in URLs:** The original ASP.NET code used `PONo` and `EnqId` and `CompId` as identifiers for a single PO. For the `CustomerPOMaster` model, `pono` was designated `primary_key=True`. If `pono` is not *globally* unique (e.g., `pono` + `enqid` + `compid` forms the unique key), the `pk` in URLs for edit/delete operations might need to be adjusted to take multiple parameters (e.g., `<str:pono>/<str:enqid>/<int:compid>/`) and the `get_object` method in views updated accordingly. For `CustomerPODetailView`, it already takes `pono` and `enqid`. If `pono` is truly unique enough for CRUD, the current setup works.
-   **Error Handling:** The `try-catch` blocks in the original C# indicate a need for robust error handling in Django views and models. The provided code includes basic `try-except` for model lookups.
-   **Frontend Libraries:** Ensure DataTables, HTMX, and Alpine.js are correctly included in your `core/base.html` (e.g., via CDN links).
    ```html
    <!-- Example in base.html -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/alpinejs@3.13.3/dist/cdn.min.js" defer></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css"/>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    ```