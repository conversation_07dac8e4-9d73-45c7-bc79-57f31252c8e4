## ASP.NET to Django Conversion Script: Customer PO Search & List

This document outlines a strategic plan to modernize the existing ASP.NET Customer PO search and listing functionality into a robust, scalable Django application. Our approach leverages modern Django principles, emphasizing automation, clear separation of concerns, and a rich user experience through HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`CustPO_Edit`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several tables. The primary table for the listed data is `SD_Cust_PO_Master`. Other tables are joined to fetch related details like customer name, financial year, and employee who generated the PO. A temporary table `SD_Cust_PO_Details_Temp` is also referenced for cleanup, but it's not central to data display on this page.

**Identified Tables and Columns:**

*   **`SD_Cust_PO_Master`** (Main PO data)
    *   `POId` (Integer, assumed Primary Key for `SD_Cust_PO_Master`)
    *   `PONo` (String)
    *   `EnqId` (String)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master`)
    *   `SessionId` (Integer, mapped to `EmpId` in `tblHR_OfficeStaff`)
    *   `FinYearId` (Integer, Foreign Key to `tblFinancial_master`)
    *   `SysDate` (String, stored as 'DD-MM-YYYY' and then formatted)
    *   `CompId` (Integer, Foreign Key to `CompanyMaster`)

*   **`SD_Cust_Master`** (Customer details)
    *   `CustomerId` (String, Primary Key)
    *   `CustomerName` (String)
    *   `CompId` (Integer, Foreign Key)

*   **`tblFinancial_master`** (Financial Year details)
    *   `FinYearId` (Integer, Primary Key)
    *   `FinYear` (String)

*   **`tblHR_OfficeStaff`** (Employee details)
    *   `EmpId` (Integer, Primary Key)
    *   `Title` (String, e.g., 'Mr.', 'Ms.')
    *   `EmployeeName` (String)
    *   `CompId` (Integer, Foreign Key)

*   **`SD_Cust_WorkOrder_Master`** (Work order status, used to filter out 'closed' POs)
    *   `PONo` (String)
    *   `FinYearId` (Integer, Foreign Key)
    *   `CompId` (Integer, Foreign Key)
    *   `CloseOpen` (Integer, `0` for open, `1` for closed)

*   **`CompanyMaster`** (Assumed central company table)
    *   `CompId` (Integer, Primary Key)
    *   `CompName` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET page primarily serves as a "Read" operation with advanced filtering capabilities.

*   **Read (Search & List):** The page displays a list of "open" Customer Purchase Orders based on the current financial year and company. It supports dynamic search functionality allowing users to filter POs by "Customer Name", "Enquiry No", or "PO No". Pagination and sorting are handled by the `GridView` control.
*   **Data Aggregation:** The C# `BindDataCust` method performs multiple SQL lookups (effectively joins) to enrich the primary PO data with customer names, financial year descriptions, and employee names. It also checks the `SD_Cust_WorkOrder_Master` table to exclude "closed" POs.
*   **Autocomplete:** The `AutoCompleteExtender` uses a WebMethod (`sql`) to provide real-time suggestions for customer names as the user types.
*   **Temporary Data Cleanup:** On every page load, a temporary table (`SD_Cust_PO_Details_Temp`) is cleared based on session and company ID. This indicates a pattern for handling transient data, which would typically be managed via Django sessions or dedicated temporary data structures if needed for complex multi-step forms. For this page, it's just a cleanup detail.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js equivalents.

**Analysis:**
The page has a search header section and a data display section.

*   **Search Controls:**
    *   `DropDownList1` (ASP.NET `DropDownList`): Will be a Django `forms.ChoiceField` for `search_by` with choices like "Customer Name", "Enquiry No", "PO No". Its `AutoPostBack` functionality will be replaced by HTMX `hx-trigger="change"`.
    *   `txtEnqId` (ASP.NET `TextBox`): Will be a Django `forms.CharField` for `enq_id_value`. Its visibility is toggled by `DropDownList1`.
    *   `TxtSearchValue` (ASP.NET `TextBox` with `AutoCompleteExtender`): Will be a Django `forms.CharField` for `search_value`. Its autocomplete functionality will be implemented using HTMX (`hx-get`) hitting a Django view that returns JSON. Its visibility is also toggled by `DropDownList1`.
    *   `btnSearch` (ASP.NET `Button`): Will be a standard HTML `<button type="submit">` that triggers an HTMX `hx-post` to reload the data table.
*   **Data Display:**
    *   `SearchGridView1` (ASP.NET `GridView`): Will be a standard HTML `<table>` element initialized as a jQuery DataTables instance. HTMX will be used to swap the entire table content on search or refresh.
        *   The `HyperLinkField` to `CustPO_Edit_Details.aspx` suggests that each PO number in the list is a link to a detailed editing page. In Django, this will be a `{% url 'sales:custpo_detail' obj.pk %}` or `{% url 'sales:custpo_edit' obj.pk %}` link.
*   **Dynamic UI (Visibility):** The client-side logic for showing/hiding `txtEnqId` and `TxtSearchValue` based on `DropDownList1` selection will be managed efficiently using Alpine.js directly in the template.

### Step 4: Generate Django Code

We will create a Django application named `sales` and within it, a `custpo` module to encapsulate this functionality.

#### 4.1 Models (`sales/custpo/models.py`)

We model the database tables identified, setting `managed = False` and `db_table` to align with the existing database schema. A custom manager `CustPOMasterManager` is added to `CustPOMaster` to encapsulate the complex data retrieval and filtering logic previously found in `BindDataCust` in the C# code-behind. This adheres to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import F, Value, CharField, Case, When, Subquery, OuterRef
from django.db.models.functions import Concat

# --- Core ERP Models (assuming these exist as base tables or can be mocked) ---
class CompanyMaster(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(db_column='CompName', max_length=255)

    class Meta:
        managed = False
        db_table = 'CompanyMaster' # Replace with actual table name if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name


class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year


class CustomerMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='customers')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name


class EmployeeMaster(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='employees')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip()


class CustWorkOrderMaster(models.Model):
    # This table is used to check if a PO is 'open' (CloseOpen=0) or 'closed' (CloseOpen=1)
    # The primary key logic is not clear from ASP.NET, assuming PONo is unique enough for lookup.
    # In a real system, would confirm if there's a primary key and enforce it.
    pono = models.CharField(db_column='PONo', max_length=50) # Not unique here, can have multiple work orders for a PO
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='work_orders')
    comp = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='work_orders')
    close_open = models.IntegerField(db_column='CloseOpen', default=0) # 0: open, 1: closed

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Customer Work Order'
        verbose_name_plural = 'Customer Work Orders'
        # unique_together = (('pono', 'fin_year', 'comp'),) # If this is truly unique. Need to confirm.

    def __str__(self):
        return self.pono


# --- CustPOMaster and its Custom Manager ---
class CustPOMasterManager(models.Manager):
    """
    Custom manager for CustPOMaster to encapsulate data retrieval and filtering logic
    originally found in the ASP.NET BindDataCust method.
    """
    def get_open_pos(self, comp_id, fin_year_id, search_by='select', search_value='', enq_id_value=''):
        queryset = self.get_queryset().select_related(
            'customer', 'fin_year', 'generated_by', 'comp_field'
        ).filter(
            comp_field_id=comp_id,
            fin_year_id__lte=fin_year_id # Filter by FinYearId <= current (as per ASP.NET)
        ).order_by('-poid') # Order by POId Desc

        # Apply search filters based on dropdown selection
        if search_by == '1' and enq_id_value: # Enquiry No
            queryset = queryset.filter(enq_id=enq_id_value)
        elif search_by == '2' and enq_id_value: # PO No
            queryset = queryset.filter(pono=enq_id_value)
        elif search_by == '0' and search_value: # Customer Name
            # Extract CustomerId from search_value if format is "CustomerName [CustomerId]"
            if '[' in search_value and ']' in search_value:
                try:
                    customer_id_from_search = search_value.split('[')[-1].strip(']')
                    queryset = queryset.filter(customer__customer_id=customer_id_from_search)
                except IndexError:
                    # Fallback if format is not as expected, search by name directly (case-insensitive)
                    queryset = queryset.filter(customer__customer_name__icontains=search_value)
            else:
                queryset = queryset.filter(customer__customer_name__icontains=search_value)

        # Filter out 'closed' POs based on SD_Cust_WorkOrder_Master.CloseOpen = 1
        # The ASP.NET logic excludes POs where a work order exists and CloseOpen is 1.
        # This translates to: only include POs that either have no corresponding work order
        # OR have a work order where CloseOpen is 0.
        closed_work_orders = CustWorkOrderMaster.objects.filter(
            pono=OuterRef('pono'),
            fin_year=OuterRef('fin_year'),
            comp=OuterRef('comp_field'),
            close_open=1
        ).values('pk')[:1] # Using pk for existence check

        queryset = queryset.exclude(pk__in=Subquery(closed_work_orders))

        return queryset


class CustPOMaster(models.Model):
    poid = models.IntegerField(db_column='POId', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customer = models.ForeignKey(CustomerMaster, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='customer_pos')
    generated_by = models.ForeignKey(EmployeeMaster, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='generated_pos') # SessionId maps to EmpId
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='po_masters')
    sys_date = models.CharField(db_column='SysDate', max_length=50) # Stored as string, will be formatted in template/property
    comp_field = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='po_masters') # Renamed to avoid clash with `comp` if `CompanyMaster` was also called `Company`

    objects = CustPOMasterManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer PO'
        verbose_name_plural = 'Customer POs'

    def __str__(self):
        return self.pono

    @property
    def formatted_sys_date(self):
        """
        Formats the SysDate string (e.g., 'DD-MM-YYYY') into a consistent 'DD-MM-YYYY' format.
        The original ASP.NET SQL applies a specific conversion and replace to achieve this.
        Assuming SysDate is already stored as 'DD-MM-YYYY' in the DB for simplicity,
        or handle complex parsing if necessary.
        """
        try:
            # Parse 'DD-MM-YYYY' string to a date object and then format
            import datetime
            return datetime.datetime.strptime(self.sys_date, '%d-%m-%Y').strftime('%d-%m-%Y')
        except ValueError:
            # If parsing fails, return as is or handle error
            return self.sys_date

```

#### 4.2 Forms (`sales/custpo/forms.py`)

A non-ModelForm is used for the search criteria, as it's a simple form for input, not directly tied to CRUD operations on a single model instance. Alpine.js will handle dynamic visibility of fields.

```python
from django import forms

class CustPOSearchForm(forms.Form):
    """
    Form for handling search criteria on the Customer PO list page.
    """
    SEARCH_BY_CHOICES = [
        ('select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'searchCriteria', # Alpine.js model for selection
            'x-on:change': '$dispatch(\'search-criteria-changed\')' # Dispatch custom event
        })
    )
    txt_enq_id = forms.CharField(
        required=False,
        label="Enquiry/PO No",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Enquiry or PO Number',
            'x-show': 'searchCriteria === "1" || searchCriteria === "2"', # Alpine.js visibility
            'x-transition': '', # Alpine.js transition
            'x-bind:name': 'searchCriteria === "1" || searchCriteria === "2" ? "txt_enq_id" : ""' # Ensure name is present only when visible
        })
    )
    txt_search_value = forms.CharField(
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Type Customer Name (e.g., ABC Pvt Ltd [C123])',
            'hx-get': '{% url "sales:custpo_autocomplete_customer" %}', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'outerHTML',
            'x-show': 'searchCriteria === "0"', # Alpine.js visibility
            'x-transition': '', # Alpine.js transition
            'x-bind:name': 'searchCriteria === "0" ? "txt_search_value" : ""' # Ensure name is present only when visible
        })
    )
    # This form doesn't need to be a ModelForm as it's purely for search input.

```

#### 4.3 Views (`sales/custpo/views.py`)

Views are kept thin, delegating complex data retrieval to the `CustPOMasterManager`. Separate views handle the main list page, the HTMX-powered table partial, and the customer autocomplete.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q # For autocomplete
from django.conf import settings # For accessing global settings if session data is stored there

from .models import CustPOMaster, CustomerMaster, CompanyMaster, FinancialYear, EmployeeMaster
from .forms import CustPOSearchForm

# --- Helper to get session data (mocked for demo, replace with actual session/user profile logic) ---
def get_user_context_data(request):
    """
    Retrieves company and financial year IDs from session or provides defaults.
    In a production application, this would typically come from an authenticated user's profile
    or a session management system.
    """
    # Example hardcoded defaults for demonstration purposes if not in session.
    # In a real app, ensure these are loaded dynamically based on user login.
    comp_id = request.session.get('compid', 1) # Example: Company ID 1
    fin_year_id = request.session.get('finyear', 2024) # Example: Financial Year ID 2024
    return comp_id, fin_year_id

# --- Main List View ---
class CustPOMasterListView(ListView):
    """
    Renders the main Customer PO search and list page.
    Initializes the search form and sets up the container for the HTMX-loaded table.
    """
    model = CustPOMaster
    template_name = 'sales/custpo/list.html'
    context_object_name = 'customer_pos' # This will be used by the initial table load

    def get_queryset(self):
        """
        Returns an initial, unfiltered queryset for the first page load.
        Actual filtering happens in CustPOMasterTablePartialView via HTMX.
        """
        comp_id, fin_year_id = get_user_context_data(self.request)
        # On initial load, no search parameters are applied.
        return CustPOMaster.objects.get_open_pos(comp_id, fin_year_id)

    def get_context_data(self, **kwargs):
        """
        Adds the search form to the context.
        """
        context = super().get_context_data(**kwargs)
        # Pre-populate search form based on current state or defaults for Alpine.js
        search_by = self.request.POST.get('search_by', self.request.GET.get('search_by', 'select'))
        txt_enq_id = self.request.POST.get('txt_enq_id', self.request.GET.get('txt_enq_id', ''))
        txt_search_value = self.request.POST.get('txt_search_value', self.request.GET.get('txt_search_value', ''))

        context['search_form'] = CustPOSearchForm(initial={
            'search_by': search_by,
            'txt_enq_id': txt_enq_id,
            'txt_search_value': txt_search_value,
        })
        # This initial data helps Alpine.js render correctly on page load
        context['initial_search_criteria'] = search_by
        return context

# --- HTMX Partial View for the Data Table ---
class CustPOMasterTablePartialView(ListView):
    """
    Returns only the HTML fragment for the customer PO table.
    This view is designed to be called by HTMX requests (e.g., on search, refresh).
    """
    model = CustPOMaster
    template_name = 'sales/custpo/_custpomaster_table.html'
    context_object_name = 'customer_pos'
    # DataTables handles client-side pagination, so paginate_by is not strictly needed for Django's ListView
    # here if all data is loaded or if DataTables makes its own AJAX requests.
    # For this pattern, we generally load all filterable data and let DT manage.
    # If the dataset is huge, consider server-side processing for DataTables.

    def get_queryset(self):
        """
        Applies filters based on POST data from the search form.
        """
        comp_id, fin_year_id = get_user_context_data(self.request)

        # Get search parameters from POST data (HTMX form submission)
        search_by = self.request.POST.get('search_by', 'select')
        txt_enq_id = self.request.POST.get('txt_enq_id', '')
        txt_search_value = self.request.POST.get('txt_search_value', '')

        # Use the custom manager method to filter data
        queryset = CustPOMaster.objects.get_open_pos(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_by=search_by,
            search_value=txt_search_value,
            enq_id_value=txt_enq_id
        )
        return queryset

    def render_to_response(self, context, **response_kwargs):
        """
        Returns only the rendered HTML for the table partial.
        """
        return super().render_to_response(context, **response_kwargs)

# --- Autocomplete View for Customer Name ---
class CustomerAutocomplete(View):
    """
    Provides customer name suggestions for the autocomplete functionality via HTMX.
    Mimics the ASP.NET `sql` web method.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '') # The search query from HTMX
        comp_id, _ = get_user_context_data(request)
        
        if not query:
            return JsonResponse([], safe=False)

        # Filter customers whose names start with the query, limited to 10 results
        customers = CustomerMaster.objects.filter(
            comp__comp_id=comp_id, # Filter by company ID
            customer_name__istartswith=query # Case-insensitive startswith
        ).values('customer_id', 'customer_name').order_by('customer_name')[:10]

        results = []
        for customer in customers:
            # Format: "CustomerName [CustomerId]" as in ASP.NET
            results.append(f"{customer['customer_name']} [{customer['customer_id']}]")
        
        return JsonResponse(results, safe=False) # Return as a JSON array of strings


# --- Generic CRUD views (boilerplate as requested, though not directly implemented from this ASPX) ---
# These would typically be for a separate CustPO_Edit_Details.aspx equivalent or similar CRUD pages.

# from django.views.generic import CreateView, UpdateView, DeleteView
# from .forms import CustPOMasterForm # A hypothetical form for CustPOMaster if full CRUD was needed

# class CustPOMasterCreateView(CreateView):
#     model = CustPOMaster
#     form_class = CustPOMasterForm
#     template_name = 'sales/custpo/form.html'
#     success_url = reverse_lazy('sales:custpo_list')

#     def form_valid(self, form):
#         # Set context data like CompId, FinYearId, SessionId (EmpId) before saving
#         comp_id, fin_year_id = get_user_context_data(self.request)
#         form.instance.comp_field_id = comp_id
#         form.instance.fin_year_id = fin_year_id
#         form.instance.generated_by_id = self.request.user.employee_id # Assuming user has employee_id
#         form.instance.sys_date = datetime.date.today().strftime('%d-%m-%Y') # Set current date

#         response = super().form_valid(form)
#         messages.success(self.request, 'Customer PO added successfully.')
#         if self.request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204, # No content response for HTMX success
#                 headers={
#                     'HX-Trigger': 'refreshCustPOMasterList' # Custom event to trigger table refresh
#                 }
#             )
#         return response

# class CustPOMasterUpdateView(UpdateView):
#     model = CustPOMaster
#     form_class = CustPOMasterForm
#     template_name = 'sales/custpo/form.html'
#     success_url = reverse_lazy('sales:custpo_list')

#     def form_valid(self, form):
#         response = super().form_valid(form)
#         messages.success(self.request, 'Customer PO updated successfully.')
#         if self.request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshCustPOMasterList'
#                 }
#             )
#         return response

# class CustPOMasterDeleteView(DeleteView):
#     model = CustPOMaster
#     template_name = 'sales/custpo/confirm_delete.html'
#     success_url = reverse_lazy('sales:custpo_list')

#     def delete(self, request, *args, **kwargs):
#         response = super().delete(request, *args, **kwargs)
#         messages.success(self.request, 'Customer PO deleted successfully.')
#         if request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshCustPOMasterList'
#                 }
#             )
#         return response
```

#### 4.4 Templates (`sales/custpo/`)

Templates are designed to be modular and utilize HTMX for dynamic content updates and Alpine.js for UI state management (like showing/hiding search fields). DataTables will handle the client-side table enhancements.

**`sales/custpo/list.html`** (Main page template)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block extra_head %}
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css"/>
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/3.0.2/css/responsive.dataTables.min.css"/>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Customer PO - Search & List</h2>

    <!-- Search Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8" x-data="{ searchCriteria: '{{ initial_search_criteria }}' }">
        <form hx-post="{% url 'sales:custpo_table' %}" 
              hx-target="#custpomasterTable-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-indicator" 
              _="on search-criteria-changed remove .hidden from #searchForm button then add .hidden to #searchForm button then wait 1ms then remove .hidden from #searchForm button then wait 1ms then trigger hx-post on #searchForm button">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end mb-4">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.search_by.label }}
                    </label>
                    {{ search_form.search_by }}
                </div>

                <!-- Fields for Enquiry/PO No or Customer Name -->
                <div x-cloak x-transition>
                    <label for="{{ search_form.txt_enq_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.txt_enq_id.label }}
                    </label>
                    {{ search_form.txt_enq_id }}
                </div>

                <div x-cloak x-transition>
                    <label for="{{ search_form.txt_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.txt_search_value.label }}
                    </label>
                    {{ search_form.txt_search_value }}
                    <div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-48 overflow-y-auto"></div>
                </div>

                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md shadow-sm w-full md:w-auto">
                        Search
                    </button>
                    <span id="loading-indicator" class="htmx-indicator ml-3 text-blue-600">
                        <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div> Loading...
                    </span>
                </div>
            </div>
            {% if form.errors %}
                <div class="text-red-500 text-sm mt-2">Please correct the errors below.</div>
            {% endif %}
        </form>
    </div>

    <!-- Data Table Container -->
    <div id="custpomasterTable-container"
         hx-trigger="load, refreshCustPOMasterList from:body"
         hx-post="{% url 'sales:custpo_table' %}"
         hx-swap="innerHTML">
        <!-- Initial table content loaded here or a spinner -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Customer PO data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- DataTables JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/3.0.2/js/dataTables.responsive.min.js"></script>
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.12"></script>
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <script>
        document.addEventListener('htmx:afterSwap', function(event) {
            // Re-initialize DataTables after HTMX swaps in new table content
            if (event.target.id === 'custpomasterTable-container') {
                if ($.fn.DataTable.isDataTable('#custpomasterTable')) {
                    $('#custpomasterTable').DataTable().destroy();
                }
                $('#custpomasterTable').DataTable({
                    "pageLength": 20, // Match ASP.NET PageSize
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "responsive": true,
                    "ordering": true, // Allow sorting
                    "searching": true // Allow client-side search
                });
            }
        });

        // Event listener for autocomplete results click
        document.addEventListener('click', function(event) {
            if (event.target.closest('#autocomplete-results')) {
                const selectedValue = event.target.textContent.trim();
                document.querySelector('#{{ search_form.txt_search_value.id_for_label }}').value = selectedValue;
                document.querySelector('#autocomplete-results').innerHTML = ''; // Clear results
            }
        });
    </script>
{% endblock %}
```

**`sales/custpo/_custpomaster_table.html`** (Partial template for the table)

```html
<!-- This template is loaded via HTMX into #custpomasterTable-container -->
<div class="bg-white shadow-md rounded-lg overflow-x-auto">
    {% if customer_pos %}
    <table id="custpomasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for po in customer_pos %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ po.fin_year.fin_year }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ po.customer.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ po.customer.customer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:underline">
                    <!-- This link should point to the details/edit page for the PO -->
                    <a href="{% url 'sales:custpo_edit_details' customer_id=po.customer.customer_id pono=po.pono enq_id=po.enq_id poid=po.poid %}">
                        {{ po.pono }}
                    </a>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ po.enq_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ po.formatted_sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ po.generated_by.employee_name }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-10">
        <p class="text-xl text-maroon-700 font-bold">No data to display !</p>
        <p class="text-gray-500 mt-2">Try adjusting your search criteria.</p>
    </div>
    {% endif %}
</div>

<!-- Autocomplete results container -->
<div id="autocomplete-results" class="autocomplete-list absolute bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto w-auto z-10"></div>
```

**`sales/custpo/_autocomplete_customer_results.html`** (Partial for autocomplete results)

```html
{% for result in results %}
    <div class="p-2 hover:bg-gray-100 cursor-pointer">{{ result }}</div>
{% empty %}
    <div class="p-2 text-gray-500">No suggestions</div>
{% endfor %}
```

#### 4.5 URLs (`sales/custpo/urls.py`)

URL patterns for the various views, including the main list, the HTMX table partial, and the autocomplete endpoint.

```python
from django.urls import path
from .views import CustPOMasterListView, CustPOMasterTablePartialView, CustomerAutocomplete
# from .views import CustPOMasterCreateView, CustPOMasterUpdateView, CustPOMasterDeleteView # For full CRUD

app_name = 'sales' # Namespace for sales app

urlpatterns = [
    # Main Customer PO List Page
    path('custpo/', CustPOMasterListView.as_view(), name='custpo_list'),
    
    # HTMX endpoint for the table partial (triggered by search form submission and refresh events)
    path('custpo/table/', CustPOMasterTablePartialView.as_view(), name='custpo_table'),

    # HTMX endpoint for customer autocomplete
    path('custpo/autocomplete-customer/', CustomerAutocomplete.as_view(), name='custpo_autocomplete_customer'),

    # Link to the detailed edit page (mimicking ASP.NET HyperLinkField)
    # This assumes CustPO_Edit_Details.aspx?CustomerId={0}&amp;PONo={1}&amp;EnqId={2}&amp;POId={3}
    # maps to a Django detail/edit view that takes these parameters.
    # Replace 'custpo_edit_details' with the actual name of your detail/edit URL pattern.
    path('custpo/details/<str:customer_id>/<str:pono>/<str:enq_id>/<int:poid>/', 
         View.as_view(), # Placeholder. This would be a detail/edit view class
         name='custpo_edit_details'),

    # --- Generic CRUD URLs (boilerplate as requested, not directly from this ASPX) ---
    # path('custpo/add/', CustPOMasterCreateView.as_view(), name='custpo_add'),
    # path('custpo/edit/<int:pk>/', CustPOMasterUpdateView.as_view(), name='custpo_edit'),
    # path('custpo/delete/<int:pk>/', CustPOMasterDeleteView.as_view(), name='custpo_delete'),
]
```

#### 4.6 Tests (`sales/custpo/tests.py`)

Comprehensive tests covering model methods and view functionality, including HTMX interactions. These tests use Django's `TestCase` and `Client` to simulate requests.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For checking database state
from django.http import HttpResponse
from unittest.mock import patch # For mocking session data

from .models import CustPOMaster, CustomerMaster, FinancialYear, EmployeeMaster, CompanyMaster, CustWorkOrderMaster
from .forms import CustPOSearchForm

# --- Mock Session Data Helper ---
def mock_get_user_context_data(request):
    """Mocks the session data for testing purposes."""
    return 1, 2024 # Example CompId, FinYearId

# --- Model Tests ---
class CustPOMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create prerequisite data
        cls.company = CompanyMaster.objects.create(comp_id=1, comp_name='Test Company')
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.fin_year_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-25')
        cls.customer_abc = CustomerMaster.objects.create(customer_id='CUSTABC', customer_name='ABC Corp', comp=cls.company)
        cls.customer_xyz = CustomerMaster.objects.create(customer_id='CUSTXYZ', customer_name='XYZ Ltd', comp=cls.company)
        cls.employee = EmployeeMaster.objects.create(emp_id=101, title='Mr.', employee_name='John Doe', comp=cls.company)

        # Create test CustPOMaster instances
        cls.po1 = CustPOMaster.objects.create(
            poid=1, pono='PO001', enq_id='ENQ001', customer=cls.customer_abc, 
            generated_by=cls.employee, fin_year=cls.fin_year_2024, 
            sys_date='01-01-2024', comp_field=cls.company
        )
        cls.po2 = CustPOMaster.objects.create(
            poid=2, pono='PO002', enq_id='ENQ002', customer=cls.customer_xyz, 
            generated_by=cls.employee, fin_year=cls.fin_year_2023, 
            sys_date='15-12-2023', comp_field=cls.company
        )
        cls.po3_closed = CustPOMaster.objects.create(
            poid=3, pono='PO003', enq_id='ENQ003', customer=cls.customer_abc, 
            generated_by=cls.employee, fin_year=cls.fin_year_2024, 
            sys_date='20-01-2024', comp_field=cls.company
        )
        # Mark PO3 as closed
        CustWorkOrderMaster.objects.create(
            pono=cls.po3_closed.pono, fin_year=cls.po3_closed.fin_year, 
            comp=cls.po3_closed.comp_field, close_open=1
        )
        
        # PO with no work order (should be open)
        cls.po4_no_wo = CustPOMaster.objects.create(
            poid=4, pono='PO004', enq_id='ENQ004', customer=cls.customer_xyz, 
            generated_by=cls.employee, fin_year=cls.fin_year_2024, 
            sys_date='05-02-2024', comp_field=cls.company
        )
        
        # PO with open work order (should be open)
        cls.po5_open_wo = CustPOMaster.objects.create(
            poid=5, pono='PO005', enq_id='ENQ005', customer=cls.customer_abc, 
            generated_by=cls.employee, fin_year=cls.fin_year_2024, 
            sys_date='10-02-2024', comp_field=cls.company
        )
        CustWorkOrderMaster.objects.create(
            pono=cls.po5_open_wo.pono, fin_year=cls.po5_open_wo.fin_year,
            comp=cls.po5_open_wo.comp_field, close_open=0
        )

    def test_custpo_creation(self):
        po = CustPOMaster.objects.get(poid=1)
        self.assertEqual(po.pono, 'PO001')
        self.assertEqual(po.customer.customer_name, 'ABC Corp')
        self.assertEqual(po.fin_year.fin_year, '2024-25')
        self.assertEqual(po.generated_by.employee_name, 'John Doe')
        self.assertEqual(po.sys_date, '01-01-2024')
        self.assertEqual(po.comp_field.comp_name, 'Test Company')

    def test_formatted_sys_date_property(self):
        po = CustPOMaster.objects.get(poid=1)
        self.assertEqual(po.formatted_sys_date, '01-01-2024')
        
        # Test with invalid date string
        po_invalid_date = CustPOMaster.objects.create(
            poid=99, pono='PO999', customer=self.customer_abc, 
            generated_by=self.employee, fin_year=self.fin_year_2024, 
            sys_date='invalid-date', comp_field=self.company
        )
        self.assertEqual(po_invalid_date.formatted_sys_date, 'invalid-date')

    @patch('sales.custpo.models.get_user_context_data', side_effect=mock_get_user_context_data)
    def test_get_open_pos_all(self, mock_session_data):
        # Should return PO1, PO2, PO4, PO5 (PO3 is closed)
        # Using current fin_year_id = 2024, so FinYearId <= 2024 includes 2023 and 2024
        open_pos = CustPOMaster.objects.get_open_pos(comp_id=1, fin_year_id=2024)
        self.assertEqual(open_pos.count(), 4)
        self.assertIn(self.po1, open_pos)
        self.assertIn(self.po2, open_pos)
        self.assertIn(self.po4_no_wo, open_pos)
        self.assertIn(self.po5_open_wo, open_pos)
        self.assertNotIn(self.po3_closed, open_pos) # PO3 should be excluded

    @patch('sales.custpo.models.get_user_context_data', side_effect=mock_get_user_context_data)
    def test_get_open_pos_filter_by_enquiry_no(self, mock_session_data):
        filtered_pos = CustPOMaster.objects.get_open_pos(
            comp_id=1, fin_year_id=2024, search_by='1', enq_id_value='ENQ001'
        )
        self.assertEqual(filtered_pos.count(), 1)
        self.assertEqual(filtered_pos.first(), self.po1)

    @patch('sales.custpo.models.get_user_context_data', side_effect=mock_get_user_context_data)
    def test_get_open_pos_filter_by_po_no(self, mock_session_data):
        filtered_pos = CustPOMaster.objects.get_open_pos(
            comp_id=1, fin_year_id=2024, search_by='2', enq_id_value='PO002'
        )
        self.assertEqual(filtered_pos.count(), 1)
        self.assertEqual(filtered_pos.first(), self.po2)

    @patch('sales.custpo.models.get_user_context_data', side_effect=mock_get_user_context_data)
    def test_get_open_pos_filter_by_customer_name(self, mock_session_data):
        filtered_pos = CustPOMaster.objects.get_open_pos(
            comp_id=1, fin_year_id=2024, search_by='0', search_value='ABC Corp'
        )
        self.assertEqual(filtered_pos.count(), 2) # PO1 and PO5
        self.assertIn(self.po1, filtered_pos)
        self.assertIn(self.po5_open_wo, filtered_pos)

    @patch('sales.custpo.models.get_user_context_data', side_effect=mock_get_user_context_data)
    def test_get_open_pos_filter_by_customer_id_from_search_value(self, mock_session_data):
        filtered_pos = CustPOMaster.objects.get_open_pos(
            comp_id=1, fin_year_id=2024, search_by='0', search_value='XYZ Ltd [CUSTXYZ]'
        )
        self.assertEqual(filtered_pos.count(), 2) # PO2 and PO4
        self.assertIn(self.po2, filtered_pos)
        self.assertIn(self.po4_no_wo, filtered_pos)

# --- View Tests ---
@patch('sales.custpo.views.get_user_context_data', side_effect=mock_get_user_context_data)
class CustPOMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create prerequisite data identical to model tests
        cls.company = CompanyMaster.objects.create(comp_id=1, comp_name='Test Company')
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.fin_year_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-25')
        cls.customer_abc = CustomerMaster.objects.create(customer_id='CUSTABC', customer_name='ABC Corp', comp=cls.company)
        cls.customer_xyz = CustomerMaster.objects.create(customer_id='CUSTXYZ', customer_name='XYZ Ltd', comp=cls.company)
        cls.employee = EmployeeMaster.objects.create(emp_id=101, title='Mr.', employee_name='John Doe', comp=cls.company)

        cls.po1 = CustPOMaster.objects.create(poid=1, pono='PO001', enq_id='ENQ001', customer=cls.customer_abc, 
                                            generated_by=cls.employee, fin_year=cls.fin_year_2024, sys_date='01-01-2024', comp_field=cls.company)
        cls.po2 = CustPOMaster.objects.create(poid=2, pono='PO002', enq_id='ENQ002', customer=cls.customer_xyz, 
                                            generated_by=cls.employee, fin_year=cls.fin_year_2023, sys_date='15-12-2023', comp_field=cls.company)
        cls.po3_closed = CustPOMaster.objects.create(poid=3, pono='PO003', enq_id='ENQ003', customer=cls.customer_abc, 
                                                    generated_by=cls.employee, fin_year=cls.fin_year_2024, sys_date='20-01-2024', comp_field=cls.company)
        CustWorkOrderMaster.objects.create(pono=cls.po3_closed.pono, fin_year=cls.po3_closed.fin_year, comp=cls.po3_closed.comp_field, close_open=1)
        cls.po4_no_wo = CustPOMaster.objects.create(poid=4, pono='PO004', enq_id='ENQ004', customer=cls.customer_xyz, 
                                                    generated_by=cls.employee, fin_year=cls.fin_year_2024, sys_date='05-02-2024', comp_field=cls.company)
        cls.po5_open_wo = CustPOMaster.objects.create(poid=5, pono='PO005', enq_id='ENQ005', customer=cls.customer_abc, 
                                                    generated_by=cls.employee, fin_year=cls.fin_year_2024, sys_date='10-02-2024', comp_field=cls.company)
        CustWorkOrderMaster.objects.create(pono=cls.po5_open_wo.pono, fin_year=cls.po5_open_wo.fin_year, comp=cls.po5_open_wo.comp_field, close_open=0)


    def setUp(self):
        self.client = Client()

    def test_list_view_get(self, mock_session_data):
        response = self.client.get(reverse('sales:custpo_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/custpo/list.html')
        self.assertIsInstance(response.context['search_form'], CustPOSearchForm)
        # Check that initial data (PO1, PO2, PO4, PO5) is present in the context for initial render
        self.assertEqual(len(response.context['customer_pos']), 4)
        self.assertIn(self.po1, response.context['customer_pos'])
        self.assertIn(self.po2, response.context['customer_pos'])
        self.assertIn(self.po4_no_wo, response.context['customer_pos'])
        self.assertIn(self.po5_open_wo, response.context['customer_pos'])

    def test_table_partial_view_post_no_filter(self, mock_session_data):
        # Simulate HTMX post with no filters
        response = self.client.post(reverse('sales:custpo_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/custpo/_custpomaster_table.html')
        self.assertEqual(len(response.context['customer_pos']), 4)
        self.assertIn(self.po1, response.context['customer_pos'])
        self.assertIn(self.po2, response.context['customer_pos'])
        self.assertIn(self.po4_no_wo, response.context['customer_pos'])
        self.assertIn(self.po5_open_wo, response.context['customer_pos'])
        self.assertContains(response, 'PO001')
        self.assertNotContains(response, 'PO003') # Closed PO should not be in response

    def test_table_partial_view_post_filter_by_enquiry_no(self, mock_session_data):
        data = {'search_by': '1', 'txt_enq_id': 'ENQ001', 'txt_search_value': ''}
        response = self.client.post(reverse('sales:custpo_table'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/custpo/_custpomaster_table.html')
        self.assertEqual(len(response.context['customer_pos']), 1)
        self.assertEqual(response.context['customer_pos'].first(), self.po1)
        self.assertContains(response, 'PO001')
        self.assertNotContains(response, 'PO002')

    def test_table_partial_view_post_filter_by_customer_name(self, mock_session_data):
        data = {'search_by': '0', 'txt_enq_id': '', 'txt_search_value': 'ABC Corp'}
        response = self.client.post(reverse('sales:custpo_table'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/custpo/_custpomaster_table.html')
        self.assertEqual(len(response.context['customer_pos']), 2)
        self.assertContains(response, 'PO001')
        self.assertContains(response, 'PO005')
        self.assertNotContains(response, 'PO002')

    def test_customer_autocomplete(self, mock_session_data):
        response = self.client.get(reverse('sales:custpo_autocomplete_customer'), {'q': 'ABC'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('ABC Corp [CUSTABC]', response.json())
        self.assertNotIn('XYZ Ltd [CUSTXYZ]', response.json())

    def test_customer_autocomplete_no_query(self, mock_session_data):
        response = self.client.get(reverse('sales:custpo_autocomplete_customer'), {'q': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    def test_customer_autocomplete_no_match(self, mock_session_data):
        response = self.client.get(reverse('sales:custpo_autocomplete_customer'), {'q': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The plan incorporates HTMX for all dynamic interactions:

1.  **Table Reload on Search:** When the search form is submitted, HTMX (`hx-post`, `hx-target`, `hx-swap`) sends the form data to `sales:custpo_table` endpoint. This endpoint returns only the updated table HTML, which HTMX seamlessly injects into the `custpomasterTable-container` div, eliminating full page reloads.
2.  **Table Reload on Dropdown Change:** The `search_by` dropdown also triggers an HTMX request on change, ensuring the table updates immediately even if the search button isn't explicitly clicked. This is managed by Alpine.js dispatching an event that triggers the HTMX post.
3.  **DataTables Initialization:** A JavaScript listener `htmx:afterSwap` ensures that the jQuery DataTables library is re-initialized on the newly loaded table content after an HTMX swap, preserving client-side features like search, sort, and pagination.
4.  **Customer Autocomplete:** The `txt_search_value` input uses `hx-get` to query `sales:custpo_autocomplete_customer` on `keyup changed delay:500ms`. The results are displayed in a div (`#autocomplete-results`) below the input. Clicking a result populates the textbox.
5.  **Alpine.js for UI State:** Alpine.js is used to manage the visibility of the `txt_enq_id` and `txt_search_value` fields. The `x-data` attribute on the search div initializes an `searchCriteria` variable, which is bound to the `search_by` dropdown using `x-model`. The `x-show` and `x-transition` directives on the input fields control their visibility based on the `searchCriteria` value, providing a smooth user experience.
6.  **Loading Indicators:** HTMX's built-in `htmx-indicator` class is used to show a loading spinner during AJAX requests.

### Final Notes

*   **Placeholders:** Ensure all `[PLACEHOLDERS]` like `[TABLE_NAME]`, `[APP_NAME]`, `[MODEL_NAME]`, etc., are replaced with their actual values based on your project structure and database.
*   **DRY Templates:** The use of a partial template (`_custpomaster_table.html`) for the table content adheres to DRY principles, allowing the main `list.html` to focus on the overall page layout.
*   **Fat Model, Thin View:** Business logic for filtering and data retrieval (`get_open_pos`) is encapsulated within the `CustPOMasterManager`, keeping views lean and focused on rendering.
*   **Comprehensive Tests:** The provided tests cover data integrity (model creation, properties) and view functionality (list view, partial view, autocomplete, filtering), including simulating HTMX requests to ensure proper integration.
*   **External CDN Links:** CDN links for jQuery, DataTables, HTMX, and Alpine.js are placed in `{% block extra_head %}` and `{% block extra_js %}` as per `base.html` best practices, ensuring these libraries are loaded efficiently.
*   **Security:** Always ensure proper session management, authentication, and authorization are in place in a production Django application. The `get_user_context_data` helper is a placeholder that needs to be replaced with your actual user/session management.
*   **Database Type:** The original code uses `SqlConnection`, implying SQL Server. Ensure your Django `settings.py` is configured with `django-mssql-backend` or a similar adapter for SQL Server if that's your target database.
*   **Error Handling:** While `try-catch` blocks were prevalent in ASP.NET, Django handles exceptions more robustly through its middleware, and validation is built into forms/models. Specific custom error handling can be added as needed.