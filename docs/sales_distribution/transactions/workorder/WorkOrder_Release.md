## ASP.NET to Django Conversion Script: Work Order Release

This plan outlines the modernization of your ASP.NET Work Order Release module to a high-performance, maintainable Django application. By leveraging modern Django principles, HTMX for dynamic interactions, and Alpine.js for lightweight frontend logic, we aim to deliver a solution that is faster, more scalable, and significantly easier to manage and extend. This automation-driven approach minimizes manual coding and streamlines the transition, reducing development time and potential errors.

### Business Value Proposition:

Migrating this module to Django will provide:
*   **Enhanced Performance:** Optimized data retrieval and lighter frontend interactions lead to faster page loads and a smoother user experience.
*   **Reduced Technical Debt:** Moving away from legacy ASP.NET Web Forms to a modern, well-structured framework improves code quality and reduces long-term maintenance costs.
*   **Improved Scalability:** Django's robust architecture and efficient ORM allow the application to handle more users and data gracefully as your business grows.
*   **Simplified Development & Maintenance:** Adherence to Django's "Don't Repeat Yourself" (DRY) principles and clear separation of concerns makes the codebase easier to understand, debug, and extend.
*   **Modern User Experience:** Seamless interactions through HTMX and Alpine.js provide a desktop-like feel without complex JavaScript, ensuring a responsive and intuitive interface.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to retrieve and display work order information. Based on the `BindDataCust` method, the primary table is `SD_Cust_WorkOrder_Master`, with lookups performed on `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff`.

**Identified Tables and Key Columns:**

*   **[TABLE_NAME]**: `SD_Cust_WorkOrder_Master`
    *   **Columns**: `Id` (PK), `EnqId`, `CustomerId` (FK), `WONo`, `PONo`, `SessionId` (FK to Employee), `FinYearId` (FK), `SysDate`, `CloseOpen`, `CompId` (FK to Company)
*   **[TABLE_NAME]**: `SD_Cust_Master`
    *   **Columns**: `CustomerId` (PK/Unique for lookup), `CustomerName`, `CompId` (FK)
*   **[TABLE_NAME]**: `tblFinancial_master`
    *   **Columns**: `FinYearId` (PK), `FinYear`
*   **[TABLE_NAME]**: `tblHR_OfficeStaff`
    *   **Columns**: `EmpId` (PK), `Title`, `EmployeeName`, `CompId` (FK)
*   **Inferred Table**: `Company` (for `CompId` across multiple tables)
    *   **Columns**: `CompId` (PK), `CompName` (placeholder)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements a "Read" operation with advanced search and filtering capabilities. There are no explicit Create, Update, or Delete operations on the Work Order records themselves shown on this page.

*   **Read:**
    *   **Data Source:** `SD_Cust_WorkOrder_Master` joined or looked up with `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff`.
    *   **Filtering:** Based on `DropDownList1` selection: Customer Name, Enquiry No, PO No, or WO No.
    *   **Search Input:** `txtEnqId` and `TxtSearchValue`.
    *   **Pagination:** `GridView1` supports paging.
    *   **Sorting:** `GridView1` supports sorting.
    *   **Auto-completion:** For `Customer Name` search via `sql` web method.
*   **Navigation/Link:** The `WONo` column is a `HyperLinkField` leading to `WorkOrder_ReleaseRPT.aspx`, implying a detail/report view for a specific work order. This will be a separate Django view.
*   **Session Management:** `Session["username"]`, `Session["finyear"]`, `Session["compid"]` are used for filtering data specific to the logged-in user's context.

### Step 3: Infer UI Components

The ASP.NET page features several common UI patterns:

*   **Data Display:** `GridView1` is used for tabular data presentation, complete with pagination and sorting. This directly translates to Django templates with DataTables.
*   **Search Controls:**
    *   `DropDownList1` (ASP.NET DropDownList) becomes a standard HTML `<select>` element in Django, likely part of a search form.
    *   `txtEnqId` and `TxtSearchValue` (ASP.NET TextBox) become HTML `<input type="text">` fields.
    *   The `AutoCompleteExtender` for `TxtSearchValue` indicates a need for dynamic search suggestions, which will be handled by HTMX.
*   **Action Button:** `btnSearch` (ASP.NET Button) becomes a standard HTML `<button>` with HTMX attributes to trigger a search.
*   **Status/Message Display:** `Label2` will be replaced by Django's `messages` framework, displayed within the base template, and potentially triggered by HTMX events.

### Step 4: Generate Django Code

We will create a new Django app named `sales` to encapsulate this module.

#### 4.1 Models (`sales/models.py`)

We'll define models for `WorkOrderMaster`, `CustomerMaster`, `FinancialYear`, `EmployeeStaff`, and a placeholder `Company` to correctly map to the existing database schema and establish relationships.

```python
from django.db import models

class Company(models.Model):
    """Represents the Company table from the legacy system."""
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(db_column='CompName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Company'  # Placeholder table name, adjust if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name or f"Company {self.comp_id}"

class FinancialYear(models.Model):
    """Represents the tblFinancial_master table."""
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class EmployeeStaff(models.Model):
    """Represents the tblHR_OfficeStaff table."""
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='comp_id')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee Staff'
        verbose_name_plural = 'Employee Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip()

class CustomerMaster(models.Model):
    """Represents the SD_Cust_Master table."""
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # PK for lookup
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='comp_id')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return self.customer_name

    @classmethod
    def get_customer_id_by_name(cls, customer_name, company_id):
        """
        Replicates fun.getCode for customer lookup.
        Assumes customer name is unique within a company or we pick the first match.
        """
        try:
            return cls.objects.get(customer_name=customer_name, company_id=company_id).customer_id
        except cls.DoesNotExist:
            return None

class WorkOrderMaster(models.Model):
    """Represents the SD_Cust_WorkOrder_Master table."""
    id = models.IntegerField(db_column='Id', primary_key=True) # DataKeyNames="Id"
    enquiry_id = models.CharField(db_column='EnqId', max_length=255, blank=True, null=True)
    customer = models.ForeignKey(CustomerMaster, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id')
    work_order_no = models.CharField(db_column='WONo', max_length=255)
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    generated_by = models.ForeignKey(EmployeeStaff, on_delete=models.DO_NOTHING, db_column='SessionId', to_field='emp_id', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', to_field='fin_year_id')
    # SysDate is stored as a specific string format; consider converting to DateField if possible during migration
    # For now, keeping it as CharField to directly map the existing data type.
    system_date = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True)
    close_open = models.IntegerField(db_column='CloseOpen') # 0 for open, 1 for closed
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='comp_id')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        # Order by WONo ASC as per C# code
        ordering = ['work_order_no']

    def __str__(self):
        return self.work_order_no

    @property
    def display_sys_date(self):
        """Converts system_date string (e.g., 'MM-DD-YYYY') to a displayable format."""
        if not self.system_date:
            return ""
        try:
            # Matches the C# parsing logic: dd-mm-yyyy for 103 style, then formats to 103 (dd/mm/yyyy)
            # The C# code substring/charindex part indicates mm-dd-yyyy which is then converted to 103 (dd/mm/yyyy)
            # Example C#: SUBSTRING(SysDate, CHARINDEX('-', SysDate) + 1, 2) + '-' + LEFT(SysDate,CHARINDEX('-', SysDate) - 1) + '-' + RIGHT(SysDate, CHARINDEX('-', REVERSE(SysDate)) - 1))
            # This is complex: it takes MM-DD-YYYY and converts to DD-MM-YYYY, then SQL Server converts to 103 which is DD/MM/YYYY.
            # Assuming the stored format is `MM-DD-YYYY`
            parts = self.system_date.split('-')
            if len(parts) == 3:
                month, day, year = parts
                return f"{day}/{month}/{year}" # Replicates SQL Server style 103
            return self.system_date # Fallback
        except (ValueError, IndexError):
            return self.system_date # Return raw if parsing fails

    @classmethod
    def get_work_orders(cls, request_user, search_type, search_value):
        """
        Retrieves work orders based on search criteria, replicating C# BindDataCust logic.
        This method is designed to be called from a thin view.
        """
        queryset = cls.objects.select_related('customer', 'financial_year', 'generated_by', 'company').filter(
            financial_year__fin_year_id__lte=request_user.financial_year_id, # Assumes user.financial_year_id
            close_open=0,
            company=request_user.company # Assumes user.company
        )

        if search_type == '0' and search_value: # Customer Name
            customer_id = CustomerMaster.get_customer_id_by_name(search_value, request_user.company.comp_id)
            if customer_id:
                queryset = queryset.filter(customer__customer_id=customer_id)
            else:
                queryset = queryset.none() # No matching customer, return empty
        elif search_type == '1' and search_value: # Enquiry No
            queryset = queryset.filter(enquiry_id=search_value)
        elif search_type == '2' and search_value: # PO No
            queryset = queryset.filter(po_no=search_value)
        elif search_type == '3' and search_value: # WO No
            queryset = queryset.filter(work_order_no=search_value)
        
        # 'Select' search_type implies no filter, or all results based on default Company/FinYear
        # The original code's 'Select' case merely toggles visibility and rebinds with CId, Eid (empty), which means no search filters applied.

        return queryset
```

#### 4.2 Forms (`sales/forms.py`)

For the search functionality, we'll create a simple `Form` rather than a `ModelForm`, as it's not directly creating or updating a model instance.

```python
from django import forms

class WorkOrderSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': "{% url 'sales:workorder_release_search_fields' %}", # HTMX endpoint to update input visibility
            'hx-trigger': 'change',
            'hx-target': '#search-inputs-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#search-indicator',
        })
    )
    search_value_text = forms.CharField(
        max_length=350,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name',
            'id': 'TxtSearchValue', # Mimic original ID for HTMX auto-completion setup
            'autocomplete': 'off', # Prevent browser autocomplete
            'hx-get': "{% url 'sales:customer_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms, search",
            'hx-target': "#autocomplete-results",
            'hx-swap': "innerHTML",
            'hx-indicator': '#customer-autocomplete-indicator',
        })
    )
    search_value_id = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Enquiry/PO/WO No',
            'id': 'txtEnqId', # Mimic original ID
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_value_text = cleaned_data.get('search_value_text')
        search_value_id = cleaned_data.get('search_value_id')

        # Basic validation: ensure one of the search fields is populated based on type
        if search_type in ['0'] and not search_value_text:
            self.add_error('search_value_text', 'Customer Name is required for this search type.')
        elif search_type in ['1', '2', '3'] and not search_value_id:
            self.add_error('search_value_id', 'Search value is required for this search type.')
        
        return cleaned_data

class PlaceholderForm(forms.Form):
    """
    A dummy form for standard CRUD views to adhere to the template structure,
    though not directly used for the WorkOrder Release page's main functionality.
    """
    pass # No fields, as we don't have direct CRUD for WorkOrderMaster on this page.
```

#### 4.3 Views (`sales/views.py`)

We'll define the main list view, a partial view for the table content (for HTMX updates), endpoints for search field toggling, and an autocomplete endpoint. We'll simulate `request.session` attributes for `company_id` and `financial_year_id` for demonstration.

```python
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin # Assume authentication
from django.db.models import F # For select_related performance

from .models import WorkOrderMaster, CustomerMaster, Company, FinancialYear, EmployeeStaff
from .forms import WorkOrderSearchForm, PlaceholderForm # PlaceholderForm for CRUD templates

# Mock user for demonstration if actual authentication is not set up
# In a real app, you'd use request.user.profile.company or similar
class MockUser:
    def __init__(self, username, company_id, financial_year_id):
        self.username = username
        self.company = Company(comp_id=company_id) # Mock Company object
        self.financial_year_id = financial_year_id
        # Ensure the mock company and financial year objects are added if not present in DB
        # For testing, you'd create them properly
        try:
            Company.objects.get(comp_id=company_id)
        except Company.DoesNotExist:
            self.company.save()
        try:
            FinancialYear.objects.get(fin_year_id=financial_year_id)
        except FinancialYear.DoesNotExist:
            FinancialYear.objects.create(fin_year_id=financial_year_id, fin_year="2023-2024")


class WorkOrderReleaseListView(LoginRequiredMixin, View):
    """
    Main view for Work Order Release page.
    Renders the search form and an initial empty table container.
    """
    template_name = 'sales/workorder_release/list.html'

    def get(self, request, *args, **kwargs):
        # Mock user for demo purposes. Replace with actual user object in production.
        # This simulates Session["compid"] and Session["finyear"]
        request.user = MockUser(
            username=request.user.username if request.user.is_authenticated else 'testuser',
            company_id=1, # Example: replace with actual company ID from session/user profile
            financial_year_id=2024 # Example: replace with actual financial year ID
        )

        form = WorkOrderSearchForm(request.GET or None)
        # Initially, no data is loaded directly, table partial loads via HTMX
        return render(request, self.template_name, {'form': form})


class WorkOrderTablePartialView(LoginRequiredMixin, ListView):
    """
    Partial view to render the Work Order table, loaded via HTMX.
    Handles search, pagination, and sorting.
    """
    model = WorkOrderMaster
    template_name = 'sales/workorder_release/_workorder_table.html'
    context_object_name = 'work_orders'
    paginate_by = 20 # Corresponds to PageSize="20"

    def get_queryset(self):
        # Mock user for demo purposes. Replace with actual user object in production.
        # This simulates Session["compid"] and Session["finyear"]
        self.request.user = MockUser(
            username=self.request.user.username if self.request.user.is_authenticated else 'testuser',
            company_id=1, # Example: replace with actual company ID from session/user profile
            financial_year_id=2024 # Example: replace with actual financial year ID
        )

        # Retrieve search parameters from GET request
        search_type = self.request.GET.get('search_type', 'Select')
        search_value_text = self.request.GET.get('search_value_text', '')
        search_value_id = self.request.GET.get('search_value_id', '')

        # Use the WorkOrderMaster's class method for business logic
        queryset = WorkOrderMaster.get_work_orders(
            self.request.user,
            search_type,
            search_value_text if search_type == '0' else search_value_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass current search parameters back to the template for DataTables initialization if needed
        context['search_type'] = self.request.GET.get('search_type', 'Select')
        context['search_value_text'] = self.request.GET.get('search_value_text', '')
        context['search_value_id'] = self.request.GET.get('search_value_id', '')
        return context


class SearchFieldsToggleView(View):
    """
    HTMX endpoint to toggle visibility of search input fields based on dropdown selection.
    This replaces the AutoPostBack logic.
    """
    def post(self, request, *args, **kwargs):
        search_type = request.POST.get('search_type', 'Select')
        
        context = {
            'search_type': search_type,
            'search_value_text_name': WorkOrderSearchForm().fields['search_value_text'].widget.attrs['name'],
            'search_value_id_name': WorkOrderSearchForm().fields['search_value_id'].widget.attrs['name'],
            'search_value_text_id': WorkOrderSearchForm().fields['search_value_text'].widget.attrs['id'],
            'search_value_id_id': WorkOrderSearchForm().fields['search_value_id'].widget.attrs['id'],
        }
        return render(request, 'sales/workorder_release/_search_inputs.html', context)


class CustomerAutocompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for customer name autocomplete suggestions.
    Replicates the static web method `sql` from ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '').strip()
        
        # Mock user for demo purposes. Replace with actual user object in production.
        request.user = MockUser(
            username=request.user.username if request.user.is_authenticated else 'testuser',
            company_id=1, # Example: replace with actual company ID from session/user profile
            financial_year_id=2024 # Example: financial year is not directly used here
        )

        if not prefix_text:
            return HttpResponse("") # Return empty response if no prefix

        customers = CustomerMaster.objects.filter(
            customer_name__istartswith=prefix_text,
            company=request.user.company # Filter by user's company
        ).values_list('customer_name', 'customer_id')[:10] # Limit to 10 results

        suggestions = [f"{name} [{id}]" for name, id in customers]
        
        # HTMX typically expects HTML for swapping, but for autocomplete,
        # we can render a simple list or use JSON if Alpine/JS processes it.
        # For simplicity and direct HTMX swap, we'll return an HTML list.
        # This can be made more sophisticated with dynamic styling.
        html_suggestions = "".join([
            f"<div class='p-2 hover:bg-gray-200 cursor-pointer' "
            f"hx-trigger='click' hx-swap='none' "
            f="_='on click "
            f"set #TxtSearchValue.value to \"{name}\" "
            f"then remove .block from #autocomplete-results "
            f"then remove .active from #TxtSearchValue'>"
            f"{name} [{id}]</div>"
            for name, id in customers
        ])
        
        # Add Alpine.js to handle click outside or escape key to close.
        return HttpResponse(f"""
            <div x-data="{ open: true }" @click.outside="open = false" x-show="open" 
                 class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
                 id="autocomplete-results-container">
                {html_suggestions}
            </div>
        """)

# --- Placeholders for CRUD views to match template structure ---
# These are not directly used by WorkOrder_Release.aspx, but good practice for full CRUD apps
class WorkOrderReleaseCreateView(View): # Dummy view, not used by source
    def get(self, request): return HttpResponse("Add form content (placeholder)")
    def post(self, request): return HttpResponse(status=204)

class WorkOrderReleaseUpdateView(View): # Dummy view, not used by source
    def get(self, request, pk): return HttpResponse(f"Edit form content for {pk} (placeholder)")
    def post(self, request, pk): return HttpResponse(status=204)

class WorkOrderReleaseDeleteView(View): # Dummy view, not used by source
    def get(self, request, pk): return HttpResponse(f"Delete confirm for {pk} (placeholder)")
    def post(self, request, pk): return HttpResponse(status=204)
```

#### 4.4 Templates (`sales/templates/sales/workorder_release/`)

We'll create the main list template, the table partial, and the search input partial.

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg mb-6">
        <h2 class="text-xl font-bold">Work Order - Release</h2>
    </div>

    <div class="bg-white p-6 rounded-b-lg shadow-lg mb-6">
        <form id="search-form" hx-get="{% url 'sales:workorder_table' %}" hx-target="#workorder-table-container" hx-swap="innerHTML" hx-trigger="submit">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end mb-4">
                <div>
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_type }}
                </div>
                <div id="search-inputs-container" class="md:col-span-2 relative">
                    <!-- HTMX will swap content here based on dropdown selection -->
                    <div class="text-center">
                        <div id="search-indicator" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 hidden htmx-request"></div>
                    </div>
                    {% include 'sales/workorder_release/_search_inputs.html' with search_type=form.search_type.value search_value_text_name=form.search_value_text.name search_value_id_name=form.search_value_id.name search_value_text_id=form.search_value_text.id_for_label search_value_id_id=form.search_value_id.id_for_label %}
                </div>
                <div>
                    <button type="submit" class="redbox bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded w-full">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <div id="workorder-table-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'sales:workorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for messages or other UI state if needed
        Alpine.data('messageHandler', () => ({
            message: '',
            show: false,
            init() {
                // Listen for custom events to show messages if needed
            }
        }));
    });

    // Handle HTMX after settle to reinitialize DataTables
    document.body.addEventListener('htmx:afterSettle', function(evt) {
        if (evt.detail.target.id === 'workorder-table-container') {
            const table = $('#workorder-release-table').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers" // For full pagination controls
            });
        }
    });

    // Alpine.js setup for autocomplete results to close on click outside
    document.addEventListener('alpine:init', () => {
        Alpine.data('autocomplete', () => ({
            showResults: false,
            init() {
                this.$watch('$store.autocomplete.results', (val) => {
                    this.showResults = val.length > 0;
                });
            },
            selectSuggestion(value) {
                document.getElementById('TxtSearchValue').value = value;
                this.showResults = false;
            }
        }));
    });

</script>
{% endblock %}
```

##### `_workorder_table.html` (Partial for HTMX)

```html
<div class="overflow-x-auto bg-white p-4 rounded-lg shadow-lg">
    <table id="workorder-release-table" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if work_orders %}
                {% for wo in work_orders %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.financial_year.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.customer.customer_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.customer.customer_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.enquiry_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.po_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <a href="{% url 'sales:workorder_report' wo.customer.customer_id wo.enquiry_id wo.id wo.po_no wo.work_order_no %}" class="text-blue-600 hover:underline">
                            {{ wo.work_order_no }}
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.display_sys_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.generated_by.employee_name }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 text-center text-red-500 font-bold text-lg">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // This script runs when the partial is loaded, ensuring DataTables is re-initialized
    $(document).ready(function() {
        $('#workorder-release-table').DataTable({
            "pageLength": {{ page_obj.per_page }}, // Use actual page size from Django Paginator
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "searching": true, // Enable DataTables built-in search box
            "ordering": true   // Enable DataTables built-in sorting
            // Note: The C# server-side filtering is now client-side via DataTables
            // or explicitly handled by HTMX search form.
            // If the original app performed server-side searching/sorting/paging,
            // DataTables needs to be configured for server-side processing.
            // For now, client-side is assumed for simplicity given HTMX partial load.
        });
    });
</script>
```

##### `_search_inputs.html` (Partial for HTMX)

```html
{% comment %}
This template is swapped into the #search-inputs-container based on the dropdown selection.
It mimics the ASP.NET AutoPostBack behavior for field visibility.
{% endcomment %}

{% if search_type == '0' %} {# Customer Name #}
    <div class="relative">
        <label for="{{ search_value_text_id }}" class="sr-only">Customer Name</label>
        <input type="text" name="{{ search_value_text_name }}" id="{{ search_value_text_id }}"
               class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
               placeholder="Enter Customer Name" autocomplete="off"
               hx-get="{% url 'sales:customer_autocomplete' %}"
               hx-trigger="keyup changed delay:500ms, search"
               hx-target="#autocomplete-results"
               hx-swap="innerHTML"
               hx-indicator="#customer-autocomplete-indicator"
               value="{{ request.GET.search_value_text }}"
               _="on keyup add .block to #autocomplete-results unless value is empty then add .active to #TxtSearchValue else remove .active from #TxtSearchValue"
        >
        <div id="autocomplete-results" class="hidden"></div>
        <div id="customer-autocomplete-indicator" class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 absolute right-3 top-1/2 -mt-2 hidden htmx-request"></div>
    </div>
{% elif search_type in '123Select' %} {# Enquiry No, PO No, WO No, or Select #}
    <label for="{{ search_value_id_id }}" class="sr-only">Search Value</label>
    <input type="text" name="{{ search_value_id_name }}" id="{{ search_value_id_id }}"
           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
           placeholder="Enter {{ search_type|lookup_search_label }}"
           value="{{ request.GET.search_value_id }}"
    >
{% endif %}

{% comment %} Custom filter for the placeholder text based on search_type {% endcomment %}
{% load custom_filters %}
```

##### `_form.html` (Placeholder, not used by source)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order (Placeholder)</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            <!-- This would normally contain fields from PlaceholderForm. -->
            <p>This form is a placeholder as no direct add/edit functionality was found for Work Orders on this page.</p>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save (Placeholder)
            </button>
        </div>
    </form>
</div>
```

##### `_confirm_delete.html` (Placeholder, not used by source)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion (Placeholder)</h3>
    <p class="text-gray-700">Are you sure you want to delete this Work Order?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{{ request.path }}" 
            hx-swap="none">
            Delete (Placeholder)
        </button>
    </div>
</div>
```

##### `sales/templatetags/custom_filters.py` (New file for custom filter)

```python
from django import template

register = template.Library()

@register.filter
def lookup_search_label(value):
    """Returns a descriptive label for the search input placeholder."""
    labels = {
        '0': 'Customer Name',
        '1': 'Enquiry No',
        '2': 'PO No',
        '3': 'WO No',
        'Select': 'Search Value', # Default for 'Select'
    }
    return labels.get(value, 'Search Value')
```
*Remember to add `sales.templatetags` to `INSTALLED_APPS` and load the filter in the template: `{% load custom_filters %}`.*

#### 4.5 URLs (`sales/urls.py`)

Define the URL patterns for the views within the `sales` app.

```python
from django.urls import path
from .views import (
    WorkOrderReleaseListView, WorkOrderTablePartialView,
    SearchFieldsToggleView, CustomerAutocompleteView,
    # Placeholder views for general CRUD template structure, though not used by source .aspx
    WorkOrderReleaseCreateView, WorkOrderReleaseUpdateView, WorkOrderReleaseDeleteView
)

app_name = 'sales' # Namespace for URLs

urlpatterns = [
    # Main Work Order Release page
    path('workorder-release/', WorkOrderReleaseListView.as_view(), name='workorder_release_list'),
    
    # HTMX endpoint for the Work Order table partial
    path('workorder-release/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    
    # HTMX endpoint for toggling search input fields visibility
    path('workorder-release/search-fields/', SearchFieldsToggleView.as_view(), name='workorder_release_search_fields'),
    
    # HTMX endpoint for customer autocomplete suggestions
    path('workorder-release/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # URL for WorkOrder_ReleaseRPT.aspx link (adjust parameters as needed for actual report view)
    # This assumes WorkOrder_ReleaseRPT.aspx is also migrated to a Django view, e.g., WorkOrderReportView
    path('workorder-release/report/<str:customer_id>/<str:enquiry_id>/<int:id>/<str:po_no>/<str:wo_no>/', 
         WorkOrderReleaseCreateView.as_view(), name='workorder_report'), # Placeholder for report view

    # Placeholder URLs for general CRUD operations (not directly from source .aspx)
    path('workorder-release/add/', WorkOrderReleaseCreateView.as_view(), name='workorder_release_add'),
    path('workorder-release/edit/<int:pk>/', WorkOrderReleaseUpdateView.as_view(), name='workorder_release_edit'),
    path('workorder-release/delete/<int:pk>/', WorkOrderReleaseDeleteView.as_view(), name='workorder_release_delete'),
]
```
*Note: You would also need to include these URLs in your project's main `urls.py` file, e.g., `path('sales/', include('sales.urls'))`.*

#### 4.6 Tests (`sales/tests.py`)

Comprehensive tests for models and views. The `MockUser` is crucial here for simulating session-like data used in the views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import QueryDict
from .models import WorkOrderMaster, CustomerMaster, FinancialYear, EmployeeStaff, Company
from .views import MockUser # Import the MockUser for testing purposes

class SalesModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.company = Company.objects.create(comp_id=1, comp_name="Test Company")
        cls.financial_year = FinancialYear.objects.create(fin_year_id=2024, fin_year="2023-2024")
        cls.employee = EmployeeStaff.objects.create(emp_id=101, title="Mr.", employee_name="Test Employee", company=cls.company)
        cls.customer1 = CustomerMaster.objects.create(customer_id="CUST001", customer_name="Alpha Customer", company=cls.company)
        cls.customer2 = CustomerMaster.objects.create(customer_id="CUST002", customer_name="Beta Customer", company=cls.company)

        # Work Order instances
        WorkOrderMaster.objects.create(
            id=1, enquiry_id="ENQ001", customer=cls.customer1, work_order_no="WO-001",
            po_no="PO-A-001", generated_by=cls.employee, financial_year=cls.financial_year,
            system_date="01-15-2024", close_open=0, company=cls.company
        )
        WorkOrderMaster.objects.create(
            id=2, enquiry_id="ENQ002", customer=cls.customer2, work_order_no="WO-002",
            po_no="PO-B-002", generated_by=cls.employee, financial_year=cls.financial_year,
            system_date="02-20-2024", close_open=0, company=cls.company
        )
        WorkOrderMaster.objects.create(
            id=3, enquiry_id="ENQ003", customer=cls.customer1, work_order_no="WO-003",
            po_no="PO-C-003", generated_by=cls.employee, financial_year=cls.financial_year,
            system_date="03-10-2024", close_open=1, company=cls.company # Closed WO
        )

    def test_work_order_creation(self):
        wo = WorkOrderMaster.objects.get(id=1)
        self.assertEqual(wo.work_order_no, "WO-001")
        self.assertEqual(wo.customer.customer_name, "Alpha Customer")
        self.assertEqual(wo.financial_year.fin_year, "2023-2024")
        self.assertEqual(wo.generated_by.employee_name, "Test Employee")
        self.assertEqual(wo.company.comp_name, "Test Company")

    def test_display_sys_date_property(self):
        wo = WorkOrderMaster.objects.get(id=1)
        self.assertEqual(wo.display_sys_date, "15/01/2024") # MM-DD-YYYY to DD/MM/YYYY
        
        wo_no_date = WorkOrderMaster(id=4, work_order_no="WO-004", customer=self.customer1,
                                     financial_year=self.financial_year, company=self.company,
                                     close_open=0, system_date=None)
        self.assertEqual(wo_no_date.display_sys_date, "")
        
        wo_invalid_date = WorkOrderMaster(id=5, work_order_no="WO-005", customer=self.customer1,
                                          financial_year=self.financial_year, company=self.company,
                                          close_open=0, system_date="InvalidDate")
        self.assertEqual(wo_invalid_date.display_sys_date, "InvalidDate")

    def test_get_customer_id_by_name(self):
        customer_id = CustomerMaster.get_customer_id_by_name("Alpha Customer", self.company.comp_id)
        self.assertEqual(customer_id, "CUST001")
        customer_id_none = CustomerMaster.get_customer_id_by_name("Non Existent", self.company.comp_id)
        self.assertIsNone(customer_id_none)
        
    def test_get_work_orders_all_open(self):
        # Mock user for context
        mock_user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
        work_orders = WorkOrderMaster.get_work_orders(mock_user, 'Select', '')
        self.assertEqual(work_orders.count(), 2) # WO-001, WO-002 (WO-003 is closed)

    def test_get_work_orders_by_customer_name(self):
        mock_user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
        work_orders = WorkOrderMaster.get_work_orders(mock_user, '0', 'Alpha Customer')
        self.assertEqual(work_orders.count(), 1)
        self.assertEqual(work_orders.first().work_order_no, "WO-001")
        
        work_orders_no_match = WorkOrderMaster.get_work_orders(mock_user, '0', 'No Such Customer')
        self.assertEqual(work_orders_no_match.count(), 0)

    def test_get_work_orders_by_enquiry_id(self):
        mock_user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
        work_orders = WorkOrderMaster.get_work_orders(mock_user, '1', 'ENQ002')
        self.assertEqual(work_orders.count(), 1)
        self.assertEqual(work_orders.first().work_order_no, "WO-002")

    def test_get_work_orders_by_po_no(self):
        mock_user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
        work_orders = WorkOrderMaster.get_work_orders(mock_user, '2', 'PO-A-001')
        self.assertEqual(work_orders.count(), 1)
        self.assertEqual(work_orders.first().work_order_no, "WO-001")

    def test_get_work_orders_by_wo_no(self):
        mock_user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
        work_orders = WorkOrderMaster.get_work_orders(mock_user, '3', 'WO-001')
        self.assertEqual(work_orders.count(), 1)
        self.assertEqual(work_orders.first().work_order_no, "WO-001")

class WorkOrderReleaseViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(comp_id=1, comp_name="Test Company")
        cls.financial_year = FinancialYear.objects.create(fin_year_id=2024, fin_year="2023-2024")
        cls.employee = EmployeeStaff.objects.create(emp_id=101, title="Mr.", employee_name="Test Employee", company=cls.company)
        cls.customer1 = CustomerMaster.objects.create(customer_id="CUST001", customer_name="Alpha Customer", company=cls.company)
        cls.customer2 = CustomerMaster.objects.create(customer_id="CUST002", customer_name="Beta Customer", company=cls.company)

        WorkOrderMaster.objects.create(
            id=1, enquiry_id="ENQ001", customer=cls.customer1, work_order_no="WO-001",
            po_no="PO-A-001", generated_by=cls.employee, financial_year=cls.financial_year,
            system_date="01-15-2024", close_open=0, company=cls.company
        )
        WorkOrderMaster.objects.create(
            id=2, enquiry_id="ENQ002", customer=cls.customer2, work_order_no="WO-002",
            po_no="PO-B-002", generated_by=cls.employee, financial_year=cls.financial_year,
            system_date="02-20-2024", close_open=0, company=cls.company
        )

    def setUp(self):
        self.client = Client()
        # Mock login for LoginRequiredMixin
        # In a real app, you'd use self.client.force_login(user)
        # For this test, we patch the view's dispatch method to inject a mock user
        from sales.views import WorkOrderReleaseListView, WorkOrderTablePartialView, CustomerAutocompleteView
        self.original_dispatch = WorkOrderReleaseListView.dispatch
        self.original_table_dispatch = WorkOrderTablePartialView.dispatch
        self.original_autocomplete_dispatch = CustomerAutocompleteView.dispatch

        def mock_dispatch(request, *args, **kwargs):
            request.user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
            return self.original_dispatch(request, *args, **kwargs)
        WorkOrderReleaseListView.dispatch = mock_dispatch

        def mock_table_dispatch(request, *args, **kwargs):
            request.user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
            return self.original_table_dispatch(request, *args, **kwargs)
        WorkOrderTablePartialView.dispatch = mock_table_dispatch

        def mock_autocomplete_dispatch(request, *args, **kwargs):
            request.user = MockUser(username='testuser', company_id=self.company.comp_id, financial_year_id=self.financial_year.fin_year_id)
            return self.original_autocomplete_dispatch(request, *args, **kwargs)
        CustomerAutocompleteView.dispatch = mock_autocomplete_dispatch

    def tearDown(self):
        from sales.views import WorkOrderReleaseListView, WorkOrderTablePartialView, CustomerAutocompleteView
        WorkOrderReleaseListView.dispatch = self.original_dispatch
        WorkOrderTablePartialView.dispatch = self.original_table_dispatch
        CustomerAutocompleteView.dispatch = self.original_autocomplete_dispatch


    def test_workorder_release_list_view(self):
        response = self.client.get(reverse('sales:workorder_release_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder_release/list.html')
        self.assertIn('form', response.context) # Check if form is passed to context

    def test_workorder_table_partial_view_no_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:workorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder_release/_workorder_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 2) # Both open WOs

    def test_workorder_table_partial_view_search_customer_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:workorder_table') + '?search_type=0&search_value_text=Alpha Customer', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder_release/_workorder_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 1)
        self.assertEqual(response.context['work_orders'][0].work_order_no, "WO-001")

    def test_workorder_table_partial_view_search_wo_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:workorder_table') + '?search_type=3&search_value_id=WO-002', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder_release/_workorder_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 1)
        self.assertEqual(response.context['work_orders'][0].work_order_no, "WO-002")

    def test_search_fields_toggle_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Simulate POST data for search_type
        data = QueryDict(mutable=True)
        data.update({'search_type': '0'}) # Customer Name
        response = self.client.post(reverse('sales:workorder_release_search_fields'), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder_release/_search_inputs.html')
        self.assertContains(response, 'name="search_value_text"') # Should show customer name field
        self.assertNotContains(response, 'name="search_value_id"')

        data.update({'search_type': '1'}) # Enquiry No
        response = self.client.post(reverse('sales:workorder_release_search_fields'), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/workorder_release/_search_inputs.html')
        self.assertContains(response, 'name="search_value_id"') # Should show ID field
        self.assertNotContains(response, 'name="search_value_text"')

    def test_customer_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:customer_autocomplete') + '?prefixText=Alph', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Customer [CUST001]')
        self.assertNotContains(response, 'Beta Customer')

        response = self.client.get(reverse('sales:customer_autocomplete') + '?prefixText=Bet', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Beta Customer [CUST002]')

        response = self.client.get(reverse('sales:customer_autocomplete') + '?prefixText=XYZ', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'Alpha Customer') # Should be empty
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search and Table Refresh:**
    *   The main search form uses `hx-get` to `{% url 'sales:workorder_table' %}` targeting `#workorder-table-container`. This means submitting the form will load the new table content without a full page refresh.
    *   `hx-trigger="load, refreshWorkOrderList from:body"` on `#workorder-table-container` ensures the table loads on initial page load and can be refreshed by a custom event (e.g., after a hypothetical CRUD operation from another module, though not present here).
    *   The `search_type` dropdown has `hx-post` to `{% url 'sales:workorder_release_search_fields' %}` which swaps the input fields in `#search-inputs-container`, mimicking `AutoPostBack`.
    *   Customer auto-complete uses `hx-get` to `{% url 'sales:customer_autocomplete' %}` with `keyup changed delay:500ms` trigger to fetch suggestions dynamically.
*   **Alpine.js for UI State and Autocomplete:**
    *   Alpine.js (via `_`) in templates is used for minor UI enhancements, such as showing/hiding autocomplete results and setting input values on click, and basic loading indicators.
    *   The autocomplete results HTML directly uses `x-data` and `@click.outside` to control its visibility.
*   **DataTables Implementation:**
    *   The `_workorder_table.html` partial includes a `script` block to initialize DataTables on the `workorder-release-table`.
    *   `htmx:afterSettle` event listener in `list.html` ensures DataTables is re-initialized whenever the table content is swapped by HTMX.
    *   Pagination and sorting are handled by DataTables client-side for loaded data. For very large datasets, DataTables can be configured for server-side processing, requiring additional HTMX endpoints for data.

### Final Notes

*   **Placeholders:** The `PlaceholderForm`, `WorkOrderReleaseCreateView`, `WorkOrderReleaseUpdateView`, `WorkOrderReleaseDeleteView`, and their corresponding templates (`_form.html`, `_confirm_delete.html`) are included to demonstrate the standard Django CBV and HTMX pattern for CRUD, even though the original ASP.NET `.aspx` file primarily focused on listing and searching. This ensures a consistent approach if full CRUD for work orders is implemented elsewhere.
*   **Session Data:** The `MockUser` class and `request.user` attribute assignment in views are critical for testing and demonstrating how to handle `Session["compid"]` and `Session["finyear"]` in Django. In a production environment, you would integrate this with Django's authentication system and user profiles.
*   **Error Handling:** The C# code uses `try-catch` blocks which are largely replaced by Django's robust form validation and built-in error handling. Specific business logic errors should be handled via form `clean` methods or model methods.
*   **HyperLinkField:** The `HyperLinkField` to `WorkOrder_ReleaseRPT.aspx` is mapped to a Django `path` and will require its own Django view and template (e.g., `workorder_report`).
*   **SQL `REPLACE(CONVERT(varchar, CONVERT(datetime, ...), 103), '/', '-')`**: The complex date conversion logic in C# SQL query for `SysDate` is handled by a `@property` `display_sys_date` in the `WorkOrderMaster` model, keeping the raw string in the database and formatting it in Python for display. If the database schema can be modified, it's highly recommended to store dates as native `DATE` or `DATETIME` types.
*   **`yui-datatable.css`:** The original CSS suggests an older YUI DataTable. The plan shifts to modern DataTables.net and relies on Tailwind CSS for styling, which is aligned with modern Django practices.

This comprehensive plan provides a clear, actionable roadmap for migrating your Work Order Release module to a modern Django application, emphasizing automation and maintainability.