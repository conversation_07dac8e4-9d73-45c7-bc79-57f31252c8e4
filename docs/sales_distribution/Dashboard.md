## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

The provided ASP.NET `Dashboard.aspx` and its code-behind `Dashboard.aspx.cs` are largely empty, serving primarily as placeholders or master page content regions. They do not contain explicit database interactions, UI controls, or business logic.

To provide a comprehensive modernization plan that demonstrates the full capabilities of Django and the outlined migration strategy, we will infer a common business entity typically found in a "Sales Distribution Dashboard" module: a **Sale** record. This allows us to illustrate the migration of CRUD operations, data display, and dynamic interactions as if they were present in a more complex ASP.NET page.

This approach ensures that while the source code is minimal, the generated Django solution provides a robust, real-world example of how such a module *would* be modernized.

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is a minimal placeholder, we infer a typical table structure for a "Sales Distribution" module. We'll assume a `sales_sale` table.

- **[TABLE_NAME]**: `sales_sale`
- **Columns**:
    - `id` (Primary Key, auto-incremented)
    - `customer_name` (e.g., NVARCHAR(255))
    - `sale_date` (e.g., DATETIME)
    - `amount` (e.g., DECIMAL(10, 2))
    - `status` (e.g., NVARCHAR(50), with values like 'Pending', 'Completed', 'Canceled')

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the lack of explicit functionality in the provided ASP.NET code, we will assume standard CRUD operations for the inferred `sales_sale` table, typical for a dashboard module:

-   **Read**: Display a list of sales records on the dashboard.
-   **Create**: Add new sales records.
-   **Update**: Modify existing sales records.
-   **Delete**: Remove sales records.
-   **Validation**: Ensure required fields are present and data types are correct (e.g., amount is a number, date is valid).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Based on the assumed backend functionality, we infer the following UI components that would typically be present in the ASP.NET application and their Django equivalents:

-   **GridView**: To display a list of `Sale` records. This will be replaced by a Django template rendering a `<table>` with DataTables for client-side functionality (searching, sorting, pagination).
-   **TextBoxes**: For capturing `customer_name`, `amount`, and `sale_date`. These will map to Django `forms.TextInput` and `forms.DateInput` widgets.
-   **DropDownList**: For selecting `status`. This will map to a Django `forms.Select` widget.
-   **Buttons/LinkButtons**: For triggering actions like "Add New Sale," "Edit," and "Delete." These will be replaced by standard `<button>` elements in Django templates, enhanced with HTMX attributes for dynamic interactions (e.g., loading forms into a modal).
-   **MasterPage**: The ASP.NET `MasterPage.master` implies a shared layout. This is replaced by Django's template inheritance using `core/base.html`.
-   **loadingNotifier.js**: A client-side script for showing a loading indicator. This functionality will be natively handled by HTMX's `hx-indicator` or Alpine.js for finer control, eliminating the need for a separate JS file.

## Step 4: Generate Django Code

The `Module_SalesDistribution_Dashboard` naming suggests a Django app named `sales`.

### 4.1 Models (`sales/models.py`)

```python
from django.db import models
from django.utils import timezone

class Sale(models.Model):
    # Django will automatically create an 'id' primary key field.
    customer_name = models.CharField(
        max_length=255, 
        db_column='customer_name',
        verbose_name='Customer Name'
    )
    sale_date = models.DateField(
        db_column='sale_date',
        default=timezone.now,
        verbose_name='Sale Date'
    )
    amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        db_column='amount',
        verbose_name='Amount'
    )
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('COMPLETED', 'Completed'),
        ('CANCELED', 'Canceled'),
    ]
    status = models.CharField(
        max_length=50, 
        choices=STATUS_CHOICES, 
        default='PENDING', 
        db_column='status',
        verbose_name='Status'
    )

    class Meta:
        managed = False  # Set to True if Django should manage this table, False if pre-existing
        db_table = 'sales_sale' # Assumed table name from existing DB
        verbose_name = 'Sale'
        verbose_name_plural = 'Sales'
        ordering = ['-sale_date']

    def __str__(self):
        return f"Sale to {self.customer_name} on {self.sale_date} for ${self.amount}"
        
    def calculate_commission(self, rate=0.05):
        """
        Calculates commission for the sale. This is an example of business logic
        residing in the model (fat model approach).
        """
        if self.status == 'COMPLETED':
            return self.amount * models.Decimal(str(rate))
        return models.Decimal('0.00')

    def update_status(self, new_status):
        """
        Updates the status of the sale. Example of a model method for state changes.
        """
        if new_status in [choice[0] for choice in self.STATUS_CHOICES]:
            self.status = new_status
            self.save()
            return True
        return False
```

### 4.2 Forms (`sales/forms.py`)

```python
from django import forms
from .models import Sale

class SaleForm(forms.ModelForm):
    class Meta:
        model = Sale
        fields = ['customer_name', 'sale_date', 'amount', 'status']
        widgets = {
            'customer_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter customer name'
            }),
            'sale_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # HTML5 date picker
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01', # Allow decimal input
                'placeholder': 'Enter amount'
            }),
            'status': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive number.")
        return amount
```

### 4.3 Views (`sales/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Sale
from .forms import SaleForm

# Helper mixin for HTMX responses (DRY)
class HTMXMessageResponseMixin:
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'{self.model.__name__} saved successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refresh{self.model.__name__}List' # Trigger refresh on list
                }
            )
        return response

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'{self.model.__name__} deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refresh{self.model.__name__}List' # Trigger refresh on list
                }
            )
        return response

class SaleListView(ListView):
    model = Sale
    template_name = 'sales/sale/list.html'
    context_object_name = 'sales' # Renamed for clarity

class SaleTablePartialView(ListView):
    """
    Renders only the table partial, designed to be fetched via HTMX.
    """
    model = Sale
    template_name = 'sales/sale/_sale_table.html'
    context_object_name = 'sales'

class SaleCreateView(HTMXMessageResponseMixin, CreateView):
    model = Sale
    form_class = SaleForm
    template_name = 'sales/sale/_sale_form.html' # Render partial for modal
    success_url = reverse_lazy('sale_list') # Fallback if not HTMX

class SaleUpdateView(HTMXMessageResponseMixin, UpdateView):
    model = Sale
    form_class = SaleForm
    template_name = 'sales/sale/_sale_form.html' # Render partial for modal
    success_url = reverse_lazy('sale_list') # Fallback if not HTMX

class SaleDeleteView(HTMXMessageResponseMixin, DeleteView):
    model = Sale
    template_name = 'sales/sale/_sale_confirm_delete.html' # Render partial for modal
    success_url = reverse_lazy('sale_list') # Fallback if not HTMX
```

### 4.4 Templates

**List Template (`sales/sale/list.html`)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ showModal: false }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sales Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200"
            hx-get="{% url 'sale_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            @click="showModal = true"
            >
            Add New Sale
        </button>
    </div>
    
    <div id="saleTable-container"
         hx-trigger="load, refreshSaleList from:body"
         hx-get="{% url 'sale_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-xl">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Sales Data...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirm delete -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-90"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-90"
         @click.away="showModal = false"
         style="display: none;"
    >
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full relative"
             @htmx:afterRequest="if (event.detail.successful) showModal = true; else showModal = false"
             @htmx:afterSwap="if (!event.detail.target.innerHTML.trim()) showModal = false"
             >
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Listen for HTMX triggers to close modal after successful form submissions/deletions
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent' && !evt.detail.target.innerHTML.trim()) {
            // If modal content is cleared (e.g., after a successful form submission returning 204)
            // Alpine.js showModal should be set to false.
            // This is handled by the @htmx:afterSwap on modalContent itself.
            // Also, close the modal explicitly if a 204 status is received.
            // This happens when HTMX sends HX-Trigger and then expects a 204 status code.
            // We can also trigger an Alpine event to close the modal
            const modalElement = document.getElementById('modal');
            if (modalElement && modalElement.__alpine && modalElement.__alpine.showModal) {
                 modalElement.__alpine.showModal = false;
            }
        }
    });

    // Event listener for custom close events from inside modal partials
    document.addEventListener('closeModal', () => {
        const modalElement = document.getElementById('modal');
        if (modalElement && modalElement.__alpine && modalElement.__alpine.showModal) {
             modalElement.__alpine.showModal = false;
        }
    });
</script>
{% endblock %}
```

**Table Partial Template (`sales/sale/_sale_table.html`)**

```html
<div class="overflow-x-auto">
    <table id="saleTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sale Date</th>
                <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for sale in sales %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ sale.customer_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ sale.sale_date|date:"Y-m-d" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900 text-right">${{ sale.amount|floatformat:2 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if sale.status == 'COMPLETED' %}bg-green-100 text-green-800
                        {% elif sale.status == 'PENDING' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ sale.get_status_display }}
                    </span>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900 text-right">${{ sale.calculate_commission|floatformat:2 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'sale_edit' sale.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        @click="$dispatch('open-modal')"
                    >
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'sale_delete' sale.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        @click="$dispatch('open-modal')"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No sales records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once per HTMX load
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#saleTable')) {
            $('#saleTable').DataTable().destroy();
        }
        
        $('#saleTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] }, // Disable sorting on SN and Actions
                { "searchable": false, "targets": [0, 6] } // Disable searching on SN and Actions
            ]
        });
    });
</script>
```

**Form Partial Template (`sales/sale/_sale_form.html`)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Sale</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) $dispatch('close-modal');">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                @click="$dispatch('close-modal')"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
                Save
            </button>
        </div>
    </form>
</div>
```

**Delete Confirmation Partial Template (`sales/sale/_sale_confirm_delete.html`)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Delete</h3>
    <p class="text-gray-700 text-lg mb-8">Are you sure you want to delete the sale for <span class="font-bold">{{ sale.customer_name }}</span> on <span class="font-bold">{{ sale.sale_date|date:"Y-m-d" }}</span> with amount <span class="font-bold">${{ sale.amount|floatformat:2 }}</span>?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) $dispatch('close-modal');">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                @click="$dispatch('close-modal')"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`sales/urls.py`)

```python
from django.urls import path
from .views import SaleListView, SaleTablePartialView, SaleCreateView, SaleUpdateView, SaleDeleteView

urlpatterns = [
    path('sales/', SaleListView.as_view(), name='sale_list'),
    path('sales/table/', SaleTablePartialView.as_view(), name='sale_table'), # HTMX endpoint for table
    path('sales/add/', SaleCreateView.as_view(), name='sale_add'),
    path('sales/edit/<int:pk>/', SaleUpdateView.as_view(), name='sale_edit'),
    path('sales/delete/<int:pk>/', SaleDeleteView.as_view(), name='sale_delete'),
]
```

### 4.6 Tests (`sales/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from decimal import Decimal
from .models import Sale

class SaleModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.sale1 = Sale.objects.create(
            customer_name='Alice Smith',
            sale_date=date(2023, 1, 15),
            amount=Decimal('150.75'),
            status='COMPLETED'
        )
        cls.sale2 = Sale.objects.create(
            customer_name='Bob Johnson',
            sale_date=date(2023, 2, 1),
            amount=Decimal('200.00'),
            status='PENDING'
        )
  
    def test_sale_creation(self):
        self.assertEqual(self.sale1.customer_name, 'Alice Smith')
        self.assertEqual(self.sale1.sale_date, date(2023, 1, 15))
        self.assertEqual(self.sale1.amount, Decimal('150.75'))
        self.assertEqual(self.sale1.status, 'COMPLETED')
        self.assertIsNotNone(self.sale1.pk)
        
    def test_str_representation(self):
        expected_str = f"Sale to Alice Smith on 2023-01-15 for $150.75"
        self.assertEqual(str(self.sale1), expected_str)
        
    def test_verbose_name_plural(self):
        self.assertEqual(Sale._meta.verbose_name_plural, 'Sales')
        
    def test_calculate_commission_completed_sale(self):
        commission = self.sale1.calculate_commission(rate=0.10)
        self.assertEqual(commission, Decimal('15.08')) # 150.75 * 0.10
        
    def test_calculate_commission_pending_sale(self):
        commission = self.sale2.calculate_commission(rate=0.10)
        self.assertEqual(commission, Decimal('0.00')) # Pending sales get 0 commission
        
    def test_update_status_valid(self):
        initial_status = self.sale2.status
        self.assertTrue(self.sale2.update_status('COMPLETED'))
        self.sale2.refresh_from_db()
        self.assertEqual(self.sale2.status, 'COMPLETED')
        
    def test_update_status_invalid(self):
        initial_status = self.sale2.status
        self.assertFalse(self.sale2.update_status('INVALID_STATUS'))
        self.sale2.refresh_from_db()
        self.assertEqual(self.sale2.status, initial_status) # Status should not change

class SaleViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.sale1 = Sale.objects.create(
            customer_name='Charlie Brown',
            sale_date=date(2023, 3, 1),
            amount=Decimal('300.00'),
            status='PENDING'
        )
        cls.sale2 = Sale.objects.create(
            customer_name='Lucy Van Pelt',
            sale_date=date(2023, 3, 5),
            amount=Decimal('50.00'),
            status='COMPLETED'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('sale_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/sale/list.html')
        self.assertTrue('sales' in response.context)
        self.assertContains(response, 'Charlie Brown')
        
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('sale_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/sale/_sale_table.html')
        self.assertTrue('sales' in response.context)
        self.assertContains(response, 'Lucy Van Pelt')
        
    def test_create_view_get(self):
        response = self.client.get(reverse('sale_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/sale/_sale_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_valid(self):
        data = {
            'customer_name': 'New Customer',
            'sale_date': '2023-04-10',
            'amount': '123.45',
            'status': 'PENDING',
        }
        response = self.client.post(reverse('sale_add'), data)
        # For non-HTMX request, it should redirect
        self.assertEqual(response.status_code, 302) 
        self.assertTrue(Sale.objects.filter(customer_name='New Customer').exists())
        self.assertEqual(Sale.objects.get(customer_name='New Customer').status, 'PENDING')
        
    def test_create_view_post_invalid(self):
        data = { # Amount is invalid
            'customer_name': 'Invalid Sale',
            'sale_date': '2023-04-10',
            'amount': '-10.00',
            'status': 'PENDING',
        }
        response = self.client.post(reverse('sale_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertFalse(Sale.objects.filter(customer_name='Invalid Sale').exists())
        self.assertContains(response, 'Amount must be a positive number.')
        
    def test_create_view_post_valid_htmx(self):
        data = {
            'customer_name': 'HTMX Customer',
            'sale_date': '2023-04-11',
            'amount': '500.00',
            'status': 'COMPLETED',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sale_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSaleList')
        self.assertTrue(Sale.objects.filter(customer_name='HTMX Customer').exists())
        
    def test_update_view_get(self):
        response = self.client.get(reverse('sale_edit', args=[self.sale1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/sale/_sale_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.sale1)
        
    def test_update_view_post_valid(self):
        data = {
            'customer_name': 'Charlie Brown Updated',
            'sale_date': '2023-03-01',
            'amount': '350.00',
            'status': 'COMPLETED',
        }
        response = self.client.post(reverse('sale_edit', args=[self.sale1.pk]), data)
        self.assertEqual(response.status_code, 302) # Non-HTMX redirect
        self.sale1.refresh_from_db()
        self.assertEqual(self.sale1.customer_name, 'Charlie Brown Updated')
        self.assertEqual(self.sale1.amount, Decimal('350.00'))
        self.assertEqual(self.sale1.status, 'COMPLETED')
        
    def test_update_view_post_valid_htmx(self):
        data = {
            'customer_name': 'HTMX Updated',
            'sale_date': '2023-03-05',
            'amount': '60.00',
            'status': 'CANCELED',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sale_edit', args=[self.sale2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSaleList')
        self.sale2.refresh_from_db()
        self.assertEqual(self.sale2.customer_name, 'HTMX Updated')
        self.assertEqual(self.sale2.status, 'CANCELED')
        
    def test_delete_view_get(self):
        response = self.client.get(reverse('sale_delete', args=[self.sale1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/sale/_sale_confirm_delete.html')
        self.assertTrue('sale' in response.context)
        self.assertEqual(response.context['sale'], self.sale1)
        
    def test_delete_view_post(self):
        sale_to_delete_pk = self.sale1.pk
        response = self.client.post(reverse('sale_delete', args=[sale_to_delete_pk]))
        self.assertEqual(response.status_code, 302) # Non-HTMX redirect
        self.assertFalse(Sale.objects.filter(pk=sale_to_delete_pk).exists())
        
    def test_delete_view_post_htmx(self):
        sale_to_delete_pk = self.sale2.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sale_delete', args=[sale_to_delete_pk]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSaleList')
        self.assertFalse(Sale.objects.filter(pk=sale_to_delete_pk).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for Dynamic Content**:
    -   `list.html` uses `hx-get="{% url 'sale_table' %}"` on `#saleTable-container` with `hx-trigger="load, refreshSaleList from:body"` to load the DataTable partial upon page load and whenever a `refreshSaleList` custom event is triggered (after CRUD operations).
    -   "Add New Sale," "Edit," and "Delete" buttons use `hx-get` to fetch the respective forms (`_sale_form.html` or `_sale_confirm_delete.html`) and `hx-target="#modalContent"` to load them into the modal.
    -   Form submissions (`_sale_form.html`, `_sale_confirm_delete.html`) use `hx-post` and `hx-swap="none"` combined with `hx-on::after-request` to trigger the `refreshSaleList` event and `close-modal` Alpine.js event upon successful operation (indicated by a 204 No Content response from Django).
-   **Alpine.js for UI State Management**:
    -   The main `list.html` template has an `x-data="{ showModal: false }"` attribute on its container.
    -   The modal `div` uses `x-show="showModal"` for display toggling, along with `x-transition` for smooth animations.
    -   Buttons that open the modal have `@click="showModal = true"` to set the Alpine.js state.
    -   Buttons that close the modal (Cancel) or the form's `hx-on::after-request` use `@click="$dispatch('close-modal')"` or `$dispatch('close-modal')` respectively. A `closeModal` custom event listener is set up in `extra_js` block to toggle the `showModal` state.
    -   `@click.away="showModal = false"` on the modal background allows closing it by clicking outside.
-   **DataTables for List Views**:
    -   The `_sale_table.html` partial uses `$('#saleTable').DataTable({...})` to initialize the jQuery DataTables plugin. This provides client-side features like searching, sorting, and pagination without requiring server-side rendering logic for these features.
    -   The JavaScript for DataTables is placed directly within the partial template, ensuring it runs when the partial is loaded by HTMX. A check for `$.fn.DataTable.isDataTable` prevents re-initialization errors.
-   **No Full Page Reloads**: All CRUD interactions (Add, Edit, Delete) are performed via HTMX, loading forms into modals and updating the list dynamically without full page refreshes.
-   **DRY Template Inheritance**: All specific templates (`list.html`) extend `core/base.html`, ensuring a consistent layout and shared CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables are managed centrally in `base.html`.

## Final Notes

This comprehensive plan transforms the minimal ASP.NET placeholder into a fully functional, modern Django application. It emphasizes:
-   **AI-assisted automation**: The steps are structured for systematic conversion, mapping ASP.NET concepts to Django patterns.
-   **Business benefits**: The focus is on a modern, responsive user experience (HTMX, Alpine.js), maintainability (Fat Model, Thin View, DRY), and scalability, reducing technical debt.
-   **Clear, actionable steps**: Each section specifies the file, code, and purpose in plain English, suitable for conversational AI guidance and non-technical oversight.
-   **Future-proof architecture**: Adherence to Django 5.0+ best practices and separation of concerns prepares the application for future enhancements.