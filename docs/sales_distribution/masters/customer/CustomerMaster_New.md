## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the `Submit_Click` method's `fun.insert` call, the target table is `SD_Cust_master`. The column names are explicitly listed in the `insert` statement.

-   **Table Name:** `SD_Cust_master`
-   **Columns and Inferred Data Types:**
    *   `SysDate` (DateTime)
    *   `SysTime` (DateTime/Time)
    *   `SessionId` (String, maps to Django user)
    *   `CompId` (Integer, maps to company ID)
    *   `FinYearId` (Integer, maps to financial year ID)
    *   `CustomerId` (String, auto-generated unique ID, e.g., "ABC001")
    *   `CustomerName` (String)
    *   `RegdAddress` (Text)
    *   `RegdCountry` (Integer/FK to Country lookup table)
    *   `RegdState` (Integer/FK to State lookup table)
    *   `RegdCity` (Integer/FK to City lookup table)
    *   `RegdPinNo` (String)
    *   `RegdContactNo` (String)
    *   `RegdFaxNo` (String)
    *   `WorkAddress` (Text)
    *   `WorkCountry` (Integer/FK)
    *   `WorkState` (Integer/FK)
    *   `WorkCity` (Integer/FK)
    *   `WorkPinNo` (String)
    *   `WorkContactNo` (String)
    *   `WorkFaxNo` (String)
    *   `MaterialDelAddress` (Text)
    *   `MaterialDelCountry` (Integer/FK)
    *   `MaterialDelState` (Integer/FK)
    *   `MaterialDelCity` (Integer/FK)
    *   `MaterialDelPinNo` (String)
    *   `MaterialDelContactNo` (String)
    *   `MaterialDelFaxNo` (String)
    *   `ContactPerson` (String)
    *   `JuridictionCode` (String)
    *   `Commissionurate` (String)
    *   `TinVatNo` (String)
    *   `Email` (String)
    *   `EccNo` (String)
    *   `Divn` (String)
    *   `TinCstNo` (String)
    *   `ContactNo` (String, main contact number)
    *   `Range` (String, renamed to `RangeNo` to avoid Python keyword collision)
    *   `PanNo` (String)
    *   `TDSCode` (String)
    *   `Remark` (Text)

For `Country`, `State`, and `City`, the ASP.NET code indicates lookup tables (`fun.dropdownCountry`, `fun.dropdownState`, `fun.dropdownCity`). We'll assume simple `tblCountry`, `tblState`, `tblCity` tables with `id` and `name` columns.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

-   **Create**: The primary functionality is to create a new customer via the `Submit_Click` event, which executes an `INSERT` statement into `SD_Cust_master`. This includes custom logic for generating `CustomerId` and comprehensive field validation.
-   **Read**:
    *   The `Page_Load` method populates dropdowns for Country, State, and City, implying read operations from lookup tables.
    *   The `Submit_Click` method performs a `SELECT` query to determine the next available `CustomerId` based on a prefix and company ID.
    *   Although not explicitly shown as a list view for existing customers, a "Master" page typically includes a list. We will implement a `ListView` for `CustomerMaster` to show existing records.
-   **Update**: Not directly evident in the provided `.aspx` or code-behind (which is specifically for "CustomerMaster_New"). However, as a master data entity, update functionality is assumed for a complete system.
-   **Delete**: Not directly evident. Assumed to be part of a complete master data management.
-   **Validation Logic**: Extensive `RequiredFieldValidator`s and a `RegularExpressionValidator` for email. The `Submit_Click` also contains a very long `if` condition for server-side validation, checking for non-empty fields and the email regex.
-   **Dependent Dropdowns**: The `DDListNewRegdCountry_SelectedIndexChanged`, `DDListNewRegdState_SelectedIndexChanged`, etc., demonstrate cascading dropdowns (Country -> State -> City). This dynamic behavior will be replicated using HTMX.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   `asp:TextBox`: Used for text input (e.g., `txtNewCustName`, `txtNewRegdAdd`, `txtNewEmail`). These will map to Django `TextInput` or `Textarea` widgets.
-   `asp:DropDownList`: Used for selecting values from a predefined list (e.g., `DDListNewRegdCountry`, `DDListNewRegdState`, `DDListNewRegdCity`). These will map to Django `Select` widgets. The `AutoPostBack="True"` indicates server-side refresh for dependent dropdowns, which will be handled by HTMX.
-   `asp:Button`: `Submit` button. This will map to a standard HTML `button` with HTMX attributes for submission.
-   `asp:RequiredFieldValidator`: Ensures fields are not empty. Handled by Django form field `required=True`.
-   `asp:RegularExpressionValidator`: Validates email format. Handled by Django `EmailField` type or custom validator.
-   `MasterPageFile="~/MasterPage.master"`: Indicates a master page structure, mapping to Django's template inheritance (`{% extends 'core/base.html' %}`).
-   Client-side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`, `confirmationAdd()`): These will be replaced by HTMX for dynamic interactions and Alpine.js for UI state management, or simple HTMX responses for notifications.

## Step 4: Generate Django Code

### 4.1 Models (`customers/models.py`)

This file will contain the `CustomerMaster` model and assumed lookup models for `Country`, `State`, and `City`.

```python
import datetime
import re
from django.db import models
from django.db.models import Max
from django.utils import timezone # For SysDate, SysTime

# --- Assumed Lookup Models based on dropdownCountry/State/City functions ---
# These models would typically reside in a separate 'locations' or 'common' app.
# For this migration, they are placed here for completeness and to support cascading dropdowns.

class Country(models.Model):
    """
    Represents a country, typically from a lookup table like 'tblCountry'.
    """
    name = models.CharField(max_length=100, unique=True, verbose_name="Country Name")

    class Meta:
        managed = False  # Assume this table is managed by the database
        db_table = 'tblCountry' # Inferred table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    """
    Represents a state within a country, typically from 'tblState'.
    """
    country = models.ForeignKey(Country, on_delete=models.CASCADE, db_column='CountryId') # Assuming CountryId as FK column
    name = models.CharField(max_length=100, verbose_name="State Name")

    class Meta:
        managed = False  # Assume this table is managed by the database
        db_table = 'tblState' # Inferred table name
        unique_together = ('country', 'name')
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    """
    Represents a city within a state, typically from 'tblCity'.
    """
    state = models.ForeignKey(State, on_delete=models.CASCADE, db_column='StateId') # Assuming StateId as FK column
    name = models.CharField(max_length=100, verbose_name="City Name")

    class Meta:
        managed = False  # Assume this table is managed by the database
        db_table = 'tblCity' # Inferred table name
        unique_together = ('state', 'name')
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

# --- Customer Master Model ---

class CustomerMasterManager(models.Manager):
    """
    Custom manager for CustomerMaster to handle specific business logic
    like generating the next CustomerId.
    """
    def get_next_customer_id(self, customer_name, comp_id):
        """
        Generates the next customer ID based on ASP.NET logic:
        First 3 chars of customer name + sequential 3-digit number.
        E.g., "ABC Company" -> "ABC001", "ABC002", etc.
        """
        if not customer_name:
            return None # Or raise an error
        
        # Replicate fun.getCustChar: first 3 characters, uppercase
        char_prefix = customer_name[:3].upper()

        # Replicate fun.select: get max CustomerId for given prefix and CompId
        # Max returns the highest value, which needs to be parsed if it's a string like 'ABC999'
        last_customer = self.filter(
            customer_id__startswith=char_prefix,
            comp_id=comp_id # Assuming comp_id is part of the filtering logic
        ).order_by('-customer_id').first() # Order by descending to get the highest ID

        if last_customer and last_customer.customer_id and len(last_customer.customer_id) >= 6:
            try:
                # Extract the numeric part (last 3 digits)
                numeric_part = int(last_customer.customer_id[3:])
                next_numeric_part = numeric_part + 1
                # Format as 3 digits with leading zeros
                return f"{char_prefix}{next_numeric_part:03d}"
            except ValueError:
                # Fallback if parsing fails (e.g., malformed ID)
                return f"{char_prefix}001"
        else:
            # First customer with this prefix
            return f"{char_prefix}001"

class CustomerMaster(models.Model):
    """
    Maps to the SD_Cust_master table in the database.
    Includes comprehensive customer details,
    split into registered, work, and material delivery addresses.
    """
    # System fields
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True, verbose_name="Session ID")
    comp_id = models.IntegerField(db_column='CompId', default=0, verbose_name="Company ID") # Assuming default 0 or taken from user context
    fin_year_id = models.IntegerField(db_column='FinYearId', default=0, verbose_name="Financial Year ID") # Assuming default 0 or taken from user context

    # Core Customer Information
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=10, unique=True, verbose_name="Customer ID")
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer's Name")

    # Registered Office Details
    regd_address = models.TextField(db_column='RegdAddress', verbose_name="Regd. Office Address")
    regd_country = models.ForeignKey(Country, on_delete=models.SET_NULL, db_column='RegdCountry', null=True, blank=True, related_name='regd_customers', verbose_name="Regd. Office Country")
    regd_state = models.ForeignKey(State, on_delete=models.SET_NULL, db_column='RegdState', null=True, blank=True, related_name='regd_customers', verbose_name="Regd. Office State")
    regd_city = models.ForeignKey(City, on_delete=models.SET_NULL, db_column='RegdCity', null=True, blank=True, related_name='regd_customers', verbose_name="Regd. Office City")
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20, verbose_name="Regd. Office PIN No.")
    regd_contact_no = models.CharField(db_column='RegdContactNo', max_length=50, verbose_name="Regd. Office Contact No.")
    regd_fax_no = models.CharField(db_column='RegdFaxNo', max_length=50, verbose_name="Regd. Office Fax No.")

    # Works/Factory Details
    work_address = models.TextField(db_column='WorkAddress', verbose_name="Works/Factory Address")
    work_country = models.ForeignKey(Country, on_delete=models.SET_NULL, db_column='WorkCountry', null=True, blank=True, related_name='work_customers', verbose_name="Works/Factory Country")
    work_state = models.ForeignKey(State, on_delete=models.SET_NULL, db_column='WorkState', null=True, blank=True, related_name='work_customers', verbose_name="Works/Factory State")
    work_city = models.ForeignKey(City, on_delete=models.SET_NULL, db_column='WorkCity', null=True, blank=True, related_name='work_customers', verbose_name="Works/Factory City")
    work_pin_no = models.CharField(db_column='WorkPinNo', max_length=20, verbose_name="Works/Factory PIN No.")
    work_contact_no = models.CharField(db_column='WorkContactNo', max_length=50, verbose_name="Works/Factory Contact No.")
    work_fax_no = models.CharField(db_column='WorkFaxNo', max_length=50, verbose_name="Works/Factory Fax No.")

    # Material Delivery Details
    material_del_address = models.TextField(db_column='MaterialDelAddress', verbose_name="Material Delivery Address")
    material_del_country = models.ForeignKey(Country, on_delete=models.SET_NULL, db_column='MaterialDelCountry', null=True, blank=True, related_name='material_customers', verbose_name="Material Delivery Country")
    material_del_state = models.ForeignKey(State, on_delete=models.SET_NULL, db_column='MaterialDelState', null=True, blank=True, related_name='material_customers', verbose_name="Material Delivery State")
    material_del_city = models.ForeignKey(City, on_delete=models.SET_NULL, db_column='MaterialDelCity', null=True, blank=True, related_name='material_customers', verbose_name="Material Delivery City")
    material_del_pin_no = models.CharField(db_column='MaterialDelPinNo', max_length=20, verbose_name="Material Delivery PIN No.")
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, verbose_name="Material Delivery Contact No.")
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, verbose_name="Material Delivery Fax No.")

    # Other Details
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, verbose_name="Contact Person")
    email = models.EmailField(db_column='Email', max_length=255, verbose_name="E-mail")
    contact_no = models.CharField(db_column='ContactNo', max_length=50, verbose_name="Contact No.") # Main contact no.
    juridiction_code = models.CharField(db_column='JuridictionCode', max_length=50, verbose_name="Juridiction Code")
    ecc_no = models.CharField(db_column='EccNo', max_length=50, verbose_name="ECC.No.")
    range_no = models.CharField(db_column='Range', max_length=50, verbose_name="Range") # Renamed from 'Range'
    commissionurate = models.CharField(db_column='Commissionurate', max_length=100, verbose_name="Commissionurate")
    divn = models.CharField(db_column='Divn', max_length=50, verbose_name="Divn")
    pan_no = models.CharField(db_column='PanNo', max_length=50, verbose_name="PAN No.")
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, verbose_name="TIN/VAT No.")
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, verbose_name="TIN/CST No.")
    tds_code = models.CharField(db_column='TDSCode', max_length=50, verbose_name="TDS Code.")
    remark = models.TextField(db_column='Remark', blank=True, null=True, verbose_name="Remarks")

    # Use custom manager
    objects = CustomerMasterManager()

    class Meta:
        managed = False  # Existing table, not managed by Django migrations
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return f"{self.customer_name} ({self.customer_id})"
    
    def save(self, *args, **kwargs):
        """
        Override save to auto-generate CustomerId for new instances.
        Assumes comp_id and fin_year_id are set, either directly or via request context.
        For simplicity in this example, comp_id is hardcoded to 1 if not set.
        In a real application, these would come from the current user's session/profile.
        """
        if not self.customer_id: # Only generate for new instances
            # In a real app, comp_id and fin_year_id would be passed from the request/session
            # For this example, we'll assume a default or use a placeholder if not set
            if not self.comp_id:
                self.comp_id = 1 # Placeholder for demonstration
            
            # Generate customer_id using the manager method
            self.customer_id = CustomerMaster.objects.get_next_customer_id(self.customer_name, self.comp_id)
            
            # Handle potential collision (though get_next_customer_id tries to prevent this)
            # This is a simple retry, a more robust solution might be needed for high concurrency.
            while CustomerMaster.objects.filter(customer_id=self.customer_id).exists():
                # If a collision happens (unlikely with sequential IDs), try next one
                # This assumes get_next_customer_id can take an increment hint or always provides next available
                # A more robust system would involve a transaction or a lock on the ID generation
                numeric_part = int(self.customer_id[3:]) + 1
                char_prefix = self.customer_id[:3]
                self.customer_id = f"{char_prefix}{numeric_part:03d}"

        # Set system date/time
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()
        
        super().save(*args, **kwargs)

```

### 4.2 Forms (`customers/forms.py`)

This file defines the form for creating and updating `CustomerMaster` instances.

```python
from django import forms
from .models import CustomerMaster, Country, State, City

class CustomerMasterForm(forms.ModelForm):
    """
    Form for CustomerMaster, mapping ASP.NET input fields to Django fields.
    Includes cascading dropdowns for Country/State/City.
    """
    # Define fields for cascading dropdowns explicitly
    # These will be populated initially and then updated via HTMX
    regd_country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('name'),
        required=True,
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/customers/get_states/', # HTMX URL for states
            'hx-target': '#id_regd_state', # Target for state dropdown
            'hx-include': 'this', # Include selected country ID
            'hx-indicator': '#loading-regd-state', # Loading indicator
        })
    )
    regd_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Initially empty
        required=True,
        empty_label="Select State",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/customers/get_cities/', # HTMX URL for cities
            'hx-target': '#id_regd_city', # Target for city dropdown
            'hx-include': 'this',
            'hx-indicator': '#loading-regd-city',
        })
    )
    regd_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Initially empty
        required=True,
        empty_label="Select City",
        widget=forms.Select(attrs={'class': 'box3 w-full'})
    )

    work_country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('name'),
        required=True,
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/customers/get_states/',
            'hx-target': '#id_work_state',
            'hx-include': 'this',
            'hx-indicator': '#loading-work-state',
        })
    )
    work_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        required=True,
        empty_label="Select State",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/customers/get_cities/',
            'hx-target': '#id_work_city',
            'hx-include': 'this',
            'hx-indicator': '#loading-work-city',
        })
    )
    work_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        required=True,
        empty_label="Select City",
        widget=forms.Select(attrs={'class': 'box3 w-full'})
    )

    material_del_country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('name'),
        required=True,
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/customers/get_states/',
            'hx-target': '#id_material_del_state',
            'hx-include': 'this',
            'hx-indicator': '#loading-material-del-state',
        })
    )
    material_del_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        required=True,
        empty_label="Select State",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/customers/get_cities/',
            'hx-target': '#id_material_del_city',
            'hx-include': 'this',
            'hx-indicator': '#loading-material-del-city',
        })
    )
    material_del_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        required=True,
        empty_label="Select City",
        widget=forms.Select(attrs={'class': 'box3 w-full'})
    )


    class Meta:
        model = CustomerMaster
        # Exclude customer_id because it's auto-generated in model's save method
        # Also exclude sys_date, sys_time, session_id, comp_id, fin_year_id as they are set automatically.
        exclude = ('customer_id', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id')
        widgets = {
            'customer_name': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'regd_address': forms.Textarea(attrs={'class': 'box3 w-full h-24'}),
            'regd_pin_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'regd_contact_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'regd_fax_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'work_address': forms.Textarea(attrs={'class': 'box3 w-full h-24'}),
            'work_pin_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'work_contact_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'work_fax_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'material_del_address': forms.Textarea(attrs={'class': 'box3 w-full h-24'}),
            'material_del_pin_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'material_del_contact_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'material_del_fax_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'contact_person': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'email': forms.EmailInput(attrs={'class': 'box3 w-full'}),
            'contact_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'juridiction_code': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'ecc_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'range_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'commissionurate': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'divn': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'pan_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'tin_vat_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'tin_cst_no': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'tds_code': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'remark': forms.Textarea(attrs={'class': 'box3 w-full h-24'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Populate initial dropdowns for existing instance (edit mode)
        if self.instance.pk:
            if self.instance.regd_country:
                self.fields['regd_state'].queryset = State.objects.filter(country=self.instance.regd_country).order_by('name')
            if self.instance.regd_state:
                self.fields['regd_city'].queryset = City.objects.filter(state=self.instance.regd_state).order_by('name')

            if self.instance.work_country:
                self.fields['work_state'].queryset = State.objects.filter(country=self.instance.work_country).order_by('name')
            if self.instance.work_state:
                self.fields['work_city'].queryset = City.objects.filter(state=self.instance.work_state).order_by('name')
            
            if self.instance.material_del_country:
                self.fields['material_del_state'].queryset = State.objects.filter(country=self.instance.material_del_country).order_by('name')
            if self.instance.material_del_state:
                self.fields['material_del_city'].queryset = City.objects.filter(state=self.instance.material_del_state).order_by('name')
    
    def clean_customer_name(self):
        customer_name = self.cleaned_data['customer_name']
        if len(customer_name) < 3:
            raise forms.ValidationError("Customer Name must be at least 3 characters long.")
        return customer_name

    def clean_email(self):
        email = self.cleaned_data['email']
        # Django's forms.EmailInput widget and EmailField already provide basic email validation.
        # This custom clean method could be used for more complex validation if needed.
        # The ASP.NET regex was: \w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*
        # Django's default is robust enough for most cases.
        return email

```

### 4.3 Views (`customers/views.py`)

This file contains the Class-Based Views (CBVs) for handling customer operations and HTMX partials for dynamic content.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming authentication

from .models import CustomerMaster, Country, State, City
from .forms import CustomerMasterForm

# --- Main CRUD Views ---

class CustomerMasterListView(LoginRequiredMixin, ListView):
    """
    Displays a list of all CustomerMaster objects.
    Serves the main page for customer management.
    """
    model = CustomerMaster
    template_name = 'customers/customer_master/list.html'
    context_object_name = 'customer_masters'

class CustomerMasterCreateView(LoginRequiredMixin, CreateView):
    """
    Handles the creation of a new CustomerMaster object.
    Used for showing the form and processing its submission.
    """
    model = CustomerMaster
    form_class = CustomerMasterForm
    template_name = 'customers/customer_master/_form.html' # Partial for modal
    success_url = reverse_lazy('customer_master_list') # Redirect not used for HTMX, but good practice

    def form_valid(self, form):
        # Set session_id (username), comp_id, fin_year_id from request.user
        # Assuming request.user has attributes like 'username', 'company_id', 'financial_year_id'
        # For this example, comp_id and fin_year_id are placeholders
        form.instance.session_id = self.request.user.username if self.request.user.is_authenticated else "anonymous"
        form.instance.comp_id = self.request.user.company_id if hasattr(self.request.user, 'company_id') else 1 # Placeholder
        form.instance.fin_year_id = self.request.user.financial_year_id if hasattr(self.request.user, 'financial_year_id') else 1 # Placeholder
        
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Master added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerMasterList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate initial dropdowns for the form (for new customer)
        context['form'].fields['regd_country'].queryset = Country.objects.all().order_by('name')
        context['form'].fields['work_country'].queryset = Country.objects.all().order_by('name')
        context['form'].fields['material_del_country'].queryset = Country.objects.all().order_by('name')
        return context

class CustomerMasterUpdateView(LoginRequiredMixin, UpdateView):
    """
    Handles the updating of an existing CustomerMaster object.
    Used for showing the form with pre-filled data and processing its submission.
    """
    model = CustomerMaster
    form_class = CustomerMasterForm
    template_name = 'customers/customer_master/_form.html' # Partial for modal
    success_url = reverse_lazy('customer_master_list')

    def form_valid(self, form):
        # Update system fields if needed, or rely on model's save method
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerMasterList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure dropdowns are correctly populated when editing an existing instance
        context['form'].fields['regd_country'].queryset = Country.objects.all().order_by('name')
        context['form'].fields['work_country'].queryset = Country.objects.all().order_by('name')
        context['form'].fields['material_del_country'].queryset = Country.objects.all().order_by('name')
        return context


class CustomerMasterDeleteView(LoginRequiredMixin, DeleteView):
    """
    Handles the deletion of a CustomerMaster object.
    Used for showing a confirmation and processing deletion.
    """
    model = CustomerMaster
    template_name = 'customers/customer_master/_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('customer_master_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerMasterList'
                }
            )
        return response

# --- HTMX Partial Views ---

class CustomerMasterTablePartialView(LoginRequiredMixin, ListView):
    """
    Renders only the table content for CustomerMaster, used by HTMX to refresh the list.
    """
    model = CustomerMaster
    template_name = 'customers/customer_master/_table.html' # Partial template
    context_object_name = 'customer_masters'

# --- HTMX Dependent Dropdown Views ---

class GetStatesView(LoginRequiredMixin, TemplateView):
    """
    Returns state options for a given country ID, for HTMX dynamic dropdowns.
    """
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('regd_country') or request.GET.get('work_country') or request.GET.get('material_del_country')
        states = State.objects.none()
        if country_id:
            try:
                states = State.objects.filter(country_id=country_id).order_by('name')
            except ValueError:
                pass # Invalid country_id, states remain empty
        
        # Render only the <option> tags for the states dropdown
        return render(request, 'customers/partials/_state_dropdown_options.html', {'states': states})

class GetCitiesView(LoginRequiredMixin, TemplateView):
    """
    Returns city options for a given state ID, for HTMX dynamic dropdowns.
    """
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('regd_state') or request.GET.get('work_state') or request.GET.get('material_del_state')
        cities = City.objects.none()
        if state_id:
            try:
                cities = City.objects.filter(state_id=state_id).order_by('name')
            except ValueError:
                pass # Invalid state_id, cities remain empty
        
        # Render only the <option> tags for the cities dropdown
        return render(request, 'customers/partials/_city_dropdown_options.html', {'cities': cities})

```

### 4.4 Templates

All templates will extend `core/base.html` (not included here) and use Tailwind CSS classes.

#### `customers/templates/customers/customer_master/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Masters</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'customer_master_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Customer
        </button>
    </div>
    
    <!-- Messages Container -->
    <div id="messages" class="mb-4">
        {% if messages %}
            {% for message in messages %}
            <div class="p-3 mb-2 text-sm text-{{ message.tags }}-700 bg-{{ message.tags }}-100 rounded-lg" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        {% endif %}
    </div>

    <div id="customer_mastersTable-container"
         hx-trigger="load, refreshCustomerMasterList from:body"
         hx-get="{% url 'customer_master_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Customer Data...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) or delete confirmation -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-300">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });

    // Custom event listener for refreshing messages (optional, if messages are outside hx-swap target)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status >= 200 && evt.detail.xhr.status < 300) {
            // If a success message is passed in HX-Trigger, handle it
            if (evt.detail.xhr.getResponseHeader('HX-Trigger') && evt.detail.xhr.getResponseHeader('HX-Trigger').includes('refreshCustomerMasterList')) {
                // You might fetch new messages or rely on Django's message system
                // A simple approach is to include messages in the main layout that refreshes
            }
        }
    });
</script>
{% endblock %}

```

#### `customers/templates/customers/customer_master/_table.html` (Partial for HTMX)

```html
<table id="customer_mastersTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer ID</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No.</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in customer_masters %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.customer_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.customer_id }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.contact_person }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.email }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.contact_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'customer_master_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'customer_master_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-6 text-center text-gray-500">No customer records found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after content is loaded
$(document).ready(function() {
    $('#customer_mastersTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [6] } // Disable ordering on Actions column
        ]
    });
});
</script>
```

#### `customers/templates/customers/customer_master/_form.html` (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Customer Name -->
            <div>
                <label for="{{ form.customer_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.customer_name.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.customer_name }}
                {% if form.customer_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.customer_name.errors }}</p>{% endif %}
            </div>

            <!-- Main Contact Info -->
            <div>
                <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.contact_person.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.contact_person }}
                {% if form.contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.email.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.email }}
                {% if form.email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.contact_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.contact_no }}
                {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
            </div>
        </div>

        <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">Registered Office Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.regd_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_address.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_address }}
                {% if form.regd_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_address.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.regd_country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_country.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_country }}
                <div id="loading-regd-state" class="htmx-indicator ml-2 text-sm text-gray-500">Loading states...</div>
                {% if form.regd_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_country.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.regd_state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_state.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_state }}
                <div id="loading-regd-city" class="htmx-indicator ml-2 text-sm text-gray-500">Loading cities...</div>
                {% if form.regd_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_state.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.regd_city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_city.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_city }}
                {% if form.regd_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_city.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.regd_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_pin_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_pin_no }}
                {% if form.regd_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.regd_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_contact_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_contact_no }}
                {% if form.regd_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.regd_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.regd_fax_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.regd_fax_no }}
                {% if form.regd_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_fax_no.errors }}</p>{% endif %}
            </div>
        </div>

        <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">Works/Factory Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.work_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_address.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_address }}
                {% if form.work_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_address.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_country.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_country }}
                <div id="loading-work-state" class="htmx-indicator ml-2 text-sm text-gray-500">Loading states...</div>
                {% if form.work_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_country.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_state.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_state }}
                <div id="loading-work-city" class="htmx-indicator ml-2 text-sm text-gray-500">Loading cities...</div>
                {% if form.work_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_state.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_city.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_city }}
                {% if form.work_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_city.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_pin_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_pin_no }}
                {% if form.work_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_contact_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_contact_no }}
                {% if form.work_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.work_fax_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.work_fax_no }}
                {% if form.work_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_fax_no.errors }}</p>{% endif %}
            </div>
        </div>

        <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">Material Delivery Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.material_del_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_address.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_address }}
                {% if form.material_del_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_address.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_country.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_country }}
                <div id="loading-material-del-state" class="htmx-indicator ml-2 text-sm text-gray-500">Loading states...</div>
                {% if form.material_del_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_country.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_state.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_state }}
                <div id="loading-material-del-city" class="htmx-indicator ml-2 text-sm text-gray-500">Loading cities...</div>
                {% if form.material_del_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_state.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_city.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_city }}
                {% if form.material_del_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_city.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_pin_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_pin_no }}
                {% if form.material_del_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_contact_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_contact_no }}
                {% if form.material_del_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.material_del_fax_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.material_del_fax_no }}
                {% if form.material_del_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_fax_no.errors }}</p>{% endif %}
            </div>
        </div>

        <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">Other Statutory Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label for="{{ form.juridiction_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.juridiction_code.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.juridiction_code }}
                {% if form.juridiction_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.juridiction_code.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.ecc_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.ecc_no }}
                {% if form.ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ecc_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.range_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.range_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.range_no }}
                {% if form.range_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.range_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.commissionurate.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.commissionurate.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.commissionurate }}
                {% if form.commissionurate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.commissionurate.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.divn.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.divn.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.divn }}
                {% if form.divn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.divn.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.pan_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.pan_no }}
                {% if form.pan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pan_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.tin_vat_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.tin_vat_no }}
                {% if form.tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_vat_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.tin_cst_no.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.tin_cst_no }}
                {% if form.tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_cst_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tds_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.tds_code.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.tds_code }}
                {% if form.tds_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tds_code.errors }}</p>{% endif %}
            </div>
        </div>
        
        <!-- Remark -->
        <div class="mt-4">
            <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.remark.label }}
            </label>
            {{ form.remark }}
            {% if form.remark.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remark.errors }}</p>{% endif %}
        </div>
        
        <!-- General form errors -->
        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Submit
            </button>
        </div>
    </form>
</div>
```

#### `customers/templates/customers/customer_master/_confirm_delete.html` (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the customer: 
        <strong class="font-medium">{{ object.customer_name }} ({{ object.customer_id }})</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'customer_master_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### `customers/templates/customers/partials/_state_dropdown_options.html` (Partial for HTMX)

```html
<option value="">Select State</option>
{% for state in states %}
<option value="{{ state.pk }}">{{ state.name }}</option>
{% endfor %}
```

#### `customers/templates/customers/partials/_city_dropdown_options.html` (Partial for HTMX)

```html
<option value="">Select City</option>
{% for city in cities %}
<option value="{{ city.pk }}">{{ city.name }}</option>
{% endfor %}
```

### 4.5 URLs (`customers/urls.py`)

This file defines the URL patterns for the `customers` application.

```python
from django.urls import path
from .views import (
    CustomerMasterListView, 
    CustomerMasterCreateView, 
    CustomerMasterUpdateView, 
    CustomerMasterDeleteView,
    CustomerMasterTablePartialView, # For HTMX table refresh
    GetStatesView, # For HTMX dependent dropdowns
    GetCitiesView, # For HTMX dependent dropdowns
)

urlpatterns = [
    # Main CRUD URLs
    path('customer_masters/', CustomerMasterListView.as_view(), name='customer_master_list'),
    path('customer_masters/add/', CustomerMasterCreateView.as_view(), name='customer_master_add'),
    path('customer_masters/edit/<str:pk>/', CustomerMasterUpdateView.as_view(), name='customer_master_edit'), # Use str:pk as CustomerId is string
    path('customer_masters/delete/<str:pk>/', CustomerMasterDeleteView.as_view(), name='customer_master_delete'), # Use str:pk
    
    # HTMX Partial URLs
    path('customer_masters/table/', CustomerMasterTablePartialView.as_view(), name='customer_master_table'),
    path('customers/get_states/', GetStatesView.as_view(), name='get_states'),
    path('customers/get_cities/', GetCitiesView.as_view(), name='get_cities'),
]

```

**Note**: Remember to include `path('customers/', include('customers.urls'))` in your project's main `urls.py`.

### 4.6 Tests (`customers/tests.py`)

This file will contain comprehensive unit tests for the models and integration tests for the views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.contrib.auth import get_user_model
from unittest.mock import patch
import datetime
from django.utils import timezone

from .models import CustomerMaster, Country, State, City

User = get_user_model() # Get the currently active user model

# --- Setup for User and Company/Financial Year (Mocking) ---
# In a real application, you would have proper User and Company models.
# For testing purposes, we can mock these attributes on a test user.
class MockCompany:
    def __init__(self, id):
        self.id = id

class MockFinancialYear:
    def __init__(self, id):
        self.id = id

class MockUser(User):
    class Meta:
        proxy = True # Prevent new table creation

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.company_id = 1 # Mocked company ID
        self.financial_year_id = 1 # Mocked financial year ID

# --- Model Tests ---

class CustomerMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create lookup data
        cls.country1 = Country.objects.create(name='Country A')
        cls.country2 = Country.objects.create(name='Country B')
        cls.state1_c1 = State.objects.create(country=cls.country1, name='State A1')
        cls.state2_c1 = State.objects.create(country=cls.country1, name='State A2')
        cls.state1_c2 = State.objects.create(country=cls.country2, name='State B1')
        cls.city1_s1 = City.objects.create(state=cls.state1_c1, name='City A1-1')
        cls.city2_s1 = City.objects.create(state=cls.state1_c1, name='City A1-2')

        # Create a test customer for existing scenarios
        CustomerMaster.objects.create(
            customer_id='TES001', # Manually set for initial test
            customer_name='Test Company',
            regd_address='123 Test St', regd_country=cls.country1, regd_state=cls.state1_c1, regd_city=cls.city1_s1, regd_pin_no='123456', regd_contact_no='111', regd_fax_no='111',
            work_address='456 Work Ave', work_country=cls.country1, work_state=cls.state1_c1, work_city=cls.city1_s1, work_pin_no='123456', work_contact_no='222', work_fax_no='222',
            material_del_address='789 Mat Rd', material_del_country=cls.country1, material_del_state=cls.state1_c1, material_del_city=cls.city1_s1, material_del_pin_no='123456', material_del_contact_no='333', material_del_fax_no='333',
            contact_person='John Doe', email='<EMAIL>', contact_no='9876543210',
            juridiction_code='JUR1', ecc_no='ECC1', range_no='RNG1', commissionurate='COMM1', divn='DIV1', pan_no='PAN1', tin_vat_no='TIN1', tin_cst_no='CST1', tds_code='TDS1',
            remark='Initial test customer',
            comp_id=1, fin_year_id=1, session_id='testuser'
        )
        CustomerMaster.objects.create(
            customer_id='ABC001',
            customer_name='ABC Corp',
            regd_address='ABC St', regd_country=cls.country1, regd_state=cls.state1_c1, regd_city=cls.city1_s1, regd_pin_no='123', regd_contact_no='11', regd_fax_no='11',
            work_address='ABC Work', work_country=cls.country1, work_state=cls.state1_c1, work_city=cls.city1_s1, work_pin_no='123', work_contact_no='22', work_fax_no='22',
            material_del_address='ABC Mat', material_del_country=cls.country1, material_del_state=cls.state1_c1, material_del_city=cls.city1_s1, material_del_pin_no='123', material_del_contact_no='33', material_del_fax_no='33',
            contact_person='Jane Doe', email='<EMAIL>', contact_no='1234567890',
            juridiction_code='JUR2', ecc_no='ECC2', range_no='RNG2', commissionurate='COMM2', divn='DIV2', pan_no='PAN2', tin_vat_no='TIN2', tin_cst_no='CST2', tds_code='TDS2',
            remark='Another test customer',
            comp_id=1, fin_year_id=1, session_id='testuser'
        )

    def test_customer_creation(self):
        customer = CustomerMaster.objects.get(customer_id='TES001')
        self.assertEqual(customer.customer_name, 'Test Company')
        self.assertEqual(customer.regd_country.name, 'Country A')
        self.assertEqual(customer.email, '<EMAIL>')
        self.assertIsNotNone(customer.sys_date)
        self.assertIsNotNone(customer.sys_time)

    def test_customer_id_generation_new_prefix(self):
        # Test new prefix
        next_id = CustomerMaster.objects.get_next_customer_id("New Brand", 1)
        self.assertEqual(next_id, 'NEW001')

    def test_customer_id_generation_existing_prefix(self):
        # Test existing prefix (ABC001 already exists, should be ABC002)
        next_id = CustomerMaster.objects.get_next_customer_id("ABC Retail", 1)
        self.assertEqual(next_id, 'ABC002') # Assuming only one ABC001 exists for comp_id 1
        
        # Create another ABC customer to test incrementing further
        CustomerMaster.objects.create(
            customer_id='ABC002', # Manually setting for test purposes (would be auto-generated in real save)
            customer_name='ABC Corp 2',
            regd_address='ABC St', regd_country=self.country1, regd_state=self.state1_c1, regd_city=self.city1_s1, regd_pin_no='123', regd_contact_no='11', regd_fax_no='11',
            work_address='ABC Work', work_country=self.country1, work_state=self.state1_c1, work_city=self.city1_s1, work_pin_no='123', work_contact_no='22', work_fax_no='22',
            material_del_address='ABC Mat', material_del_country=self.country1, material_del_state=self.state1_c1, material_del_city=self.city1_s1, material_del_pin_no='123', material_del_contact_no='33', material_del_fax_no='33',
            contact_person='Jane Doe', email='<EMAIL>', contact_no='1234567890',
            juridiction_code='JUR2', ecc_no='ECC2', range_no='RNG2', commissionurate='COMM2', divn='DIV2', pan_no='PAN2', tin_vat_no='TIN2', tin_cst_no='CST2', tds_code='TDS2',
            remark='Another test customer',
            comp_id=1, fin_year_id=1, session_id='testuser'
        )
        next_id_after_abc002 = CustomerMaster.objects.get_next_customer_id("ABC Super", 1)
        self.assertEqual(next_id_after_abc002, 'ABC003')

    def test_customer_id_generation_different_company(self):
        # If the prefix exists for comp_id 1, but we query for comp_id 2
        next_id = CustomerMaster.objects.get_next_customer_id("ABC Corp", 2)
        self.assertEqual(next_id, 'ABC001') # Should restart for a new company ID

    def test_customer_id_generation_invalid_name(self):
        self.assertIsNone(CustomerMaster.objects.get_next_customer_id("", 1))
        # Test short name, should still use first 3 chars
        self.assertEqual(CustomerMaster.objects.get_next_customer_id("AB", 1), 'AB001')


    def test_save_method_auto_id_and_sys_fields(self):
        customer = CustomerMaster(
            customer_name='New Test Customer',
            regd_address='123 New St', regd_country=self.country1, regd_state=self.state1_c1, regd_city=self.city1_s1, regd_pin_no='123456', regd_contact_no='111', regd_fax_no='111',
            work_address='456 New Ave', work_country=self.country1, work_state=self.state1_c1, work_city=self.city1_s1, work_pin_no='123456', work_contact_no='222', work_fax_no='222',
            material_del_address='789 New Rd', material_del_country=self.country1, material_del_state=self.state1_c1, material_del_city=self.city1_s1, material_del_pin_no='123456', material_del_contact_no='333', material_del_fax_no='333',
            contact_person='Jane Smith', email='<EMAIL>', contact_no='1234567890',
            juridiction_code='JUR3', ecc_no='ECC3', range_no='RNG3', commissionurate='COMM3', divn='DIV3', pan_no='PAN3', tin_vat_no='TIN3', tin_cst_no='CST3', tds_code='TDS3',
            remark='New auto-ID customer',
            session_id='testuser', comp_id=1, fin_year_id=1
        )
        customer.save()
        
        self.assertIsNotNone(customer.customer_id)
        self.assertTrue(customer.customer_id.startswith(customer.customer_name[:3].upper()))
        self.assertRegex(customer.customer_id, r'^[A-Z]{3}\d{3}$') # e.g., 'NEW001'
        self.assertEqual(customer.sys_date, timezone.now().date())
        self.assertEqual(customer.sys_time.hour, timezone.now().time().hour) # Check hour to avoid sub-second differences

class CustomerMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user for authentication
        cls.user = MockUser.objects.create_user(username='testuser', password='password123')
        
        # Create lookup data (same as model test for consistency)
        cls.country1 = Country.objects.create(name='Country A')
        cls.country2 = Country.objects.create(name='Country B')
        cls.state1_c1 = State.objects.create(country=cls.country1, name='State A1')
        cls.state2_c1 = State.objects.create(country=cls.country1, name='State A2')
        cls.city1_s1 = City.objects.create(state=cls.state1_c1, name='City A1-1')

        # Create test data for all tests
        CustomerMaster.objects.create(
            customer_id='CUS001',
            customer_name='Customer One',
            regd_address='1 Main St', regd_country=cls.country1, regd_state=cls.state1_c1, regd_city=cls.city1_s1, regd_pin_no='10001', regd_contact_no='111', regd_fax_no='111',
            work_address='1 Work Rd', work_country=cls.country1, work_state=cls.state1_c1, work_city=cls.city1_s1, work_pin_no='10001', work_contact_no='222', work_fax_no='222',
            material_del_address='1 Del Ave', material_del_country=cls.country1, material_del_state=cls.state1_c1, material_del_city=cls.city1_s1, material_del_pin_no='10001', material_del_contact_no='333', material_del_fax_no='333',
            contact_person='Adam Smith', email='<EMAIL>', contact_no='12345',
            juridiction_code='J1', ecc_no='E1', range_no='R1', commissionurate='C1', divn='D1', pan_no='P1', tin_vat_no='T1', tin_cst_no='CS1', tds_code='TD1', remark='Test customer 1',
            comp_id=1, fin_year_id=1, session_id='testuser'
        )
        CustomerMaster.objects.create(
            customer_id='CUS002',
            customer_name='Customer Two',
            regd_address='2 Second St', regd_country=cls.country1, regd_state=cls.state1_c1, regd_city=cls.city1_s1, regd_pin_no='10002', regd_contact_no='112', regd_fax_no='112',
            work_address='2 Work Rd', work_country=cls.country1, work_state=cls.state1_c1, work_city=cls.city1_s1, work_pin_no='10002', work_contact_no='223', work_fax_no='223',
            material_del_address='2 Del Ave', material_del_country=cls.country1, material_del_state=cls.state1_c1, material_del_city=cls.city1_s1, material_del_pin_no='10002', material_del_contact_no='334', material_del_fax_no='334',
            contact_person='Eve White', email='<EMAIL>', contact_no='67890',
            juridiction_code='J2', ecc_no='E2', range_no='R2', commissionurate='C2', divn='D2', pan_no='P2', tin_vat_no='T2', tin_cst_no='CS2', tds_code='TD2', remark='Test customer 2',
            comp_id=1, fin_year_id=1, session_id='testuser'
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')

    # --- List View Tests ---
    def test_list_view_get(self):
        response = self.client.get(reverse('customer_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_master/list.html')
        self.assertContains(response, 'Customer Masters')

    def test_list_view_requires_login(self):
        self.client.logout()
        response = self.client.get(reverse('customer_master_list'))
        self.assertNotEqual(response.status_code, 200) # Should redirect to login

    # --- Table Partial View Tests (HTMX) ---
    def test_table_partial_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customer_master_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_master/_table.html')
        self.assertTrue('customer_masters' in response.context)
        self.assertContains(response, 'Customer One') # Check if data is present
        self.assertContains(response, 'Customer Two')

    # --- Create View Tests ---
    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customer_master_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_master/_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        customer_count_before = CustomerMaster.objects.count()
        data = {
            'customer_name': 'New Test Customer',
            'regd_address': 'New Regd Add', 'regd_country': self.country1.pk, 'regd_state': self.state1_c1.pk, 'regd_city': self.city1_s1.pk, 'regd_pin_no': '12345', 'regd_contact_no': '123', 'regd_fax_no': '123',
            'work_address': 'New Work Add', 'work_country': self.country1.pk, 'work_state': self.state1_c1.pk, 'work_city': self.city1_s1.pk, 'work_pin_no': '12345', 'work_contact_no': '123', 'work_fax_no': '123',
            'material_del_address': 'New Mat Add', 'material_del_country': self.country1.pk, 'material_del_state': self.state1_c1.pk, 'material_del_city': self.city1_s1.pk, 'material_del_pin_no': '12345', 'material_del_contact_no': '123', 'material_del_fax_no': '123',
            'contact_person': 'New Contact', 'email': '<EMAIL>', 'contact_no': '9998887776',
            'juridiction_code': 'J3', 'ecc_no': 'E3', 'range_no': 'R3', 'commissionurate': 'C3', 'divn': 'D3', 'pan_no': 'P3', 'tin_vat_no': 'T3', 'tin_cst_no': 'CS3', 'tds_code': 'TD3',
            'remark': 'New customer via form',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_master_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerMasterList')
        
        self.assertEqual(CustomerMaster.objects.count(), customer_count_before + 1)
        new_customer = CustomerMaster.objects.get(customer_name='New Test Customer')
        self.assertIsNotNone(new_customer.customer_id)
        self.assertRegex(new_customer.customer_id, r'^[A-Z]{3}\d{3}$') # e.g., 'NEW001'

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer Master added successfully.')

    def test_create_view_post_invalid(self):
        customer_count_before = CustomerMaster.objects.count()
        data = {
            'customer_name': 'Too Short', # Invalid name
            'email': 'invalid-email', # Invalid email
            # Missing many required fields
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_master_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # HTMX renders form again with errors
        self.assertTemplateUsed(response, 'customers/customer_master/_form.html')
        self.assertTrue('form' in response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertEqual(CustomerMaster.objects.count(), customer_count_before) # No new object created

    # --- Update View Tests ---
    def test_update_view_get(self):
        customer_to_edit = CustomerMaster.objects.get(customer_id='CUS001')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customer_master_edit', args=[customer_to_edit.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_master/_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.customer_name, 'Customer One')

    def test_update_view_post_success(self):
        customer_to_update = CustomerMaster.objects.get(customer_id='CUS001')
        updated_name = 'Updated Customer One'
        data = {
            'customer_name': updated_name,
            'regd_address': '1 Main St', 'regd_country': self.country1.pk, 'regd_state': self.state1_c1.pk, 'regd_city': self.city1_s1.pk, 'regd_pin_no': '10001', 'regd_contact_no': '111', 'regd_fax_no': '111',
            'work_address': '1 Work Rd', 'work_country': self.country1.pk, 'work_state': self.state1_c1.pk, 'work_city': self.city1_s1.pk, 'work_pin_no': '10001', 'work_contact_no': '222', 'work_fax_no': '222',
            'material_del_address': '1 Del Ave', 'material_del_country': self.country1.pk, 'material_del_state': self.state1_c1.pk, 'material_del_city': self.city1_s1.pk, 'material_del_pin_no': '10001', 'material_del_contact_no': '333', 'material_del_fax_no': '333',
            'contact_person': 'Adam Smith', 'email': '<EMAIL>', 'contact_no': '12345',
            'juridiction_code': 'J1', 'ecc_no': 'E1', 'range_no': 'R1', commissionurate='C1', divn='D1', pan_no='P1', tin_vat_no='T1', tin_cst_no='CS1', tds_code='TD1', remark='Test customer 1',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_master_edit', args=[customer_to_update.pk]), data, **headers)

        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerMasterList')
        
        customer_to_update.refresh_from_db()
        self.assertEqual(customer_to_update.customer_name, updated_name)
        self.assertEqual(customer_to_update.email, '<EMAIL>')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer Master updated successfully.')

    # --- Delete View Tests ---
    def test_delete_view_get(self):
        customer_to_delete = CustomerMaster.objects.get(customer_id='CUS002')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customer_master_delete', args=[customer_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_master/_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].customer_id, 'CUS002')

    def test_delete_view_post_success(self):
        customer_count_before = CustomerMaster.objects.count()
        customer_to_delete = CustomerMaster.objects.get(customer_id='CUS002')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('customer_master_delete', args=[customer_to_delete.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerMasterList')
        
        self.assertEqual(CustomerMaster.objects.count(), customer_count_before - 1)
        self.assertFalse(CustomerMaster.objects.filter(customer_id='CUS002').exists())

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Customer Master deleted successfully.')

    # --- Dependent Dropdown Tests ---
    def test_get_states_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('get_states'), {'regd_country': self.country1.pk}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/partials/_state_dropdown_options.html')
        self.assertContains(response, f'<option value="{self.state1_c1.pk}">{self.state1_c1.name}</option>')
        self.assertContains(response, f'<option value="{self.state2_c1.pk}">{self.state2_c1.name}</option>')
        self.assertNotContains(response, self.state1_c2.name) # Should not contain states from other countries

    def test_get_cities_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('get_cities'), {'regd_state': self.state1_c1.pk}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/partials/_city_dropdown_options.html')
        self.assertContains(response, f'<option value="{self.city1_s1.pk}">{self.city1_s1.name}</option>')
        self.assertContains(response, f'<option value="{self.city2_s1.pk}">{self.city2_s1.name}</option>')

```

## Step 5: HTMX and Alpine.js Integration

The provided Django code structure heavily relies on HTMX for dynamic content loading and form submissions, and Alpine.js for general UI state management (like modal visibility).

-   **HTMX for CRUD operations**:
    -   `customer_master_list.html` uses `hx-get` to fetch the table content (`customer_master_table` URL) on page load and `refreshCustomerMasterList` event.
    -   Add/Edit/Delete buttons use `hx-get` to fetch their respective forms/confirmations into a modal (`#modalContent`).
    -   Forms (`_form.html`, `_confirm_delete.html`) use `hx-post` for submission, with `hx-swap="none"` and `HX-Trigger` headers to signal `refreshCustomerMasterList` to the main page. This ensures the table refreshes without a full page reload, and the modal closes (via Alpine.js).
-   **HTMX for Dependent Dropdowns**:
    -   In `_form.html`, the `regd_country`, `work_country`, `material_del_country` dropdowns have `hx-get` attributes targeting their respective state dropdowns (`hx-target`).
    -   Similarly, state dropdowns have `hx-get` targeting city dropdowns.
    -   The `hx-include="this"` ensures the selected value of the parent dropdown is sent as a parameter.
    -   `hx-indicator` provides visual feedback during AJAX requests.
-   **Alpine.js for Modals**:
    -   The `#modal` div in `customer_master_list.html` uses Alpine.js's `_` (hyperscript) or could use `x-data` to control its `hidden` class. `on click add .is-active to #modal` shows the modal, and `on click if event.target.id == 'modal' remove .is-active from me` closes it when clicking outside.
-   **DataTables**:
    -   The `_table.html` partial explicitly includes the JavaScript to initialize DataTables on the `#customer_mastersTable`. This ensures that when the table content is refreshed via HTMX, DataTables is re-initialized for the new content, providing client-side searching, sorting, and pagination.

## Final Notes

-   The `comp_id` and `fin_year_id` in the `CustomerMaster` model and `CustomerMasterCreateView` are placeholders. In a full ERP system, these would typically be derived from the authenticated user's profile or selected company/financial year context.
-   The `Country`, `State`, `City` models are simplified and assume existing `tblCountry`, `tblState`, `tblCity` tables. Their `db_column` values for foreign keys (`CountryId`, `StateId`) are inferred.
-   `LoginRequiredMixin` is used for all views, implying a Django authentication setup.
-   The `pk` in URLs for update/delete is `str:pk` because `customer_id` (the primary key in the model) is a string (e.g., 'CUS001').
-   All input fields and selects use the `box3 w-full` class (from ASP.NET) and Tailwind CSS classes for consistent styling.
-   The migration prioritizes automated approaches where possible, with manual definition for schema, forms, and views based on the ASP.NET code's explicit structure. The bulk of the dynamic behavior is offloaded to HTMX and Alpine.js, reducing complex JavaScript development.