This modernization plan details the transition of your ASP.NET customer listing and search functionality to a robust, modern Django application. We will leverage AI-assisted automation to systematically convert your existing logic into Django's "Fat Model, Thin View" architecture, emphasizing HTMX for dynamic interactions and DataTables for superior data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the provided ASP.NET code, the following database tables and their relevant columns have been identified. Note that `CustomerId` is the primary key for `SD_Cust_master`. The other tables serve as lookup/related data.

*   **Main Table:** `SD_Cust_master`
    *   **Columns:** `RegdAddress`, `RegdCountry`, `RegdState`, `RegdCity`, `CustomerId`, `CustomerName`, `SessionId`, `FinYearId`, `SysDate`, `CompId`, `SalesId`.
*   **Lookup Tables:**
    *   `tblFinancial_master`: `FinYearId`, `FinYear`
    *   `tblHR_OfficeStaff`: `EmpId`, `Title`, `EmployeeName` (where `SessionId` maps to `EmpId`)
    *   `tblcountry`: `CId`, `CountryName`
    *   `tblState`: `SId`, `StateName`
    *   `tblCity`: `CityId`, `CityName`

### Step 2: Identify Backend Functionality

The ASP.NET page `CustomerMaster_Print.aspx` primarily performs **Read** operations with advanced filtering and display.

*   **Read (List & Search):**
    *   The `BindDataCust` method is responsible for fetching customer data, applying filters based on `FinYearId`, `CompId`, and optional `CustomerId` or `CustomerName` search.
    *   It performs multiple nested lookups to enrich customer data with financial year, employee name, and full address (combining country, state, city names). This will be optimized using Django's ORM `select_related`.
    *   Pagination is handled by `SearchGridView1_PageIndexChanging`.
*   **Search Autocomplete:**
    *   The `sql` WebMethod provides autocomplete suggestions for `CustomerName` along with `CustomerId`.
*   **Redirect for "Print All":**
    *   The `btnPrintAll_Click` event redirects to another page (`Customer_Details_Print_All.aspx`) with a dynamically generated `Key`.
*   **CRUD Operations:** While this specific page focuses on listing/searching, the overall migration plan requires placeholder Create, Update, and Delete operations for the `Customer` entity, as per the automation guidelines. These will be implemented as modals triggered by HTMX.

### Step 3: Infer UI Components

The ASP.NET controls translate to the following Django/HTMX/Alpine.js components:

*   **`TxtSearchValue` (TextBox with `AutoCompleteExtender`):**
    *   Django `forms.TextInput` for the search field.
    *   HTMX `hx-get` to a Django view for real-time table updates on input change.
    *   jQuery UI Autocomplete (integrated via `extra_js` block in the template) consuming an HTMX/JSON endpoint for suggestions.
*   **`Search` (Button):**
    *   An HTMX button that triggers a table refresh based on the search input.
*   **`SearchGridView1` (GridView):**
    *   Replaced by a standard HTML `<table>` that will be initialized as a DataTables instance.
    *   The table content will be loaded and refreshed dynamically via HTMX `hx-get` to a Django partial view.
*   **`btnPrintAll` (Button):**
    *   An HTMX button that triggers a redirect to the "Print All" page.
*   **Master Page Content Placeholders:** Replaced by Django's template inheritance (`{% extends 'core/base.html' %}`).
*   **AJAX Modal:** The `AjaxControlToolkit` functionality for forms will be replaced by HTMX-driven modals, managed with `hyperscript` for simple UI state.

---

### Step 4: Generate Django Code

We will create a Django application named `customers` for this module.

#### 4.1 Models (`customers/models.py`)

This file will define Django models corresponding to the identified database tables. All models will be `managed = False` to integrate with the existing database. The `Customer` model will include properties to encapsulate the complex data aggregation logic previously found in the ASP.NET code-behind's `BindDataCust` method, adhering to the "Fat Model" principle.

```python
from django.db import models
import datetime
import random
import string

# Helper models for lookup tables (unmanaged, assuming existing DB tables)
class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class Employee(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}. {self.employeename}".strip()

class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    countryname = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.countryname

class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    statename = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.statename

class City(models.Model):
    cityid = models.IntegerField(db_column='CityId', primary_key=True)
    cityname = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.cityname

# Main Customer model
class Customer(models.Model):
    # CustomerId is the primary key in the ASP.NET code for DataKeyNames
    customerid = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) 
    customername = models.CharField(db_column='CustomerName', max_length=255)
    regdaddress = models.CharField(db_column='RegdAddress', max_length=500, blank=True, null=True)
    
    # Foreign keys to lookup tables
    regdcountry = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='RegdCountry', related_name='customers_regd_country', blank=True, null=True)
    regdstate = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='RegdState', related_name='customers_regd_state', blank=True, null=True)
    regdcity = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='RegdCity', related_name='customers_regd_city', blank=True, null=True)
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='customers', blank=True, null=True)
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='customers', blank=True, null=True) # Assuming SessionId maps to EmpId
    
    sysdate = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True) # Stored as string in DB, converted to readable format in property
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    salesid = models.IntegerField(db_column='SalesId', blank=True, null=True) # Used for ordering in original query

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'
        # Ordering as per ASP.NET code "Order By SalesId Desc"
        ordering = ['-salesid']

    def __str__(self):
        return self.customername

    @property
    def formatted_sysdate(self):
        """
        Converts the SysDate string (expected DD-MM-YYYY) to DD-MM-YYYY format for display.
        Handles potential parsing errors gracefully.
        """
        if self.sysdate:
            try:
                # The original ASP.NET code formats as DD-MM-YYYY after several conversions
                return datetime.datetime.strptime(self.sysdate, '%d-%m-%Y').strftime('%d-%m-%Y')
            except (ValueError, TypeError):
                # Fallback for invalid date strings
                return self.sysdate 
        return ""

    @property
    def full_address(self):
        """Constructs the full address string based on original ASP.NET concatenation logic."""
        address_lines = []
        if self.regdaddress:
            address_lines.append(self.regdaddress)
        
        city_state_parts = []
        if self.regdcity:
            city_state_parts.append(self.regdcity.cityname)
        if self.regdstate:
            city_state_parts.append(self.regdstate.statename)
        
        if city_state_parts:
            address_lines.append(", ".join(city_state_parts))
            
        if self.regdcountry:
            address_lines.append(self.regdcountry.countryname + ".")

        return ",\n".join(address_lines)

    @property
    def generated_by(self):
        """Returns the employee name who generated the record."""
        return str(self.sessionid) if self.sessionid else ''

    @property
    def financial_year_display(self):
        """Returns the financial year string."""
        return str(self.finyearid) if self.finyearid else ''

    def get_random_key(self, length=10):
        """Generates a random alphanumeric key, simulating fun.GetRandomAlphaNumeric()."""
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for i in range(length))

```

#### 4.2 Forms (`customers/forms.py`)

A Django `ModelForm` for the `Customer` model, including fields likely editable in a typical CRUD scenario. Widgets are defined with Tailwind CSS classes for consistent styling.

```python
from django import forms
from .models import Customer, Country, State, City, FinancialYear

class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        # Exclude fields like sysdate, sessionid, compid, salesid as they are typically set automatically or internally.
        fields = [
            'customerid', 
            'customername',
            'regdaddress',
            'regdcountry',
            'regdstate',
            'regdcity',
            'finyearid',
        ]
        widgets = {
            'customerid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customername': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regdaddress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'regdcountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regdstate': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regdcity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_customername(self):
        customername = self.cleaned_data['customername']
        if len(customername) < 3:
            raise forms.ValidationError("Customer name must be at least 3 characters long.")
        return customername

```

#### 4.3 Views (`customers/views.py`)

Django Class-Based Views (CBVs) are used for the main list page, the HTMX-powered table partial, and the autocomplete functionality. Generic CRUD views are included for completeness as per the overall migration strategy. Views are kept thin, delegating complex logic to the models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q 
from django.shortcuts import render, redirect

from .models import Customer, FinancialYear, Employee, Country, State, City
from .forms import CustomerForm

import json
import random
import string

# Main Customer List View (renders the initial page structure)
class CustomerListView(ListView):
    model = Customer
    template_name = 'customers/customer_list.html'
    context_object_name = 'customers'
    
    def get_queryset(self):
        # The actual table data is loaded via HTMX into a separate partial view.
        # This view primarily sets up the page container and search elements.
        return Customer.objects.none() # Return empty queryset as data is loaded by HTMX

# HTMX Partial View for the Customer DataTables table
class CustomerTablePartialView(ListView):
    model = Customer
    template_name = 'customers/_customer_table.html'
    context_object_name = 'customers'
    paginate_by = 16 # Matches original ASP.NET GridView PageSize

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Optimize queries by pre-fetching related data to avoid N+1 issues
        queryset = queryset.select_related(
            'regdcountry', 'regdstate', 'regdcity', 'finyearid', 'sessionid'
        )

        search_value = self.request.GET.get('search_value', '').strip()
        comp_id = self.request.session.get('compid') # Retrieve company ID from session

        # Filter by company ID
        if comp_id:
            queryset = queryset.filter(compid=comp_id)
        
        # Implement search logic similar to ASP.NET's BindDataCust
        if search_value:
            # Check if search value is from autocomplete (e.g., "CustomerName [CustomerId]")
            if '[' in search_value and ']' in search_value:
                try:
                    customer_id_part = search_value.split('[')[-1].rstrip(']')
                    queryset = queryset.filter(customerid=customer_id_part)
                except IndexError:
                    # Fallback if parsing fails, search by name or ID
                    queryset = queryset.filter(Q(customername__icontains=search_value) | Q(customerid__icontains=search_value))
            else:
                queryset = queryset.filter(Q(customername__icontains=search_value) | Q(customerid__icontains=search_value))

        # Filter by financial year (FinYearId <= Session["finyear"])
        fin_year_id = self.request.session.get('finyear')
        if fin_year_id:
            # Assumes finyearid in session directly corresponds to FinancialYear.finyearid
            # Using __lte for "less than or equal to"
            queryset = queryset.filter(finyearid__finyearid__lte=fin_year_id) 

        return queryset

# HTMX endpoint for customer search autocomplete suggestions
class CustomerSearchAutocomplete(View):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '') # 'term' is the common parameter name for autocomplete libraries
        comp_id = request.session.get('compid')

        customers = Customer.objects.all()
        if comp_id:
            customers = customers.filter(compid=comp_id)

        # Filter by customer name starting with the prefix text, order, and limit results
        customers = customers.filter(customername__istartswith=prefix_text).order_by('customername')[:10]

        results = []
        for customer in customers:
            results.append(f"{customer.customername} [{customer.customerid}]")
        
        return JsonResponse(results, safe=False) # safe=False for non-dict objects

# View for "Print All" button redirection
class CustomerPrintAllView(View):
    def get(self, request, *args, **kwargs):
        # Generate a random key for the redirect URL, as in original ASP.NET
        customer_obj = Customer() # Instantiate to call the model method (or make it a static helper)
        random_key = customer_obj.get_random_key()
        
        # Redirect to the target print details page with URL parameters
        return redirect(reverse_lazy('customers:customer_print_all_details') + f'?ModId=2&SubModId=7&Key={random_key}')

# Placeholder for the actual "Print All Details" page content
class CustomerDetailsPrintAllView(View):
    def get(self, request, *args, **kwargs):
        key = request.GET.get('Key')
        mod_id = request.GET.get('ModId')
        sub_mod_id = request.GET.get('SubModId')
        
        context = {
            'key': key,
            'mod_id': mod_id,
            'sub_mod_id': sub_mod_id,
            'message': f"This is the Customer Print All page. Key: {key}, ModId: {mod_id}, SubModId: {sub_mod_id}",
        }
        return render(request, 'customers/customer_print_all.html', context)


# --- Generic CRUD Views (for modal interactions, as per migration guidelines) ---
class CustomerCreateView(CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/_customer_form.html' # Rendered in a modal
    success_url = reverse_lazy('customers:customer_list') # Not strictly used for HTMX but good practice

    def form_valid(self, form):
        # Assign session-based or auto-generated fields before saving
        # Example: form.instance.compid = self.request.session.get('compid', 1) 
        # Example: form.instance.sessionid = Employee.objects.get(empid=self.request.session.get('user_emp_id', 1))
        # Example: form.instance.sysdate = datetime.date.today().strftime('%d-%m-%Y')
        response = super().form_valid(form)
        messages.success(self.request, 'Customer added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # HTMX expects 204 No Content for successful form submission that triggers a swap
                headers={
                    'HX-Trigger': json.dumps({'refreshCustomerList': None, 'closeModal': None}) # Trigger refresh and close modal
                }
            )
        return response

class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/_customer_form.html' # Rendered in a modal
    success_url = reverse_lazy('customers:customer_list') 

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshCustomerList': None, 'closeModal': None})
                }
            )
        return response

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'customers/_customer_confirm_delete.html' # Rendered in a modal
    success_url = reverse_lazy('customers:customer_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshCustomerList': None, 'closeModal': None})
                }
            )
        return response

```

#### 4.4 Templates (`customers/templates/customers/`)

The templates are structured for HTMX partial loading. `customer_list.html` sets up the main page, and `_customer_table.html` handles the dynamic DataTables content. Forms and delete confirmations are also partials for modal use.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Master - Print</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow"
            hx-get="{% url 'customers:customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Customer
        </button>
    </div>

    <div class="mb-4 flex flex-wrap items-center space-x-2">
        <label for="id_search_value" class="text-gray-700 font-medium">Customer Name:</label>
        <input type="text" id="id_search_value" name="search_value" 
               class="flex-grow box3 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
               placeholder="Search by customer name or code"
               hx-get="{% url 'customers:customer_table' %}"
               hx-target="#customerTable-container"
               hx-trigger="keyup changed delay:500ms, search_button_click from:#search_button"
               hx-indicator="#search-spinner">
        
        <button id="search_button"
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded shadow redbox"
            _="on click send search_button_click to #id_search_value"> {# Trigger search on input #}
            Search
        </button>
        
        <button id="print_all_button"
            class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded shadow redbox"
            hx-get="{% url 'customers:customer_print_all' %}"
            hx-target="body" hx-swap="outerHTML"> {# Replaces current page with print page #}
            Print All
        </button>

        <span id="search-spinner" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </span>
    </div>

    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'customers:customer_table' %}"
         hx-target="this"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Initial loading state -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading customer data...</p>
        </div>
    </div>
    
    <!-- Modal for forms (add, edit, delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on closeModal remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // For simple modal visibility, hyperscript `on click` is sufficient.
    });

    // Initialize jQuery UI Autocomplete for the search input
    // Ensure jQuery UI CSS and JS are loaded in core/base.html
    $(function() {
        $("#id_search_value").autocomplete({
            source: "{% url 'customers:customer_search_autocomplete' %}",
            minLength: 1, // Minimum prefix length from ASP.NET
            select: function(event, ui) {
                // When an item is selected, set the input value and trigger HTMX search
                $("#id_search_value").val(ui.item.value);
                htmx.trigger(document.getElementById('id_search_value'), 'changed');
            }
        });
    });

    // Re-initialize DataTables after HTMX swaps new table content into the DOM
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'customerTable-container') {
            $('#customerTable').DataTable({
                "pageLength": 16, // From ASP.NET PageSize
                "lengthMenu": [[10, 16, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy previous instance before initializing new one
                "responsive": true // Add responsiveness for better mobile experience
            });
        }
    });

    // Optional: Initial DataTables setup for the first page load if not using hx-trigger="load" for the table
    // (but hx-trigger="load" on #customerTable-container already handles it effectively)
    $(document).ready(function() {
        if ($('#customerTable').length && !$.fn.DataTable.isDataTable('#customerTable')) {
            $('#customerTable').DataTable({
                "pageLength": 16,
                "lengthMenu": [[10, 16, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
{% endblock %}
```

```html
<!-- customers/templates/customers/_customer_table.html -->
<div class="overflow-x-auto">
    <table id="customerTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for customer in customers %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ customer.financial_year_display }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-left text-sm text-blue-600 hover:text-blue-800">
                    <a href="{% url 'customers:customer_detail' pk=customer.pk %}" class="underline">
                        {{ customer.customername }}
                    </a>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ customer.customerid }}</td>
                <td class="py-4 px-6 whitespace-pre-wrap text-left text-sm text-gray-500">{{ customer.full_address }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ customer.formatted_sysdate }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-left text-sm text-gray-500">{{ customer.generated_by }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 shadow-sm"
                        hx-get="{% url 'customers:customer_edit' pk=customer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded shadow-sm"
                        hx-get="{% url 'customers:customer_delete' pk=customer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-lg text-red-500 font-medium">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

```html
<!-- customers/templates/customers/_customer_form.html -->
<div class="p-6 relative">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

```html
<!-- customers/templates/customers/_customer_confirm_delete.html -->
<div class="p-6 relative">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the customer <strong>{{ object.customername }} ({{ object.customerid }})</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

```html
<!-- customers/templates/customers/customer_print_all.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Customer Print All Report</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <p class="text-lg text-gray-700 mb-4">
            This page represents the comprehensive report for all customers.
        </p>
        <p class="text-gray-600 mb-4">
            It dynamically receives parameters from the previous page:
            <ul class="list-disc list-inside ml-4">
                <li><strong>Key:</strong> <span class="font-mono text-purple-700">{{ key }}</span></li>
                <li><strong>ModId:</strong> <span class="font-mono text-purple-700">{{ mod_id }}</span></li>
                <li><strong>SubModId:</strong> <span class="font-mono text-purple-700">{{ sub_mod_id }}</span></li>
            </ul>
        </p>
        <p class="text-gray-600">
            In a full implementation, this page would fetch and render detailed customer data
            based on these parameters, suitable for printing or PDF generation.
        </p>
        <div class="mt-6">
            <a href="{% url 'customers:customer_list' %}" 
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow">
                Back to Customer List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`customers/urls.py`)

Defines URL patterns for the customer module, including the main list view, HTMX partials, autocomplete, and CRUD operations. `app_name` is set for namespacing.

```python
from django.urls import path
from .views import (
    CustomerListView, 
    CustomerTablePartialView, 
    CustomerSearchAutocomplete,
    CustomerCreateView, 
    CustomerUpdateView, 
    CustomerDeleteView,
    CustomerPrintAllView, 
    CustomerDetailsPrintAllView
)

app_name = 'customers' # Namespace for this application's URLs

urlpatterns = [
    # Main page for Customer Master Print
    path('', CustomerListView.as_view(), name='customer_list'),
    
    # HTMX endpoint to load/refresh the DataTables table content
    path('table/', CustomerTablePartialView.as_view(), name='customer_table'),
    
    # HTMX/JSON endpoint for the customer name autocomplete functionality
    path('search-autocomplete/', CustomerSearchAutocomplete.as_view(), name='customer_search_autocomplete'),

    # URL for the "Print All" button (redirects to customer_print_all_details)
    path('print-all/', CustomerPrintAllView.as_view(), name='customer_print_all'),
    
    # Target page for "Print All" redirect, renders comprehensive print view
    path('print-details/', CustomerDetailsPrintAllView.as_view(), name='customer_print_all_details'), 
    
    # CRUD operation endpoints (typically loaded in modals via HTMX)
    path('add/', CustomerCreateView.as_view(), name='customer_add'),
    path('edit/<str:pk>/', CustomerUpdateView.as_view(), name='customer_edit'), # CustomerId is string
    path('delete/<str:pk>/', CustomerDeleteView.as_view(), name='customer_delete'), # CustomerId is string
    
    # Placeholder for a customer detail view (as indicated by HyperLinkField in original ASP.NET)
    # This would link to customer_details_print.aspx logic. Re-using print_all_details temporarily.
    path('details/<str:pk>/', CustomerDetailsPrintAllView.as_view(), name='customer_detail'),
]
```

#### 4.6 Tests (`customers/tests.py`)

Comprehensive unit tests for the `Customer` model and integration tests for all views. This ensures functionality, data integrity, and proper HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.http import JsonResponse
import datetime
import json

from .models import Customer, FinancialYear, Employee, Country, State, City

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent lookup data
        cls.fin_year = FinancialYear.objects.create(finyearid=2023, finyear='2023-2024')
        cls.employee = Employee.objects.create(empid=101, title='Mr', employeename='John Doe')
        cls.country = Country.objects.create(cid=1, countryname='India')
        cls.state = State.objects.create(sid=1, statename='Maharashtra')
        cls.city = City.objects.create(cityid=1, cityname='Mumbai')

        # Create test customer data
        cls.customer1 = Customer.objects.create(
            customerid='CUST001',
            customername='Alpha Customer',
            regdaddress='123 Main St',
            regdcountry=cls.country,
            regdstate=cls.state,
            regdcity=cls.city,
            finyearid=cls.fin_year,
            sessionid=cls.employee,
            sysdate='01-01-2023', # DD-MM-YYYY format
            compid=1,
            salesid=10
        )
        cls.customer2 = Customer.objects.create(
            customerid='CUST002',
            customername='Beta Customer',
            regdaddress='456 Oak Ave',
            regdcountry=cls.country,
            regdstate=cls.state,
            regdcity=cls.city,
            finyearid=cls.fin_year,
            sessionid=cls.employee,
            sysdate='15-02-2023',
            compid=1,
            salesid=5
        )
        cls.customer3 = Customer.objects.create(
            customerid='CUST003',
            customername='Gamma Customer',
            regdaddress='789 Pine Ln',
            regdcountry=cls.country,
            regdstate=cls.state,
            regdcity=cls.city,
            finyearid=cls.fin_year,
            sessionid=cls.employee,
            sysdate='20-03-2024',
            compid=2, # Different company ID for filtering tests
            salesid=20
        )

    def test_customer_creation(self):
        obj = Customer.objects.get(pk='CUST001')
        self.assertEqual(obj.customername, 'Alpha Customer')
        self.assertEqual(obj.regdaddress, '123 Main St')
        self.assertEqual(obj.regdcountry.countryname, 'India')
        self.assertEqual(obj.finyearid.finyear, '2023-2024')
        self.assertEqual(obj.sessionid.employeename, 'John Doe')

    def test_formatted_sysdate_property(self):
        obj = Customer.objects.get(pk='CUST001')
        self.assertEqual(obj.formatted_sysdate, '01-01-2023')
        
        # Test with an invalid date string
        obj.sysdate = 'invalid-date'
        self.assertEqual(obj.formatted_sysdate, 'invalid-date') # Should return original invalid string
        
        # Test with None
        obj.sysdate = None
        self.assertEqual(obj.formatted_sysdate, '')


    def test_full_address_property(self):
        obj = Customer.objects.get(pk='CUST001')
        expected_address = "123 Main St,\nMumbai, Maharashtra,\nIndia."
        self.assertEqual(obj.full_address, expected_address)

        # Test with missing address components
        customer_no_address = Customer.objects.create(
            customerid='CUST004', customername='No Address',
            regdcountry=self.country, regdstate=self.state, regdcity=self.city,
            finyearid=self.fin_year, sessionid=self.employee, sysdate='01-01-2023', compid=1, salesid=1
        )
        self.assertEqual(customer_no_address.full_address, "Mumbai, Maharashtra,\nIndia.")

        customer_bare = Customer.objects.create(
            customerid='CUST005', customername='Bare Customer',
            finyearid=self.fin_year, sessionid=self.employee, sysdate='01-01-2023', compid=1, salesid=2
        )
        self.assertEqual(customer_bare.full_address, "")

    def test_generated_by_property(self):
        obj = Customer.objects.get(pk='CUST001')
        self.assertEqual(obj.generated_by, 'Mr. John Doe')
        obj.sessionid = None
        self.assertEqual(obj.generated_by, '')

    def test_financial_year_display_property(self):
        obj = Customer.objects.get(pk='CUST001')
        self.assertEqual(obj.financial_year_display, '2023-2024')
        obj.finyearid = None
        self.assertEqual(obj.financial_year_display, '')

    def test_get_random_key_method(self):
        obj = Customer.objects.get(pk='CUST001')
        key1 = obj.get_random_key()
        key2 = obj.get_random_key()
        self.assertIsInstance(key1, str)
        self.assertEqual(len(key1), 10) # Default length
        self.assertNotEqual(key1, key2) # Highly unlikely to be the same

    def test_model_ordering(self):
        # SalesId Desc
        customers = Customer.objects.filter(compid=1)
        self.assertEqual(customers[0].customerid, 'CUST001') # salesid=10
        self.assertEqual(customers[1].customerid, 'CUST002') # salesid=5


class CustomerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data and test customers for views
        cls.fin_year_2023 = FinancialYear.objects.create(finyearid=2023, finyear='2023-2024')
        cls.fin_year_2024 = FinancialYear.objects.create(finyearid=2024, finyear='2024-2025')
        cls.employee = Employee.objects.create(empid=101, title='Mr', employeename='John Doe')
        cls.country = Country.objects.create(cid=1, countryname='India')
        cls.state = State.objects.create(sid=1, statename='Maharashtra')
        cls.city = City.objects.create(cityid=1, cityname='Mumbai')

        cls.customer1 = Customer.objects.create(
            customerid='CUST001', customername='Alpha Customer', regdaddress='Add1',
            regdcountry=cls.country, regdstate=cls.state, regdcity=cls.city,
            finyearid=cls.fin_year_2023, sessionid=cls.employee, sysdate='01-01-2023', compid=1, salesid=10
        )
        cls.customer2 = Customer.objects.create(
            customerid='CUST002', customername='Beta Customer', regdaddress='Add2',
            regdcountry=cls.country, regdstate=cls.state, regdcity=cls.city,
            finyearid=cls.fin_year_2024, sessionid=cls.employee, sysdate='02-02-2024', compid=1, salesid=5
        )
        cls.customer3 = Customer.objects.create(
            customerid='CUST003', customername='Gamma Customer', regdaddress='Add3',
            regdcountry=cls.country, regdstate=cls.state, regdcity=cls.city,
            finyearid=cls.fin_year_2023, sessionid=cls.employee, sysdate='03-03-2023', compid=2, salesid=15
        )
    
    def setUp(self):
        self.client = Client()
        # Mock session variables as they are used for filtering
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024 # Current financial year for filtering
        session.save()

    def test_customer_list_view(self):
        response = self.client.get(reverse('customers:customer_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_list.html')
        # This view only renders the container, not the actual table data, so no customers should be in content
        self.assertNotContains(response, 'Alpha Customer') 

    def test_customer_table_partial_view_no_search(self):
        response = self.client.get(reverse('customers:customer_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/_customer_table.html')
        # Should show customers for compid=1 and finyearid <= 2024
        self.assertContains(response, 'Alpha Customer') # finyearid=2023, compid=1
        self.assertContains(response, 'Beta Customer')  # finyearid=2024, compid=1
        self.assertNotContains(response, 'Gamma Customer') # compid=2

    def test_customer_table_partial_view_search_by_name(self):
        response = self.client.get(reverse('customers:customer_table'), {'search_value': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Customer')
        self.assertNotContains(response, 'Beta Customer') # Filtered out by search
        self.assertNotContains(response, 'Gamma Customer') # Filtered out by compid

    def test_customer_table_partial_view_search_by_code(self):
        response = self.client.get(reverse('customers:customer_table'), {'search_value': 'CUST002'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Beta Customer')
        self.assertNotContains(response, 'Alpha Customer') # Filtered out by search

    def test_customer_table_partial_view_search_by_autocomplete_format(self):
        # Simulate autocomplete selection "Alpha Customer [CUST001]"
        response = self.client.get(reverse('customers:customer_table'), {'search_value': 'Alpha Customer [CUST001]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Customer')
        self.assertNotContains(response, 'Beta Customer')

    def test_customer_table_partial_view_pagination(self):
        # Create more customers to test pagination
        for i in range(10, 30):
            Customer.objects.create(
                customerid=f'CUST{i}', customername=f'Customer {i}',
                regdcountry=self.country, regdstate=self.state, regdcity=self.city,
                finyearid=self.fin_year_2023, sessionid=self.employee, sysdate='01-01-2023', compid=1, salesid=i
            )
        response = self.client.get(reverse('customers:customer_table')) # Default paginate_by is 16
        self.assertEqual(response.status_code, 200)
        # Should be 16 customers on the first page, plus customer1, customer2 (total 18 initially for compid 1, finyear <=2024)
        # The paginator will then cut this down to 16.
        self.assertEqual(len(response.context['customers']), 16)

    def test_customer_search_autocomplete_view(self):
        response = self.client.get(reverse('customers:customer_search_autocomplete'), {'term': 'al'})
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, JsonResponse)
        data = json.loads(response.content)
        self.assertIn('Alpha Customer [CUST001]', data)
        self.assertNotIn('Beta Customer [CUST002]', data) # 'al' is not in 'Beta'
        self.assertNotIn('Gamma Customer [CUST003]', data) # Different compid

        response = self.client.get(reverse('customers:customer_search_autocomplete'), {'term': 'cust'})
        data = json.loads(response.content)
        self.assertIn('Alpha Customer [CUST001]', data)
        self.assertIn('Beta Customer [CUST002]', data)
        self.assertNotIn('Gamma Customer [CUST003]', data) # Different compid

    def test_customer_print_all_view_redirect(self):
        response = self.client.get(reverse('customers:customer_print_all'))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertIn(reverse('customers:customer_print_all_details'), response.url)
        self.assertIn('ModId=2', response.url)
        self.assertIn('SubModId=7', response.url)
        self.assertIn('Key=', response.url) # Check for key parameter

    def test_customer_details_print_all_view_content(self):
        response = self.client.get(reverse('customers:customer_print_all_details') + '?ModId=2&SubModId=7&Key=RANDOMKEY123')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/customer_print_all.html')
        self.assertContains(response, 'RANDOMKEY123')
        self.assertContains(response, 'ModId: 2')
        self.assertContains(response, 'SubModId: 7')

    # HTMX CRUD View Tests
    def test_customer_create_view_get_htmx(self):
        response = self.client.get(reverse('customers:customer_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/_customer_form.html')
        self.assertContains(response, 'Add Customer')
        self.assertContains(response, 'hx-post')

    def test_customer_create_view_post_htmx_success(self):
        data = {
            'customerid': 'NEWCUST001',
            'customername': 'New Added Customer',
            'regdaddress': 'New Address 1',
            'regdcountry': self.country.cid,
            'regdstate': self.state.sid,
            'regdcity': self.city.cityid,
            'finyearid': self.fin_year_2023.finyearid,
        }
        response = self.client.post(reverse('customers:customer_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        # Check if the trigger contains 'refreshCustomerList' and 'closeModal'
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        self.assertTrue(Customer.objects.filter(customerid='NEWCUST001').exists())

    def test_customer_update_view_get_htmx(self):
        response = self.client.get(reverse('customers:customer_edit', args=[self.customer1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/_customer_form.html')
        self.assertContains(response, 'Edit Customer')
        self.assertContains(response, self.customer1.customername)

    def test_customer_update_view_post_htmx_success(self):
        data = {
            'customerid': self.customer1.pk, # Primary key must be included for ModelForm update
            'customername': 'Updated Alpha Customer',
            'regdaddress': 'Updated Address for Alpha',
            'regdcountry': self.country.cid,
            'regdstate': self.state.sid,
            'regdcity': self.city.cityid,
            'finyearid': self.fin_year_2023.finyearid,
        }
        response = self.client.post(reverse('customers:customer_edit', args=[self.customer1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])
        self.customer1.refresh_from_db()
        self.assertEqual(self.customer1.customername, 'Updated Alpha Customer')

    def test_customer_delete_view_get_htmx(self):
        response = self.client.get(reverse('customers:customer_delete', args=[self.customer1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'customers/_customer_confirm_delete.html')
        self.assertContains(response, self.customer1.customername)

    def test_customer_delete_view_post_htmx_success(self):
        customer_to_delete_pk = self.customer1.pk 
        response = self.client.post(reverse('customers:customer_delete', args=[customer_to_delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])
        self.assertFalse(Customer.objects.filter(pk=customer_to_delete_pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Content and Forms:**
    *   The `customer_list.html` page uses `hx-trigger="load"` and `hx-get` to dynamically load the `_customer_table.html` partial into `#customerTable-container` on page load.
    *   Search functionality is implemented with `hx-trigger="keyup changed delay:500ms"` on the search input, also targeting `#customerTable-container`.
    *   CRUD operations (Add, Edit, Delete) are initiated by buttons with `hx-get` targeting `#modalContent` (within a hidden `#modal` div). This loads the respective form/confirmation partials into a modal.
    *   Form submissions (`hx-post`) from within the modal use `hx-swap="none"` and the Django views return `204 No Content` with `HX-Trigger` headers (`refreshCustomerList`, `closeModal`) to update the table and close the modal without a full page refresh.
    *   An `htmx:afterSwap` event listener in `customer_list.html` ensures DataTables is correctly re-initialized every time the table content is updated via HTMX.
*   **Alpine.js for UI State:**
    *   While not heavily used for complex state in this specific implementation, a basic `on click` hyperscript attribute is used for modal visibility (`add .is-active to #modal`, `remove .is-active from me`). If more complex client-side state (e.g., dynamic dropdowns, form validation feedback without server roundtrips) were required, Alpine.js would be extensively integrated.
*   **DataTables for List Views:**
    *   The `_customer_table.html` partial renders the main customer list inside a `<table>` tag.
    *   The `customer_list.html` includes JavaScript that initializes this table with DataTables once it's loaded into the DOM (via the `htmx:afterSwap` event listener). This provides client-side searching, sorting, and pagination.
*   **Autocomplete Integration:**
    *   jQuery UI Autocomplete is used (assuming its assets are linked in `base.html`) for the `TxtSearchValue` input, making an AJAX call to the `customer_search_autocomplete` Django endpoint for suggestions.

---

### Final Notes

This plan systematically addresses the migration from ASP.NET to Django, focusing on automation-friendly patterns and modern web development best practices. The emphasis on "Fat Models, Thin Views," HTMX, and DataTables ensures a performant, maintainable, and user-friendly application. Remember to replace placeholders like `[APP_NAME]` and configure database connections appropriately in your Django settings for `managed=False` models. The test suite provides a robust framework to ensure the correctness of the migrated functionality.