This modernization plan details the automated conversion of your ASP.NET `CategoryEdit` module to a modern Django 5.0+ application. We focus on migrating the core functionality of displaying, editing, and managing "Work Order Categories," emphasizing a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for robust list presentation.

## ASP.NET to Django Conversion Script:

This document outlines a systematic, automation-driven approach to transform your existing ASP.NET module into a performant, maintainable Django solution.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   **NEVER** include `base.html` template code in your output - assume it already exists and is extended.
-   Focus ONLY on component-specific code for the current module (`category` app).
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the ASP.NET code, we identify the following:

-   **Database Table Name:** `tblSD_WO_Category` (from `fun.select("*", "tblSD_WO_Category", ...)`)
-   **Columns:**
    -   `CId`: Integer (Primary Key, `DataKeyNames="CId"`, `lblCid` in `EditItemTemplate`).
    -   `CName`: String (`Text='<%# Bind("CName") %>'`, `txtCate` in `EditItemTemplate`).
    -   `Symbol`: String (`Text='<%# Bind("Symbol") %>'`, `Label3`).
    -   `HasSubCat`: Stored as `1` or `0` in DB, but handled as a boolean (`"Yes"` / `"No"` display, `CheckBox1` for edit).
    -   `CompId`: String (from `Session["compid"]`, used in SQL filter).
    -   `FinYearId`: String (from `Session["finyear"]`, used in SQL filter).

**Inferred Relationships (for business logic):**

-   `SD_Cust_WorkOrder_Master`: Used to check if a category is in use (prevents deletion/update).
-   `tblSD_WO_SubCategory`: Used to check if a category has subcategories (affects update logic for `HasSubCat`).

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business rules in the ASP.NET code.

**Instructions:**

The module primarily handles "Read" and "Update" operations for WO Categories, with conditional restrictions.

-   **Read (Display List):**
    -   The `fillGrid()` method retrieves all records from `tblSD_WO_Category` for the current `CompId` and `FinYearId`.
    -   Displays `CId`, `CName`, `Symbol`, and `HasSubCat` (converted to "Yes"/"No").
-   **Update (Edit Record):**
    -   Triggered by `GridView1_RowCommand` (specifically `Update` command).
    -   Allows editing `CName` and `HasSubCat`. `Symbol` is displayed but not editable.
    -   **Validation/Business Rules:**
        -   `CName` is a required field.
        -   **Strict Update Constraint:** If a category *currently* has `HasSubCat` set to "Yes" (meaning `HasSubCat` is `1` in the database) **AND** it genuinely has associated entries in `tblSD_WO_SubCategory`, *no fields* of that category can be updated. This is a critical business rule to replicate.
        -   **General Edit/Delete Constraint:** If a category has associated entries in `SD_Cust_WorkOrder_Master`, its edit (and implied delete) functionality is disabled/hidden in the UI. We will implement this as a `can_be_modified` check.
-   **Create:** Not explicitly shown as a direct feature in the `CategoryEdit.aspx` (which focuses on "Edit"), but a standard CRUD operation. We will include it for a complete modernization.
-   **Delete:** Not explicitly available in the provided ASP.NET UI/code, but implied by the "edit" button being hidden based on usage. We will implement a `DeleteView` and enforce the `can_be_deleted` check (if used in `SD_Cust_WorkOrder_Master`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django mapping.

**Instructions:**

-   **`GridView` (`GridView1`):** This will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side searching, sorting, and pagination.
-   **`asp:Content` Placeholders:** These map directly to Django template blocks (e.g., `{% block content %}`, `{% block extra_js %}`).
-   **`asp:TextBox` (`txtCate`):** Maps to Django `forms.CharField` with `widgets.TextInput`.
-   **`asp:Label` (`Label1`, `Label2`, `Label3`, `lblsubcatNo`, `lblCid`, `lblMessage`):** Maps to displaying model field values directly in templates, or Django's `messages` framework for alerts.
-   **`asp:CheckBox` (`CheckBox1`):** Maps to Django `forms.BooleanField` with `widgets.CheckboxInput`.
-   **`asp:CommandField` (Edit):** Replaced by HTMX-driven buttons in the DataTable's "Actions" column that load forms into a modal.
-   **`asp:RequiredFieldValidator` (`ReqCat`):** Handled by Django's robust form validation system.
-   **Client-Side Scripts (`PopUpMsg.js`, `loadingNotifier.js`):** Replaced by **HTMX** for dynamic content loading (e.g., forms in modals) and **Alpine.js** for simple UI state management (e.g., showing/hiding the modal). No custom JavaScript will be written for these interactions.

### Step 4: Generate Django Code

We will create a new Django application named `category` to encapsulate this module.

#### 4.1 Models (`category/models.py`)

**Task:** Create Django models based on the identified database schema and associated tables.

**Instructions:**

-   The primary model is `Category`.
-   We'll define `CompId` and `FinYearId` as fields in `Category`, assuming they are columns in `tblSD_WO_Category` based on the SQL filter.
-   `managed = False` is set for all models as per instructions to work with an existing database schema.
-   Business logic for `can_be_deleted` and `can_be_updated` will be implemented as methods on the `Category` model, querying the related (dummy) `SD_Cust_WorkOrder_Master` and `TblSD_WO_SubCategory` tables.

```python
from django.db import models
from django.db.models import F

class Category(models.Model):
    # CId is the primary key and will be an integer
    CId = models.IntegerField(db_column='CId', primary_key=True)
    CName = models.CharField(db_column='CName', max_length=255, verbose_name="Category Name")
    Symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True, verbose_name="Symbol")
    # HasSubCat is stored as '1' or '0' in DB, mapped to BooleanField for Django convenience
    # We will ensure '1'/'0' conversion during save/load if `managed=False` and actual DB field is char/int
    # For now, let's assume it maps correctly to a Boolean, or handle conversion in properties/methods.
    # The ASP.NET code converts '1' to "Yes" and '0' to "No". We'll use BooleanField directly.
    HasSubCat = models.BooleanField(db_column='HasSubCat', default=False, verbose_name="Has SubCategory")
    CompId = models.CharField(db_column='CompId', max_length=50, blank=True, null=True, verbose_name="Company ID")
    FinYearId = models.CharField(db_column='FinYearId', max_length=50, blank=True, null=True, verbose_name="Financial Year ID")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblSD_WO_Category' # Explicitly names the database table
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'
        # Ordering as per ASP.NET: Order by CId desc
        ordering = ['-CId']

    def __str__(self):
        return self.CName or f"Category (ID: {self.CId})"

    # --- Dummy models for dependency checks ---
    # These models are defined within Category to keep them logically grouped
    # and to clarify their purpose for business logic within Category.
    # In a larger application, these might be defined as separate top-level models
    # in their respective apps if they represent full entities.
    class SD_Cust_WorkOrder_Master(models.Model):
        # Assuming CId is the foreign key linking to Category
        CId = models.IntegerField(db_column='CId', primary_key=True) # Or models.ForeignKey if Category is managed
        # Other fields of SD_Cust_WorkOrder_Master, not relevant for this specific check
        class Meta:
            managed = False
            db_table = 'SD_Cust_WorkOrder_Master' # The actual table name

    class TblSD_WO_SubCategory(models.Model):
        # Assuming CId is the foreign key linking to Category
        CId = models.IntegerField(db_column='CId', primary_key=True) # Or models.ForeignKey if Category is managed
        # Other fields of tblSD_WO_SubCategory, not relevant for this specific check
        class Meta:
            managed = False
            db_table = 'tblSD_WO_SubCategory' # The actual table name

    # --- Business Logic (Fat Model) ---
    def has_associated_work_orders(self):
        """Checks if this category is used in SD_Cust_WorkOrder_Master."""
        return Category.SD_Cust_WorkOrder_Master.objects.filter(CId=self.CId).exists()

    def has_associated_subcategories(self):
        """Checks if this category has associated subcategories."""
        return Category.TblSD_WO_SubCategory.objects.filter(CId=self.CId).exists()

    def can_be_deleted(self):
        """
        Determines if the category can be deleted.
        Based on ASP.NET: cannot delete if used in SD_Cust_WorkOrder_Master.
        """
        return not self.has_associated_work_orders()

    def can_be_updated(self):
        """
        Determines if the category can be updated.
        Based on ASP.NET:
        1. Cannot update if it has associated work orders.
        2. Cannot update IF (current HasSubCat is True AND it has associated subcategories).
        This is a very strict rule, preventing *any* update under condition 2.
        """
        if self.has_associated_work_orders():
            return False # Cannot update if used in work orders

        if self.HasSubCat and self.has_associated_subcategories():
            return False # Cannot update if HasSubCat is True AND it has subcategories

        return True # Otherwise, updates are allowed
```

#### 4.2 Forms (`category/forms.py`)

**Task:** Define a Django form for user input, adhering to validation rules.

**Instructions:**

-   Create a `ModelForm` for the `Category` model.
-   Include `CName`, `HasSubCat` as editable fields. `Symbol` is read-only in ASP.NET, so it won't be in `fields` for the form. `CId` is the primary key and generally not edited via form. `CompId` and `FinYearId` will be handled implicitly by the view or pre-set.
-   Add appropriate widgets with Tailwind CSS classes.
-   Implement custom validation for `HasSubCat` if needed beyond the model's `can_be_updated` logic. The primary validation relies on the `can_be_updated` method on the model.

```python
from django import forms
from .models import Category

class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['CName', 'HasSubCat'] # Symbol is not editable in original ASP.NET
        widgets = {
            'CName': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'HasSubCat': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'}),
        }
        labels = {
            'CName': 'Category Name',
            'HasSubCat': 'Has SubCategory',
        }

    def clean(self):
        cleaned_data = super().clean()
        # The `can_be_updated` check is primarily on the model and enforced in the view.
        # However, if CName is empty, Django's built-in validation will catch it.
        # We also need to explicitly check if `CName` is provided, as it was a RequiredFieldValidator.
        cname = cleaned_data.get('CName')
        if not cname:
            self.add_error('CName', 'Category Name is required.')
        return cleaned_data

```

#### 4.3 Views (`category/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views.

**Instructions:**

-   `CategoryListView`: Displays all categories.
-   `CategoryTablePartialView`: An HTMX-specific view to render only the DataTable portion. This allows the main `list.html` to update the table dynamically without full page reloads.
-   `CategoryCreateView`: Adds new categories.
-   `CategoryUpdateView`: Edits existing categories, incorporating the `can_be_updated` logic.
-   `CategoryDeleteView`: Deletes categories, incorporating the `can_be_deleted` logic.
-   Views will remain thin (5-15 lines) with business logic delegated to the model.
-   HTMX headers for `HX-Trigger` will be used to signal list refreshes.
-   `CompId` and `FinYearId` will be filtered from the queryset. Assuming `Session` values as in ASP.NET, we'll retrieve them from `request.session`.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from .models import Category
from .forms import CategoryForm

# Helper to get session values for filtering (mimics ASP.NET Session usage)
def get_session_context(request):
    # In a real app, these might come from a user profile or a context processor
    # For this example, we'll use dummy values or assume they are in session
    compid = request.session.get('compid', 'DEFAULT_COMP_ID') # Replace with actual logic
    finyearid = request.session.get('finyear', 'DEFAULT_FINYEAR_ID') # Replace with actual logic
    return {'CompId': compid, 'FinYearId': finyearid}

class CategoryListView(TemplateView):
    """
    Main view for displaying the WO Categories list page.
    The actual table content is loaded via HTMX by CategoryTablePartialView.
    """
    template_name = 'category/category_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Messages from successful operations will be displayed here
        return context

class CategoryTablePartialView(ListView):
    """
    HTMX-specific view to render only the DataTable content.
    Used for initial load and after CRUD operations to refresh the table.
    """
    model = Category
    template_name = 'category/_category_table.html'
    context_object_name = 'categories'

    def get_queryset(self):
        # Apply the same filtering as ASP.NET code (CompId and FinYearId)
        session_context = get_session_context(self.request)
        return Category.objects.filter(
            CompId=session_context['CompId'],
            FinYearId__lte=session_context['FinYearId'] # ASP.NET uses <= for FinYearId
        )

class CategoryCreateView(CreateView):
    model = Category
    form_class = CategoryForm
    template_name = 'category/_category_form.html' # This will be loaded into a modal
    success_url = reverse_lazy('category_list') # Redirect not used directly with HTMX, but for non-HTMX flow

    def form_valid(self, form):
        # Set CompId and FinYearId from session before saving
        session_context = get_session_context(self.request)
        form.instance.CompId = session_context['CompId']
        form.instance.FinYearId = session_context['FinYearId']

        # For create, CId might be auto-generated by the database or handled otherwise.
        # If CId is truly an IntegerField and not auto-incrementing, you might need a custom PK generator
        # or ensure the database handles it. Assuming the DB generates it.
        # If the DB does not auto-generate PKs, you'd need to set form.instance.CId here
        # or use a Sequence in SQL. For now, we assume PK is handled by DB for new records or it's a fixed value.

        response = super().form_valid(form)
        messages.success(self.request, 'WO Category added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing more than trigger
                headers={
                    'HX-Trigger': 'refreshCategoryList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # If form is invalid and it's an HTMX request, return the form with errors
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class CategoryUpdateView(UpdateView):
    model = Category
    form_class = CategoryForm
    template_name = 'category/_category_form.html' # This will be loaded into a modal
    context_object_name = 'category' # Name for the instance in template
    success_url = reverse_lazy('category_list')

    def get_object(self, queryset=None):
        # Ensure the object retrieved belongs to the current CompId and FinYearId if needed for security
        obj = super().get_object(queryset)
        session_context = get_session_context(self.request)
        # This check mirrors the ASP.NET filtering; if not found, it's a 404
        if obj.CompId != session_context['CompId'] or obj.FinYearId > session_context['FinYearId']:
            raise Http404("Category not found or not accessible.")
        return obj

    def form_valid(self, form):
        # Business logic: Check if category can be updated before saving
        if not form.instance.can_be_updated():
            # ASP.NET showed an alert. We can add a form error or messages.error
            messages.error(self.request, 'You cannot edit this record, it is being used.')
            # Re-render the form with error for HTMX
            if self.request.headers.get('HX-Request'):
                return render(self.request, self.template_name, {'form': form})
            return super().form_invalid(form) # Fallback for non-HTMX

        response = super().form_valid(form)
        messages.success(self.request, 'WO Category updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCategoryList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class CategoryDeleteView(DeleteView):
    model = Category
    template_name = 'category/_category_confirm_delete.html' # Loaded into a modal
    context_object_name = 'category'
    success_url = reverse_lazy('category_list')

    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        session_context = get_session_context(self.request)
        if obj.CompId != session_context['CompId'] or obj.FinYearId > session_context['FinYearId']:
            raise Http404("Category not found or not accessible.")
        return obj

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Business logic: Check if category can be deleted
        if not self.object.can_be_deleted():
            messages.error(self.request, 'You cannot delete this record, it is being used.')
            # For HTMX, we can return a 200 OK with the same modal but an error message
            if request.headers.get('HX-Request'):
                # Render the delete confirmation template again but with the error message
                return render(request, self.template_name, {'category': self.object, 'error_message': 'Cannot delete: Category is in use.'})
            return super().delete(request, *args, **kwargs) # This path would normally raise PermissionDenied or redirect.

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'WO Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to clear the modal and trigger refresh
                headers={
                    'HX-Trigger': 'refreshCategoryList'
                }
            )
        return response
```

#### 4.4 Templates (`category/templates/category/`)

**Task:** Create templates for each view, leveraging HTMX and Alpine.js.

**Instructions:**

-   `category_list.html`: The main page, extends `core/base.html`. Contains the structure for the modal and a container for the HTMX-loaded table.
-   `_category_table.html`: A partial template containing only the DataTable `<table>` structure, rendered by `CategoryTablePartialView`. This is dynamically updated via HTMX. Includes DataTables JS initialization.
-   `_category_form.html`: A partial template for both create and update forms, loaded into the modal.
-   `_category_confirm_delete.html`: A partial template for delete confirmation, loaded into the modal.

##### `category/templates/category/category_list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">WO Categories - Edit</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'category_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modalBackground then add .scale-100 to #modalPanel"
        >
            Add New Category
        </button>
    </div>

    <!-- Messages display area -->
    {% if messages %}
    <div id="messages" x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)"
         class="mb-4 p-3 rounded-md shadow-sm text-sm {% if 'success' in message.tags %}bg-green-100 text-green-700{% elif 'error' in message.tags %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
        {% for message in messages %}
        <p {% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</p>
        {% endfor %}
    </div>
    {% endif %}
    
    <div
        id="categoryTable-container"
        hx-trigger="load, refreshCategoryList from:body"
        hx-get="{% url 'category_table' %}"
        hx-swap="innerHTML"
        class="bg-white shadow-md rounded-lg overflow-hidden"
    >
        <!-- Initial loading state -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading categories...</p>
        </div>
    </div>

    <!-- Modal structure -->
    <div id="modal" class="fixed inset-0 z-50 hidden items-center justify-center"
         x-data="{ open: false }" x-show="open"
         _="on htmx:afterOnLoad from #modalContent if !event.detail.failedResponse then set open to true
            on click.outside #modalPanel set open to false
            on closeModal set open to false
            on htmx:afterRequest from #modalContent if event.detail.xhr.status == 204 set open to false"
    >
        <div id="modalBackground" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity ease-out duration-300"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
        ></div>

        <div id="modalPanel" class="relative bg-white rounded-lg shadow-xl transform transition-all sm:max-w-xl sm:w-full"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
            <div id="modalContent" class="p-6">
                <!-- Content loaded by HTMX -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js setup for modal management is mostly in the HTML attributes directly
        // on the modal element.
        console.log('Alpine.js initialized.');
    });

    // Listen for HTMX success event and trigger modal close
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // This case is handled by `on htmx:afterRequest from #modalContent` Alpine.js directive
            // which sets `open` to false.
        }
        // If messages are returned, fade them out
        const messagesDiv = document.getElementById('messages');
        if (messagesDiv) {
            messagesDiv.setAttribute('x-data', '{ show: true }');
            messagesDiv.setAttribute('x-show', 'show');
            messagesDiv.setAttribute('x-init', 'setTimeout(() => show = false, 5000)');
        }
    });

    // Global HTMX event listener for after requests
    document.body.addEventListener('htmx:afterRequest', function(event) {
        // Close modal if a 204 status (No Content) is received, which typically means success with no UI update.
        if (event.detail.xhr.status === 204) {
            document.querySelector('#modal')._x_dataStack[0].open = false; // Access Alpine.js instance
        }
    });
</script>
{% endblock %}
```

##### `category/templates/category/_category_table.html`

```html
<table id="categoryTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Has SubCategory</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for category in categories %}
        <tr>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ category.CName }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ category.Symbol|default:"-" }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                {% if category.HasSubCat %}Yes{% else %}No{% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                {% if category.can_be_updated %}
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'category_edit' category.CId %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modalBackground then add .scale-100 to #modalPanel"
                >
                    Edit
                </button>
                {% else %}
                <button
                    class="bg-gray-400 text-gray-700 font-bold py-1 px-3 rounded-md text-xs mr-2 cursor-not-allowed"
                    disabled
                    title="Cannot edit: Category in use or has subcategories."
                >
                    Edit
                </button>
                {% endif %}

                {% if category.can_be_deleted %}
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'category_delete' category.CId %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modalBackground then add .scale-100 to #modalPanel"
                >
                    Delete
                </button>
                {% else %}
                <button
                    class="bg-gray-400 text-gray-700 font-bold py-1 px-3 rounded-md text-xs cursor-not-allowed"
                    disabled
                    title="Cannot delete: Category is in use."
                >
                    Delete
                </button>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#categoryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions
            ]
        });
    });
</script>
```

##### `category/templates/category/_category_form.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} WO Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML">
        {% csrf_token %}

        <div class="space-y-5">
            <div>
                <label for="{{ form.CName.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.CName.label }}<span class="text-red-500">*</span>
                </label>
                <div class="mt-1">
                    {{ form.CName }}
                </div>
                {% if form.CName.errors %}
                <p class="mt-2 text-sm text-red-600">{{ form.CName.errors.as_text }}</p>
                {% endif %}
            </div>

            <div class="flex items-center">
                {{ form.HasSubCat }}
                <label for="{{ form.HasSubCat.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-900">
                    {{ form.HasSubCat.label }}
                </label>
                {% if form.HasSubCat.errors %}
                <p class="mt-2 text-sm text-red-600">{{ form.HasSubCat.errors.as_text }}</p>
                {% endif %}
            </div>

            <!-- Display non-editable fields if available in context -->
            {% if form.instance.Symbol %}
            <div>
                <label class="block text-sm font-medium text-gray-700">Symbol</label>
                <p class="mt-1 text-sm text-gray-900">{{ form.instance.Symbol }}</p>
            </div>
            {% endif %}

        </div>

        {% if form.non_field_errors %}
        <div class="mt-5 text-red-600 text-sm">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        {% if error_message %}
        <div class="mt-5 text-red-600 text-sm">
            <p>{{ error_message }}</p>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click send closeModal to #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            >
                Save Category
            </button>
        </div>
    </form>
</div>
```

##### `category/templates/category/_category_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the category: <strong>{{ category.CName }}</strong>?</p>

    {% if error_message %}
    <div class="mb-5 text-red-600 text-sm">
        <p>{{ error_message }}</p>
    </div>
    {% endif %}

    <div class="mt-8 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
            _="on click send closeModal to #modal"
        >
            Cancel
        </button>
        <button
            hx-post="{% url 'category_delete' category.CId %}"
            hx-swap="none"
            type="button"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
        >
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`category/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**

-   Paths for list, create, update, delete, and the HTMX-specific table partial view.
-   Consistent naming patterns.

```python
from django.urls import path
from .views import (
    CategoryListView,
    CategoryCreateView,
    CategoryUpdateView,
    CategoryDeleteView,
    CategoryTablePartialView
)

urlpatterns = [
    # Main list page
    path('categories/', CategoryListView.as_view(), name='category_list'),

    # HTMX partial for the table content
    path('categories/table/', CategoryTablePartialView.as_view(), name='category_table'),

    # CRUD operations loaded into modal via HTMX
    path('categories/add/', CategoryCreateView.as_view(), name='category_add'),
    path('categories/edit/<int:pk>/', CategoryUpdateView.as_view(), name='category_edit'),
    path('categories/delete/<int:pk>/', CategoryDeleteView.as_view(), name='category_delete'),
]
```

#### 4.6 Tests (`category/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**

-   Cover `Category` model methods (`can_be_deleted`, `can_be_updated`).
-   Test all view endpoints (list, create, update, delete) for success and failure conditions, including HTMX interactions.
-   Ensure test data accurately reflects scenarios (e.g., categories with/without work orders/subcategories).

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import Category

# Mock the session context for consistency in tests
def mock_get_session_context(request):
    return {'CompId': 'TEST_COMP_ID', 'FinYearId': '2023'}

@patch('category.views.get_session_context', side_effect=mock_get_session_context)
class CategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.category1 = Category.objects.create(
            CId=1, CName='Category A', Symbol='CA', HasSubCat=False,
            CompId='TEST_COMP_ID', FinYearId='2023'
        )
        cls.category2 = Category.objects.create(
            CId=2, CName='Category B', Symbol='CB', HasSubCat=True,
            CompId='TEST_COMP_ID', FinYearId='2023'
        )
        cls.category3 = Category.objects.create(
            CId=3, CName='Category C', Symbol='CC', HasSubCat=True,
            CompId='TEST_COMP_ID', FinYearId='2022' # Different finyear for filtering tests
        )
        cls.category_other_comp = Category.objects.create(
            CId=4, CName='Category D', Symbol='CD', HasSubCat=False,
            CompId='OTHER_COMP_ID', FinYearId='2023'
        )

    def test_category_creation(self, mock_get_session_context):
        self.assertEqual(self.category1.CName, 'Category A')
        self.assertEqual(self.category2.HasSubCat, True)
        self.assertEqual(self.category1.CompId, 'TEST_COMP_ID')

    def test_str_representation(self, mock_get_session_context):
        self.assertEqual(str(self.category1), 'Category A')
        
    @patch('category.models.Category.SD_Cust_WorkOrder_Master.objects.filter')
    def test_has_associated_work_orders(self, mock_filter, mock_get_session_context):
        mock_filter.return_value.exists.return_value = True
        self.assertTrue(self.category1.has_associated_work_orders())
        mock_filter.return_value.exists.return_value = False
        self.assertFalse(self.category1.has_associated_work_orders())

    @patch('category.models.Category.TblSD_WO_SubCategory.objects.filter')
    def test_has_associated_subcategories(self, mock_filter, mock_get_session_context):
        mock_filter.return_value.exists.return_value = True
        self.assertTrue(self.category2.has_associated_subcategories())
        mock_filter.return_value.exists.return_value = False
        self.assertFalse(self.category2.has_associated_subcategories())

    @patch('category.models.Category.has_associated_work_orders')
    def test_can_be_deleted(self, mock_has_wo, mock_get_session_context):
        mock_has_wo.return_value = False
        self.assertTrue(self.category1.can_be_deleted())
        mock_has_wo.return_value = True
        self.assertFalse(self.category1.can_be_deleted())

    @patch('category.models.Category.has_associated_work_orders')
    @patch('category.models.Category.has_associated_subcategories')
    def test_can_be_updated(self, mock_has_subcat, mock_has_wo, mock_get_session_context):
        # Case 1: No work orders, no subcategories, HasSubCat=False -> Can update
        mock_has_wo.return_value = False
        mock_has_subcat.return_value = False
        self.category1.HasSubCat = False # Ensure this is the initial state for the test
        self.assertTrue(self.category1.can_be_updated())

        # Case 2: Has work orders -> Cannot update
        mock_has_wo.return_value = True
        self.assertFalse(self.category1.can_be_updated())
        mock_has_wo.return_value = False # Reset for next test

        # Case 3: HasSubCat=True AND has subcategories -> Cannot update
        self.category2.HasSubCat = True # Current state of category2
        mock_has_subcat.return_value = True # Category2 has actual subcategories
        self.assertFalse(self.category2.can_be_updated())

        # Case 4: HasSubCat=True BUT no subcategories -> Can update (ASP.NET rule is strict only if subcats exist)
        mock_has_subcat.return_value = False
        self.assertTrue(self.category2.can_be_updated())

        # Case 5: HasSubCat=False, has subcategories -> Can update (This should not happen logically, but ASP.NET allows if HasSubCat is false)
        self.category1.HasSubCat = False
        mock_has_subcat.return_value = True
        self.assertTrue(self.category1.can_be_updated())


@patch('category.views.get_session_context', side_effect=mock_get_session_context)
class CategoryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.category1 = Category.objects.create(
            CId=10, CName='Test Cat 1', Symbol='T1', HasSubCat=False,
            CompId='TEST_COMP_ID', FinYearId='2023'
        )
        self.category2 = Category.objects.create(
            CId=11, CName='Test Cat 2', Symbol='T2', HasSubCat=True,
            CompId='TEST_COMP_ID', FinYearId='2023'
        )
        # Category that should be restricted for update/delete (mock business rules)
        self.restricted_category = Category.objects.create(
            CId=12, CName='Restricted Cat', Symbol='RC', HasSubCat=True,
            CompId='TEST_COMP_ID', FinYearId='2023'
        )

    def test_list_view(self, mock_get_session_context):
        response = self.client.get(reverse('category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'category/category_list.html')

    def test_table_partial_view(self, mock_get_session_context):
        response = self.client.get(reverse('category_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'category/_category_table.html')
        self.assertContains(response, 'Test Cat 1')
        self.assertContains(response, 'Test Cat 2')
        # Check filtering by CompId and FinYearId
        Category.objects.create(CId=13, CName='Old Cat', Symbol='OC', HasSubCat=False, CompId='TEST_COMP_ID', FinYearId='2021')
        Category.objects.create(CId=14, CName='Other Comp Cat', Symbol='OT', HasSubCat=False, CompId='OTHER_COMP_ID', FinYearId='2023')
        response = self.client.get(reverse('category_table'))
        self.assertContains(response, 'Old Cat') # FinYearId__lte=2023
        self.assertNotContains(response, 'Other Comp Cat') # Wrong CompId

    def test_create_view_get(self, mock_get_session_context):
        response = self.client.get(reverse('category_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'category/_category_form.html')
        self.assertContains(response, 'Add WO Category')

    def test_create_view_post_success(self, mock_get_session_context):
        # We need to mock CId to be set, assuming DB handles it.
        # If DB auto-increments, no need to set. If it's a fixed value, set it.
        # For tests, we can patch objects.create or simply let it create, then check existence.
        # For CId, using `max_id` + 1 for testing purposes.
        max_cid = Category.objects.all().order_by('-CId').first().CId if Category.objects.exists() else 0
        new_cid = max_cid + 1

        data = {
            'CId': new_cid, # Include CId if it's not auto-incrementing in the DB
            'CName': 'New Category',
            'HasSubCat': False
        }
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCategoryList', response.headers['HX-Trigger'])
        self.assertTrue(Category.objects.filter(CName='New Category', CompId='TEST_COMP_ID', FinYearId='2023').exists())

    def test_create_view_post_invalid(self, mock_get_session_context):
        data = {
            'CName': '', # Missing required field
            'HasSubCat': False
        }
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'category/_category_form.html')
        self.assertContains(response, 'Category Name is required.')

    def test_update_view_get(self, mock_get_session_context):
        response = self.client.get(reverse('category_edit', args=[self.category1.CId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'category/_category_form.html')
        self.assertContains(response, 'Edit WO Category')
        self.assertContains(response, 'value="Test Cat 1"')

    @patch('category.models.Category.can_be_updated', return_value=True)
    def test_update_view_post_success(self, mock_can_be_updated, mock_get_session_context):
        data = {
            'CName': 'Updated Category',
            'HasSubCat': True
        }
        response = self.client.post(reverse('category_edit', args=[self.category1.CId]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.CName, 'Updated Category')
        self.assertEqual(self.category1.HasSubCat, True)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCategoryList', response.headers['HX-Trigger'])

    @patch('category.models.Category.can_be_updated', return_value=False)
    def test_update_view_post_restricted(self, mock_can_be_updated, mock_get_session_context):
        data = {
            'CName': 'Attempt to update',
            'HasSubCat': False
        }
        response = self.client.post(reverse('category_edit', args=[self.restricted_category.CId]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form with error
        self.assertContains(response, 'You cannot edit this record, it is being used.')
        # Ensure the category was not updated
        self.restricted_category.refresh_from_db()
        self.assertNotEqual(self.restricted_category.CName, 'Attempt to update')

    @patch('category.models.Category.can_be_deleted', return_value=True)
    def test_delete_view_get(self, mock_can_be_deleted, mock_get_session_context):
        response = self.client.get(reverse('category_delete', args=[self.category1.CId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'category/_category_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')

    @patch('category.models.Category.can_be_deleted', return_value=True)
    def test_delete_view_post_success(self, mock_can_be_deleted, mock_get_session_context):
        response = self.client.post(reverse('category_delete', args=[self.category1.CId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Category.objects.filter(CId=self.category1.CId).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCategoryList', response.headers['HX-Trigger'])

    @patch('category.models.Category.can_be_deleted', return_value=False)
    def test_delete_view_post_restricted(self, mock_can_be_deleted, mock_get_session_context):
        response = self.client.post(reverse('category_delete', args=[self.restricted_category.CId]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render confirmation with error
        self.assertContains(response, 'You cannot delete this record, it is being used.')
        # Ensure the category was not deleted
        self.assertTrue(Category.objects.filter(CId=self.restricted_category.CId).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX:**
    -   `category_list.html` uses `hx-get="{% url 'category_table' %}" hx-trigger="load, refreshCategoryList from:body"` to load the DataTable initially and refresh it after CRUD operations.
    -   Add/Edit/Delete buttons use `hx-get` to fetch partial forms (`_category_form.html`, `_category_confirm_delete.html`) and `hx-target="#modalContent"` to load them into the modal.
    -   Form submissions (`hx-post`) from within the modal target the same modal content, or use `hx-swap="none"` with `status=204` responses to trigger a global `HX-Trigger` event (`refreshCategoryList`).
-   **Alpine.js:**
    -   The modal (`#modal`) uses `x-data="{ open: false }"` and `x-show="open"` for visibility control.
    -   `on click` directives are used on buttons to toggle `open` status or send custom events (`closeModal`).
    -   `on htmx:afterOnLoad` and `on htmx:afterRequest` directives within the modal manage its state (e.g., show modal after content loaded, hide after 204 status).
    -   Messages from Django's `messages` framework are displayed in `category_list.html` with Alpine.js `x-data` and `x-init` for auto-fading.
-   **DataTables:**
    -   The `_category_table.html` partial includes the `$(document).ready(function() { $('#categoryTable').DataTable({...}); });` script, ensuring DataTables initializes each time the table partial is loaded via HTMX.
    -   Client-side searching, sorting, and pagination are handled automatically by DataTables.

### Final Notes

-   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with the specific details derived from the ASP.NET code (e.g., `Category`, `tblSD_WO_Category`).
-   **DRY Templates:** The use of `_category_table.html`, `_category_form.html`, and `_category_confirm_delete.html` as partials ensures that UI components are reusable and rendered dynamically via HTMX, avoiding redundant code.
-   **Fat Model, Thin View:** Business logic, such as `can_be_deleted` and `can_be_updated`, is encapsulated within the `Category` model, keeping the `views.py` concise and focused on orchestrating data flow and responses.
-   **Comprehensive Tests:** Unit tests for model methods and integration tests for views cover the core functionality and critical business rules, ensuring the migrated application behaves as expected.
-   **HTMX and Alpine.js:** All user interactions (adding, editing, deleting records, refreshing the list) are designed to occur without full page reloads, providing a smooth, modern user experience.