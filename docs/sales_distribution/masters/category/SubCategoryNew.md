## ASP.NET to Django Conversion Script: SubCategory Management

This document outlines a comprehensive plan to migrate the ASP.NET SubCategory management functionality to a modern Django application. Our approach focuses on automation, clean architecture, and the use of cutting-edge web technologies like HTMX and Alpine.js to deliver a highly performant and user-friendly experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`SubCategory`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the `SqlDataSource` definitions, we identify two key tables: `tblSD_WO_SubCategory` and `tblSD_WO_Category`.

**Inferred Database Schema:**

**Table: `tblSD_WO_SubCategory`**
- `SCId` (Primary Key, Integer)
- `CId` (Foreign Key, Integer, references `tblSD_WO_Category.CId`)
- `SCName` (String)
- `Symbol` (String, Max Length 1)
- `CompId` (Integer)
- `SysDate` (String, stored as date in legacy, will convert to `DateTimeField`)
- `SysTime` (String, stored as time in legacy, will convert to `DateTimeField`)
- `FinYearId` (Integer)
- `SessionId` (String)

**Table: `tblSD_WO_Category`**
- `CId` (Primary Key, Integer)
- `CName` (String)
- `Symbol` (String)
- `CompId` (Integer)
- `FinYearId` (Integer)
- `HasSubCat` (Boolean, inferred from `!='0'` check)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements **Create** and **Read** operations.

-   **Create**: New SubCategory records are added via the GridView footer (command "Add") or EmptyDataTemplate (command "Add1").
    -   Key logic: Data validation (required fields), session-based parameter injection (`CompId`, `FinYearId`, `SessionId`, `SysDate`, `SysTime`), and a uniqueness check for `Symbol` combined with `CId`, `CompId`, and `FinYearId`.
-   **Read**: SubCategory records are retrieved and displayed in a paged grid.
-   **Update**: No explicit update functionality is present in the provided ASP.NET code. As per the requirements, we will implement standard update functionality.
-   **Delete**: No explicit delete functionality is present. As per the requirements, we will implement standard delete functionality.

**Validation Logic Identified:**
-   `SCName` and `Symbol` are required fields.
-   The combination of `Symbol`, `CId`, `CompId`, and `FinYearId` must be unique for new entries.

### Step 3: Infer UI Components

The ASP.NET `GridView` is the central UI component, displaying tabular data with pagination and an inline form for adding new entries.

-   **Data Display:** `GridView` (to be replaced by DataTables in a Django template).
-   **Input Fields:** `asp:TextBox` (for `SCName`, `Symbol`) and `asp:DropDownList` (for `Category`). These will be replaced by Django `forms.TextInput` and `forms.ModelChoiceField` respectively.
-   **Actions:** `asp:Button` for "Insert". Edit and Delete actions will be added as standard HTMX buttons on each row.
-   **Messages:** `asp:Label` (`lblMessage`) for displaying feedback. This will be handled by Django's messages framework and HTMX `HX-Trigger` to notify the UI.

---

### Step 4: Generate Django Code

We will structure the Django application under `sales_distribution` to manage subcategories.

#### 4.1 Models (`sales_distribution/models.py`)

We'll define two models: `Category` (as `SubCategory` depends on it) and `SubCategory`. The `SubCategory` model will encapsulate the business logic for uniqueness and auto-population of system fields.

```python
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError

class Category(models.Model):
    # Using existing CId as primary key, though Django typically uses 'id'
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    has_subcat = models.BooleanField(db_column='HasSubCat', default=True) # Inferred from '!='0'

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblSD_WO_Category'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class SubCategory(models.Model):
    # Using existing SCId as primary key
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    category = models.ForeignKey(Category, on_delete=models.PROTECT, db_column='CId', related_name='subcategories')
    scname = models.CharField(db_column='SCName', max_length=255, verbose_name='SubCategory Name')
    symbol = models.CharField(db_column='Symbol', max_length=1, verbose_name='Symbol') # MaxLength=1 as per ASP.NET

    # System/Audit fields as found in ASP.NET
    compid = models.IntegerField(db_column='CompId')
    sysdate = models.CharField(db_column='SysDate', max_length=10) # Stored as string in legacy
    systime = models.CharField(db_column='SysTime', max_length=8)  # Stored as string in legacy
    finyearid = models.IntegerField(db_column='FinYearId')
    sessionid = models.CharField(db_column='SessionId', max_length=255)

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblSD_WO_SubCategory'
        verbose_name = 'Sub Category'
        verbose_name_plural = 'Sub Categories'
        # Composite unique constraint for the symbol validation
        unique_together = (('symbol', 'category', 'compid', 'finyearid'),)


    def __str__(self):
        return f"{self.scname} ({self.symbol})"

    def clean(self):
        # Business logic for unique symbol per category/company/financial year
        if self.symbol:
            self.symbol = self.symbol.upper() # Ensure symbol is uppercase as in legacy code

        # Uniqueness check (handled by unique_together in Meta, but keeping specific check for clarity and error message)
        # This explicit check also handles case-insensitivity if needed or more complex logic
        if self.pk is None: # Only for new objects
            if SubCategory.objects.filter(
                symbol=self.symbol,
                category=self.category,
                compid=self.compid,
                finyearid=self.finyearid
            ).exists():
                raise ValidationError("SubCategory symbol is already used for this Category, Company, and Financial Year.")
    
    def save(self, *args, **kwargs):
        # Auto-populate system fields before saving
        # Assuming request context (e.g., user, session data) is passed via view/form or accessible
        # For demonstration, we'll use placeholder values that would ideally come from session/user context.
        # In a real app, you might use request.user.compid, request.session.get('finyear'), etc.
        # Or have a middleware or custom manager set these.
        
        # This is a placeholder for how these values would be set.
        # For testing, we'll set sensible defaults or pass them.
        if not self.sysdate:
            self.sysdate = timezone.now().strftime('%d-%m-%Y') # Matches legacy string format
        if not self.systime:
            self.systime = timezone.now().strftime('%H:%M:%S') # Matches legacy string format
        
        # These would come from the user's session or profile
        # For simplicity in this auto-generated code, they would be passed from the view/form
        # Example: self.compid = request.user.profile.company_id
        # Example: self.finyearid = request.session.get('current_fin_year_id')
        # Example: self.sessionid = request.user.username
        
        # Ensure symbol is uppercase upon saving, as in legacy code
        self.symbol = self.symbol.upper()

        super().save(*args, **kwargs)

```

#### 4.2 Forms (`sales_distribution/forms.py`)

The form will handle user input for `Category`, `SubCategory Name`, and `Symbol`. We will also implement custom validation logic if needed beyond `unique_together` defined in the model.

```python
from django import forms
from .models import SubCategory, Category

class SubCategoryForm(forms.ModelForm):
    # Category dropdown to filter by HasSubCat and display 'Symbol - CName'
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(has_subcat=True), # Filter categories that 'HasSubCat'
        empty_label="Select Category",
        label="Category",
        to_field_name='cid', # Use cid as the value for the dropdown
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = SubCategory
        fields = ['category', 'scname', 'symbol']
        widgets = {
            'scname': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter SubCategory Name'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'maxlength': '1', 'placeholder': 'Enter Symbol (1 char)'}),
        }
        
    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # Get request object to access session/user
        super().__init__(*args, **kwargs)
        # Customize category queryset if dynamic filtering by CompId/FinYearId is needed
        # self.fields['category'].queryset = Category.objects.filter(
        #     compid=self.request.user.profile.company_id,
        #     finyearid=self.request.session.get('current_fin_year_id'),
        #     has_subcat=True
        # )

    def clean_symbol(self):
        symbol = self.cleaned_data['symbol']
        return symbol.upper() # Ensure symbol is uppercase for consistency and validation

    def clean(self):
        cleaned_data = super().clean()
        
        # Placeholder for dynamic CompId and FinYearId from session/user
        # In a real app, these would come from request.user or request.session
        compid_val = self.request.user.get_company_id() if self.request and hasattr(self.request.user, 'get_company_id') else 1 # Example placeholder
        finyearid_val = self.request.session.get('finyear_id', 1) if self.request else 1 # Example placeholder
        
        category = cleaned_data.get('category')
        symbol = cleaned_data.get('symbol')

        if category and symbol:
            # Check for existing symbol only if adding a new object
            if not self.instance.pk: # For create operations
                if SubCategory.objects.filter(
                    symbol=symbol,
                    category=category,
                    compid=compid_val,
                    finyearid=finyearid_val
                ).exists():
                    self.add_error('symbol', "This symbol is already used for the selected Category, Company, and Financial Year.")
        
        # Populate CompId, FinYearId, SessionId into instance for model's save method
        if self.instance:
            self.instance.compid = compid_val
            self.instance.finyearid = finyearid_val
            self.instance.sessionid = self.request.user.username if self.request and self.request.user.is_authenticated else 'unknown'

        return cleaned_data
```

#### 4.3 Views (`sales_distribution/views.py`)

Views will be thin, primarily handling HTTP requests, form validation, and delegating business logic to the models or forms. They will interact with HTMX for dynamic content updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.db import IntegrityError # For handling unique_together errors

from .models import SubCategory
from .forms import SubCategoryForm

class SubCategoryListView(ListView):
    model = SubCategory
    template_name = 'sales_distribution/subcategory/list.html'
    context_object_name = 'subcategories'

    # The actual data for the DataTable is fetched by HTMX via SubCategoryTablePartialView
    def get_queryset(self):
        # This queryset might be used for initial context if needed, but not for DataTable itself
        # For actual DataTable, data will be fetched by the partial view, potentially filtered by session
        # Example: Filter by current user's company and financial year
        # compid = self.request.user.get_company_id() if hasattr(self.request.user, 'get_company_id') else 1
        # finyearid = self.request.session.get('finyear_id', 1)
        # return SubCategory.objects.filter(compid=compid, finyearid__lte=finyearid)
        return SubCategory.objects.all() # Return all for now, filtering handled in partial view if needed


class SubCategoryTablePartialView(ListView):
    model = SubCategory
    template_name = 'sales_distribution/subcategory/_subcategory_table.html'
    context_object_name = 'subcategories'

    def get_queryset(self):
        # Implement filtering based on session data as in ASP.NET SqlDataSource
        # This logic needs to be dynamic based on your session/user management in Django
        # Example: Assuming user has 'company_id' and 'financial_year_id' attributes
        # And assuming tblSD_WO_Category.FinYearId <= @FinYearId maps to FinYearId <= current_fin_year_id
        
        # Placeholder for actual session/user data
        compid_val = self.request.user.get_company_id() if hasattr(self.request.user, 'get_company_id') else 1 
        finyearid_val = self.request.session.get('finyear_id', 1) # Default to 1 if not found

        # Replicating the ASP.NET SELECT command's WHERE clause
        # SELECT ... FROM [tblSD_WO_SubCategory],[tblSD_WO_Category] Where [tblSD_WO_SubCategory].[CId]=[tblSD_WO_Category].[CId] And [tblSD_WO_SubCategory].[CompId] = @CompId AND [tblSD_WO_SubCategory].[FinYearId] <= @FinYearId order by [tblSD_WO_SubCategory].[SCId] desc
        queryset = SubCategory.objects.select_related('category').filter(
            compid=compid_val,
            finyearid__lte=finyearid_val # Use __lte for '<='
        ).order_by('-scid') # Order by SCId desc

        return queryset

class SubCategoryCreateView(CreateView):
    model = SubCategory
    form_class = SubCategoryForm
    template_name = 'sales_distribution/subcategory/_subcategory_form.html' # Use partial template
    success_url = reverse_lazy('subcategory_list') # Not directly used for HTMX, but good practice

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request # Pass request to form for session/user data
        return kwargs

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Sub Category added successfully.')
            # HTMX response for successful form submission
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # No content, indicates success without navigating
                    headers={
                        'HX-Trigger': 'refreshSubCategoryList, closeModals' # Custom event to refresh list and close modal
                    }
                )
            return response
        except IntegrityError:
            # Handle potential unique constraint violation if not caught by form.clean
            messages.error(self.request, "A sub category with this symbol already exists for the selected category, company, and financial year.")
            return self.form_invalid(form)


    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        if self.request.headers.get('HX-Request'):
            # Re-render the form with errors for HTMX
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class SubCategoryUpdateView(UpdateView):
    model = SubCategory
    form_class = SubCategoryForm
    template_name = 'sales_distribution/subcategory/_subcategory_form.html'
    success_url = reverse_lazy('subcategory_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Sub Category updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSubCategoryList, closeModals'
                    }
                )
            return response
        except IntegrityError:
            messages.error(self.request, "A sub category with this symbol already exists for the selected category, company, and financial year.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class SubCategoryDeleteView(DeleteView):
    model = SubCategory
    template_name = 'sales_distribution/subcategory/_subcategory_confirm_delete.html'
    success_url = reverse_lazy('subcategory_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Sub Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSubCategoryList, closeModals'
                }
            )
        return response

    def get(self, request, *args, **kwargs):
        # Render delete confirmation for HTMX
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return self.render_to_response(context)
```

#### 4.4 Templates

Templates will be split into a main list view and partials for the table, forms, and delete confirmation, facilitating HTMX reloads.

**`sales_distribution/subcategory/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Sub Categories</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'subcategory_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
            aria-label="Add New Sub Category">
            <i class="fas fa-plus-circle mr-2"></i> Add New Sub Category
        </button>
    </div>
    
    <div id="subcategoryTable-container"
         hx-trigger="load, refreshSubCategoryList from:body"
         hx-get="{% url 'subcategory_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-4">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Sub Categories...</p>
        </div>
    </div>
    
    <!-- Modals -->
    <div id="modal" class="fixed inset-0 z-50 hidden opacity-0 transition-opacity duration-300 ease-out flex items-center justify-center bg-gray-900 bg-opacity-50"
         _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from me then remove .scale-100 from #modalContent"
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        
        <div id="modalContent" class="bg-white rounded-lg shadow-xl transform scale-95 transition-transform duration-300 ease-out max-w-2xl w-full mx-4"
             _="on closeModals from body remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from me">
            <!-- Content will be loaded by HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading content...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            show: false,
            open() { this.show = true },
            close() { this.show = false },
        }));
    });

    // Initialize DataTable after HTMX content load
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'subcategoryTable-container') {
            $('#subcategoryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center flex-wrap"lf><"block w-full overflow-x-auto"t><"flex justify-between items-center flex-wrap"ip>',
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        }
    });
</script>
{% endblock %}
```

**`sales_distribution/subcategory/_subcategory_table.html`** (Partial for DataTables)

```html
<table id="subcategoryTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 rounded-lg shadow-sm">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Category</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SubCategory Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-100">
        {% for obj in subcategories %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.category.symbol }} - {{ obj.category.cname }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.scname }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1.5 px-3 rounded-md mr-2 transition duration-200 ease-in-out shadow-sm"
                    hx-get="{% url 'subcategory_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                    aria-label="Edit Sub Category">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-medium py-1.5 px-3 rounded-md transition duration-200 ease-in-out shadow-sm"
                    hx-get="{% url 'subcategory_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent"
                    aria-label="Delete Sub Category">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No Sub Categories found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`sales_distribution/subcategory/_subcategory_form.html`** (Partial for Create/Update)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Sub Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
            {% endif %}
            {% if field.help_text %}
            <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`sales_distribution/subcategory/_subcategory_confirm_delete.html`** (Partial for Delete)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-lg text-gray-700 mb-8">Are you sure you want to delete the Sub Category "<span class="font-bold">{{ object.scname }} ({{ object.symbol }})</span>" from Category "<span class="font-bold">{{ object.category.cname }}</span>"?</p>
    
    <form hx-post="{% url 'subcategory_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-200 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`sales_distribution/urls.py`)

```python
from django.urls import path
from .views import SubCategoryListView, SubCategoryCreateView, SubCategoryUpdateView, SubCategoryDeleteView, SubCategoryTablePartialView

urlpatterns = [
    path('subcategories/', SubCategoryListView.as_view(), name='subcategory_list'),
    path('subcategories/table/', SubCategoryTablePartialView.as_view(), name='subcategory_table'), # HTMX partial
    path('subcategories/add/', SubCategoryCreateView.as_view(), name='subcategory_add'),
    path('subcategories/<int:pk>/edit/', SubCategoryUpdateView.as_view(), name='subcategory_edit'),
    path('subcategories/<int:pk>/delete/', SubCategoryDeleteView.as_view(), name='subcategory_delete'),
]
```

#### 4.6 Tests (`sales_distribution/tests.py`)

Comprehensive tests for both models and views will ensure the migrated functionality is robust and correct.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from datetime import datetime
from unittest.mock import patch

from .models import Category, SubCategory

# Mock request.user methods for testing purposes if they are used in forms/views
class MockUser:
    def __init__(self, username='testuser', company_id=1, is_authenticated=True):
        self.username = username
        self.company_id = company_id
        self.is_authenticated = is_authenticated

    def get_company_id(self):
        return self.company_id

class SubCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a mock Category for foreign key
        cls.category1 = Category.objects.create(
            cid=101, cname='Test Category A', symbol='A', compid=1, finyearid=2023, has_subcat=True
        )
        cls.category2 = Category.objects.create(
            cid=102, cname='Test Category B', symbol='B', compid=1, finyearid=2023, has_subcat=True
        )

        # Create initial SubCategory for tests
        cls.subcategory1 = SubCategory.objects.create(
            scid=1,
            category=cls.category1,
            scname='Test SubCategory 1',
            symbol='X',
            compid=1,
            sysdate='01-01-2023',
            systime='10:00:00',
            finyearid=2023,
            sessionid='testuser'
        )
  
    def test_subcategory_creation(self):
        obj = SubCategory.objects.get(scid=1)
        self.assertEqual(obj.scname, 'Test SubCategory 1')
        self.assertEqual(obj.symbol, 'X')
        self.assertEqual(obj.category, self.category1)
        self.assertEqual(obj.compid, 1)
        self.assertEqual(obj.finyearid, 2023)
        self.assertEqual(obj.sessionid, 'testuser')
        
    def test_subcategory_str_representation(self):
        obj = SubCategory.objects.get(scid=1)
        self.assertEqual(str(obj), "Test SubCategory 1 (X)")

    def test_category_str_representation(self):
        self.assertEqual(str(self.category1), "A - Test Category A")

    def test_symbol_uppercase_on_save(self):
        new_sub = SubCategory(
            scid=2, category=self.category1, scname='New Sub', symbol='y',
            compid=1, finyearid=2023, sysdate='02-01-2023', systime='11:00:00', sessionid='testuser'
        )
        new_sub.save()
        self.assertEqual(new_sub.symbol, 'Y')
        
    def test_unique_symbol_validation(self):
        # This should fail due to unique_together constraint
        duplicate_sub = SubCategory(
            scid=3, category=self.category1, scname='Duplicate Sub', symbol='X',
            compid=1, finyearid=2023, sysdate='03-01-2023', systime='12:00:00', sessionid='testuser'
        )
        with self.assertRaises(ValidationError): # Model's clean method handles this
            duplicate_sub.full_clean() # Calls clean() and validates unique_together
        
        # Test unique for different category
        new_sub_different_category = SubCategory(
            scid=4, category=self.category2, scname='Unique Sub', symbol='X',
            compid=1, finyearid=2023, sysdate='04-01-2023', systime='13:00:00', sessionid='testuser'
        )
        try:
            new_sub_different_category.full_clean()
            new_sub_different_category.save()
        except ValidationError:
            self.fail("ValidationError raised unexpectedly for different category.")

    def test_category_has_subcat_filter(self):
        Category.objects.create(cid=103, cname='No SubCat', symbol='N', compid=1, finyearid=2023, has_subcat=False)
        form = SubCategoryForm()
        # Only categories with has_subcat=True should be in the queryset
        self.assertIn(self.category1, form.fields['category'].queryset)
        self.assertNotIn(Category.objects.get(cid=103), form.fields['category'].queryset)

class SubCategoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category1 = Category.objects.create(
            cid=101, cname='Test Category A', symbol='A', compid=1, finyearid=2023, has_subcat=True
        )
        cls.category2 = Category.objects.create(
            cid=102, cname='Test Category B', symbol='B', compid=1, finyearid=2023, has_subcat=True
        )
        cls.subcategory1 = SubCategory.objects.create(
            scid=1, category=cls.category1, scname='View SubCategory 1', symbol='V',
            compid=1, sysdate='01-01-2023', systime='10:00:00', finyearid=2023, sessionid='testuser'
        )
        cls.subcategory2 = SubCategory.objects.create(
            scid=2, category=cls.category2, scname='View SubCategory 2', symbol='W',
            compid=1, sysdate='01-01-2023', systime='10:00:00', finyearid=2023, sessionid='testuser'
        )
    
    def setUp(self):
        self.client = Client()
        self.mock_user = MockUser(username='testuser', company_id=1)
        self.client.force_login(self.mock_user) # Assuming user is logged in
        # Mocking get_company_id and finyear_id for form/view to pick up
        with patch('sales_distribution.forms.SubCategoryForm.clean') as mock_clean:
            mock_clean.return_value = {} # Allow clean to pass without custom logic for these tests
            self.client.session['finyear_id'] = 2023 # Mock session data

    def test_list_view(self):
        response = self.client.get(reverse('subcategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/list.html')
        # subcategories context will be empty as it's loaded by partial view
        self.assertContains(response, '<div id="subcategoryTable-container"')

    def test_table_partial_view_htmx(self):
        # Test for HTMX request for the table partial
        response = self.client.get(reverse('subcategory_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_table.html')
        self.assertContains(response, 'View SubCategory 1')
        self.assertContains(response, 'View SubCategory 2')
        self.assertContains(response, 'id="subcategoryTable"')

    def test_create_view_get(self):
        response = self.client.get(reverse('subcategory_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertTrue('form' in response.context)
        
    @patch('sales_distribution.models.SubCategory.save')
    def test_create_view_post_success(self, mock_save):
        mock_save.return_value = None # Prevent actual DB save for unit test
        data = {
            'category': self.category1.cid,
            'scname': 'New Test SubCategory',
            'symbol': 'Z',
        }
        # Simulate HTMX request
        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSubCategoryList', response.headers['HX-Trigger'])
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sub Category added successfully.')
        mock_save.assert_called_once()
        
    def test_create_view_post_invalid(self):
        data = {
            'category': self.category1.cid,
            'scname': '', # Invalid, required field
            'symbol': 'A',
        }
        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertFormError(response, 'form', 'scname', ['This field is required.'])
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_create_view_post_duplicate_symbol(self):
        # Create a duplicate entry data
        data = {
            'category': self.category1.cid,
            'scname': 'Another Sub',
            'symbol': 'V', # This symbol already exists for category1, compid=1, finyearid=2023
        }
        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertFormError(response, 'form', 'symbol', ["This symbol is already used for the selected Category, Company, and Financial Year."])
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors below.')

    def test_update_view_get(self):
        obj = self.subcategory1
        response = self.client.get(reverse('subcategory_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    @patch('sales_distribution.models.SubCategory.save')
    def test_update_view_post_success(self, mock_save):
        mock_save.return_value = None
        obj = self.subcategory1
        data = {
            'category': obj.category.cid,
            'scname': 'Updated SubCategory',
            'symbol': 'X', # Changed symbol
        }
        response = self.client.post(reverse('subcategory_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSubCategoryList', response.headers['HX-Trigger'])
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sub Category updated successfully.')
        mock_save.assert_called_once()
        # Verify changes (if mock_save wasn't used)
        # updated_obj = SubCategory.objects.get(pk=obj.pk)
        # self.assertEqual(updated_obj.scname, 'Updated SubCategory')

    def test_delete_view_get(self):
        obj = self.subcategory1
        response = self.client.get(reverse('subcategory_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/subcategory/_subcategory_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_success(self):
        obj_to_delete = SubCategory.objects.create(
            scid=99, category=self.category1, scname='Delete Test', symbol='D',
            compid=1, sysdate='01-01-2023', systime='10:00:00', finyearid=2023, sessionid='testuser'
        )
        self.assertTrue(SubCategory.objects.filter(pk=obj_to_delete.pk).exists())
        
        response = self.client.post(reverse('subcategory_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSubCategoryList', response.headers['HX-Trigger'])
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sub Category deleted successfully.')
        self.assertFalse(SubCategory.objects.filter(pk=obj_to_delete.pk).exists())
```

### Step 5: HTMX and Alpine.js Integration

-   **HTMX for dynamic updates:**
    -   The `list.html` template uses `hx-get` on `subcategoryTable-container` to load the table content dynamically from `{% url 'subcategory_table' %}`. It's triggered on `load` and `refreshSubCategoryList` event.
    -   Add/Edit/Delete buttons use `hx-get` to fetch the form/delete confirmation into the modal, targeting `#modalContent`.
    -   Form submissions use `hx-post` to the same URL, and on success (`status=204`), `HX-Trigger` sends `refreshSubCategoryList` and `closeModals` events to refresh the table and close the modal.
-   **Alpine.js for UI state management:**
    -   The modal in `list.html` uses `x-data` and `x-show` with transitions for smooth opening/closing.
    -   `_=` (hyperscript) attributes are used to control the modal's visibility (`add .flex to #modal`, `remove .flex from me`) based on HTMX triggers and click events.
-   **DataTables for list views:**
    -   The `_subcategory_table.html` partial contains the `<table>` element with `id="subcategoryTable"`.
    -   The `extra_js` block in `list.html` includes a JavaScript snippet that initializes DataTables on this table `id` after HTMX swaps the content into the DOM (`htmx:afterSwap` event).
-   **No full page reloads:** All CRUD operations and table refreshes are handled via HTMX, avoiding full page reloads.
-   **DRY Template Inheritance:** All templates extend `core/base.html`, adhering to DRY principles.
-   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for modern styling.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET SubCategory functionality to Django. By leveraging modern Django patterns, HTMX, Alpine.js, and DataTables, the new application will be efficient, maintainable, and provide a superior user experience, aligning with the business goals of modernization and improved performance. The emphasis on automation and clear instructions ensures that this process can be guided and overseen by non-technical stakeholders through conversational AI.