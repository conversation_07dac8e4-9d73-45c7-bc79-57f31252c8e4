## ASP.NET to Django Conversion Script: Customer Quotation Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

### Modernization Plan for Customer Quotation Module

The provided ASP.NET code for `CustQuotation.aspx` is very minimal, containing only content placeholders and an empty `Page_Load` method in its code-behind. This means there are no explicit UI controls, database interactions, or business logic defined within the provided snippets.

Therefore, this modernization plan will proceed by making common assumptions about a "Customer Quotation Report" page within an ERP system. We will design a full Django module that handles Customer Quotations, including a list view, and the typical Create, Read, Update, and Delete (CRUD) operations, demonstrating how a legacy report page could evolve into a full-featured, interactive module in Django.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the minimal ASP.NET code, we cannot directly extract database schema details. Based on the page name `CustQuotation.aspx` and its context in a `SalesDistribution_Reports` module, we will infer a database table that holds customer quotation information.

**Inference:**
-   **Table Name:** We will assume a table named `tblCustomerQuotations`.
-   **Columns:** We will assume the following columns, typical for a customer quotation:
    -   `QuotationId` (Primary Key, integer)
    -   `QuotationNo` (String, unique identifier for the quote)
    -   `CustomerName` (String, name of the customer)
    -   `QuotationDate` (Date, when the quote was issued)
    -   `TotalAmount` (Decimal, the total value of the quotation)
    -   `IsApproved` (Boolean, status of the quotation)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code snippet does not explicitly define any CRUD (Create, Read, Update, Delete) operations. The `Page_Load` method is empty, and there are no data source controls (like `SqlDataSource`) or UI controls (like `GridView` with associated commands) that would reveal data operations.

**Inference:**
While the ASP.NET page is a "Report" page, a modern ERP system would likely require not just viewing but also managing (creating, updating, deleting) customer quotations. For a comprehensive Django modernization, we will assume the need for all CRUD operations:
-   **Read:** Displaying a list of all customer quotations.
-   **Create:** Adding new customer quotations.
-   **Update:** Modifying existing customer quotations.
-   **Delete:** Removing customer quotations.
-   **Validation:** Basic field validation for required inputs and data types.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET code has no declared UI controls within the `Content` blocks. It only refers to a master page and includes a `loadingNotifier.js` script.

**Inference:**
-   **List View:** A `GridView` is the most common ASP.NET control for displaying lists of data. We will infer the need for a similar tabular display in Django, implemented using DataTables for features like sorting and pagination.
-   **Form View:** For Create/Update operations, `TextBoxes`, `DropDownLists`, and `Buttons` would typically be used. We will translate these into Django forms with appropriate HTML input types.
-   **Actions:** `Button` or `LinkButton` controls would trigger actions. In Django, these will be handled via HTMX interactions, triggering modal forms or delete confirmations.
-   **Client-Side Scripting:** The `loadingNotifier.js` suggests a need for client-side feedback during asynchronous operations. This will be handled gracefully by HTMX's built-in indicators and Alpine.js for managing modal states, reducing the need for custom JavaScript.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `sales`, to house the Customer Quotation module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
We will define the `CustomerQuotation` model mapping directly to the inferred `tblCustomerQuotations` table, ensuring `managed = False` for existing databases. We will also add a simple business logic method to demonstrate the "fat model" principle.

**File: `sales/models.py`**

```python
from django.db import models

class CustomerQuotation(models.Model):
    id = models.AutoField(db_column='QuotationId', primary_key=True)
    quotation_number = models.CharField(db_column='QuotationNo', max_length=50, unique=True, verbose_name="Quotation Number")
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer Name")
    quotation_date = models.DateField(db_column='QuotationDate', verbose_name="Quotation Date")
    total_amount = models.DecimalField(db_column='TotalAmount', max_digits=10, decimal_places=2, verbose_name="Total Amount")
    is_approved = models.BooleanField(db_column='IsApproved', default=False, verbose_name="Approved")

    class Meta:
        managed = False  # Important: Django won't manage this table's creation/deletion
        db_table = 'tblCustomerQuotations'
        verbose_name = 'Customer Quotation'
        verbose_name_plural = 'Customer Quotations'
        ordering = ['-quotation_date', 'quotation_number']

    def __str__(self):
        return f"Quote No: {self.quotation_number} for {self.customer_name}"
        
    def calculate_vat(self, vat_rate=0.15):
        """
        Example of business logic in the model: calculates VAT for the total amount.
        """
        return self.total_amount * models.Decimal(str(vat_rate))

    def mark_as_approved(self):
        """
        Example of business logic in the model: sets the quotation as approved.
        """
        if not self.is_approved:
            self.is_approved = True
            self.save()
            return True
        return False
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
We will create a `ModelForm` for the `CustomerQuotation` model, including all editable fields and applying Tailwind CSS classes via widgets.

**File: `sales/forms.py`**

```python
from django import forms
from .models import CustomerQuotation

class CustomerQuotationForm(forms.ModelForm):
    class Meta:
        model = CustomerQuotation
        fields = ['quotation_number', 'customer_name', 'quotation_date', 'total_amount', 'is_approved']
        widgets = {
            'quotation_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quotation_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_approved': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        
    def clean_total_amount(self):
        """
        Example of form validation. Ensure total amount is positive.
        """
        total_amount = self.cleaned_data['total_amount']
        if total_amount <= 0:
            raise forms.ValidationError("Total amount must be a positive value.")
        return total_amount
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We will define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for `CustomerQuotation`, along with a separate view for the HTMX-loaded table partial. Views remain thin by delegating complex logic (like `calculate_vat`) to the model.

**File: `sales/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import CustomerQuotation
from .forms import CustomerQuotationForm

class CustomerQuotationListView(ListView):
    model = CustomerQuotation
    template_name = 'sales/customerquotation/list.html'
    context_object_name = 'customer_quotations' # This will be plural as requested

class CustomerQuotationTablePartialView(ListView):
    model = CustomerQuotation
    template_name = 'sales/customerquotation/_customerquotation_table.html'
    context_object_name = 'customer_quotations' # This will be plural as requested

class CustomerQuotationCreateView(CreateView):
    model = CustomerQuotation
    form_class = CustomerQuotationForm
    template_name = 'sales/customerquotation/_customerquotation_form.html'
    success_url = reverse_lazy('customerquotation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Quotation added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerQuotationList'
                }
            )
        return response

class CustomerQuotationUpdateView(UpdateView):
    model = CustomerQuotation
    form_class = CustomerQuotationForm
    template_name = 'sales/customerquotation/_customerquotation_form.html'
    success_url = reverse_lazy('customerquotation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer Quotation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerQuotationList'
                }
            )
        return response

class CustomerQuotationDeleteView(DeleteView):
    model = CustomerQuotation
    template_name = 'sales/customerquotation/_customerquotation_confirm_delete.html'
    success_url = reverse_lazy('customerquotation_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer Quotation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCustomerQuotationList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will extend `core/base.html`, use DataTables for the list, and HTMX/Alpine.js for dynamic interactions and modals.

**File: `sales/templates/sales/customerquotation/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Customer Quotations</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300"
            hx-get="{% url 'customerquotation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Quotation
        </button>
    </div>
    
    <div id="customerquotationTable-container"
         hx-trigger="load, refreshCustomerQuotationList from:body"
         hx-get="{% url 'customerquotation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Customer Quotations...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for more complex UI states.
    // For simple modal open/close, the `_` syntax in HTMX attributes is often sufficient.
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            // Trigger Alpine.js show on modal
            const modal = document.getElementById('modal');
            if (modal && typeof Alpine !== 'undefined') {
                Alpine.data(modal).show = true;
            }
        }
    });

    document.addEventListener('htmx:beforeSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { // HTMX 204 response for successful form submission
            // Hide modal after successful form submission (handled by HX-Trigger -> refresh...)
            const modal = document.getElementById('modal');
            if (modal && typeof Alpine !== 'undefined') {
                Alpine.data(modal).show = false;
            }
        }
    });
</script>
{% endblock %}
```

**File: `sales/templates/sales/customerquotation/_customerquotation_table.html`**

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="customerquotationTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No.</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation Date</th>
                <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in customer_quotations %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap">{{ obj.quotation_number }}</td>
                <td class="py-3 px-6 whitespace-nowrap">{{ obj.customer_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap">{{ obj.quotation_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-right">${{ obj.total_amount|floatformat:2 }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-center">
                    {% if obj.is_approved %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                    {% else %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">No</span>
                    {% endif %}
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-center text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300"
                        hx-get="{% url 'customerquotation_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300"
                        hx-get="{% url 'customerquotation_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#customerquotationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false
        });
    });
</script>
```

**File: `sales/templates/sales/customerquotation/_customerquotation_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Customer Quotation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
                Save Quotation
                <span id="form-indicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

**File: `sales/templates/sales/customerquotation/_customerquotation_confirm_delete.html`**

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you sure you want to delete the quotation "{{ object.quotation_number }}" for "{{ object.customer_name }}"?</p>
    
    <form hx-post="{% url 'customerquotation_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
We will define URL patterns for the list, table partial (for HTMX), add, edit, and delete operations.

**File: `sales/urls.py`**

```python
from django.urls import path
from .views import (
    CustomerQuotationListView, 
    CustomerQuotationTablePartialView,
    CustomerQuotationCreateView, 
    CustomerQuotationUpdateView, 
    CustomerQuotationDeleteView
)

urlpatterns = [
    path('customerquotations/', CustomerQuotationListView.as_view(), name='customerquotation_list'),
    path('customerquotations/table/', CustomerQuotationTablePartialView.as_view(), name='customerquotation_table'), # HTMX partial
    path('customerquotations/add/', CustomerQuotationCreateView.as_view(), name='customerquotation_add'),
    path('customerquotations/edit/<int:pk>/', CustomerQuotationUpdateView.as_view(), name='customerquotation_edit'),
    path('customerquotations/delete/<int:pk>/', CustomerQuotationDeleteView.as_view(), name='customerquotation_delete'),
]
```
*Note: Remember to include this `sales/urls.py` in your project's main `urls.py` by adding `path('sales/', include('sales.urls')),` to your `urlpatterns`.*

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and properties, and integration tests for all views to ensure functionality and HTMX interactions.

**File: `sales/tests.py`**

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import CustomerQuotation
from datetime import date
from decimal import Decimal

class CustomerQuotationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.quotation1 = CustomerQuotation.objects.create(
            quotation_number='Q001',
            customer_name='Acme Corp',
            quotation_date=date(2023, 1, 15),
            total_amount=Decimal('1000.00'),
            is_approved=False
        )
        cls.quotation2 = CustomerQuotation.objects.create(
            quotation_number='Q002',
            customer_name='Globex Inc',
            quotation_date=date(2023, 2, 20),
            total_amount=Decimal('2500.50'),
            is_approved=True
        )
  
    def test_customer_quotation_creation(self):
        obj = CustomerQuotation.objects.get(quotation_number='Q001')
        self.assertEqual(obj.customer_name, 'Acme Corp')
        self.assertEqual(obj.total_amount, Decimal('1000.00'))
        self.assertFalse(obj.is_approved)
        
    def test_quotation_number_label(self):
        obj = CustomerQuotation.objects.get(id=self.quotation1.id)
        field_label = obj._meta.get_field('quotation_number').verbose_name
        self.assertEqual(field_label, 'Quotation Number')
        
    def test_str_representation(self):
        obj = CustomerQuotation.objects.get(id=self.quotation1.id)
        self.assertEqual(str(obj), 'Quote No: Q001 for Acme Corp')

    def test_calculate_vat_method(self):
        obj = CustomerQuotation.objects.get(id=self.quotation1.id)
        vat_amount = obj.calculate_vat(vat_rate=0.10)
        self.assertEqual(vat_amount, Decimal('100.00'))
        
    def test_mark_as_approved_method(self):
        obj = CustomerQuotation.objects.get(id=self.quotation1.id)
        self.assertFalse(obj.is_approved)
        result = obj.mark_as_approved()
        self.assertTrue(result)
        obj.refresh_from_db() # Reload to get updated status
        self.assertTrue(obj.is_approved)
        
        # Test marking an already approved quotation
        obj2 = CustomerQuotation.objects.get(id=self.quotation2.id)
        self.assertTrue(obj2.is_approved)
        result = obj2.mark_as_approved()
        self.assertFalse(result) # Should return False if already approved

class CustomerQuotationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.quotation = CustomerQuotation.objects.create(
            quotation_number='QVIEW001',
            customer_name='View Test Co',
            quotation_date=date(2024, 3, 1),
            total_amount=Decimal('500.00'),
            is_approved=False
        )
    
    def setUp(self):
        # Set up data for each test method (if needed, e.g. for user login)
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('customerquotation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerquotation/list.html')
        self.assertIn('customer_quotations', response.context) # Check context object name
        self.assertIsInstance(response.context['customer_quotations'].first(), CustomerQuotation)
        
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('customerquotation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerquotation/_customerquotation_table.html')
        self.assertIn('customer_quotations', response.context)
        self.assertContains(response, 'QVIEW001') # Check if data is rendered

    def test_create_view_get(self):
        response = self.client.get(reverse('customerquotation_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerquotation/_customerquotation_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_success(self):
        data = {
            'quotation_number': 'QNEW001',
            'customer_name': 'New Customer Ltd',
            'quotation_date': '2024-04-01',
            'total_amount': '1500.75',
            'is_approved': 'on'
        }
        response = self.client.post(reverse('customerquotation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for success
        self.assertTrue(CustomerQuotation.objects.filter(quotation_number='QNEW001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerQuotationList')

    def test_create_view_post_invalid(self):
        data = { # Invalid data, e.g., missing required fields or invalid amount
            'quotation_number': 'QINVALID',
            'customer_name': 'Invalid Inc',
            'quotation_date': '2024-04-02',
            'total_amount': '-100.00', # Invalid amount
            'is_approved': 'off'
        }
        response = self.client.post(reverse('customerquotation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX expects 200 OK with form errors
        self.assertTemplateUsed(response, 'sales/customerquotation/_customerquotation_form.html')
        self.assertFalse(CustomerQuotation.objects.filter(quotation_number='QINVALID').exists())
        self.assertContains(response, 'Total amount must be a positive value.')

    def test_update_view_get(self):
        obj = CustomerQuotation.objects.get(id=self.quotation.id)
        response = self.client.get(reverse('customerquotation_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerquotation/_customerquotation_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        obj = CustomerQuotation.objects.get(id=self.quotation.id)
        new_customer_name = 'Updated Customer Name'
        data = {
            'quotation_number': obj.quotation_number,
            'customer_name': new_customer_name,
            'quotation_date': obj.quotation_date.strftime('%Y-%m-%d'),
            'total_amount': str(obj.total_amount),
            'is_approved': 'on'
        }
        response = self.client.post(reverse('customerquotation_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.customer_name, new_customer_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerQuotationList')
        
    def test_delete_view_get(self):
        obj = CustomerQuotation.objects.get(id=self.quotation.id)
        response = self.client.get(reverse('customerquotation_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/customerquotation/_customerquotation_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success(self):
        obj = CustomerQuotation.objects.get(id=self.quotation.id)
        response = self.client.post(reverse('customerquotation_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(CustomerQuotation.objects.filter(id=obj.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCustomerQuotationList')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django code already demonstrates the integration as follows:

-   **HTMX for dynamic updates:**
    -   The `customerquotation/list.html` uses `hx-get` to load the `_customerquotation_table.html` initially and upon `refreshCustomerQuotationList` event.
    -   Buttons for "Add New Quotation", "Edit", and "Delete" use `hx-get` to load forms/confirmation modals into `#modalContent`.
    -   Form submissions (`hx-post`) in `_customerquotation_form.html` and `_customerquotation_confirm_delete.html` trigger a `204 No Content` response from the Django views, coupled with an `HX-Trigger: refreshCustomerQuotationList` header, causing the main list to automatically refresh without a full page reload.
    -   HTMX's built-in `htmx-indicator` class is used on the form submit button to show a loading spinner.

-   **Alpine.js for UI state management:**
    -   The `#modal` in `list.html` uses Alpine.js (`x-data`, `x-show`, `x-transition`) for managing its visibility and smooth transitions.
    -   The `_` (Hyperscript) syntax is used on buttons to add/remove the `is-active` class on the modal, which Alpine.js picks up.
    -   A small JavaScript block in `extra_js` of `list.html` ensures Alpine.js's state for the modal is correctly updated after HTMX swaps content.

-   **DataTables for list views:**
    -   The `_customerquotation_table.html` includes a `<script>` block to initialize DataTables on the `#customerquotationTable` element. This provides client-side searching, sorting, and pagination for the rendered data.

-   **HTMX-only interactions:**
    -   All dynamic interactions, including form loading and submission, are driven by HTMX attributes, eliminating the need for custom, complex JavaScript functions for these interactions. Alpine.js is used purely for declarative UI state and animations.

## Final Notes

This comprehensive plan provides a robust, modern Django solution for managing Customer Quotations, directly addressing the principles of fat models, thin views, and modern frontend techniques (HTMX + Alpine.js + DataTables). While the original ASP.NET code was minimal, this migration strategy showcases how conversational AI can interpret potential functionality and generate a complete, automated solution adhering to best practices and ready for further development. This approach prioritizes business value by delivering a highly interactive, responsive, and maintainable application with significantly reduced manual effort in the transition.