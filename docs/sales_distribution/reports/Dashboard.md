## ASP.NET to Django Conversion Script: Sales Distribution Dashboard Modernization

This modernization plan addresses the transition of your ASP.NET Dashboard from the `Module_SalesDistribution_Reports_Dashboard` module to a modern Django-based solution.

**Analysis of Provided ASP.NET Code:**

The provided ASP.NET code (`Dashboard.aspx` and `Dashboard.aspx.cs`) is exceptionally minimal. The `.aspx` file primarily defines content placeholders and includes a single JavaScript file (`loadingNotifier.js`). The C# code-behind only contains an empty `Page_Load` method, indicating no explicit database interactions, UI control definitions, or business logic within this specific snippet.

Given the context of a "Dashboard" within a "Sales Distribution" module, it's highly probable that this page was intended to display reports, summary data, or provide navigation to other sales-related functionalities. Since no specific data structures or operations are present, this plan will propose a generic but representative data model (`SalesRecord`) and associated CRUD (Create, Read, Update, Delete) functionality to illustrate the full migration process, which can then be adapted to actual data schema once identified.

The focus will be on leveraging Django's "Fat Model, Thin View" architecture, ensuring a clean separation of concerns, and implementing a highly dynamic user experience using HTMX and Alpine.js, integrated with DataTables for efficient data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
As the provided ASP.NET code contains no explicit database schema, we will infer a typical structure for "Sales Distribution Reports." We will assume a table named `tbl_sales_records` which holds individual sales entries or aggregated report data.

**Inferred Schema:**
*   **Table Name:** `tbl_sales_records`
*   **Columns:**
    *   `id` (Primary Key, integer)
    *   `sales_date` (Date)
    *   `region` (Text/Varchar, e.g., 'North', 'South')
    *   `amount` (Decimal/Money)
    *   `product_category` (Text/Varchar, e.g., 'Electronics', 'Apparel')
    *   `is_active` (Boolean, for soft deletes or status)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the dashboard nature, we assume a basic set of operations to manage the underlying report data for demonstrative purposes, or potentially for an administrative view of the reports.

*   **Read:** Displaying a list of sales records, possibly with filtering or sorting. This would typically be a `GridView` or similar data display control in ASP.NET.
*   **Create:** Adding new sales records. This would involve a form with input fields and a submit button.
*   **Update:** Modifying existing sales records. This often involves an "Edit" button next to each record in a list view, leading to a pre-filled form.
*   **Delete:** Removing sales records. This would involve a "Delete" button, possibly with a confirmation prompt.

No explicit validation logic was found, but common validations (e.g., required fields, numeric range for amount) will be assumed and implemented in the Django form.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on the assumed backend functionality and typical dashboard design:

*   **GridView (or similar data display):** For presenting the `SalesRecord` list. This will be replaced by a Django template rendering a `<table>` enhanced by DataTables.
*   **TextBoxes:** For inputting `sales_date`, `region`, `amount`, `product_category`. These will become Django `forms.TextInput` or `forms.NumberInput` widgets.
*   **Buttons/LinkButtons:** For triggering actions like "Add New," "Edit," and "Delete." These will be replaced by HTMX-enabled buttons that interact with Django views without full page reloads.

No client-side JavaScript interactions were evident in the provided `loadingNotifier.js` snippet, but typical ASP.NET postbacks for data operations will be converted to HTMX requests, and any client-side UI state management (e.g., showing/hiding elements) will utilize Alpine.js.

### Step 4: Generate Django Code

We will create a new Django application named `reports` to house this module.

#### 4.1 Models (`reports/models.py`)

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `SalesRecord` will map to the `tbl_sales_records` table.

```python
from django.db import models

class SalesRecord(models.Model):
    """
    Represents a single sales record or an entry in a sales report.
    This model is mapped to an existing database table.
    """
    sales_date = models.DateField(
        db_column='sales_date',
        verbose_name='Sales Date',
        help_text='The date of the sales record.'
    )
    region = models.CharField(
        max_length=100,
        db_column='region',
        verbose_name='Region',
        help_text='The geographical region of the sale.'
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_column='amount',
        verbose_name='Amount',
        help_text='The monetary amount of the sale.'
    )
    product_category = models.CharField(
        max_length=100,
        db_column='product_category',
        verbose_name='Product Category',
        help_text='The category of the product sold.'
    )
    is_active = models.BooleanField(
        db_column='is_active',
        default=True,
        verbose_name='Is Active',
        help_text='Indicates if the record is currently active.'
    )

    class Meta:
        managed = False  # Set to False if the table already exists in the database
        db_table = 'tbl_sales_records'
        verbose_name = 'Sales Record'
        verbose_name_plural = 'Sales Records'
        ordering = ['-sales_date', 'region']

    def __str__(self):
        return f"Sales for {self.region} on {self.sales_date} - ${self.amount}"

    def get_display_amount(self):
        """
        Business logic example: Format the amount for display.
        """
        return f"${self.amount:,.2f}"

    def activate(self):
        """
        Business logic example: Activate a sales record.
        """
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False

    def deactivate(self):
        """
        Business logic example: Deactivate a sales record (soft delete).
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

```

#### 4.2 Forms (`reports/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for `SalesRecord` to handle input and validation.

```python
from django import forms
from .models import SalesRecord

class SalesRecordForm(forms.ModelForm):
    """
    Form for creating and updating SalesRecord instances.
    Includes Tailwind CSS classes for styling.
    """
    class Meta:
        model = SalesRecord
        fields = ['sales_date', 'region', 'amount', 'product_category', 'is_active']
        widgets = {
            'sales_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'region': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., North America'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01',
                'placeholder': 'e.g., 123.45'
            }),
            'product_category': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Electronics'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'
            }),
        }
        
    def clean_amount(self):
        """
        Custom validation for the amount field to ensure it's positive.
        """
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive value.")
        return amount

```

#### 4.3 Views (`reports/views.py`)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views will be thin, delegating business logic to the `SalesRecord` model. HTMX responses will trigger updates on the client side.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import SalesRecord
from .forms import SalesRecordForm

class SalesRecordListView(ListView):
    """
    Displays a list of all SalesRecord objects.
    This view serves the main dashboard page.
    """
    model = SalesRecord
    template_name = 'reports/salesrecord/list.html'
    context_object_name = 'sales_records' # Renamed for clarity

class SalesRecordTablePartialView(ListView):
    """
    Returns the partial HTML for the sales records table.
    Designed to be loaded via HTMX for dynamic updates.
    """
    model = SalesRecord
    template_name = 'reports/salesrecord/_salesrecord_table.html'
    context_object_name = 'sales_records'

class SalesRecordCreateView(CreateView):
    """
    Handles the creation of new SalesRecord objects.
    Used for both initial GET request to display the form and POST to process it.
    """
    model = SalesRecord
    form_class = SalesRecordForm
    template_name = 'reports/salesrecord/_salesrecord_form.html' # Use partial template
    success_url = reverse_lazy('salesrecord_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Sales Record added successfully.')
        # HTMX-specific response: return 204 No Content with HX-Trigger for list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalesRecordList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add Sales Record' # Title for modal
        return context

class SalesRecordUpdateView(UpdateView):
    """
    Handles the updating of existing SalesRecord objects.
    Used for both initial GET request to display the form and POST to process it.
    """
    model = SalesRecord
    form_class = SalesRecordForm
    template_name = 'reports/salesrecord/_salesrecord_form.html' # Use partial template
    success_url = reverse_lazy('salesrecord_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Sales Record updated successfully.')
        # HTMX-specific response: return 204 No Content with HX-Trigger for list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalesRecordList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Sales Record' # Title for modal
        return context

class SalesRecordDeleteView(DeleteView):
    """
    Handles the deletion of SalesRecord objects.
    Displays a confirmation page on GET and performs deletion on POST.
    """
    model = SalesRecord
    template_name = 'reports/salesrecord/_salesrecord_confirm_delete.html' # Use partial template
    success_url = reverse_lazy('salesrecord_list')

    def delete(self, request, *args, **kwargs):
        # Example of moving simple business logic to model (soft delete)
        self.object = self.get_object()
        # self.object.deactivate() # If using soft delete, call model method
        # For hard delete:
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Sales Record deleted successfully.')
        # HTMX-specific response: return 204 No Content with HX-Trigger for list refresh
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalesRecordList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Delete Sales Record' # Title for modal
        return context

```

#### 4.4 Templates (`reports/templates/reports/salesrecord/`)

**Task:** Create templates for each view.

**Instructions:**
Templates will leverage HTMX for dynamic content loading and Alpine.js for modal management. DataTables will be used for the list view.

**`reports/templates/reports/salesrecord/list.html`**
This is the main dashboard page.

```html
{% extends 'core/base.html' %}

{% block title %}Sales Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sales Records Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out"
            hx-get="{% url 'salesrecord_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .block to #modal then add .opacity-100 to #modal backdrop then add .translate-y-0 to #modal dialog"
        >
            <i class="fas fa-plus mr-2"></i>Add New Sales Record
        </button>
    </div>

    <!-- DataTables container -->
    <div id="salesrecordTable-container"
         hx-trigger="load, refreshSalesRecordList from:body"
         hx-get="{% url 'salesrecord_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="flex items-center justify-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading sales data...</p>
        </div>
    </div>

    <!-- Universal Modal for HTMX forms -->
    <div id="modal" class="fixed inset-0 z-50 overflow-y-auto hidden"
         x-data="{ show: false }"
         x-init="$watch('show', value => {
             if (value) {
                 document.body.classList.add('overflow-hidden');
                 $el.classList.remove('hidden');
             } else {
                 document.body.classList.remove('overflow-hidden');
                 $el.classList.add('hidden');
             }
         })"
         x-show="show"
         _="on hx-after-swap from #modalContent add .block to #modal then add .opacity-100 to #modal backdrop then add .translate-y-0 to #modal dialog set show to true"
         _="on click if event.target.id == 'modal' or event.target.id == 'modal-backdrop' set show to false then remove .opacity-100 from #modal backdrop then remove .translate-y-0 from #modal dialog wait 100ms then remove .block from #modal"
         >
        <div id="modal-backdrop" class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity duration-300 ease-in-out opacity-0"></div>
        <div class="flex items-center justify-center min-h-screen p-4"
             id="modal-dialog"
             _="on click if event.target.id == 'modal-close-button' set show to false then remove .opacity-100 from #modal backdrop then remove .translate-y-0 from #modal dialog wait 100ms then remove .block from #modal"
             >
            <div id="modalContent"
                 class="bg-white rounded-lg shadow-xl transform transition-all sm:max-w-xl w-full p-0 scale-95 opacity-0 duration-300 ease-out"
                 x-bind:class="show ? 'scale-100 opacity-100' : 'scale-95 opacity-0'"
                 >
                <!-- Content loaded by HTMX will appear here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is primarily for UI state management.
    // The modal's show/hide logic is handled via Alpine.js directly
    // combined with htmx-after-swap to trigger Alpine's state.
    document.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.hasAttribute('hx-post')) {
            // This is for form submissions that return 204 No Content
            // Close the modal manually via Alpine's state
            const modal = document.getElementById('modal');
            if (modal && modal.__alpine && modal.__alpine.data) {
                modal.__alpine.data.show = false;
            }
        }
    });
</script>
{% endblock %}
```

**`reports/templates/reports/salesrecord/_salesrecord_table.html`**
This partial renders the DataTables table.

```html
<div class="overflow-x-auto">
    <table id="salesRecordTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Sales Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Region</th>
                <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Product Category</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Active</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in sales_records %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.sales_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.region }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-right text-sm text-gray-800">{{ obj.get_display_amount }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.product_category }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ obj.is_active|yesno:"Yes,No" }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-200 ease-in-out"
                        hx-get="{% url 'salesrecord_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal backdrop then add .translate-y-0 to #modal dialog"
                    >
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-200 ease-in-out"
                        hx-get="{% url 'salesrecord_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal backdrop then add .translate-y-0 to #modal dialog"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-500">No sales records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance before re-initializing to prevent errors on HTMX swap
        if ($.fn.DataTable.isDataTable('#salesRecordTable')) {
            $('#salesRecordTable').DataTable().destroy();
        }
        $('#salesRecordTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [[ 1, "desc" ]] // Order by Sales Date descending by default
        });
    });
</script>
```

**`reports/templates/reports/salesrecord/_salesrecord_form.html`**
This partial renders the form for both create and update operations.

```html
<div class="p-6">
    <div class="flex justify-between items-center mb-5 border-b pb-3">
        <h3 class="text-xl font-semibold text-gray-900">{{ title }}</h3>
        <button id="modal-close-button" class="text-gray-500 hover:text-gray-700 text-2xl leading-none">
            &times;
        </button>
    </div>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                <div class="mt-1">
                    {{ field }}
                </div>
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                id="modal-cancel-button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-200 ease-in-out"
                _="on click remove .block from #modal then remove .opacity-100 from #modal backdrop then remove .translate-y-0 from #modal dialog wait 100ms then set show to false on #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out"
            >
                Save
                <span id="form-indicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

**`reports/templates/reports/salesrecord/_salesrecord_confirm_delete.html`**
This partial handles the delete confirmation.

```html
<div class="p-6">
    <div class="flex justify-between items-center mb-5 border-b pb-3">
        <h3 class="text-xl font-semibold text-gray-900">{{ title }}</h3>
        <button id="modal-close-button" class="text-gray-500 hover:text-gray-700 text-2xl leading-none">
            &times;
        </button>
    </div>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the sales record for <strong>{{ salesrecord.region }} on {{ salesrecord.sales_date }}</strong> with amount <strong>{{ salesrecord.get_display_amount }}</strong>? This action cannot be undone.</p>
    <form hx-post="{% url 'salesrecord_delete' salesrecord.pk %}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                id="modal-cancel-button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-200 ease-in-out"
                _="on click remove .block from #modal then remove .opacity-100 from #modal backdrop then remove .translate-y-0 from #modal dialog wait 100ms then set show to false on #modal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out"
            >
                Delete
                <span id="delete-indicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`reports/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs for list, partial table, create, update, and delete.

```python
from django.urls import path
from .views import (
    SalesRecordListView,
    SalesRecordTablePartialView,
    SalesRecordCreateView,
    SalesRecordUpdateView,
    SalesRecordDeleteView
)

urlpatterns = [
    # Main dashboard view
    path('sales-records/', SalesRecordListView.as_view(), name='salesrecord_list'),

    # HTMX partial for the data table
    path('sales-records/table/', SalesRecordTablePartialView.as_view(), name='salesrecord_table'),

    # CRUD operations
    path('sales-records/add/', SalesRecordCreateView.as_view(), name='salesrecord_add'),
    path('sales-records/edit/<int:pk>/', SalesRecordUpdateView.as_view(), name='salesrecord_edit'),
    path('sales-records/delete/<int:pk>/', SalesRecordDeleteView.as_view(), name='salesrecord_delete'),
]

```
Remember to include these URLs in your project's main `urls.py`:
`path('reports/', include('reports.urls')),`

#### 4.6 Tests (`reports/tests.py`)

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for all views, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from decimal import Decimal

from .models import SalesRecord
from .forms import SalesRecordForm

class SalesRecordModelTest(TestCase):
    """
    Unit tests for the SalesRecord model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single sales record for all tests
        cls.sales_record = SalesRecord.objects.create(
            sales_date=date(2023, 1, 15),
            region='North',
            amount=Decimal('1500.75'),
            product_category='Electronics',
            is_active=True
        )
        cls.inactive_sales_record = SalesRecord.objects.create(
            sales_date=date(2023, 2, 1),
            region='South',
            amount=Decimal('500.25'),
            product_category='Apparel',
            is_active=False
        )

    def test_sales_record_creation(self):
        """Test that a SalesRecord instance is created correctly."""
        self.assertEqual(self.sales_record.sales_date, date(2023, 1, 15))
        self.assertEqual(self.sales_record.region, 'North')
        self.assertEqual(self.sales_record.amount, Decimal('1500.75'))
        self.assertEqual(self.sales_record.product_category, 'Electronics')
        self.assertTrue(self.sales_record.is_active)

    def test_sales_record_str_method(self):
        """Test the __str__ method of the SalesRecord model."""
        expected_str = "Sales for North on 2023-01-15 - $1500.75"
        self.assertEqual(str(self.sales_record), expected_str)

    def test_sales_date_verbose_name(self):
        """Test the verbose_name for the sales_date field."""
        field_label = self.sales_record._meta.get_field('sales_date').verbose_name
        self.assertEqual(field_label, 'Sales Date')

    def test_get_display_amount_method(self):
        """Test the get_display_amount method for correct formatting."""
        self.assertEqual(self.sales_record.get_display_amount(), '$1,500.75')

    def test_activate_method(self):
        """Test the activate method."""
        self.assertFalse(self.inactive_sales_record.is_active)
        self.assertTrue(self.inactive_sales_record.activate())
        self.assertTrue(self.inactive_sales_record.is_active)
        self.assertFalse(self.sales_record.activate()) # Should return False if already active

    def test_deactivate_method(self):
        """Test the deactivate method."""
        self.assertTrue(self.sales_record.is_active)
        self.assertTrue(self.sales_record.deactivate())
        self.assertFalse(self.sales_record.is_active)
        self.assertFalse(self.inactive_sales_record.deactivate()) # Should return False if already inactive

class SalesRecordViewsTest(TestCase):
    """
    Integration tests for SalesRecord views using Django's test client.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.sales_record_1 = SalesRecord.objects.create(
            sales_date=date(2023, 3, 1),
            region='East',
            amount=Decimal('200.00'),
            product_category='Books',
            is_active=True
        )
        cls.sales_record_2 = SalesRecord.objects.create(
            sales_date=date(2023, 3, 10),
            region='West',
            amount=Decimal('750.50'),
            product_category='Software',
            is_active=False
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test SalesRecordListView displays correctly."""
        response = self.client.get(reverse('salesrecord_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/salesrecord/list.html')
        self.assertIn('sales_records', response.context)
        self.assertEqual(len(response.context['sales_records']), SalesRecord.objects.count())

    def test_table_partial_view_get(self):
        """Test SalesRecordTablePartialView for HTMX content."""
        response = self.client.get(reverse('salesrecord_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/salesrecord/_salesrecord_table.html')
        self.assertIn('sales_records', response.context)
        self.assertContains(response, '<thead>') # Check for table structure
        self.assertContains(response, self.sales_record_1.region)

    def test_create_view_get(self):
        """Test SalesRecordCreateView displays form."""
        response = self.client.get(reverse('salesrecord_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/salesrecord/_salesrecord_form.html')
        self.assertIsInstance(response.context['form'], SalesRecordForm)

    def test_create_view_post_success(self):
        """Test successful creation via POST."""
        data = {
            'sales_date': '2023-04-01',
            'region': 'Central',
            'amount': '999.99',
            'product_category': 'Services',
            'is_active': 'on'
        }
        response = self.client.post(reverse('salesrecord_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertTrue(SalesRecord.objects.filter(region='Central').exists())
        self.assertEqual(SalesRecord.objects.last().amount, Decimal('999.99'))

    def test_create_view_post_invalid(self):
        """Test invalid form submission for creation."""
        data = { # Amount is invalid
            'sales_date': '2023-04-01',
            'region': 'Central',
            'amount': '-100.00',
            'product_category': 'Services',
            'is_active': 'on'
        }
        response = self.client.post(reverse('salesrecord_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Amount must be a positive value.')
        self.assertFalse(SalesRecord.objects.filter(region='Central').exists())

    def test_create_view_post_htmx(self):
        """Test HTMX-specific response for creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'sales_date': '2023-04-02',
            'region': 'Northwest',
            'amount': '123.45',
            'product_category': 'Tools',
            'is_active': 'on'
        }
        response = self.client.post(reverse('salesrecord_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalesRecordList')
        self.assertTrue(SalesRecord.objects.filter(region='Northwest').exists())


    def test_update_view_get(self):
        """Test SalesRecordUpdateView displays form with existing data."""
        url = reverse('salesrecord_edit', args=[self.sales_record_1.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/salesrecord/_salesrecord_form.html')
        self.assertIsInstance(response.context['form'], SalesRecordForm)
        self.assertEqual(response.context['form'].instance, self.sales_record_1)

    def test_update_view_post_success(self):
        """Test successful update via POST."""
        url = reverse('salesrecord_edit', args=[self.sales_record_1.pk])
        data = {
            'sales_date': '2023-03-01', # Same date
            'region': 'East (Updated)',
            'amount': '250.00', # Updated amount
            'product_category': 'Books',
            'is_active': 'on'
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 302)
        self.sales_record_1.refresh_from_db()
        self.assertEqual(self.sales_record_1.region, 'East (Updated)')
        self.assertEqual(self.sales_record_1.amount, Decimal('250.00'))

    def test_update_view_post_htmx(self):
        """Test HTMX-specific response for update."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        url = reverse('salesrecord_edit', args=[self.sales_record_1.pk])
        data = {
            'sales_date': '2023-03-01',
            'region': 'East (HTMX Update)',
            'amount': '300.00',
            'product_category': 'Books',
            'is_active': 'on'
        }
        response = self.client.post(url, data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalesRecordList')
        self.sales_record_1.refresh_from_db()
        self.assertEqual(self.sales_record_1.region, 'East (HTMX Update)')

    def test_delete_view_get(self):
        """Test SalesRecordDeleteView displays confirmation."""
        url = reverse('salesrecord_delete', args=[self.sales_record_1.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/salesrecord/_salesrecord_confirm_delete.html')
        self.assertIn('salesrecord', response.context)
        self.assertEqual(response.context['salesrecord'], self.sales_record_1)

    def test_delete_view_post_success(self):
        """Test successful deletion via POST."""
        count_before = SalesRecord.objects.count()
        url = reverse('salesrecord_delete', args=[self.sales_record_1.pk])
        response = self.client.post(url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(SalesRecord.objects.count(), count_before - 1)
        self.assertFalse(SalesRecord.objects.filter(pk=self.sales_record_1.pk).exists())

    def test_delete_view_post_htmx(self):
        """Test HTMX-specific response for deletion."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        count_before = SalesRecord.objects.count()
        url = reverse('salesrecord_delete', args=[self.sales_record_2.pk]) # Delete the second record
        response = self.client.post(url, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalesRecordList')
        self.assertEqual(SalesRecord.objects.count(), count_before - 1)
        self.assertFalse(SalesRecord.objects.filter(pk=self.sales_record_2.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated Django code already demonstrates comprehensive HTMX and Alpine.js integration:

*   **HTMX for Dynamic Updates:**
    *   The `salesrecord/list.html` uses `hx-get` on `salesrecordTable-container` to load the `_salesrecord_table.html` partial on page load and `refreshSalesRecordList` event.
    *   "Add," "Edit," and "Delete" buttons in the table trigger `hx-get` requests to load forms (`_salesrecord_form.html`) or confirmation pages (`_salesrecord_confirm_delete.html`) into a modal identified by `#modalContent`.
    *   Form submissions (`hx-post`) from the modal use `hx-swap="none"` and the Django views return `HTTP 204 No Content` along with an `HX-Trigger` header (`refreshSalesRecordList`). This instructs HTMX to close the modal (via Alpine.js interaction) and then trigger a refresh of the `salesrecordTable-container`, ensuring the list is always up-to-date without a full page reload.
    *   Loading indicators (`htmx-indicator`) are used on forms for visual feedback during asynchronous operations.

*   **Alpine.js for UI State Management:**
    *   A main Alpine.js component (`x-data="{ show: false }"`) manages the visibility and transition effects of the universal modal.
    *   HTMX's `hx-after-swap` attribute, combined with `_` (Hyperscript), is used to interact with Alpine.js state (`set show to true`) to ensure the modal correctly appears after HTMX has loaded its content.
    *   Similarly, closing buttons within the modal use `_` to set `show` to `false`, triggering the Alpine.js transition to hide the modal. This ensures smooth UI interactions independent of HTMX's network requests.

*   **DataTables for List Views:**
    *   The `_salesrecord_table.html` partial includes a `<script>` block that initializes `$('#salesRecordTable').DataTable()`. This script is executed whenever the partial is loaded or reloaded by HTMX, ensuring the DataTables functionality (searching, sorting, pagination) is always active on the dynamically loaded table.

*   **DRY Template Inheritance:**
    *   All templates explicitly extend `core/base.html`, adhering to the DRY principle by centralizing CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS in the base template (which is not included in this output as per instructions).

*   **No Custom JavaScript (beyond framework glue):**
    *   The entire dynamic interaction stack relies solely on HTMX and Alpine.js attributes, eliminating the need for complex, hand-written JavaScript files.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the hypothetical "Sales Distribution Dashboard" functionality to Django. By following these steps, your organization can achieve:

*   **Modern Architecture:** Transition to a clean, maintainable Django application with fat models and thin views.
*   **Enhanced User Experience:** Deliver a highly responsive and dynamic user interface using HTMX and Alpine.js, minimizing full page reloads.
*   **Efficient Data Presentation:** Utilize DataTables for superior client-side data management, improving user interaction with large datasets.
*   **Reduced Development Effort:** Leverage automation-friendly code generation and established patterns, reducing manual coding and potential errors.
*   **Improved Maintainability:** Benefit from Django's structured approach, comprehensive testing, and clear separation of concerns, making future enhancements easier.

This automated approach ensures that the migration process is systematic and repeatable, providing a solid foundation for continuous modernization.