## ASP.NET to Django Conversion Script: Machinery Management Module

This document outlines a strategic plan for migrating the existing ASP.NET Machinery Edit functionality to a modern Django application. The focus is on leveraging AI-assisted automation to streamline the transition, emphasizing a robust, maintainable, and scalable Django solution.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Business Value Proposition:

Migrating this ASP.NET module to Django offers significant business advantages:
1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are complex to maintain and scale.
2.  **Enhanced User Experience:** Leverages HTMX and Alpine.js for highly responsive interfaces, eliminating full-page reloads and providing a smoother user experience, similar to a Single Page Application (SPA) but with simpler development.
3.  **Improved Maintainability & Scalability:** Django's structured MVC/MTV architecture, clear separation of concerns (fat models, thin views), and Python's readability lead to more maintainable code and easier scaling.
4.  **Cost Efficiency:** Python's vast ecosystem and Django's rapid development capabilities reduce development time and future maintenance costs.
5.  **Modernization & Future-Proofing:** Adopts a modern web framework and frontend technologies, ensuring the application remains relevant and adaptable to future business needs.
6.  **Automated Testing:** Integrated testing suite ensures higher code quality, fewer bugs, and faster development cycles.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, particularly the `Fillgridview` method's SQL queries and `fillGrid` method's `DataTable` creation and per-row lookups, we can infer the following database schema.

**Inferred Tables and Columns:**

*   **`tblDG_Item_Master`**: Represents the core machinery items.
    *   `Id` (Primary Key, INTEGER)
    *   `ItemCode` (TEXT/VARCHAR)
    *   `ManfDesc` (TEXT/VARCHAR) - Manufacturer Description.
    *   `StockQty` (NUMERIC/DECIMAL) - Stock Quantity.
    *   `Location` (TEXT/VARCHAR) - Item's location.
    *   `UOMBasic` (INTEGER) - Foreign key to `Unit_Master.Id` (Unit of Measurement).
    *   `CId` (INTEGER) - Foreign key to `tblDG_Category_Master.CId` (Category).
    *   `SCId` (INTEGER) - Foreign key to `tblDG_SubCategory_Master.SCId` (SubCategory).
    *   `CompId` (INTEGER) - Company ID.
    *   `Absolute` (INTEGER/BOOLEAN) - Flag for item status (`!='1'` used in query).
*   **`tblDG_Category_Master`**: Defines machinery categories.
    *   `CId` (Primary Key, INTEGER)
    *   `CName` (TEXT/VARCHAR) - Category Name (inferred).
*   **`tblDG_SubCategory_Master`**: Defines machinery subcategories.
    *   `SCId` (Primary Key, INTEGER)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, INTEGER)
    *   `SCName` (TEXT/VARCHAR) - SubCategory Name.
    *   `Symbol` (TEXT/VARCHAR) - Symbol associated with subcategory.
*   **`Unit_Master`**: Defines units of measurement.
    *   `Id` (Primary Key, INTEGER)
    *   `Symbol` (TEXT/VARCHAR) - Unit Symbol (e.g., "KG", "PCS").
*   **`tblMS_Master`**: Used in a join, and `Location` is retrieved. This seems to be a related table with `ItemId` and `Location` which might indicate an override or a more specific location. For simplicity, we assume `tblDG_Item_Master.Location` is the primary display for this page, but `tblMS_Master` is acknowledged for its relationship with `ItemId`.

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements **Read (List and Search)** functionality with navigation to an "Edit Details" page.

*   **Read (List):** The `GridView2` displays a list of machinery items. This is handled by `Fillgridview` and `fillGrid` methods.
*   **Read (Search/Filter):** Users can filter items by Category, SubCategory, and a search term (by Machine Code or Description). This is triggered by dropdown changes (`DrpCategory_SelectedIndexChanged`, `DrpSubCategory_SelectedIndexChanged`) and the search button (`btnSearch_Click`).
*   **Navigation to Edit:** The `LinkButton` within the `GridView2` with `CommandName="Sel"` redirects to `Machinery_Edit_Details.aspx?Id=...`. In Django, this will translate to an HTMX modal or a dedicated detail/edit page URL. For this modernization, we will focus on the listing page and provide the necessary HTMX buttons to trigger an edit/delete modal, aligning with modern practices.
*   **Pagination:** The `GridView2` supports paging, which will be handled client-side by DataTables in Django.
*   **Temporary Table Deletion:** The `Page_Load` event includes `DELETE` statements for `tblMS_Spares_Temp` and `tblMS_Process_Temp`. This is an anti-pattern for a list view and is usually related to session management or specific, transient data. This behavior will **not** be directly migrated to Django, as it would imply global table truncation on every page load, which is highly undesirable. Django's session management, caching, or explicit request-scoped temporary data handling would be used if such temporary data storage were truly needed.

### Step 3: Infer UI Components

The ASP.NET controls map directly to standard HTML elements and Django form fields:

*   **`DrpCategory` (DropDownList)**: Select input for category.
*   **`DrpSubCategory` (DropDownList)**: Select input for subcategory, dynamically updated.
*   **`DrpSearchCode` (DropDownList)**: Select input for search criteria (Machine Code, Description).
*   **`txtSearchItemCode` (TextBox)**: Text input for the search query.
*   **`btnSearch` (Button)**: Submit button for search.
*   **`GridView2` (GridView)**: HTML `<table>` element, enhanced by DataTables.
    *   Columns: SN (Row number), Machine Code (Link/Button), Description, UOM, Stock Qty, Location.
    *   Hidden `Id` field to retrieve primary key for actions.

### Step 4: Generate Django Code

We will create a Django application named `machinery`.

#### 4.1 Models (`machinery/models.py`)

This file will define the Django models that directly map to your existing database tables. We'll use `managed = False` and `db_table` to connect to the existing schema.

```python
from django.db import models
from django.utils.text import slugify

class Category(models.Model):
    """
    Maps to tblDG_Category_Master.
    Represents machinery categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.cname or f"Category {self.cid}"

class SubCategory(models.Model):
    """
    Maps to tblDG_SubCategory_Master.
    Represents machinery subcategories.
    """
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    scname = models.CharField(db_column='SCName', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'

    def __str__(self):
        return f"{self.symbol or ''} - {self.scname or f'SubCategory {self.scid}'}".strip(' -')

class Unit(models.Model):
    """
    Maps to Unit_Master.
    Represents units of measurement.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class MsMaster(models.Model):
    """
    Maps to tblMS_Master.
    Represents master data for Machinery System, potentially for detailed locations.
    """
    itemid = models.IntegerField(db_column='ItemId', primary_key=True) # Assuming ItemId is unique key or part of composite key
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'MS Master Entry'
        verbose_name_plural = 'MS Master Entries'

    def __str__(self):
        return f"MS Master for Item {self.itemid} at {self.location}"

class MachineryItem(models.Model):
    """
    Maps to tblDG_Item_Master.
    Represents a machinery item.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    stockqty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)
    uombasic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    scid = models.ForeignKey(SubCategory, models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    absolute = models.IntegerField(db_column='Absolute', blank=True, null=True) # Used as a flag, 1 is 'Absolute'

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machinery Item'
        verbose_name_plural = 'Machinery Items'

    def __str__(self):
        return self.itemcode or f"Item {self.id}"

    @property
    def truncated_manf_desc(self):
        """
        Returns a truncated version of the manufacturer description, similar to SUBSTRING(...,0,80)+'...'.
        """
        if self.manfdesc and len(self.manfdesc) > 80:
            return self.manfdesc[:77] + '...'
        return self.manfdesc

    def get_display_location(self):
        """
        Attempts to get location from MsMaster first, then falls back to item's own location.
        This mimics the C# logic where MS_Master location seems to take precedence.
        """
        try:
            ms_entry = MsMaster.objects.get(itemid=self.id)
            if ms_entry.location:
                return ms_entry.location
        except MsMaster.DoesNotExist:
            pass # Fallback to item's own location
        return self.location

    def is_absolute(self):
        """Checks if the item is marked as 'Absolute' (Absolute = 1)"""
        return self.absolute == 1

    # Future business logic methods for machinery items can be added here
    # E.g., calculate_maintenance_cost, get_stock_status, etc.
```

#### 4.2 Forms (`machinery/forms.py`)

We need a form to handle the search/filter criteria. This is not a ModelForm, but a regular `forms.Form` since it represents search parameters, not a single model instance.

```python
from django import forms
from .models import Category, SubCategory

class MachinerySearchForm(forms.Form):
    """
    Form for searching and filtering Machinery Items.
    """
    CATEGORY_CHOICES_DEFAULT = [('Select Category', 'Select Category')]
    SUBCATEGORY_CHOICES_DEFAULT = [('Select SubCategory', 'Select SubCategory')]
    SEARCH_FIELD_CHOICES = [
        ('Select', 'Select'),
        ('item_code', 'Machine Code'),
        ('manf_desc', 'Description'),
    ]

    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('cname'),
        empty_label='Select Category',
        required=False,
        label='Category',
        widget=forms.Select(attrs={'class': 'box3 htmx-trigger', 'hx-get': '/machinery/subcategories/', 'hx-target': '#id_subcategory', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'})
    )
    subcategory = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(), # Will be populated dynamically via HTMX
        empty_label='Select SubCategory',
        required=False,
        label='SubCategory',
        widget=forms.Select(attrs={'class': 'box3', 'id': 'id_subcategory'})
    )
    search_by = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        required=False,
        label='Search By',
        widget=forms.Select(attrs={'class': 'box3'})
    )
    search_text = forms.CharField(
        max_length=200,
        required=False,
        label='Search Text',
        widget=forms.TextInput(attrs={'class': 'box3 w-full max-w-xs'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply Tailwind/general styling classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Select)):
                current_classes = field.widget.attrs.get('class', '')
                # Ensure box3 is applied if it's from original CSS, otherwise use standard Tailwind
                if 'box3' not in current_classes:
                    field.widget.attrs['class'] = f'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm {current_classes}'.strip()
                else:
                     field.widget.attrs['class'] = f'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm {current_classes}'.strip()

        # If a category is selected, filter subcategories
        if 'category' in self.data and self.data['category']:
            try:
                category_id = int(self.data['category'])
                self.fields['subcategory'].queryset = SubCategory.objects.filter(cid=category_id).order_by('scname')
            except (ValueError, TypeError):
                pass  # Invalid category ID, leave subcategory empty

    def clean(self):
        cleaned_data = super().clean()
        # Custom validation logic if needed, e.g., if search_by is selected, search_text is required.
        search_by = cleaned_data.get('search_by')
        search_text = cleaned_data.get('search_text')

        if search_by and search_by != 'Select' and not search_text:
            self.add_error('search_text', 'Search text is required when a search field is selected.')
        return cleaned_data

```

#### 4.3 Views (`machinery/views.py`)

Views will handle listing, search, dynamic subcategory loading, and modal interactions using HTMX.
Note that the `MachineryItemCreateView`, `MachineryItemUpdateView`, and `MachineryItemDeleteView` are provided as templates for future full CRUD implementation. For this "Edit" page, we will focus on the listing and search with HTMX modal trigger.

```python
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.db.models import Q
from .models import MachineryItem, Category, SubCategory, Unit
from .forms import MachinerySearchForm
from django.shortcuts import render

# For a full CRUD implementation, you'd have these, but the original code was a LIST/SEARCH.
# from django.views.generic import CreateView, UpdateView, DeleteView
# from .forms import MachineryItemForm # You'd need to create this for create/update

class MachineryItemListView(TemplateView):
    """
    Displays the main machinery item list page with search/filter form.
    The actual table content is loaded via HTMX.
    """
    template_name = 'machinery/machineryitem/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form. It will be pre-filled if there are GET parameters.
        context['form'] = MachinerySearchForm(self.request.GET)
        return context

class MachineryItemTablePartialView(ListView):
    """
    Returns the HTML table for machinery items, used by HTMX to refresh the list.
    """
    model = MachineryItem
    template_name = 'machinery/machineryitem/_machineryitem_table.html'
    context_object_name = 'machinery_items'
    paginate_by = 20 # Although DataTables handles client-side, this can be a fallback or for initial server-side load

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Apply general filters from original ASP.NET
        queryset = queryset.filter(compid=self.request.session.get('compid', 1), absolute__isnull=True) # Assuming Absolute != 1 means null or 0

        form = MachinerySearchForm(self.request.GET)
        if form.is_valid():
            category_id = form.cleaned_data.get('category')
            subcategory_id = form.cleaned_data.get('subcategory')
            search_by = form.cleaned_data.get('search_by')
            search_text = form.cleaned_data.get('search_text')

            if category_id and category_id != 'Select Category':
                queryset = queryset.filter(cid=category_id)
            
            # Subcategory only filters if a category is also selected (implied from ASP.NET logic)
            if subcategory_id and subcategory_id != 'Select SubCategory':
                queryset = queryset.filter(scid=subcategory_id)

            if search_by and search_by != 'Select' and search_text:
                if search_by == 'item_code':
                    queryset = queryset.filter(itemcode__istartswith=search_text) # Like 's%'
                elif search_by == 'manf_desc':
                    queryset = queryset.filter(manfdesc__icontains=search_text) # Like '%s%'
        
        # Original sort order was 'Order By tblDG_Item_Master.Id Desc'
        return queryset.order_by('-id')

    # This method is not strictly necessary for DataTables which uses client-side pagination,
    # but could be used if server-side pagination were required or if initial load is paginated.
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # DataTables will handle pagination and filtering, so we pass the full queryset
        return context


class SubcategoryOptionsView(TemplateView):
    """
    Returns the updated subcategory dropdown options based on the selected category,
    to be swapped via HTMX.
    """
    template_name = 'machinery/machineryitem/_subcategory_options.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        category_id = self.request.GET.get('category', None)
        subcategories = SubCategory.objects.none()
        if category_id and category_id != 'Select Category':
            try:
                category_id = int(category_id)
                subcategories = SubCategory.objects.filter(cid=category_id).order_by('scname')
            except (ValueError, TypeError):
                pass
        context['subcategories'] = subcategories
        return context

# The following views are placeholders for full CRUD.
# For this "Machinery_Edit" (list) page, the original ASP.NET redirects
# to a separate "Machinery_Edit_Details.aspx". We will use HTMX modals for a modern approach.

# class MachineryItemCreateView(CreateView):
#     model = MachineryItem
#     form_class = MachineryItemForm # Needs to be defined
#     template_name = 'machinery/machineryitem/form.html'
#     success_url = reverse_lazy('machinery_list')

#     def form_valid(self, form):
#         # Example of assigning session data as in ASP.NET
#         # form.instance.compid = self.request.session.get('compid')
#         # form.instance.created_by = self.request.session.get('username')
#         response = super().form_valid(form)
#         messages.success(self.request, 'Machinery Item added successfully.')
#         if self.request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshMachineryItemList'
#                 }
#             )
#         return response

# class MachineryItemUpdateView(UpdateView):
#     model = MachineryItem
#     form_class = MachineryItemForm # Needs to be defined
#     template_name = 'machinery/machineryitem/form.html'
#     success_url = reverse_lazy('machinery_list')

#     def form_valid(self, form):
#         response = super().form_valid(form)
#         messages.success(self.request, 'Machinery Item updated successfully.')
#         if self.request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshMachineryItemList'
#                 }
#             )
#         return response

# class MachineryItemDeleteView(DeleteView):
#     model = MachineryItem
#     template_name = 'machinery/machineryitem/confirm_delete.html'
#     success_url = reverse_lazy('machinery_list')

#     def delete(self, request, *args, **kwargs):
#         response = super().delete(request, *args, **kwargs)
#         messages.success(self.request, 'Machinery Item deleted successfully.')
#         if request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshMachineryItemList'
#                 }
#             )
#         return response

```

#### 4.4 Templates (`machinery/templates/machinery/machineryitem/`)

We'll need three main templates: `list.html` (the main page), `_machineryitem_table.html` (the HTMX partial for the table), and `_subcategory_options.html` (HTMX partial for dropdown).

**`machinery/templates/machinery/machineryitem/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Machinery - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Machinery Items</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'machinery_add' %}" {# Placeholder if add view exists #}
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Machinery Item
        </button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search & Filter</h3>
        <form hx-get="{% url 'machinery_table' %}" hx-target="#machineryItemTable-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.category.label }}
                    </label>
                    {{ form.category }}
                </div>
                <div id="id_subcategory-container"> {# Target for subcategory HTMX swap #}
                    <label for="{{ form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.subcategory.label }}
                    </label>
                    {{ form.subcategory }}
                </div>
                <div>
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_by.label }}
                    </label>
                    {{ form.search_by }}
                </div>
                <div>
                    <label for="{{ form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_text.label }}
                    </label>
                    {{ form.search_text }}
                </div>
                <div class="md:col-span-1 lg:col-span-4 flex justify-end">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded shadow">
                        Search
                    </button>
                </div>
            </div>
            {% if form.errors %}
                <div class="text-red-500 text-sm mt-2">
                    {% for field in form %}
                        {% for error in field.errors %}
                            <p>{{ field.label }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>
    
    <div id="machineryItemTable-container"
         hx-trigger="load, refreshMachineryItemList from:body"
         hx-get="{% url 'machinery_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Machinery Items...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation (shared) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterSwap remove .is-active from #modal if event.detail.xhr.status == 204 or event.detail.xhr.status == 302">
             <!-- Modal content will be loaded here via HTMX -->
             <div class="text-center py-8">
                 <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
                 <p class="mt-2">Loading...</p>
             </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });

    // Ensure DataTables is initialized only after the table content is loaded
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'machineryItemTable-container') {
            const table = document.getElementById('machineryItemTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 20, // Match original page size
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": 0 } // SN column not orderable
                    ]
                });
            }
        }
    });

    // To handle message display from Django messages framework
    document.addEventListener('DOMContentLoaded', function() {
        const messagesDiv = document.getElementById('django-messages');
        if (messagesDiv) {
            messagesDiv.innerHTML = `
                {% if messages %}
                    {% for message in messages %}
                    <div class="absolute top-4 right-4 bg-{{ message.tags }}-500 text-white px-4 py-2 rounded shadow-md"
                        x-data="{ show: true }"
                        x-init="setTimeout(() => show = false, 3000)"
                        x-show="show"
                        x-transition:enter="transition ease-out duration-300"
                        x-transition:enter-start="opacity-0 transform translate-x-full"
                        x-transition:enter-end="opacity-100 transform translate-x-0"
                        x-transition:leave="transition ease-in duration-300"
                        x-transition:leave-start="opacity-100 transform translate-x-0"
                        x-transition:leave-end="opacity-0 transform translate-x-full">
                        {{ message }}
                    </div>
                    {% endfor %}
                {% endif %}
            `;
        }
    });
</script>
{% endblock %}
```

**`machinery/templates/machinery/machineryitem/_machineryitem_table.html`**

```html
<table id="machineryItemTable" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in machinery_items %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {# Original LinkButton "Sel" redirects to details. We will use HTMX for modal. #}
                <button type="button" class="text-blue-600 hover:text-blue-900 font-medium"
                        hx-get="{% url 'machinery_edit' item.id %}" {# Assumes a machinery_edit URL #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                    {{ item.itemcode }}
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.truncated_manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uombasic.symbol|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.stockqty|floatformat:"-2" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.get_display_location|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                    hx-get="{% url 'machinery_edit' item.id %}" {# Placeholder if edit view exists #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'machinery_delete' item.id %}" {# Placeholder if delete view exists #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center text-gray-500">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`machinery/templates/machinery/machineryitem/_subcategory_options.html`**

```html
<div id="id_subcategory-container">
    <label for="id_subcategory" class="block text-sm font-medium text-gray-700">SubCategory</label>
    <select name="subcategory" id="id_subcategory" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3">
        <option value="Select SubCategory">Select SubCategory</option>
        {% for subcategory in subcategories %}
            <option value="{{ subcategory.scid }}">{{ subcategory.symbol }} - {{ subcategory.scname }}</option>
        {% endfor %}
    </select>
</div>
```

**`machinery/templates/machinery/machineryitem/form.html` (Placeholder for Edit/Add Form)**
*Note: This template would be requested by HTMX for Create/Update views.*

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit Machinery Item{% else %}Add New Machinery Item{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" to allow HTMX triggers in headers #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`machinery/templates/machinery/machineryitem/confirm_delete.html` (Placeholder for Delete Confirmation)**
*Note: This template would be requested by HTMX for Delete views.*

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the machinery item "<strong>{{ object.itemcode }} - {{ object.truncated_manf_desc }}</strong>"? This action cannot be undone.
    </p>
    <form hx-delete="{% url 'machinery_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`machinery/urls.py`)

This file defines the URL patterns for your Django application.

```python
from django.urls import path
from .views import MachineryItemListView, MachineryItemTablePartialView, SubcategoryOptionsView
# from .views import MachineryItemCreateView, MachineryItemUpdateView, MachineryItemDeleteView # Uncomment for full CRUD

urlpatterns = [
    # Main list page for machinery items
    path('machinery/', MachineryItemListView.as_view(), name='machinery_list'),
    
    # HTMX endpoint to load the table content (for initial load and search/filter)
    path('machinery/table/', MachineryItemTablePartialView.as_view(), name='machinery_table'),
    
    # HTMX endpoint to dynamically load subcategory options based on category selection
    path('machinery/subcategories/', SubcategoryOptionsView.as_view(), name='subcategory_options'),

    # Placeholder URLs for full CRUD operations, triggered via HTMX modals
    # path('machinery/add/', MachineryItemCreateView.as_view(), name='machinery_add'),
    # path('machinery/edit/<int:pk>/', MachineryItemUpdateView.as_view(), name='machinery_edit'),
    # path('machinery/delete/<int:pk>/', MachineryItemDeleteView.as_view(), name='machinery_delete'),
]
```

#### 4.6 Tests (`machinery/tests.py`)

Comprehensive tests for models and views ensure reliability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import MachineryItem, Category, SubCategory, Unit, MsMaster
from .forms import MachinerySearchForm

class ModelTestBase(TestCase):
    """Base class to set up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        cls.category_1 = Category.objects.create(cid=1, cname='Electronics')
        cls.category_2 = Category.objects.create(cid=2, cname='Mechanical')
        cls.subcategory_1 = SubCategory.objects.create(scid=101, cid=cls.category_1, scname='Industrial Robots', symbol='IR')
        cls.subcategory_2 = SubCategory.objects.create(scid=102, cid=cls.category_1, scname='CNC Machines', symbol='CNC')
        cls.subcategory_3 = SubCategory.objects.create(scid=201, cid=cls.category_2, scname='Lathes', symbol='LT')
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')
        
        cls.item_1 = MachineryItem.objects.create(
            id=1,
            itemcode='MACH001',
            manfdesc='High-precision Industrial Robot Arm for assembly',
            stockqty=10.00,
            location='Warehouse A, Section 1',
            uombasic=cls.unit_pcs,
            cid=cls.category_1,
            scid=cls.subcategory_1,
            compid=1,
            absolute=0 # Not absolute
        )
        cls.item_2 = MachineryItem.objects.create(
            id=2,
            itemcode='MACH002',
            manfdesc='Compact CNC Milling Machine for small parts production',
            stockqty=5.50,
            location='Warehouse B, Section 2',
            uombasic=cls.unit_kg,
            cid=cls.category_1,
            scid=cls.subcategory_2,
            compid=1,
            absolute=0
        )
        cls.item_3 = MachineryItem.objects.create(
            id=3,
            itemcode='MACH003',
            manfdesc='Heavy Duty Lathe Machine for metalworking',
            stockqty=2.00,
            location='Factory Floor',
            uombasic=cls.unit_pcs,
            cid=cls.category_2,
            scid=cls.subcategory_3,
            compid=1,
            absolute=1 # Is absolute, should be filtered out by default queries
        )
        MsMaster.objects.create(itemid=cls.item_1.id, location='Specific Location X')


class MachineryItemModelTest(ModelTestBase):
    def test_machinery_item_creation(self):
        item = MachineryItem.objects.get(id=1)
        self.assertEqual(item.itemcode, 'MACH001')
        self.assertEqual(item.manfdesc, 'High-precision Industrial Robot Arm for assembly')
        self.assertEqual(item.stockqty, 10.00)
        self.assertEqual(item.location, 'Warehouse A, Section 1')
        self.assertEqual(item.uombasic.symbol, 'PCS')
        self.assertEqual(item.cid.cname, 'Electronics')
        self.assertEqual(item.scid.scname, 'Industrial Robots')
        self.assertEqual(item.compid, 1)
        self.assertEqual(item.absolute, 0)

    def test_truncated_manf_desc(self):
        item = MachineryItem.objects.get(id=1)
        # Test truncation
        self.assertEqual(item.truncated_manf_desc, 'High-precision Industrial Robot Arm for assembly') # Not long enough to truncate
        
        long_desc_item = MachineryItem.objects.create(
            id=4, itemcode='LONGDESC', manfdesc='A very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very much in the sense of the end of the day or the week. This is an exciting prospect for anyone looking to embrace modern technological architectures.

It's a fantastic idea for a presentation at an industry conference for software development professionals, as it tackles a common challenge in the corporate world: how to modernize legacy systems. The presentation could go through the following steps:

1.  **Introduction**: Briefly explain the common problem of legacy ASP.NET applications and the need for modernization. Introduce Django as a robust, modern alternative and HTMX/Alpine.js for a performant, modern frontend.
2.  **The Challenge**: Present a simplified version of the ASP.NET code provided, highlighting its drawbacks (e.g., tight coupling, dated UI, security concerns like SQL injection in the C#).
3.  **The Modernization Strategy (Django & HTMX/Alpine.js)**:
    *   **Backend (Django)**: Explain the "Fat Model, Thin View" philosophy. Show how to translate the database schema into Django models with `managed=False`. Discuss the benefits of Django's ORM over raw SQL.
    *   **Frontend (HTMX & Alpine.js)**: Introduce HTMX for declarative AJAX and Alpine.js for lightweight JavaScript interactivity. Emphasize how this avoids the complexity of full-blown SPA frameworks. Explain how DataTables fits into this for rich table experiences.
4.  **Live Code Walkthrough (Key Components)**:
    *   **Models**: Show the `MachineryItem` model, `Category`, `SubCategory`, `Unit`, and `MsMaster`. Highlight properties like `truncated_manf_desc` and `get_display_location` as examples of moving business logic to models.
    *   **Forms**: Demonstrate the `MachinerySearchForm`, explaining how it handles filtering inputs.
    *   **Views**: Present the `MachineryItemListView` and `MachineryItemTablePartialView`. Explain the role of `TemplateView` for the main page and `ListView` for the HTMX-loaded table. Highlight the `get_queryset` method's use of Django ORM for filtering.
    *   **Templates**: Walk through `list.html`, `_machineryitem_table.html`, and `_subcategory_options.html`. Show how HTMX attributes (`hx-get`, `hx-target`, `hx-swap`, `hx-trigger`) replace ASP.NET PostBacks and event handlers. Explain the integration with DataTables.
    *   **URLs**: Show the `machinery/urls.py` structure.
5.  **Testing Strategy**: Emphasize the importance of robust testing. Show examples from `machinery/tests.py`, covering model methods and view interactions (including HTMX responses).
6.  **Benefits & Outcomes**: Reiterate the business benefits:
    *   Faster development cycles.
    *   Reduced maintenance costs.
    *   Improved user experience.
    *   Future-proof architecture.
    *   Easier to onboard new developers due to Python's readability and Django's conventions.
7.  **Q&A**: Open the floor for questions.

**Visual Aids for the Presentation:**

*   **Before/After Screenshots**: Show the old ASP.NET page and a mock-up of the new Django/HTMX version, highlighting the cleaner UI and interactive elements.
*   **Architecture Diagrams**: Simple diagrams illustrating the ASP.NET architecture vs. the Django/HTMX architecture, emphasizing the separation of concerns.
*   **Code Snippet Comparisons**: Side-by-side comparisons of key ASP.NET C# logic and its Django/Python ORM equivalent.
*   **Live Demo**: A short, pre-recorded or live demo of the migrated Django application to show the dynamic filtering and table interactions.

This presentation would be highly valuable for organizations struggling with similar modernization challenges, offering a clear, actionable pathway to a more agile and efficient web application. It positions AI-assisted automation as a key enabler for such transitions, which would be a compelling aspect for an industry conference.```markdown
## ASP.NET to Django Conversion Script: Machinery Management Module

This document outlines a strategic plan for migrating the existing ASP.NET Machinery Edit functionality to a modern Django application. The focus is on leveraging AI-assisted automation to streamline the transition, emphasizing a robust, maintainable, and scalable Django solution.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Business Value Proposition:

Migrating this ASP.NET module to Django offers significant business advantages:
1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are complex to maintain and scale, ensuring the longevity and adaptability of your ERP system.
2.  **Enhanced User Experience:** Leverages HTMX and Alpine.js for highly responsive interfaces, eliminating full-page reloads and providing a smoother, more interactive user experience, similar to a modern Single Page Application (SPA) but with significantly simpler development and maintenance overhead.
3.  **Improved Maintainability & Scalability:** Django's structured Model-View-Template (MTV) architecture, clear separation of concerns (enforcing fat models and thin views), and Python's readability lead to more maintainable code, easier debugging, and straightforward scaling as your business grows.
4.  **Cost Efficiency:** Python's vast ecosystem, Django's "batteries included" philosophy, and rapid development capabilities reduce initial development time and long-term maintenance costs.
5.  **Modernization & Future-Proofing:** Adopts a modern, widely-supported web framework and cutting-edge frontend technologies, ensuring the application remains relevant, secure, and adaptable to future technological advancements and business requirements.
6.  **Automated Testing:** The integrated testing suite mandates higher code quality, fewer bugs, and faster, more confident development cycles, minimizing disruption to your operations.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the provided ASP.NET code, particularly the `Fillgridview` method's SQL queries and `fillGrid` method's `DataTable` creation and per-row lookups, we can infer the following database schema. Note that primary keys are typically `Id` in ASP.NET code, but may be `tblName_Id` in the actual database. We will assume the `Id` or `CId`/`SCId` in the C# code maps directly to database column names, and use them as `db_column` values.

**Inferred Tables and Columns:**

*   **`tblDG_Item_Master`**: Represents the core machinery items.
    *   `Id` (Primary Key, INTEGER)
    *   `ItemCode` (TEXT/VARCHAR)
    *   `ManfDesc` (TEXT/VARCHAR) - Manufacturer Description.
    *   `StockQty` (NUMERIC/DECIMAL) - Stock Quantity.
    *   `Location` (TEXT/VARCHAR) - Item's location.
    *   `UOMBasic` (INTEGER) - Foreign key to `Unit_Master.Id` (Unit of Measurement).
    *   `CId` (INTEGER) - Foreign key to `tblDG_Category_Master.CId` (Category).
    *   `SCId` (INTEGER) - Foreign key to `tblDG_SubCategory_Master.SCId` (SubCategory).
    *   `CompId` (INTEGER) - Company ID (e.g., `Session["compid"]`).
    *   `Absolute` (INTEGER/BOOLEAN) - Flag for item status (`!='1'` used in query).
*   **`tblDG_Category_Master`**: Defines machinery categories.
    *   `CId` (Primary Key, INTEGER)
    *   `CName` (TEXT/VARCHAR) - Category Name (inferred from `drpDesignCategory`).
*   **`tblDG_SubCategory_Master`**: Defines machinery subcategories.
    *   `SCId` (Primary Key, INTEGER)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, INTEGER)
    *   `SCName` (TEXT/VARCHAR) - SubCategory Name.
    *   `Symbol` (TEXT/VARCHAR) - Symbol associated with subcategory (used to form `Symbol+' - '+SCName`).
*   **`Unit_Master`**: Defines units of measurement.
    *   `Id` (Primary Key, INTEGER)
    *   `Symbol` (TEXT/VARCHAR) - Unit Symbol (e.g., "KG", "PCS").
*   **`tblMS_Master`**: Used in a join, and `Location` is retrieved. This seems to be a related table with `ItemId` and `Location` which might indicate an override or a more specific location. For this modernization, we will model this relationship and prioritize `tblMS_Master.Location` if available, mirroring the original logic.

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements **Read (List and Search)** functionality with navigation to an "Edit Details" page.

*   **Read (List):** The `GridView2` displays a paginated list of machinery items. This is handled by `Fillgridview` and `fillGrid` methods.
*   **Read (Search/Filter):** Users can filter items by Category, SubCategory, and a search term (by Machine Code or Description). This is triggered by dropdown changes (`OnSelectedIndexChanged`) and the search button (`OnClick`).
*   **Navigation to Edit/Details:** The `LinkButton` within the `GridView2` with `CommandName="Sel"` redirects to `Machinery_Edit_Details.aspx?Id=...`. In Django, this will translate to an HTMX-powered button/link that loads an edit form into a modal, or redirects to a dedicated detail/edit page. Our plan will focus on a modern HTMX modal approach for a more fluid user experience.
*   **Pagination:** The `GridView2` supports paging (`AllowPaging="True"`, `PageSize="20"`), which will be handled client-side by DataTables in Django for efficiency and responsiveness.
*   **Temporary Table Deletion:** The `Page_Load` event includes `DELETE` statements for `tblMS_Spares_Temp` and `tblMS_Process_Temp`. This is an unusual and problematic pattern for a listing page and is often indicative of mismanaged session-scoped data or global cleanup. This behavior will **not** be directly migrated to Django, as it would imply global table truncation on every page load, which is a severe anti-pattern. Django's session management, caching, or explicit request-scoped temporary data handling would be used if such temporary data storage were genuinely needed in a safe manner.

### Step 3: Infer UI Components

The ASP.NET controls map directly to standard HTML elements and Django form fields, enhanced with Tailwind CSS for styling and HTMX/Alpine.js for interactivity.

*   **`DrpCategory` (DropDownList)**: Standard HTML `<select>` for category selection.
*   **`DrpSubCategory` (DropDownList)**: Standard HTML `<select>` for subcategory selection, dynamically updated via HTMX based on category choice.
*   **`DrpSearchCode` (DropDownList)**: Standard HTML `<select>` for defining the search criteria (Machine Code or Description).
*   **`txtSearchItemCode` (TextBox)**: Standard HTML `<input type="text">` for the search query.
*   **`btnSearch` (Button)**: Standard HTML `<button>` to trigger the search, driven by HTMX.
*   **`GridView2` (GridView)**: Standard HTML `<table>` element, which will be transformed into a DataTables-enhanced table.
    *   Columns displayed: SN (Row number), Machine Code (as a clickable link/button), Description, UOM, Stock Qty, Location, and an "Actions" column for Edit/Delete buttons.
    *   The hidden `Id` field in the ASP.NET GridView will be implicitly handled by Django's ORM and HTMX, as the `pk` (primary key) is passed in URLs.

### Step 4: Generate Django Code

We will organize the migrated functionality into a new Django application, let's call it `machinery`.

#### 4.1 Models (`machinery/models.py`)

This file will define the Django models that directly map to your existing database tables. We'll use `managed = False` and `db_table` to connect to the existing schema. Foreign key relationships are explicitly defined using `models.ForeignKey` with `models.DO_NOTHING` as per `managed=False` best practices. Custom properties are added to encapsulate business logic (e.g., truncation, location determination).

```python
from django.db import models
from django.utils.text import slugify

class Category(models.Model):
    """
    Maps to tblDG_Category_Master.
    Represents machinery categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.cname or f"Category {self.cid}"

class SubCategory(models.Model):
    """
    Maps to tblDG_SubCategory_Master.
    Represents machinery subcategories.
    """
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    scname = models.CharField(db_column='SCName', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'

    def __str__(self):
        return f"{self.symbol or ''} - {self.scname or f'SubCategory {self.scid}'}".strip(' -')

class Unit(models.Model):
    """
    Maps to Unit_Master.
    Represents units of measurement.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class MsMaster(models.Model):
    """
    Maps to tblMS_Master.
    Represents master data for Machinery System, used for specific location overrides.
    """
    # Assuming ItemId is unique key or part of composite key in tblMS_Master.
    # If not unique, Django's PK must be auto-generated or explicitly defined.
    # For now, treating ItemId as the primary key as per usage context in C#
    itemid = models.IntegerField(db_column='ItemId', primary_key=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'MS Master Entry'
        verbose_name_plural = 'MS Master Entries'

    def __str__(self):
        return f"MS Master for Item {self.itemid} at {self.location}"

class MachineryItem(models.Model):
    """
    Maps to tblDG_Item_Master.
    Represents a machinery item.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    stockqty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)
    uombasic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    scid = models.ForeignKey(SubCategory, models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    absolute = models.IntegerField(db_column='Absolute', blank=True, null=True) # Used as a flag, 1 is 'Absolute'

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machinery Item'
        verbose_name_plural = 'Machinery Items'

    def __str__(self):
        return self.itemcode or f"Item {self.id}"

    @property
    def truncated_manf_desc(self):
        """
        Returns a truncated version of the manufacturer description, similar to SUBSTRING(...,0,80)+'...'.
        """
        if self.manfdesc and len(self.manfdesc) > 80:
            return self.manfdesc[:77] + '...'
        return self.manfdesc

    def get_display_location(self):
        """
        Attempts to get location from MsMaster first, then falls back to item's own location.
        This mimics the C# logic where MS_Master location seems to take precedence during display.
        """
        try:
            ms_entry = MsMaster.objects.get(itemid=self.id)
            if ms_entry.location:
                return ms_entry.location
        except MsMaster.DoesNotExist:
            pass # Fallback to item's own location if no MsMaster entry or location is null
        return self.location

    def is_absolute(self):
        """Checks if the item is marked as 'Absolute' (Absolute = 1)."""
        return self.absolute == 1

    # Additional business logic methods for machinery items can be added here
    # E.g., calculate_maintenance_cost(), get_stock_status(), etc.
```

#### 4.2 Forms (`machinery/forms.py`)

This file will define a Django form to handle the search/filter criteria. This is a regular `forms.Form` (not a `ModelForm`) because it represents search parameters rather than a single model instance. HTMX attributes are included in the widgets to facilitate dynamic updates of the subcategory dropdown.

```python
from django import forms
from .models import Category, SubCategory

class MachinerySearchForm(forms.Form):
    """
    Form for searching and filtering Machinery Items.
    """
    SEARCH_FIELD_CHOICES = [
        ('Select', 'Select'),
        ('item_code', 'Machine Code'),
        ('manf_desc', 'Description'),
    ]

    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('cname'),
        empty_label='Select Category',
        required=False,
        label='Category',
        # HTMX attributes for dynamic subcategory loading
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/machinery/subcategories/', 'hx-target': '#id_subcategory-container', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'})
    )
    subcategory = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(), # Initially empty, populated dynamically via HTMX
        empty_label='Select SubCategory',
        required=False,
        label='SubCategory',
        widget=forms.Select(attrs={'class': 'box3', 'id': 'id_subcategory'}) # ID for HTMX target
    )
    search_by = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        required=False,
        label='Search By',
        widget=forms.Select(attrs={'class': 'box3'})
    )
    search_text = forms.CharField(
        max_length=200,
        required=False,
        label='Search Text',
        widget=forms.TextInput(attrs={'class': 'box3 w-full max-w-xs'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply Tailwind CSS classes to all form fields for consistent styling
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Select)):
                current_classes = field.widget.attrs.get('class', '')
                # Ensure existing classes like 'box3' are preserved, while adding Tailwind defaults
                field.widget.attrs['class'] = f'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm {current_classes}'.strip()

        # If a category is already selected (e.g., on initial load after postback),
        # pre-filter the subcategory queryset.
        if 'category' in self.data and self.data['category']:
            try:
                category_id = int(self.data['category'])
                self.fields['subcategory'].queryset = SubCategory.objects.filter(cid=category_id).order_by('scname')
            except (ValueError, TypeError):
                pass  # Handle invalid category ID gracefully, leave subcategory empty

    def clean(self):
        """
        Custom validation for search criteria.
        If a search_by field is selected, search_text should not be empty.
        """
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_text = cleaned_data.get('search_text')

        if search_by and search_by != 'Select' and not search_text:
            self.add_error('search_text', 'Search text is required when a specific search field is selected.')
        return cleaned_data

# Note: A MachineryItemForm would be needed for CreateView/UpdateView (full CRUD).
# from .models import MachineryItem
# class MachineryItemForm(forms.ModelForm):
#     class Meta:
#         model = MachineryItem
#         fields = ['itemcode', 'manfdesc', 'stockqty', 'location', 'uombasic', 'cid', 'scid']
#         widgets = {
#             'itemcode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
#             'manfdesc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'rows': 3}),
#             'stockqty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
#             'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
#             'uombasic': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
#             'cid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
#             'scid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm'}),
#         }
```

#### 4.3 Views (`machinery/views.py`)

Views will handle rendering the main page, processing search/filter requests, and dynamically loading subcategory options. We use `TemplateView` for the main page to keep it thin, and `ListView` for the HTMX-loaded table content. Note the `CompId` filtering from the original ASP.NET code, which is handled via session data.

```python
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.db.models import Q
from .models import MachineryItem, Category, SubCategory, Unit
from .forms import MachinerySearchForm
from django.shortcuts import render # For direct rendering if needed, though CBVs handle most cases

# For a full CRUD implementation, you'd uncomment and use these:
# from django.views.generic import CreateView, UpdateView, DeleteView
# from .forms import MachineryItemForm

class MachineryItemListView(TemplateView):
    """
    Displays the main machinery item list page with the search/filter form.
    The actual table content is loaded dynamically via HTMX.
    """
    template_name = 'machinery/machineryitem/list.html'

    def get_context_data(self, **kwargs):
        """
        Populates the context with the search form, pre-filled with GET parameters if any.
        """
        context = super().get_context_data(**kwargs)
        # Initialize the search form. It will be pre-filled if there are GET parameters,
        # allowing form validation to reflect previous search states.
        context['form'] = MachinerySearchForm(self.request.GET)
        return context

class MachineryItemTablePartialView(ListView):
    """
    Returns the HTML table for machinery items, used by HTMX to refresh the list.
    This view encapsulates the data fetching and filtering logic.
    """
    model = MachineryItem
    template_name = 'machinery/machineryitem/_machineryitem_table.html'
    context_object_name = 'machinery_items'
    # paginate_by = 20 # DataTables handles client-side pagination, but this could be for server-side if needed

    def get_queryset(self):
        """
        Constructs the queryset based on search and filter parameters from the request.
        Mirrors the filtering logic from the original ASP.NET Fillgridview method.
        """
        queryset = super().get_queryset()
        
        # Apply mandatory filters from original ASP.NET (CompId and Absolute flag)
        comp_id = self.request.session.get('compid', None)
        if comp_id is not None:
            queryset = queryset.filter(compid=comp_id)
        
        # Filter out items where Absolute = 1
        queryset = queryset.filter(Q(absolute__isnull=True) | Q(absolute=0))

        form = MachinerySearchForm(self.request.GET)
        if form.is_valid():
            category = form.cleaned_data.get('category')
            subcategory = form.cleaned_data.get('subcategory')
            search_by = form.cleaned_data.get('search_by')
            search_text = form.cleaned_data.get('search_text')

            # Filter by Category if selected and not 'Select Category'
            if category and category.cid != 'Select Category':
                queryset = queryset.filter(cid=category)
            
            # Filter by SubCategory only if selected and not 'Select SubCategory'
            # and (implied from ASP.NET) a category is also selected
            if subcategory and subcategory.scid != 'Select SubCategory' and category:
                queryset = queryset.filter(scid=subcategory)

            # Apply search text filter based on selected search_by option
            if search_by and search_by != 'Select' and search_text:
                if search_by == 'item_code':
                    queryset = queryset.filter(itemcode__istartswith=search_text) # Case-insensitive startswith
                elif search_by == 'manf_desc':
                    queryset = queryset.filter(manfdesc__icontains=search_text) # Case-insensitive contains
        
        # Original sort order was 'Order By tblDG_Item_Master.Id Desc'
        return queryset.order_by('-id')


class SubcategoryOptionsView(TemplateView):
    """
    Returns the updated subcategory dropdown options based on the selected category ID.
    This view is specifically designed to be called by HTMX for dynamic dropdown updates.
    """
    template_name = 'machinery/machineryitem/_subcategory_options.html'

    def get_context_data(self, **kwargs):
        """
        Filters subcategories based on the 'category' GET parameter.
        """
        context = super().get_context_data(**kwargs)
        category_id = self.request.GET.get('category', None)
        subcategories = SubCategory.objects.none() # Default to empty queryset
        if category_id and category_id != 'Select Category':
            try:
                category_id = int(category_id)
                subcategories = SubCategory.objects.filter(cid=category_id).order_by('scname')
            except (ValueError, TypeError):
                pass # Invalid category ID, subcategories remain empty
        context['subcategories'] = subcategories
        return context

# The following views are placeholders for full CRUD operations (Create, Update, Delete).
# The original ASP.NET "Machinery_Edit.aspx" was primarily a list and search page that
# then redirected to "Machinery_Edit_Details.aspx" for actual editing.
# For a modern Django/HTMX application, these operations would typically be handled
# via HTMX-loaded modals, making the user experience smoother.

# class MachineryItemCreateView(CreateView):
#     model = MachineryItem
#     form_class = MachineryItemForm # This form needs to be defined in forms.py
#     template_name = 'machinery/machineryitem/form.html'
#     success_url = reverse_lazy('machinery_list') # Redirect back to list on success

#     def form_valid(self, form):
#         # Example of assigning session data to the new instance
#         # This mimics the ASP.NET session usage for compid etc.
#         # form.instance.compid = self.request.session.get('compid', 1)
#         # form.instance.created_by = self.request.session.get('username')
#         response = super().form_valid(form)
#         messages.success(self.request, 'Machinery Item added successfully.')
#         # For HTMX requests, return 204 No Content and trigger a client-side event
#         if self.request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshMachineryItemList' # Custom event to refresh the table
#                 }
#             )
#         return response

# class MachineryItemUpdateView(UpdateView):
#     model = MachineryItem
#     form_class = MachineryItemForm # This form needs to be defined in forms.py
#     template_name = 'machinery/machineryitem/form.html'
#     success_url = reverse_lazy('machinery_list')

#     def form_valid(self, form):
#         response = super().form_valid(form)
#         messages.success(self.request, 'Machinery Item updated successfully.')
#         if self.request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshMachineryItemList'
#                 }
#             )
#         return response

# class MachineryItemDeleteView(DeleteView):
#     model = MachineryItem
#     template_name = 'machinery/machineryitem/confirm_delete.html'
#     success_url = reverse_lazy('machinery_list')

#     def delete(self, request, *args, **kwargs):
#         response = super().delete(request, *args, **kwargs)
#         messages.success(self.request, 'Machinery Item deleted successfully.')
#         if request.headers.get('HX-Request'):
#             return HttpResponse(
#                 status=204,
#                 headers={
#                     'HX-Trigger': 'refreshMachineryItemList'
#                 }
#             )
#         return response
```

#### 4.4 Templates (`machinery/templates/machinery/machineryitem/`)

These templates render the UI components. They extensively use HTMX attributes for dynamic content updates and Alpine.js for basic UI state (like modal visibility). They extend `core/base.html` for consistent layout (but `base.html` content is not included here as per instructions).

**`machinery/templates/machinery/machineryitem/list.html`**
This is the main page template that loads the search form and an empty container for the HTMX-powered DataTables.

```html
{% extends 'core/base.html' %}

{% block title %}Machinery - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery Items</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'machinery_add' %}" {# This URL is a placeholder for a future CreateView #}
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Machinery Item
        </button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-xl mb-6 border border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search & Filter</h3>
        <form hx-get="{% url 'machinery_table' %}" hx-target="#machineryItemTable-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.category.label }}
                    </label>
                    {{ form.category }}
                </div>
                {# This div is the HTMX target for dynamic subcategory updates #}
                <div id="id_subcategory-container">
                    <label for="{{ form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.subcategory.label }}
                    </label>
                    {{ form.subcategory }}
                </div>
                <div>
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.search_by.label }}
                    </label>
                    {{ form.search_by }}
                </div>
                <div>
                    <label for="{{ form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.search_text.label }}
                    </label>
                    {{ form.search_text }}
                </div>
                <div class="md:col-span-1 lg:col-span-4 flex justify-end mt-4 md:mt-0">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow transition duration-150 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
            {% if form.errors %}
                <div class="text-red-600 text-sm mt-4 p-3 bg-red-50 rounded-md border border-red-200">
                    <p class="font-bold mb-1">Please correct the following errors:</p>
                    {% for field in form %}
                        {% for error in field.errors %}
                            <p>- {{ field.label }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                        <p>- {{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>
    
    {# This container will be updated by HTMX when the table needs refreshing #}
    <div id="machineryItemTable-container"
         hx-trigger="load, refreshMachineryItemList from:body" {# Loads on page load, and on custom event #}
         hx-get="{% url 'machinery_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Machinery Items...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation (shared) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             _="on htmx:afterSwap remove .is-active from #modal if event.detail.xhr.status == 204 or event.detail.xhr.status == 302">
             {# Modal content will be loaded here via HTMX #}
             <div class="text-center py-8">
                 <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
                 <p class="mt-2 text-gray-600">Loading...</p>
             </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state.
        // For example, managing modal states or dynamic form fields not handled by HTMX alone.
    });

    // Ensure DataTables is initialized only after the table content is loaded via HTMX.
    // This listener targets the container where the table is swapped in.
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'machineryItemTable-container') {
            const table = document.getElementById('machineryItemTable');
            if (table && !$.fn.DataTable.isDataTable(table)) { // Check if DataTables is not already initialized
                $(table).DataTable({
                    "pageLength": 20, // Matches original ASP.NET GridView page size
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": 0 } // SN column is not orderable
                    ],
                    "responsive": true
                });
            }
        }
    });

    // Handle Django messages display using Alpine.js for transient alerts.
    // This assumes a container with id 'django-messages' exists in base.html.
    document.addEventListener('DOMContentLoaded', function() {
        const messagesDiv = document.getElementById('django-messages');
        if (messagesDiv) {
            // Example of how to structure Django messages in base.html for Alpine.js/Tailwind
            // This script would typically be placed in base.html if it's a global message handler.
            // For this exercise, it's just illustrative.
            if (messagesDiv.innerHTML.trim() !== '') { // Check if there are messages
                // You would typically have Alpine.js components in base.html
                // that pick up these messages and show them.
                // For demonstration:
                console.log('Django messages present, handle their display via Alpine.js in base.html');
            }
        }
    });
</script>
{% endblock %}
```

**`machinery/templates/machinery/machineryitem/_machineryitem_table.html`**
This partial template contains only the `<table>` element for the DataTables. It is designed to be swapped into `list.html` by HTMX.

```html
<table id="machineryItemTable" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in machinery_items %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {# Original LinkButton "Sel" redirects to details. Using HTMX for modal. #}
                <button type="button" class="text-blue-600 hover:text-blue-900 font-medium hover:underline"
                        hx-get="{% url 'machinery_edit' item.pk %}" {# Placeholder if edit view exists #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                    {{ item.itemcode }}
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.truncated_manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uombasic.symbol|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.stockqty|floatformat:"-2" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.get_display_location|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 flex space-x-2">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'machinery_edit' item.pk %}" {# Placeholder for edit view #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'machinery_delete' item.pk %}" {# Placeholder for delete view #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center text-gray-500 text-lg">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`machinery/templates/machinery/machineryitem/_subcategory_options.html`**
This partial template renders the `<select>` element for subcategories. It is swapped into `list.html` by HTMX when the category dropdown changes.

```html
{# The outer div is crucial for hx-target="outerHTML" to replace the entire input group #}
<div id="id_subcategory-container">
    <label for="id_subcategory" class="block text-sm font-medium text-gray-700 mb-1">SubCategory</label>
    <select name="subcategory" id="id_subcategory" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3">
        <option value="Select SubCategory">Select SubCategory</option>
        {% for subcategory in subcategories %}
            <option value="{{ subcategory.scid }}">{{ subcategory.symbol }} - {{ subcategory.scname }}</option>
        {% endfor %}
    </select>
</div>
```

**`machinery/templates/machinery/machineryitem/form.html` (Placeholder for Edit/Add Form)**
This partial template would be used by `MachineryItemCreateView` and `MachineryItemUpdateView` and loaded into the modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit Machinery Item{% else %}Add New Machinery Item{% endif %}
    </h3>
    {# hx-swap="none" is used here to prevent the modal content from being replaced. #}
    {# The server will return a 204 No Content with HX-Trigger for refreshing the list. #}
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`machinery/templates/machinery/machineryitem/confirm_delete.html` (Placeholder for Delete Confirmation)**
This partial template would be used by `MachineryItemDeleteView` and loaded into the modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the machinery item "<strong>{{ object.itemcode|default:"N/A" }} - {{ object.truncated_manf_desc|default:"N/A" }}</strong>"? This action cannot be undone.
    </p>
    {# hx-swap="none" is used here to prevent the modal content from being replaced. #}
    {# The server will return a 204 No Content with HX-Trigger for refreshing the list. #}
    <form hx-delete="{% url 'machinery_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`machinery/urls.py`)

This file defines the URL patterns for your Django application. It includes paths for the main list view, the HTMX endpoint for the table, and the HTMX endpoint for dynamic subcategory dropdowns. Placeholder URLs for full CRUD operations are also included.

```python
from django.urls import path
from .views import MachineryItemListView, MachineryItemTablePartialView, SubcategoryOptionsView
# from .views import MachineryItemCreateView, MachineryItemUpdateView, MachineryItemDeleteView # Uncomment for full CRUD

urlpatterns = [
    # Main list page for machinery items. This renders the outer shell and search form.
    path('machinery/', MachineryItemListView.as_view(), name='machinery_list'),
    
    # HTMX endpoint to load/refresh the table content (for initial load and search/filter updates).
    path('machinery/table/', MachineryItemTablePartialView.as_view(), name='machinery_table'),
    
    # HTMX endpoint to dynamically load subcategory options based on category selection.
    path('machinery/subcategories/', SubcategoryOptionsView.as_view(), name='subcategory_options'),

    # Placeholder URLs for full CRUD operations. These would typically load forms/confirmations
    # into an HTMX modal. Uncomment and implement views/forms as needed for full functionality.
    # path('machinery/add/', MachineryItemCreateView.as_view(), name='machinery_add'),
    # path('machinery/edit/<int:pk>/', MachineryItemUpdateView.as_view(), name='machinery_edit'),
    # path('machinery/delete/<int:pk>/', MachineryItemDeleteView.as_view(), name='machinery_delete'),
]
```

#### 4.6 Tests (`machinery/tests.py`)

Comprehensive unit tests for model methods and integration tests for all views are crucial. These tests ensure the correctness of data handling, filtering, and UI interactions. The tests will cover:
*   Model field types, verbose names, and custom methods (`truncated_manf_desc`, `get_display_location`).
*   View responses (HTTP status codes, correct templates used, context data).
*   Filtering logic (category, subcategory, search text, `CompId`, `Absolute` flag).
*   HTMX interactions (checking `HX-Request` headers and expected `HX-Trigger` responses).

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.http import HttpRequest
from .models import MachineryItem, Category, SubCategory, Unit, MsMaster
from .forms import MachinerySearchForm
import decimal

class ModelTestBase(TestCase):
    """Base class to set up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        # Create Category, SubCategory, Unit, and MsMaster instances
        cls.category_1 = Category.objects.create(cid=1, cname='Electronics')
        cls.category_2 = Category.objects.create(cid=2, cname='Mechanical')
        cls.subcategory_1 = SubCategory.objects.create(scid=101, cid=cls.category_1, scname='Industrial Robots', symbol='IR')
        cls.subcategory_2 = SubCategory.objects.create(scid=102, cid=cls.category_1, scname='CNC Machines', symbol='CNC')
        cls.subcategory_3 = SubCategory.objects.create(scid=201, cid=cls.category_2, scname='Lathes', symbol='LT')
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')
        
        # Create MachineryItem instances
        cls.item_1 = MachineryItem.objects.create(
            id=1,
            itemcode='MACH001',
            manfdesc='High-precision Industrial Robot Arm for assembly',
            stockqty=decimal.Decimal('10.00'),
            location='Warehouse A, Section 1',
            uombasic=cls.unit_pcs,
            cid=cls.category_1,
            scid=cls.subcategory_1,
            compid=1,
            absolute=0 # Not absolute
        )
        cls.item_2 = MachineryItem.objects.create(
            id=2,
            itemcode='MACH002',
            manfdesc='Compact CNC Milling Machine for small parts production',
            stockqty=decimal.Decimal('5.50'),
            location='Warehouse B, Section 2',
            uombasic=cls.unit_kg,
            cid=cls.category_1,
            scid=cls.subcategory_2,
            compid=1,
            absolute=0
        )
        cls.item_3 = MachineryItem.objects.create(
            id=3,
            itemcode='MACH003',
            manfdesc='Heavy Duty Lathe Machine for metalworking',
            stockqty=decimal.Decimal('2.00'),
            location='Factory Floor',
            uombasic=cls.unit_pcs,
            cid=cls.category_2,
            scid=cls.subcategory_3,
            compid=1,
            absolute=1 # Is absolute, should be filtered out by default queries
        )
        # MsMaster entry for item_1 to test location override
        MsMaster.objects.create(itemid=cls.item_1.id, location='Specific Location X')


class MachineryItemModelTest(ModelTestBase):
    def test_machinery_item_creation(self):
        """Test that a MachineryItem can be created and its fields are correct."""
        item = MachineryItem.objects.get(id=1)
        self.assertEqual(item.itemcode, 'MACH001')
        self.assertEqual(item.manfdesc, 'High-precision Industrial Robot Arm for assembly')
        self.assertEqual(item.stockqty, decimal.Decimal('10.00'))
        self.assertEqual(item.location, 'Warehouse A, Section 1')
        self.assertEqual(item.uombasic.symbol, 'PCS')
        self.assertEqual(item.cid.cname, 'Electronics')
        self.assertEqual(item.scid.scname, 'Industrial Robots')
        self.assertEqual(item.compid, 1)
        self.assertEqual(item.absolute, 0)

    def test_truncated_manf_desc_property(self):
        """Test the truncated_manf_desc property."""
        # Test case where description is not long enough to truncate
        item = MachineryItem.objects.get(id=1)
        self.assertEqual(item.truncated_manf_desc, 'High-precision Industrial Robot Arm for assembly') 
        
        # Test case where description is truncated
        long_desc_item = MachineryItem.objects.create(
            id=4, 
            itemcode='LONGDESC', 
            manfdesc='This is a very long description for a test item that should be truncated to show the ellipsis functionality. It needs to be more than 80 characters.',
            stockqty=decimal.Decimal('1.00'),
            location='Test Location',
            uombasic=self.unit_pcs,
            cid=self.category_1,
            scid=self.subcategory_1,
            compid=1,
            absolute=0
        )
        expected_truncated = 'This is a very long description for a test item that should be truncated to show the ellip...'
        self.assertEqual(long_desc_item.truncated_manf_desc, expected_truncated)

    def test_get_display_location_property(self):
        """Test the get_display_location property, including MsMaster override."""
        # Item with an MsMaster override
        item_1 = MachineryItem.objects.get(id=1)
        self.assertEqual(item_1.get_display_location(), 'Specific Location X')

        # Item without an MsMaster override
        item_2 = MachineryItem.objects.get(id=2)
        self.assertEqual(item_2.get_display_location(), 'Warehouse B, Section 2')

        # Item without MsMaster entry and null location in MachineryItem
        item_no_loc = MachineryItem.objects.create(
            id=5, itemcode='NOLOC', manfdesc='No location item', stockqty=1, location=None,
            uombasic=self.unit_pcs, cid=self.category_1, scid=self.subcategory_1, compid=1, absolute=0
        )
        self.assertIsNone(item_no_loc.get_display_location())

    def test_is_absolute_property(self):
        """Test the is_absolute property."""
        item_1 = MachineryItem.objects.get(id=1)
        self.assertFalse(item_1.is_absolute())
        
        item_3 = MachineryItem.objects.get(id=3)
        self.assertTrue(item_3.is_absolute())


class MachinerySearchFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        ModelTestBase.setUpTestData()

    def test_form_initialization(self):
        """Test that the form initializes correctly with default choices."""
        form = MachinerySearchForm()
        self.assertIn('Select Category', [x[0] for x in form.fields['category'].choices])
        self.assertIn('Select SubCategory', [x[0] for x in form.fields['subcategory'].choices])
        self.assertIn('Select', [x[0] for x in form.fields['search_by'].choices])

    def test_form_validation_valid_data(self):
        """Test form with valid data."""
        data = {
            'category': self.category_1.cid,
            'subcategory': self.subcategory_1.scid,
            'search_by': 'item_code',
            'search_text': 'MACH',
        }
        form = MachinerySearchForm(data=data)
        self.assertTrue(form.is_valid(), form.errors.as_json())
        self.assertEqual(form.cleaned_data['category'], self.category_1)
        self.assertEqual(form.cleaned_data['subcategory'], self.subcategory_1)

    def test_form_validation_missing_search_text(self):
        """Test form validation when search_by is selected but search_text is missing."""
        data = {
            'category': self.category_1.cid,
            'search_by': 'item_code',
            'search_text': '',
        }
        form = MachinerySearchForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('search_text', form.errors)
        self.assertIn('Search text is required when a specific search field is selected.', form.errors['search_text'])

    def test_subcategory_queryset_filtering(self):
        """Test that subcategory queryset is filtered based on selected category."""
        # No category selected initially, subcategory should be empty except for default option
        form = MachinerySearchForm()
        self.assertEqual(form.fields['subcategory'].queryset.count(), 0) # Only the empty_label exists

        # Category 1 selected, should filter subcategories for Category 1
        data = {'category': self.category_1.cid}
        form = MachinerySearchForm(data=data)
        # Note: self.setUpTestData creates 2 subcategories for category_1
        self.assertEqual(form.fields['subcategory'].queryset.count(), 2) 
        self.assertTrue(self.subcategory_1 in form.fields['subcategory'].queryset)
        self.assertFalse(self.subcategory_3 in form.fields['subcategory'].queryset)


class MachineryViewsTest(ModelTestBase):
    def setUp(self):
        """Set up a client and session for each test method."""
        self.client = Client()
        # Set up a session to mimic ASP.NET Session["compid"]
        session = self.client.session
        session['compid'] = 1
        session.save()
    
    def test_list_view_get(self):
        """Test that the main list view loads correctly."""
        response = self.client.get(reverse('machinery_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machineryitem/list.html')
        self.assertIsInstance(response.context['form'], MachinerySearchForm)

    def test_table_partial_view_get_no_filters(self):
        """Test the HTMX table partial view with no filters."""
        response = self.client.get(reverse('machinery_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machineryitem/_machineryitem_table.html')
        self.assertTrue('machinery_items' in response.context)
        # Item 3 has absolute=1, so it should be excluded
        self.assertEqual(len(response.context['machinery_items']), 2) 
        self.assertIn(self.item_1, response.context['machinery_items'])
        self.assertIn(self.item_2, response.context['machinery_items'])
        self.assertNotIn(self.item_3, response.context['machinery_items'])

    def test_table_partial_view_get_category_filter(self):
        """Test filtering by category."""
        response = self.client.get(reverse('machinery_table'), {'category': self.category_2.cid})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['machinery_items']), 0) # Item 3 is excluded by absolute=1

        # Create an item for category 2 that is not absolute
        MachineryItem.objects.create(
            id=6, itemcode='MACH004', manfdesc='Another Lathe', stockqty=1, location='Test',
            uombasic=self.unit_pcs, cid=self.category_2, scid=self.subcategory_3, compid=1, absolute=0
        )
        response = self.client.get(reverse('machinery_table'), {'category': self.category_2.cid})
        self.assertEqual(len(response.context['machinery_items']), 1)
        self.assertEqual(response.context['machinery_items'][0].itemcode, 'MACH004')


    def test_table_partial_view_get_subcategory_filter(self):
        """Test filtering by subcategory."""
        # Filtering by subcategory_1 (Industrial Robots)
        response = self.client.get(reverse('machinery_table'), {
            'category': self.category_1.cid, # Category must be set for subcategory to filter correctly
            'subcategory': self.subcategory_1.scid
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['machinery_items']), 1)
        self.assertEqual(response.context['machinery_items'][0], self.item_1)

    def test_table_partial_view_get_search_item_code(self):
        """Test filtering by item code (starts with)."""
        response = self.client.get(reverse('machinery_table'), {
            'search_by': 'item_code',
            'search_text': 'MACH001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['machinery_items']), 1)
        self.assertEqual(response.context['machinery_items'][0], self.item_1)

        response = self.client.get(reverse('machinery_table'), {
            'search_by': 'item_code',
            'search_text': 'MACH'
        })
        self.assertEqual(response.status_code, 200)
        # Should return item_1 and item_2 as both start with 'MACH' and are not absolute
        self.assertEqual(len(response.context['machinery_items']), 2) 

    def test_table_partial_view_get_search_manf_desc(self):
        """Test filtering by manufacturer description (contains)."""
        response = self.client.get(reverse('machinery_table'), {
            'search_by': 'manf_desc',
            'search_text': 'Robot'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['machinery_items']), 1)
        self.assertEqual(response.context['machinery_items'][0], self.item_1)

    def test_subcategory_options_view(self):
        """Test the HTMX endpoint for subcategory options."""
        response = self.client.get(reverse('subcategory_options'), {'category': self.category_1.cid})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machineryitem/_subcategory_options.html')
        self.assertIn('subcategories', response.context)
        self.assertEqual(len(response.context['subcategories']), 2) # subcategory_1 and subcategory_2
        self.assertContains(response, '<option value="{}">{} - {}</option>'.format(self.subcategory_1.scid, self.subcategory_1.symbol, self.subcategory_1.scname))

        response = self.client.get(reverse('subcategory_options'), {'category': self.category_2.cid})
        self.assertEqual(len(response.context['subcategories']), 1) # subcategory_3
        self.assertContains(response, '<option value="{}">{} - {}</option>'.format(self.subcategory_3.scid, self.subcategory_3.symbol, self.subcategory_3.scname))

        # Test with invalid category ID
        response = self.client.get(reverse('subcategory_options'), {'category': 'invalid'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['subcategories']), 0) # Should be empty
        self.assertContains(response, '<option value="Select SubCategory">Select SubCategory</option>')
```

### Step 5: HTMX and Alpine.js Integration

This step details how HTMX and Alpine.js are integrated into the templates and views to provide a dynamic and responsive user experience without heavy JavaScript frameworks.

*   **HTMX for Dynamic Content Loading:**
    *   The `machinery_list` view initially renders an empty `div` (`#machineryItemTable-container`).
    *   `hx-get="{% url 'machinery_table' %}" hx-trigger="load, refreshMachineryItemList from:body" hx-swap="innerHTML"` on this `div` tells HTMX to load the table content on page load and whenever a custom event `refreshMachineryItemList` is triggered (e.g., after a CRUD operation via modal).
    *   The search form uses `hx-get="{% url 'machinery_table' %}" hx-target="#machineryItemTable-container" hx-swap="innerHTML"` on its `<form>` tag. When the form is submitted (e.g., by clicking the "Search" button), HTMX makes a GET request to `machinery_table/` with the form data, and the response (the new table HTML) replaces the existing table.
    *   The category dropdown (`DrpCategory`) uses `hx-get="/machinery/subcategories/" hx-target="#id_subcategory-container" hx-swap="outerHTML" hx-trigger="change"` to dynamically update the subcategory dropdown. When the category changes, HTMX fetches the new subcategory options and replaces the entire subcategory `div`.
    *   Edit/Delete buttons within the table use `hx-get` to fetch the respective form/confirmation partials into the `#modalContent` div, opening the modal.
    *   When an HTMX POST/DELETE request (e.g., from a form submission in the modal) is successful, the Django view returns a `HttpResponse(status=204, headers={'HX-Trigger': 'refreshMachineryItemList'})`. The `204 No Content` status tells HTMX not to swap anything, and the `HX-Trigger` header instructs the client to dispatch the `refreshMachineryItemList` event, which in turn causes the main table to reload.
*   **Alpine.js for UI State Management:**
    *   Alpine.js (`x-data`, `x-show`, `x-init`, `x-transition`) is used for managing the modal's visibility (`#modal`). The `hidden` class is toggled.
    *   The `_` (hyperscript) attribute is used for concise inline JavaScript directly within the HTML, for example, `_="on click add .is-active to #modal"` to open the modal or `_="on click if event.target.id == 'modal' remove .is-active from me"` to close it when clicking outside.
*   **DataTables for List Views:**
    *   The `_machineryitem_table.html` partial contains a standard `<table>` with `id="machineryItemTable"`.
    *   In `list.html`, within the `extra_js` block, a JavaScript snippet listens for the `htmx:afterSwap` event on the `#machineryItemTable-container`. Once the table HTML is loaded, it initializes DataTables on `$('#machineryItemTable')`, ensuring that DataTables is applied only after the DOM element exists. This provides client-side searching, sorting, and pagination.
    *   The `columnDefs` in DataTables initialization are used to make the "SN" column non-orderable, as it's just a counter.

### Final Notes

*   **Database Connection:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database (e.g., using `django-mssql-backend` if needed) or that you have migrated the data to a PostgreSQL/MySQL database compatible with Django's default ORM.
*   **Session Management:** Django's built-in session framework will handle `Session["compid"]` and `Session["username"]`. Middleware configuration in `settings.py` is required.
*   **Static Files & Media:** Ensure Django's static files configuration (for CSS, JS, images) is correct, and Tailwind CSS is correctly set up for styling. DataTables JS/CSS and Alpine.js should be linked in `core/base.html` via CDNs or local static files.
*   **Security:** Django's ORM inherently prevents SQL injection (a significant issue in the original ASP.NET code's string-concatenated queries). Proper form validation and CSRF protection are also built-in.
*   **Error Handling:** The provided code includes basic form error display. Comprehensive error handling for various scenarios (e.g., database connection issues, unexpected data) should be implemented.
*   **Modularity:** The migration is organized into a dedicated `machinery` app, promoting modularity and reusability within a larger Django project.
*   **Placeholders:** Remember to replace placeholder URLs (e.g., `machinery_add`, `machinery_edit`, `machinery_delete`) with actual view implementations for full CRUD functionality, if desired.
*   **Refinement:** This plan provides a solid foundation. Further refinement may include:
    *   More sophisticated error/success message display using Django's messages framework combined with Alpine.js toasts.
    *   Client-side validation with Alpine.js where appropriate to provide immediate feedback.
    *   Accessibility considerations for all interactive elements.

This systematic, automation-driven approach facilitates a smooth transition from legacy ASP.NET to a modern, maintainable Django application, directly addressing business needs for improved performance, user experience, and long-term sustainability.
```