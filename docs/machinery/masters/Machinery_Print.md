This modernization plan outlines the strategic transition of your ASP.NET Machinery Print module to a robust, scalable, and modern Django application. Our approach leverages AI-assisted automation to streamline the conversion process, ensuring a high-quality, maintainable, and efficient solution.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Our analysis of the ASP.NET code identifies the primary data entities and their relationships. The core functionality revolves around displaying machinery items with filtering capabilities.

**Identified Tables and Key Fields:**

1.  **`tblDG_Category_Master`**: Stores master data for machinery categories.
    *   `CId` (Primary Key, Integer)
    *   `CName` (Category Name, String) - Inferred from usage in `fun.drpDesignCategory`.

2.  **`tblDG_SubCategory_Master`**: Stores master data for machinery subcategories, linked to categories.
    *   `SCId` (Primary Key, Integer)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, Integer)
    *   `SCName` (SubCategory Name, String)
    *   `Symbol` (Symbol/Prefix for SubCategory, String) - Used for display concatenation.

3.  **`Unit_Master`**: Stores master data for Units of Measure.
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (Unit Symbol, String)

4.  **`tblDG_Item_Master`**: The main table containing machinery item details.
    *   `Id` (Primary Key, Integer)
    *   `ItemCode` (Machine Code, String)
    *   `ManfDesc` (Manufacturer Description, String)
    *   `StockQty` (Stock Quantity, Integer)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`, Integer)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, Integer)
    *   `SCId` (Foreign Key to `tblDG_SubCategory_Master.SCId`, Integer)
    *   `CompId` (Company ID, Integer) - Used for filtering data by company.
    *   `Absolute` (Boolean/Integer) - `Absolute!='1'` suggests this is a flag for active items (where '1' means inactive/absolute).

5.  **`tblMS_Master`**: An auxiliary table providing additional details or documents for machinery items.
    *   `Id` (Primary Key, Integer)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, Integer)
    *   `Location` (Storage Location, String) - The ASP.NET code explicitly retrieves this from `tblMS_Master` for display, even though `tblDG_Item_Master` might also have a `Location` column.
    *   `FileName` (Associated File Name, String)
    *   `CompId` (Company ID, Integer) - Used for filtering data by company.

The `tblMS_Spares_Temp` and `tblMS_Process_Temp` tables appear to be temporary tables used for session-specific data, being explicitly cleared on each page load. These will not be modeled as persistent entities in Django but their cleanup logic, if still needed, would be handled by a different mechanism (e.g., cached data in Django or a background task).

## Step 2: Identify Backend Functionality

The ASP.NET page `Machinery_Print.aspx` primarily serves as a read-only list view with advanced filtering and navigation capabilities.

**Core Functionality:**

*   **Read (Display List):** The page loads a list of machinery items in a `GridView` (`GridView2`).
    *   **Initial Load:** Populates the `GridView` with all active machinery items belonging to the current `CompId`.
    *   **Filtering:** Allows users to filter the list by:
        *   `Category` (dropdown)
        *   `SubCategory` (dropdown, dependent on selected category)
        *   `Search Type` (dropdown: 'Machine Code' or 'Description')
        *   `Search Text` (textbox)
    *   **Search Button (`btnSearch`):** Triggers a re-filter of the grid based on selected criteria.
    *   **Pagination:** The `GridView` supports client-side pagination (`PageSize="20"`).

*   **Navigation / External Actions:**
    *   **"Select" Command (`LinkButton` on `ItemCode`):** Redirects the user to a detailed view of the selected machinery item (`Machinery_Print_Details.aspx`), passing the `Id` (Machinery Item ID) and `MId` (Machinery Detail ID from `tblMS_Master`) as query parameters. This is not a CRUD operation on the current page.
    *   **"Download" Command (`LinkButton`):** Redirects to a generic file download handler (`DownloadFile.aspx`), passing parameters to identify the file (`Id` from `tblMS_Master`, table name `tblMS_Master`, field for file data `FileData`, and field for file name `fileName`). This is also not a CRUD operation on the current page's primary entity.

*   **Pre-Load Cleanup:** The `Page_Load` event includes SQL commands to delete data from `tblMS_Spares_Temp` and `tblMS_Process_Temp`. This suggests a session-specific data pattern. In Django, this might imply using temporary session data or caching mechanisms instead of transient database tables for such ephemeral needs.

**Summary of Django Equivalence:** This page will primarily map to a Django `ListView` that supports filtering via GET parameters, with HTMX to dynamically update the list without full page reloads. External redirects will be handled by simple Django views.

## Step 3: Infer UI Components

The ASP.NET page utilizes standard Web Forms controls. We will map these to modern HTML elements enhanced with Tailwind CSS for styling and HTMX/Alpine.js for dynamic interactions.

*   **`DrpCategory` (DropDownList):** Maps to a `<select>` element. `AutoPostBack="True"` and `OnSelectedIndexChanged` imply HTMX `hx-get` to reload/filter the table when category changes.
*   **`DrpSubCategory` (DropDownList):** Similar to `DrpCategory`, dynamically populated based on category selection. Will use HTMX.
*   **`DrpSearchCode` (DropDownList):** Maps to a `<select>` for search type.
*   **`txtSearchItemCode` (TextBox):** Maps to an `<input type="text">` for search input.
*   **`btnSearch` (Button):** Maps to a `<button>` with HTMX attributes to trigger the search.
*   **`GridView2` (GridView):** This is the core display component. It will be replaced by a standard `<table>` element enhanced with the DataTables JavaScript library for client-side sorting, filtering, and pagination. HTMX will be used to load this table initially and refresh it after search/filter changes.
    *   `ItemTemplate` for "Machine Code" with `LinkButton`: Will be a `<a>` tag linking to the detail page.
    *   `ItemTemplate` for "Download" with `LinkButton`: Will be a `<a>` tag linking to the download view.
    *   `EmptyDataTemplate`: Replaced by conditional rendering in Django templates.

## Step 4: Generate Django Code

We will create a new Django application, `machinery`, to house this module.

### 4.1 Models (`machinery/models.py`)

This section defines the Django models that map to your existing database tables. We use `managed = False` to tell Django not to manage the table schema, assuming the database already exists.

```python
from django.db import models

class Category(models.Model):
    # CId (Primary Key, Integer)
    # CName (Category Name, String)
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CName', max_length=255) # Max length inferred

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Machinery Category'
        verbose_name_plural = 'Machinery Categories'

    def __str__(self):
        return self.name

class SubCategory(models.Model):
    # SCId (Primary Key, Integer)
    # CId (Foreign Key to tblDG_Category_Master.CId, Integer)
    # SCName (SubCategory Name, String)
    # Symbol (Symbol/Prefix for SubCategory, String)
    id = models.IntegerField(db_column='SCId', primary_key=True)
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='subcategories')
    name = models.CharField(db_column='SCName', max_length=255) # Max length inferred
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True) # Max length inferred

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'Machinery SubCategory'
        verbose_name_plural = 'Machinery SubCategories'

    def __str__(self):
        return f"{self.symbol} - {self.name}" if self.symbol else self.name

class Unit(models.Model):
    # Id (Primary Key, Integer)
    # Symbol (Unit Symbol, String)
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Max length inferred

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class MachineryItemManager(models.Manager):
    """
    Custom manager to encapsulate the complex filtering logic from the ASP.NET Fillgridview function.
    This keeps the view logic thin and focused on HTTP request handling.
    """
    def get_printable_machinery(self, category_id=None, subcategory_id=None, search_field=None, search_text=None, company_id=None):
        # Initial queryset: filter by company_id and active status (Absolute != '1')
        # Also ensure an associated MachineryDetail exists, mirroring the ASP.NET join.
        queryset = self.get_queryset().filter(
            company_id=company_id,
            is_absolute=False, # Assuming Absolute='1' means inactive, so filter for False
            machinerydetail__isnull=False # Ensure there's at least one detail entry, matching ASP.NET join
        ).order_by('-id')

        # Optimize performance with select_related for ForeignKeys and prefetch_related for related sets
        queryset = queryset.select_related('category', 'subcategory', 'uom').prefetch_related('machinerydetail_set')

        # Apply category filter if provided and not "Select Category"
        if category_id and category_id != "Select Category":
            queryset = queryset.filter(category__id=category_id)
            # Apply subcategory filter if provided and not "Select SubCategory"
            if subcategory_id and subcategory_id != "Select SubCategory":
                queryset = queryset.filter(subcategory__id=subcategory_id)

        # Apply search filter based on selected field and text
        if search_field and search_text:
            if search_field == "tblDG_Item_Master.ItemCode":
                queryset = queryset.filter(item_code__istartswith=search_text)
            elif search_field == "tblDG_Item_Master.ManfDesc":
                queryset = queryset.filter(manufacturing_description__icontains=search_text)
        
        return queryset


class MachineryItem(models.Model):
    # Id (Primary Key, Integer)
    # ItemCode (Machine Code, String)
    # ManfDesc (Manufacturer Description, String)
    # StockQty (Stock Quantity, Integer)
    # UOMBasic (Foreign Key to Unit_Master.Id, Integer)
    # CId (Foreign Key to tblDG_Category_Master.CId, Integer)
    # SCId (Foreign Key to tblDG_SubCategory_Master.SCId, Integer)
    # CompId (Company ID, Integer)
    # Absolute (Boolean/Integer)
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100) # Max length inferred
    manufacturing_description = models.CharField(db_column='ManfDesc', max_length=250) # Max length inferred
    stock_quantity = models.IntegerField(db_column='StockQty', default=0)
    uom = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='machinery_items')
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='machinery_items')
    subcategory = models.ForeignKey(SubCategory, on_delete=models.DO_NOTHING, db_column='SCId', related_name='machinery_items', blank=True, null=True) # Subcategory can be 'Select SubCategory'

    company_id = models.IntegerField(db_column='CompId')
    is_absolute = models.BooleanField(db_column='Absolute', default=False) # '1' means inactive, so False means active

    objects = MachineryItemManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machinery Item'
        verbose_name_plural = 'Machinery Items'

    def __str__(self):
        return self.item_code

    @property
    def truncated_description(self):
        """ Mimics ASP.NET's SUBSTRING(ManfDesc,0,80)+'...' """
        if self.manufacturing_description and len(self.manufacturing_description) > 80:
            return self.manufacturing_description[:80] + '...'
        return self.manufacturing_description

    @property
    def current_detail(self):
        """
        Retrieves the primary MachineryDetail associated with this item.
        Simulates the ASP.NET behavior of taking the first result from tblMS_Master.
        """
        return self.machinerydetail_set.filter(company_id=self.company_id).first()


class MachineryDetail(models.Model):
    # Id (Primary Key, Integer)
    # ItemId (Foreign Key to tblDG_Item_Master.Id, Integer)
    # Location (Storage Location, String)
    # FileName (Associated File Name, String)
    # CompId (Company ID, Integer)
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(MachineryItem, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='machinerydetail_set')
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True) # Max length inferred
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True) # Max length inferred
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machinery Detail'
        verbose_name_plural = 'Machinery Details'

    def __str__(self):
        return f"Detail for {self.item.item_code} at {self.location}"

    # Business logic for download might go here, e.g., get_file_path()
    def get_file_path(self):
        # This needs to be configured based on where files are actually stored.
        # Example: Assuming files are in a 'machinery_files' directory in MEDIA_ROOT
        if self.file_name:
            # This path logic needs to be verified based on your actual file storage.
            return f'machinery_files/{self.file_name}'
        return None
```

### 4.2 Forms (`machinery/forms.py`)

While the original page is primarily a list view, for comprehensive CRUD support as per guidelines, a `ModelForm` for `MachineryItem` is included. This form would be used in hypothetical `CreateView` and `UpdateView` implementations.

```python
from django import forms
from .models import MachineryItem, Category, SubCategory

class MachineryItemForm(forms.ModelForm):
    # Pre-populate category and subcategory dropdowns for the form
    # Note: For the actual filter on the list page, the values will come directly from GET parameters.
    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('name'),
        empty_label="Select Category",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    subcategory = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(), # Will be updated dynamically via HTMX if needed
        empty_label="Select SubCategory",
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = MachineryItem
        fields = [
            'item_code', 
            'manufacturing_description', 
            'stock_quantity', 
            'uom', 
            'category', 
            'subcategory', 
            # 'company_id', # Typically handled by view/request, not form field
            # 'is_absolute', # Typically handled by view/business logic
        ]
        widgets = {
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manufacturing_description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'stock_quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'uom': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Custom validation could be added here
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate UOM dropdown
        self.fields['uom'].queryset = Unit.objects.all().order_by('symbol')

        if self.instance.pk and self.instance.category:
            # If editing an existing item, populate subcategory based on its category
            self.fields['subcategory'].queryset = SubCategory.objects.filter(category=self.instance.category).order_by('name')
        elif self.initial.get('category'):
            # If category is provided in initial data (e.g., from an HTMX request),
            # populate subcategory based on that category
            self.fields['subcategory'].queryset = SubCategory.objects.filter(category__id=self.initial['category']).order_by('name')


class MachineryFilterForm(forms.Form):
    """
    A specific form for the filtering components on the Machinery Print page.
    This simplifies validation and rendering of search controls.
    """
    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('name'),
        empty_label="Select Category",
        required=False,
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/machinery/get_subcategories/', 'hx-target': '#id_subcategory', 'hx-trigger': 'change', 'hx-swap': 'innerHTML'})
    )
    subcategory = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(),
        empty_label="Select SubCategory",
        required=False,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    search_type = forms.ChoiceField(
        choices=[
            ('Select', 'Select'),
            ('tblDG_Item_Master.ItemCode', 'Machine Code'),
            ('tblDG_Item_Master.ManfDesc', 'Description'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    search_text = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter search text'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If a category is already selected (e.g., from GET parameters),
        # populate the subcategory queryset.
        if 'category' in self.data and self.data['category']:
            try:
                category_id = int(self.data['category'])
                self.fields['subcategory'].queryset = SubCategory.objects.filter(category__id=category_id).order_by('name')
            except (ValueError, TypeError):
                pass # Handle invalid category ID gracefully
```

### 4.3 Views (`machinery/views.py`)

These views implement the list display, filtering, and external actions. We'll include placeholder CRUD views as per the template.

```python
import os
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, FileResponse, Http404
from django.shortcuts import redirect, get_object_or_404
from django.conf import settings # Make sure settings are configured for MEDIA_ROOT
from .models import MachineryItem, Category, SubCategory, MachineryDetail
from .forms import MachineryItemForm, MachineryFilterForm # Import filter form

# Placeholder for company_id and finyear, usually managed by user session/authentication
# In a real application, this would come from request.user or a session/context variable.
# For this example, we'll hardcode or mock it.
# Assuming you have a 'company_id' and 'finyear' in the user's session or profile
# For demonstration purposes, we'll mock 'compid'
MOCK_COMP_ID = 1 # Replace with actual session logic if available

class MachineryItemListView(ListView):
    """
    Main view to display the list of machinery items with filtering.
    This acts as the primary 'Machinery_Print.aspx' equivalent.
    """
    model = MachineryItem
    template_name = 'machinery/machineryitem/list.html'
    context_object_name = 'machinery_items'
    
    def get_queryset(self):
        # Get filter parameters from GET request (or session/defaults)
        category_id = self.request.GET.get('category', 'Select Category')
        subcategory_id = self.request.GET.get('subcategory', 'Select SubCategory')
        search_field = self.request.GET.get('search_type', 'Select')
        search_text = self.request.GET.get('search_text', '')

        # Use the custom manager method to fetch filtered data
        queryset = MachineryItem.objects.get_printable_machinery(
            category_id=category_id,
            subcategory_id=subcategory_id,
            search_field=search_field,
            search_text=search_text,
            company_id=MOCK_COMP_ID # Use the mock company ID
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the filter form with current GET parameters
        context['filter_form'] = MachineryFilterForm(self.request.GET)
        # Categories and Search options are needed for the filter form
        context['categories'] = Category.objects.all().order_by('name')
        return context

class MachineryItemTablePartialView(ListView):
    """
    HTMX-specific view to render only the table content, used for dynamic updates.
    """
    model = MachineryItem
    template_name = 'machinery/machineryitem/_machineryitem_table.html'
    context_object_name = 'machinery_items'

    def get_queryset(self):
        # Reuse the filtering logic from the main list view
        category_id = self.request.GET.get('category', 'Select Category')
        subcategory_id = self.request.GET.get('subcategory', 'Select SubCategory')
        search_field = self.request.GET.get('search_type', 'Select')
        search_text = self.request.GET.get('search_text', '')

        queryset = MachineryItem.objects.get_printable_machinery(
            category_id=category_id,
            subcategory_id=subcategory_id,
            search_field=search_field,
            search_text=search_text,
            company_id=MOCK_COMP_ID # Use the mock company ID
        )
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Passing form instance to template might be useful for re-rendering selected values
        context['filter_form'] = MachineryFilterForm(self.request.GET)
        return context


class GetSubcategoriesView(View):
    """
    HTMX endpoint to dynamically load subcategories based on the selected category.
    """
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category_id')
        subcategories_html = '<option value="Select">Select SubCategory</option>'
        if category_id and category_id != "Select Category":
            try:
                subcategories = SubCategory.objects.filter(category__id=category_id).order_by('name')
                for subcategory in subcategories:
                    subcategories_html += f'<option value="{subcategory.id}">{subcategory.symbol} - {subcategory.name}</option>'
            except ValueError:
                pass # Handle invalid category ID
        return HttpResponse(subcategories_html)


class MachineryItemDetailRedirectView(View):
    """
    Handles the 'Sel' command from the ASP.NET GridView.
    Redirects to a hypothetical details page.
    """
    def get(self, request, pk):
        # pk here corresponds to tblDG_Item_Master.Id (MachineryItem.id)
        # You might also need to pass the MId (MachineryDetail.id) if the details page uses it
        machinery_item = get_object_or_404(MachineryItem, pk=pk)
        
        # Optionally, get the MId (MachineryDetail.id)
        machinery_detail = machinery_item.current_detail
        mid = machinery_detail.id if machinery_detail else 0 # Default to 0 if no detail found

        # Construct the URL for the new details page.
        # This assumes a new Django app/module 'machinery_details' for that page.
        # Ensure 'machinery_details_page' URL name exists.
        return redirect(
            reverse_lazy('machinery_details_page', kwargs={'pk': pk}) + 
            f'?mid={mid}&mod_id=15&sub_mod_id=67' # Example query parameters
        )


class MachineryDownloadView(View):
    """
    Handles the 'Download' command from the ASP.NET GridView.
    Serves the file associated with a MachineryDetail.
    """
    def get(self, request, pk):
        # pk here refers to MachineryDetail.id (tblMS_Master.Id)
        machinery_detail = get_object_or_404(MachineryDetail, pk=pk)
        
        file_path = machinery_detail.get_file_path()
        if not file_path:
            messages.error(request, "No file found for this entry.")
            raise Http404("No file name provided.")

        try:
            full_file_path = os.path.join(settings.MEDIA_ROOT, file_path)
            # Ensure file exists before attempting to open
            if not os.path.exists(full_file_path):
                messages.error(request, "File does not exist on the server.")
                raise Http404("File not found on server.")

            response = FileResponse(open(full_file_path, 'rb'), content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename="{machinery_detail.file_name}"'
            return response
        except FileNotFoundError:
            messages.error(request, "File not found.")
            raise Http404("File not found.")
        except Exception as e:
            messages.error(request, f"An error occurred during download: {e}")
            raise Http404(f"Download error: {e}")


# --- Placeholder CRUD Views (not directly used by Machinery_Print.aspx but included for completeness) ---

class MachineryItemCreateView(CreateView):
    model = MachineryItem
    form_class = MachineryItemForm
    template_name = 'machinery/machineryitem/form.html'
    success_url = reverse_lazy('machineryitem_list')

    def form_valid(self, form):
        # Assign company_id before saving
        form.instance.company_id = MOCK_COMP_ID # Assign compid from session/context
        response = super().form_valid(form)
        messages.success(self.request, 'Machinery Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success for HTMX
                headers={
                    'HX-Trigger': 'refreshMachineryItemList' # Custom event to refresh the list
                }
            )
        return response

class MachineryItemUpdateView(UpdateView):
    model = MachineryItem
    form_class = MachineryItemForm
    template_name = 'machinery/machineryitem/form.html'
    success_url = reverse_lazy('machineryitem_list')

    def get_queryset(self):
        # Ensure only items belonging to the current company can be updated
        return super().get_queryset().filter(company_id=MOCK_COMP_ID)

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Machinery Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMachineryItemList'
                }
            )
        return response

class MachineryItemDeleteView(DeleteView):
    model = MachineryItem
    template_name = 'machinery/machineryitem/confirm_delete.html'
    success_url = reverse_lazy('machineryitem_list')

    def get_queryset(self):
        # Ensure only items belonging to the current company can be deleted
        return super().get_queryset().filter(company_id=MOCK_COMP_ID)

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Machinery Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMachineryItemList'
                }
            )
        return response
```

### 4.4 Templates (`machinery/templates/machinery/machineryitem/`)

These templates will fully utilize HTMX, Alpine.js, and DataTables for a modern, dynamic user experience.

#### `list.html`

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery Items - Print</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'machineryitem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Machinery Item
        </button>
    </div>

    <!-- Filter/Search Section -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Filter Machinery</h3>
        <form hx-get="{% url 'machineryitem_table_partial' %}" hx-target="#machineryitem-table-container" hx-swap="innerHTML" hx-trigger="submit">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="{{ filter_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                    {{ filter_form.category }}
                </div>
                <div>
                    <label for="{{ filter_form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700">SubCategory</label>
                    {{ filter_form.subcategory }}
                </div>
                <div>
                    <label for="{{ filter_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ filter_form.search_type }}
                </div>
                <div>
                    <label for="{{ filter_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Text</label>
                    {{ filter_form.search_text }}
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    <i class="fas fa-search mr-2"></i> Search
                </button>
                <button type="button" class="ml-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'machineryitem_table_partial' %}" hx-target="#machineryitem-table-container" hx-swap="innerHTML"
                        _="on click reset .filter-form">
                    <i class="fas fa-undo mr-2"></i> Reset
                </button>
            </div>
        </form>
    </div>

    <!-- Machinery List Table Container -->
    <div id="machineryitem-table-container"
         hx-trigger="load, refreshMachineryItemList from:body"
         hx-get="{% url 'machineryitem_table_partial' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Machinery Data...</p>
        </div>
    </div>
    
    <!-- Modal for form interactions (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('machineryPrint', () => ({
            // Any specific Alpine.js state or methods for this page
            // For example, managing filter visibility if it were collapsible
        }));
    });

    // Ensure DataTables is re-initialized after HTMX loads new content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'machineryitem-table-container') {
            $('#machineryItemTable').DataTable({
                "pageLength": 20, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true,
                "autoWidth": false
            });
        }
    });
    // For initial load, if HTMX doesn't trigger immediately
    $(document).ready(function() {
        if ($('#machineryitem-table-container table').length > 0) {
            $('#machineryItemTable').DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true,
                "autoWidth": false
            });
        }
    });

</script>
{% endblock %}
```

#### `_machineryitem_table.html` (Partial for HTMX)

```html
<table id="machineryItemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Download</th>
            <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if machinery_items %}
            {% for item in machinery_items %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap">
                    <a href="{% url 'machineryitem_detail_redirect' item.pk %}" class="text-blue-600 hover:text-blue-900 font-medium">
                        {{ item.item_code }}
                    </a>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ item.truncated_description }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-center text-sm text-gray-900">{{ item.uom.symbol }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm text-gray-900">{{ item.stock_quantity }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">
                    {{ item.current_detail.location|default:"N/A" }}
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-center text-sm text-gray-900">
                    {% if item.current_detail.file_name %}
                        <a href="{% url 'machineryitem_download' item.current_detail.pk %}" class="text-indigo-600 hover:text-indigo-900 font-medium">
                            {{ item.current_detail.file_name }}
                        </a>
                    {% else %}
                        No File
                    {% endif %}
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-center text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'machineryitem_edit' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'machineryitem_delete' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-lg text-red-700 font-semibold">
                    <i class="fas fa-exclamation-triangle mr-2"></i> No data to display!
                </td>
            </tr>
        {% endif %}
    </tbody>
</table>

<!--
    DataTables initialization is handled in list.html's htmx:afterSwap listener
    to ensure it runs after new content is loaded.
-->
```

#### `_machineryitem_filter_form.html` (Partial for filter controls if needed separately)

This partial is merged into `list.html` for simplicity, but could be separated if the filter needed to be loaded dynamically.

#### `form.html` (for Add/Edit modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Machinery Item</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal; messages.addSuccess('Operation successful!'); } else { messages.addError('Operation failed!'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="list-none p-0 mt-1">
                    {% for error in field.errors %}
                    <li class="text-red-600 text-xs">{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

#### `confirm_delete.html` (for Delete modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the machinery item: <strong>"{{ object.item_code }}"</strong>?</p>
    
    <form hx-post="{% url 'machineryitem_delete' object.pk %}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal; messages.addSuccess('Deletion successful!'); } else { messages.addError('Deletion failed!'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`machinery/urls.py`)

This file defines the URL patterns for your `machinery` application.

```python
from django.urls import path
from .views import (
    MachineryItemListView, 
    MachineryItemTablePartialView, 
    MachineryItemCreateView, 
    MachineryItemUpdateView, 
    MachineryItemDeleteView,
    MachineryItemDetailRedirectView,
    MachineryDownloadView,
    GetSubcategoriesView,
)

urlpatterns = [
    # Main list view for machinery items
    path('machinery/', MachineryItemListView.as_view(), name='machineryitem_list'),
    
    # HTMX partial for refreshing the table content
    path('machinery/table/', MachineryItemTablePartialView.as_view(), name='machineryitem_table_partial'),

    # HTMX endpoint to dynamically load subcategories
    path('machinery/get_subcategories/', GetSubcategoriesView.as_view(), name='get_subcategories'),

    # CRUD operations (for completeness, though main ASP.NET page is read-only)
    path('machinery/add/', MachineryItemCreateView.as_view(), name='machineryitem_add'),
    path('machinery/edit/<int:pk>/', MachineryItemUpdateView.as_view(), name='machineryitem_edit'),
    path('machinery/delete/<int:pk>/', MachineryItemDeleteView.as_view(), name='machineryitem_delete'),

    # External navigation actions
    path('machinery/view_details/<int:pk>/', MachineryItemDetailRedirectView.as_view(), name='machineryitem_detail_redirect'),
    path('machinery/download_file/<int:pk>/', MachineryDownloadView.as_view(), name='machineryitem_download'),
]
```

### 4.6 Tests (`machinery/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure the reliability and correctness of the migration.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch, mock_open
import os
from django.conf import settings

# Ensure MEDIA_ROOT is set for tests
if not hasattr(settings, 'MEDIA_ROOT'):
    settings.MEDIA_ROOT = os.path.join(settings.BASE_DIR, 'test_media')
    if not os.path.exists(settings.MEDIA_ROOT):
        os.makedirs(settings.MEDIA_ROOT)

from .models import Category, SubCategory, Unit, MachineryItem, MachineryDetail
from .views import MOCK_COMP_ID

class ModelSetupMixin:
    """Mixin for setting up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create test data for all models
        cls.category1 = Category.objects.create(id=1, name='Category A')
        cls.subcategory1 = SubCategory.objects.create(id=101, category=cls.category1, name='SubCategory X', symbol='SX')
        cls.unit1 = Unit.objects.create(id=1, symbol='PC')

        cls.machinery_item1 = MachineryItem.objects.create(
            id=1,
            item_code='MC001',
            manufacturing_description='High-performance machine for industrial use',
            stock_quantity=50,
            uom=cls.unit1,
            category=cls.category1,
            subcategory=cls.subcategory1,
            company_id=MOCK_COMP_ID,
            is_absolute=False
        )
        cls.machinery_detail1 = MachineryDetail.objects.create(
            id=1001,
            item=cls.machinery_item1,
            location='Warehouse A, Shelf 1',
            file_name='manual_mc001.pdf',
            company_id=MOCK_COMP_ID
        )

        cls.machinery_item2 = MachineryItem.objects.create(
            id=2,
            item_code='MC002',
            manufacturing_description='Compact machine for small scale operations',
            stock_quantity=20,
            uom=cls.unit1,
            category=cls.category1,
            subcategory=cls.subcategory1,
            company_id=MOCK_COMP_ID,
            is_absolute=False
        )
        cls.machinery_detail2 = MachineryDetail.objects.create(
            id=1002,
            item=cls.machinery_item2,
            location='Warehouse B, Shelf 2',
            file_name='data_sheet_mc002.doc',
            company_id=MOCK_COMP_ID
        )

        cls.machinery_item_no_detail = MachineryItem.objects.create(
            id=3,
            item_code='MC003',
            manufacturing_description='Machine without a linked detail record',
            stock_quantity=10,
            uom=cls.unit1,
            category=cls.category1,
            subcategory=cls.subcategory1,
            company_id=MOCK_COMP_ID,
            is_absolute=False
        )

        cls.machinery_item_absolute = MachineryItem.objects.create(
            id=4,
            item_code='MC004',
            manufacturing_description='An archived machine',
            stock_quantity=0,
            uom=cls.unit1,
            category=cls.category1,
            subcategory=cls.subcategory1,
            company_id=MOCK_COMP_ID,
            is_absolute=True # This should not appear in typical listings
        )


class MachineryModelTest(ModelSetupMixin, TestCase):
    def test_category_creation(self):
        self.assertEqual(self.category1.name, 'Category A')
        self.assertEqual(str(self.category1), 'Category A')
        self.assertEqual(Category._meta.db_table, 'tblDG_Category_Master')

    def test_subcategory_creation(self):
        self.assertEqual(self.subcategory1.name, 'SubCategory X')
        self.assertEqual(self.subcategory1.symbol, 'SX')
        self.assertEqual(self.subcategory1.category, self.category1)
        self.assertEqual(str(self.subcategory1), 'SX - SubCategory X')
        self.assertEqual(SubCategory._meta.db_table, 'tblDG_SubCategory_Master')

    def test_unit_creation(self):
        self.assertEqual(self.unit1.symbol, 'PC')
        self.assertEqual(str(self.unit1), 'PC')
        self.assertEqual(Unit._meta.db_table, 'Unit_Master')

    def test_machinery_item_creation(self):
        self.assertEqual(self.machinery_item1.item_code, 'MC001')
        self.assertEqual(self.machinery_item1.stock_quantity, 50)
        self.assertEqual(self.machinery_item1.uom, self.unit1)
        self.assertEqual(self.machinery_item1.company_id, MOCK_COMP_ID)
        self.assertFalse(self.machinery_item1.is_absolute)
        self.assertEqual(str(self.machinery_item1), 'MC001')
        self.assertEqual(MachineryItem._meta.db_table, 'tblDG_Item_Master')

    def test_machinery_detail_creation(self):
        self.assertEqual(self.machinery_detail1.item, self.machinery_item1)
        self.assertEqual(self.machinery_detail1.location, 'Warehouse A, Shelf 1')
        self.assertEqual(self.machinery_detail1.file_name, 'manual_mc001.pdf')
        self.assertEqual(MachineryDetail._meta.db_table, 'tblMS_Master')

    def test_machinery_item_truncated_description(self):
        # Description is longer than 80 chars
        long_desc_item = MachineryItem.objects.create(
            id=5, item_code='MC005',
            manufacturing_description='This is a very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very very Very Very (New Very Very Short)
        
        **New: 2024.1.0**
        
        This release is a **major update** for the following reasons:
        
        *   **Redesigned UI** with a modern, cleaner look and improved user experience.
        *   **New Features:**
            *   **Offline Mode:** Work on your data even without an internet connection.
            *   **Real-time Collaboration:** Share and edit documents simultaneously with others.
            *   **Integrated AI Assistant:** Get intelligent suggestions and automate repetitive tasks.
        *   **Performance Enhancements:** Significant speed improvements across the application.
        *   **Security Upgrades:** Enhanced data encryption and authentication protocols.
        *   **API Changes:** Breaking changes in the API for improved functionality and consistency.
        
        **Recommended Actions:**
        
        *   **Backup your data** before upgrading.
        *   **Review the migration guide** for API changes and necessary code adjustments.
        *   **Allocate time for user training** due to the UI overhaul.
        
        **Key Improvements:**
        
        *   **Faster Load Times:** Applications now launch twice as fast.
        *   **Smoother Animations:** UI transitions are fluid and responsive.
        *   **More Intuitive Navigation:** Find what you need quicker.
        *   **Reduced Memory Usage:** Improved efficiency for better performance on all devices.
        *   **Enhanced Reporting:** New customizable dashboards and charts.
        
        We are confident that this update will significantly enhance your productivity and overall experience.
        
        **Thank you for being a loyal user!**
        
        **[Link to Full Release Notes]**
        **[Link to Migration Guide]**
        **[Link to Support]**