## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

Given the minimal ASP.NET code provided (an empty dashboard page with an empty `Page_Load` event), there are no explicit database interactions, UI components, or backend logic to directly convert. This scenario suggests the ASP.NET page is a placeholder or a container for other modules/reports.

For a comprehensive modernization plan, we will assume this dashboard page would typically display information about a core entity related to "Machinery" (inferred from the `Module_Machinery_Reports_Dashboard` namespace). We will proceed by creating a standard Django CRUD (Create, Read, Update, Delete) module for a hypothetical `Machine` entity, demonstrating the complete migration pattern. This provides a robust, reusable example that can be applied to other modules.

Let's assume we are building a `machinery` Django application within your project.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not explicitly define a database table or its columns. In a typical ASP.NET application, this information would be found in `SqlDataSource` controls, direct ADO.NET code, or embedded SQL within the `.aspx.cs` file.

**Inference:**
Since the module is `Machinery_Reports_Dashboard`, we will infer a primary entity for "Machinery".

*   **Inferred Table Name:** `tblMachines` (common ASP.NET naming convention)
*   **Inferred Columns:**
    *   `MachineId` (Primary Key, integer)
    *   `Name` (Varchar, e.g., 'Lathe Machine', 'CNC Mill')
    *   `SerialNumber` (Varchar, unique identifier)
    *   `PurchaseDate` (Date)
    *   `Status` (Varchar, e.g., 'Operational', 'Under Maintenance', 'Retired')
    *   `Location` (Varchar, where the machine is located)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code (`Dashboard.aspx` and `Dashboard.aspx.cs`) contains no active backend functionality or business logic (e.g., `GridView` bindings, `Button` click events, or data manipulation). The `Page_Load` method is empty.

**Inference:**
While this specific page is blank, a "Dashboard" for `Machinery` would typically involve at least "Read" operations to display a list of machines. Furthermore, to manage machinery, "Create", "Update", and "Delete" operations are essential. We will implement all CRUD operations to provide a complete, modern Django solution that can be extended.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `Dashboard.aspx` file is empty within its content placeholders, meaning no UI controls (like `GridView`, `TextBox`, `Button`) are explicitly defined on this page. The presence of `MasterPage.master` and multiple content placeholders implies a structured layout.

**Inference:**
Given the lack of specific UI, we will design the Django application to include:
*   A list view (analogous to a `GridView`) for displaying all machines, powered by DataTables for advanced features.
*   Modal forms for adding, editing, and confirming deletion of machines, driven by HTMX for a seamless user experience.
*   A button to trigger the "Add New Machine" modal.

---

## Step 4: Generate Django Code

We will create a Django application named `machinery` to house this module.

### 4.1 Models (`machinery/models.py`)

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The `Machine` model will map to the `tblMachines` table. We set `managed = False` because Django will be connecting to an existing database table, not creating it. Business logic pertinent to a `Machine` (e.g., checking operational status, calculating age) will reside as methods within this model.

```python
from django.db import models
from django.utils import timezone
from datetime import date

class Machine(models.Model):
    """
    Represents a piece of machinery in the ERP system, mapping to the existing tblMachines table.
    """
    machine_id = models.AutoField(db_column='MachineId', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, verbose_name='Machine Name')
    serial_number = models.CharField(db_column='SerialNumber', max_length=100, unique=True, verbose_name='Serial Number')
    purchase_date = models.DateField(db_column='PurchaseDate', verbose_name='Purchase Date')
    status = models.CharField(db_column='Status', max_length=50, verbose_name='Current Status') # e.g., Operational, Under Maintenance, Retired
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True, verbose_name='Location')

    class Meta:
        managed = False  # Django will not manage table creation/deletion
        db_table = 'tblMachines'
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'
        ordering = ['name'] # Default ordering for lists

    def __str__(self):
        """
        Returns a string representation of the Machine instance.
        """
        return f"{self.name} ({self.serial_number})"

    def get_age_in_years(self):
        """
        Calculates the age of the machine in full years since its purchase date.
        This is an example of business logic residing in the model.
        """
        today = date.today()
        return today.year - self.purchase_date.year - ((today.month, today.day) < (self.purchase_date.month, self.purchase_date.day))

    def is_operational(self):
        """
        Checks if the machine's status indicates it is operational.
        """
        return self.status.lower() == 'operational'

    def update_status(self, new_status):
        """
        Updates the machine's status and saves the change.
        """
        if new_status and self.status != new_status:
            self.status = new_status
            self.save()
            return True
        return False

    # Example of a manager method for common queries (Fat Model principle)
    @classmethod
    def get_operational_machines(cls):
        """
        Returns a QuerySet of all machines currently marked as 'Operational'.
        """
        return cls.objects.filter(status__iexact='operational')

```

### 4.2 Forms (`machinery/forms.py`)

**Task:** Define a Django form for user input for the `Machine` model.

**Instructions:**
A `ModelForm` will be used to automatically generate fields from the `Machine` model. Widgets will be customized with Tailwind CSS classes for consistent styling. Custom validation for fields can be added here if needed.

```python
from django import forms
from .models import Machine

class MachineForm(forms.ModelForm):
    """
    Form for creating and updating Machine instances.
    """
    class Meta:
        model = Machine
        fields = ['name', 'serial_number', 'purchase_date', 'status', 'location']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter machine name'}),
            'serial_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter serial number'}),
            'purchase_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'},
                                   choices=[('Operational', 'Operational'), ('Under Maintenance', 'Under Maintenance'), ('Retired', 'Retired'), ('Other', 'Other')]),
            'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter location (optional)'}),
        }
        labels = {
            'name': 'Machine Name',
            'serial_number': 'Serial Number',
            'purchase_date': 'Purchase Date',
            'status': 'Status',
            'location': 'Location',
        }

    def clean_serial_number(self):
        """
        Example of custom form validation: ensure serial number is unique (beyond ModelForm's check).
        This is more for demonstration; ModelForm usually handles unique fields.
        """
        serial_number = self.cleaned_data['serial_number']
        if self.instance.pk: # If updating an existing instance
            if Machine.objects.exclude(pk=self.instance.pk).filter(serial_number=serial_number).exists():
                raise forms.ValidationError("This serial number already exists for another machine.")
        else: # If creating a new instance
            if Machine.objects.filter(serial_number=serial_number).exists():
                raise forms.ValidationError("This serial number already exists.")
        return serial_number

```

### 4.3 Views (`machinery/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
Views are kept intentionally thin, adhering to the "Fat Model, Thin View" principle. All business logic is delegated to the `Machine` model. HTMX headers (`HX-Trigger`) are used to inform the client-side about successful operations, prompting list refreshes without full page reloads. A `TablePartialView` is added to handle HTMX requests for just the data table.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Machine
from .forms import MachineForm

class MachineListView(ListView):
    """
    Displays a list of all machines.
    The actual table content is loaded via HTMX by MachineTablePartialView.
    """
    model = Machine
    template_name = 'machinery/machine/list.html'
    context_object_name = 'machines' # Renamed from machine_list for clarity

    def get_queryset(self):
        """
        Returns the queryset for the list view.
        Example of delegating query logic to the model.
        """
        return Machine.objects.all()

class MachineTablePartialView(TemplateView):
    """
    Renders only the table portion of the machine list.
    Designed to be loaded via HTMX into MachineListView.
    """
    template_name = 'machinery/machine/_machine_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['machines'] = Machine.objects.all()
        return context

class MachineCreateView(CreateView):
    """
    Handles creation of new machine instances.
    Renders a form within a modal.
    """
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/machine/_machine_form.html' # Partial for HTMX modal
    success_url = reverse_lazy('machine_list') # Not directly used if HTMX handles redirect/swap

    def form_valid(self, form):
        """
        Called when form data is valid. Saves the instance and sends HTMX trigger.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Machine added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, respond with no content and a trigger header
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshMachineList' # Custom event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        """
        Called when form data is invalid. Rerenders the form with errors.
        """
        if self.request.headers.get('HX-Request'):
            # If invalid via HTMX, re-render the form itself with errors
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class MachineUpdateView(UpdateView):
    """
    Handles updating existing machine instances.
    Renders a form within a modal.
    """
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/machine/_machine_form.html' # Partial for HTMX modal
    context_object_name = 'machine' # Use 'machine' for clarity in template
    success_url = reverse_lazy('machine_list')

    def form_valid(self, form):
        """
        Called when form data is valid. Updates the instance and sends HTMX trigger.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Machine updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMachineList'
                }
            )
        return response

    def form_invalid(self, form):
        """
        Called when form data is invalid. Rerenders the form with errors.
        """
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class MachineDeleteView(DeleteView):
    """
    Handles deletion of machine instances.
    Renders a confirmation prompt within a modal.
    """
    model = Machine
    template_name = 'machinery/machine/_machine_confirm_delete.html' # Partial for HTMX modal
    context_object_name = 'machine'
    success_url = reverse_lazy('machine_list')

    def delete(self, request, *args, **kwargs):
        """
        Performs the deletion and sends HTMX trigger.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Machine deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMachineList'
                }
            )
        return response

```

### 4.4 Templates (`machinery/templates/machinery/machine/`)

**Task:** Create templates for each view, leveraging partials for HTMX and DRY principles.

**Instructions:**
*   `list.html` is the main page that loads the DataTables content via HTMX.
*   `_machine_table.html` is the HTMX partial for the DataTables table.
*   `_machine_form.html` is the HTMX partial for the create/update forms within a modal.
*   `_machine_confirm_delete.html` is the HTMX partial for the delete confirmation modal.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'machine_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Machine
        </button>
    </div>

    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div id="machineTable-container"
             hx-trigger="load, refreshMachineList from:body"
             hx-get="{% url 'machine_table' %}"
             hx-swap="innerHTML">
            <!-- Initial loading state for HTMX -->
            <div class="flex items-center justify-center p-8 text-gray-600">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                <p>Loading machines data...</p>
            </div>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on keydown from document if key is 'Escape' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be initialized here if needed for broader UI state.
        // For example, managing modal visibility with Alpine.js if HTMX is not exclusively used.
        // In this setup, HTMX + Hyperscript are handling modal display directly.
    });
</script>
{% endblock %}
```

#### `_machine_table.html` (Partial)

```html
{% load static %}
<div class="p-6">
    <table id="machineTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for machine in machines %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ machine.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ machine.serial_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ machine.purchase_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        {% if machine.status == 'Operational' %}bg-green-100 text-green-800
                        {% elif machine.status == 'Under Maintenance' %}bg-yellow-100 text-yellow-800
                        {% elif machine.status == 'Retired' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ machine.status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap">{{ machine.location|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-200"
                        hx-get="{% url 'machine_edit' machine.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-200"
                        hx-get="{% url 'machine_delete' machine.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No machines found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once per table load
    // This script runs every time _machine_table.html is loaded via HTMX
    if ($.fn.DataTable.isDataTable('#machineTable')) {
        $('#machineTable').DataTable().destroy();
    }
    $(document).ready(function() {
        $('#machineTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "responsive": true,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "lengthMenu": "Show _MENU_ entries",
                "search": "Search:",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "Showing 0 to 0 of 0 entries",
                "infoFiltered": "(filtered from _MAX_ total entries)",
                "zeroRecords": "No matching records found",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
</script>
```

#### `_machine_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Machine</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status == 204) { document.getElementById('modal').classList.remove('is-active'); messages.success('Machine saved successfully!'); } else { console.log('Form submission failed or had validation errors.'); }">
        {% csrf_token %}

        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            {% for field in form %}
            <div class="relative">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-200">
                Save Machine
            </button>
        </div>
    </form>
</div>
```

#### `_machine_confirm_delete.html` (Partial)

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you sure you want to delete the machine "<strong>{{ machine.name }} ({{ machine.serial_number }})</strong>"?</p>
    <p class="text-red-600 mb-6">This action cannot be undone.</p>

    <form hx-delete="{% url 'machine_delete' machine.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status == 204) { document.getElementById('modal').classList.remove('is-active'); messages.success('Machine deleted successfully!'); } else { console.log('Deletion failed.'); }">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-200">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`machinery/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for each CRUD operation and the HTMX partial for the table, ensuring clear and semantic naming conventions.

```python
from django.urls import path
from .views import (
    MachineListView,
    MachineCreateView,
    MachineUpdateView,
    MachineDeleteView,
    MachineTablePartialView
)

urlpatterns = [
    # Main list view (initial load for the page)
    path('machines/', MachineListView.as_view(), name='machine_list'),
    # HTMX endpoint for the table partial (reloads only the table)
    path('machines/table/', MachineTablePartialView.as_view(), name='machine_table'),
    # HTMX endpoint for adding a new machine (modal form)
    path('machines/add/', MachineCreateView.as_view(), name='machine_add'),
    # HTMX endpoint for editing an existing machine (modal form)
    path('machines/edit/<int:pk>/', MachineUpdateView.as_view(), name='machine_edit'),
    # HTMX endpoint for deleting a machine (modal confirmation)
    path('machines/delete/<int:pk>/', MachineDeleteView.as_view(), name='machine_delete'),
]

```
**Note:** You would need to include these URLs in your project's main `urls.py` file, e.g.:
`path('machinery/', include('machinery.urls')),`

### 4.6 Tests (`machinery/tests.py`)

**Task:** Write comprehensive tests for the model and views.

**Instructions:**
Tests cover model functionality (creation, field attributes, custom methods) and view functionality (GET/POST requests, template usage, form validation, HTMX interactions). This ensures robustness and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Machine
from datetime import date
from django.contrib.messages import get_messages

class MachineModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.machine1 = Machine.objects.create(
            name='CNC Mill',
            serial_number='SN-12345',
            purchase_date=date(2020, 1, 15),
            status='Operational',
            location='Workshop A'
        )
        cls.machine2 = Machine.objects.create(
            name='Lathe Machine',
            serial_number='SN-67890',
            purchase_date=date(2018, 5, 20),
            status='Under Maintenance',
            location='Workshop B'
        )

    def test_machine_creation(self):
        """Test that a Machine instance is created correctly."""
        self.assertEqual(self.machine1.name, 'CNC Mill')
        self.assertEqual(self.machine1.serial_number, 'SN-12345')
        self.assertEqual(self.machine1.purchase_date, date(2020, 1, 15))
        self.assertEqual(self.machine1.status, 'Operational')
        self.assertEqual(self.machine1.location, 'Workshop A')
        self.assertIsNotNone(self.machine1.machine_id)

    def test_name_label(self):
        """Test the verbose name for the 'name' field."""
        field_label = self.machine1._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Machine Name')

    def test_serial_number_unique(self):
        """Test that serial number is unique."""
        with self.assertRaises(Exception): # Expect an IntegrityError on save
            Machine.objects.create(
                name='Another CNC',
                serial_number='SN-12345', # Duplicate
                purchase_date=date(2021, 1, 1),
                status='Operational',
                location='Workshop C'
            )

    def test_str_method(self):
        """Test the __str__ method of the Machine model."""
        self.assertEqual(str(self.machine1), 'CNC Mill (SN-12345)')

    def test_get_age_in_years_method(self):
        """Test the get_age_in_years business logic method."""
        # Adjusting purchase date to ensure accurate age calculation for testing
        test_machine = Machine.objects.create(
            name='Test Age Machine',
            serial_number='AGE-001',
            purchase_date=date(2020, 10, 20),
            status='Operational'
        )
        # Assuming current date is after 2020-10-20 to get an age > 0
        expected_age = date.today().year - 2020 - ((date.today().month, date.today().day) < (10, 20))
        self.assertEqual(test_machine.get_age_in_years(), expected_age)

    def test_is_operational_method(self):
        """Test the is_operational business logic method."""
        self.assertTrue(self.machine1.is_operational())
        self.assertFalse(self.machine2.is_operational())

    def test_update_status_method(self):
        """Test the update_status business logic method."""
        self.assertTrue(self.machine1.update_status('Under Maintenance'))
        self.machine1.refresh_from_db()
        self.assertEqual(self.machine1.status, 'Under Maintenance')
        self.assertFalse(self.machine1.update_status('Under Maintenance')) # No change, should return False

    def test_get_operational_machines_manager_method(self):
        """Test the custom manager method to retrieve operational machines."""
        operational_machines = Machine.get_operational_machines()
        self.assertIn(self.machine1, operational_machines)
        self.assertNotIn(self.machine2, operational_machines)
        self.assertEqual(operational_machines.count(), 1) # Only machine1 is operational

class MachineViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.machine = Machine.objects.create(
            name='Test Machine',
            serial_number='TM-001',
            purchase_date=date(2022, 3, 1),
            status='Operational',
            location='Test Lab'
        )

    def setUp(self):
        # Set up a client for each test method
        self.client = Client()

    def test_machine_list_view_get(self):
        """Test the GET request for the machine list view."""
        response = self.client.get(reverse('machine_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machine/list.html')
        # Check that the main list view doesn't directly contain machine objects,
        # as they are loaded via HTMX into a partial.
        self.assertNotIn('machines', response.context) # machines are in partial view

    def test_machine_table_partial_view_get(self):
        """Test the GET request for the HTMX table partial view."""
        response = self.client.get(reverse('machine_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machine/_machine_table.html')
        self.assertIn('machines', response.context)
        self.assertContains(response, self.machine.name)
        self.assertContains(response, self.machine.serial_number)

    def test_machine_create_view_get(self):
        """Test the GET request for the machine creation form (modal)."""
        response = self.client.get(reverse('machine_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machine/_machine_form.html')
        self.assertIn('form', response.context)

    def test_machine_create_view_post_valid(self):
        """Test the POST request for valid machine creation."""
        data = {
            'name': 'New Machine',
            'serial_number': 'NEW-001',
            'purchase_date': '2023-01-01',
            'status': 'Operational',
            'location': 'New Site'
        }
        # Simulate HTMX request for form submission
        response = self.client.post(reverse('machine_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX submission
        self.assertTrue(Machine.objects.filter(serial_number='NEW-001').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Machine added successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMachineList', response.headers['HX-Trigger'])


    def test_machine_create_view_post_invalid(self):
        """Test the POST request for invalid machine creation (e.g., duplicate serial)."""
        data = {
            'name': 'Invalid Machine',
            'serial_number': self.machine.serial_number, # Duplicate serial number
            'purchase_date': '2023-01-01',
            'status': 'Operational'
        }
        response = self.client.post(reverse('machine_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'machinery/machine/_machine_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('serial_number', response.context['form'].errors)
        self.assertFalse(Machine.objects.filter(name='Invalid Machine').exists())


    def test_machine_update_view_get(self):
        """Test the GET request for the machine update form (modal)."""
        response = self.client.get(reverse('machine_edit', args=[self.machine.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machine/_machine_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.machine)

    def test_machine_update_view_post_valid(self):
        """Test the POST request for valid machine update."""
        updated_name = 'Updated Test Machine'
        data = {
            'name': updated_name,
            'serial_number': self.machine.serial_number, # Keep same serial
            'purchase_date': '2022-03-01',
            'status': 'Under Maintenance',
            'location': 'New Location'
        }
        response = self.client.post(reverse('machine_edit', args=[self.machine.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.name, updated_name)
        self.assertEqual(self.machine.status, 'Under Maintenance')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Machine updated successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMachineList', response.headers['HX-Trigger'])

    def test_machine_delete_view_get(self):
        """Test the GET request for the machine deletion confirmation (modal)."""
        response = self.client.get(reverse('machine_delete', args=[self.machine.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machine/_machine_confirm_delete.html')
        self.assertIn('machine', response.context)
        self.assertEqual(response.context['machine'], self.machine)

    def test_machine_delete_view_post(self):
        """Test the DELETE request for machine deletion."""
        # Create a machine specifically for deletion to avoid affecting other tests
        machine_to_delete = Machine.objects.create(
            name='To Delete',
            serial_number='DEL-001',
            purchase_date=date(2023, 1, 1),
            status='Operational'
        )
        response = self.client.delete(reverse('machine_delete', args=[machine_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Machine.objects.filter(pk=machine_to_delete.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Machine deleted successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMachineList', response.headers['HX-Trigger'])

    def test_machine_delete_view_post_non_existent(self):
        """Test deleting a non-existent machine."""
        response = self.client.delete(reverse('machine_delete', args=[9999]), HTTP_HX_REQUEST='true') # Non-existent PK
        self.assertEqual(response.status_code, 404) # Not Found

```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided Django code heavily relies on HTMX for dynamic interactions, eliminating the need for traditional JavaScript frameworks.

*   **HTMX for CRUD Operations:**
    *   **List View:** The `machine_list.html` template uses `hx-get="{% url 'machine_table' %}"` with `hx-trigger="load, refreshMachineList from:body"` to initially load and subsequently refresh the table content dynamically.
    *   **Modal Forms:** Buttons for "Add New Machine", "Edit", and "Delete" use `hx-get` to fetch the respective form/confirmation partials (`_machine_form.html`, `_machine_confirm_delete.html`) and load them into a common `#modalContent` div.
    *   **Form Submission:** Forms within the modals use `hx-post` (or `hx-delete` for delete) with `hx-swap="none"`. Upon successful submission (HTTP 204 No Content), the backend sends an `HX-Trigger: refreshMachineList` header, which causes the main list view to re-fetch and update its table.
    *   **Error Handling:** If a form submission fails (e.g., validation errors), the view re-renders the form with errors, and HTMX automatically swaps the invalid form back into the modal, allowing the user to correct input without losing context.
*   **Alpine.js for UI State Management:**
    *   While not explicitly used for complex data binding in this example (HTMX handles most dynamic content), Alpine.js is ideal for simple UI state management, such as toggling modal visibility, managing dropdowns, or handling client-side form interactions (e.g., resetting fields).
    *   In the provided templates, Hyperscript (`_`) is used for basic modal toggling (`on click add .is-active to #modal`), which is a lightweight alternative that integrates well with HTMX for simple UI behaviors. If more complex interactivity beyond simple show/hide is needed, Alpine.js can be introduced.
*   **DataTables for List Views:**
    *   The `_machine_table.html` partial directly initializes DataTables on the `machineTable` HTML element. This provides out-of-the-box client-side searching, sorting, and pagination without requiring any custom Django view logic for these functionalities.
    *   The JavaScript for DataTables is embedded within the partial, ensuring it runs every time the table content is refreshed via HTMX. A check `if ($.fn.DataTable.isDataTable('#machineTable')) { $('#machineTable').DataTable().destroy(); }` prevents re-initialization errors.
*   **No Full Page Reloads:** All CRUD operations and list refreshes are handled dynamically using HTMX, providing a fast and responsive user experience similar to a Single Page Application (SPA) but with the simplicity of server-rendered HTML.
*   **DRY Template Inheritance:** All module templates (`list.html`) extend `core/base.html` (not included in output), which would contain shared CDN links for DataTables, HTMX, Alpine.js, Tailwind CSS, etc., ensuring consistency across the application.

---

## Final Notes

This comprehensive plan transforms the blank ASP.NET dashboard page into a modern, interactive Django application module for `Machinery` management.

*   **Placeholders Replaced:** All `[PLACEHOLDER]` values have been replaced with concrete examples using the `Machine` entity.
*   **DRY Templates:** The use of partial templates (`_machine_table.html`, `_machine_form.html`, `_machine_confirm_delete.html`) ensures that HTML components are reusable and maintainable.
*   **Fat Models, Thin Views:** Business logic (e.g., `get_age_in_years`, `is_operational`, `update_status`) is encapsulated within the `Machine` model, keeping `views.py` concise and focused on orchestrating requests and responses.
*   **Comprehensive Tests:** Robust unit and integration tests cover models and views, ensuring code quality and reducing the risk of regressions during future development.
*   **HTMX and Alpine.js:** These technologies enable rich, dynamic user interfaces without the complexity of traditional JavaScript frameworks, adhering to the "HTML over the wire" philosophy. The provided code is directly runnable once integrated into a Django project with the necessary settings (database, installed apps, static files, etc.).

This structured approach, driven by AI-assisted analysis and automation principles, provides a clear, actionable roadmap for migrating and modernizing legacy ASP.NET applications to a modern Django ecosystem.