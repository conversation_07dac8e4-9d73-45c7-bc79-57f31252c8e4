## ASP.NET to Django Conversion Script: Job Scheduling Module

This document outlines a comprehensive plan to modernize the existing ASP.NET Job Scheduling input page into a robust, scalable Django 5.0+ application. Our approach leverages AI-assisted automation, focusing on 'Fat Model, Thin View' architecture, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for superior data presentation.

This modernization will transform your legacy system into a high-performance, maintainable, and user-friendly solution, significantly reducing future development and maintenance costs while improving system responsiveness and user experience.

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several tables. For this specific page, the primary focus is on staging job schedule details, which are then committed to permanent tables.

*   `tblMS_JobSchedule_Details_Temp`: Stores temporary job schedule details before final submission. This will be our primary model for the input and display grids.
*   `tblMS_JobShedule_Master`: Stores the main job schedule header information upon final submission.
*   `tblMS_JobSchedule_Details`: Stores the permanent job schedule details upon final submission.
*   `tblDG_Item_Master`: Used for fetching machine names and main item details (ItemCode, Description, UOM).
*   `tblPln_Process_Master`: Used for fetching process names.
*   `tblHR_OfficeStaff`: Used for fetching Incharge and Operator names for auto-completion.
*   `SD_Cust_WorkOrder_Master`: Used for fetching batch numbers.
*   `tblMS_Master`: Used for mapping machines to processes.
*   `tblDG_BOM_Master` & `Unit_Master`: Used for fetching BOM details.

**Identified Columns for `tblMS_JobSchedule_Details_Temp`:**

*   `Id` (PK, int)
*   `CompId` (int)
*   `SessionId` (string)
*   `Shift` (int)
*   `Type` (int)
*   `FromDate` (datetime)
*   `ToDate` (datetime)
*   `FromTime` (string, will be `time` in Django)
*   `ToTime` (string, will be `time` in Django)
*   `Process` (int) - foreign key to `tblPln_Process_Master.Id`
*   `Qty` (float/double)
*   `Incharge` (string) - employee ID, foreign key to `tblHR_OfficeStaff.EmpId`
*   `Operator` (string) - employee ID, foreign key to `tblHR_OfficeStaff.EmpId`
*   `MachineId` (int) - foreign key to `tblDG_Item_Master.Id`
*   `ItemId` (int) - foreign key to `tblDG_Item_Master.Id` (main item)
*   `BatchNo` (float/double)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The ASP.NET page orchestrates a multi-step process for scheduling.

*   **Read (Display & Lookups):**
    *   Retrieves item and work order details (Item Code, Description, UOM, BOM Qty, WoNo) to display at the top of the page.
    *   Dynamically populates machine dropdowns based on available machines.
    *   Populates process dropdowns based on the selected machine.
    *   Retrieves batch numbers based on the work order.
    *   Fetches and displays existing temporary job schedule details from `tblMS_JobSchedule_Details_Temp` in a DataTables format.
    *   Provides auto-completion for Incharge and Operator fields from employee master.
*   **Create (Staging & Final):**
    *   **Staging:** Adds new schedule entries to `tblMS_JobSchedule_Details_Temp` (temporary table) via the "Add" button in `GridView1`. This includes validation for date/time overlaps and order.
    *   **Final:** On "Submit", it transfers all entries from `tblMS_JobSchedule_Details_Temp` to `tblMS_JobShedule_Master` (header) and `tblMS_JobSchedule_Details` (details), generating a new `JobNo`.
*   **Delete (Staging):** Allows deletion of temporary schedule entries from `tblMS_JobSchedule_Details_Temp` via the "Delete" button in `GridView2`.
*   **Validation:** Extensive validation is performed for required fields, date/time formats, and logical consistency (e.g., `FromDate` < `ToDate`, `FromTime` < `ToTime`, ensuring no machine schedule overlaps).
*   **Session Management:** `CompId`, `FinYearId`, `SessionId` are critical for data isolation and will be handled by Django's authentication and session system.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The page combines a main information display, an input form (simulated by `GridView1`), and a dynamic list of staged entries (`GridView2`).

*   **Header Labels:** `lblItemCode`, `lblunit`, `lblBomqty`, `lblDesc`, `lblWoNo` display static details related to the main item and work order.
*   **Input Grid (`GridView1` equivalent):**
    *   `DrpMachine`, `DrpProcess`, `DrpType`, `DrpShift`, `DrpBatchNO` (Dropdowns for selection).
    *   `TxtBatchQty`, `TxtFdate`, `TxtTdate`, `TxtIncharge`, `TxtOperator` (Text inputs).
    *   `CalendarExtender`, `TimeSelector` (Date and time pickers).
    *   `AutoCompleteExtender` (for Incharge/Operator).
    *   `LinkBtnAdd` (to add a row to the temporary list).
*   **Display Grid (`GridView2` equivalent):**
    *   Displays temporary schedule entries.
    *   `LinkBtnAdd` (to delete a row from the temporary list).
    *   Pagination is handled by `GridView2`.
*   **Action Buttons:** `btnSubmit` (finalizes schedule), `Btncancel` (cancels and redirects).

This structure lends itself perfectly to HTMX-driven partial updates and a single-page application feel without complex JavaScript frameworks.

### Step 4: Generate Django Code

We will create a Django application named `machinery_schedule` to house the new components.

#### 4.1 Models (`machinery_schedule/models.py`)

We'll define the core models, mapping them to the existing database tables. We'll also add helper properties for fetching related display names without joining tables in every query, adhering to the 'Fat Model' principle.

```python
from django.db import models
from datetime import date, time

# Helper models (assuming they exist or will be migrated as well)
# These are kept minimal for managed=False, focusing on lookup fields
class ItemMaster(models.Model):
    # Id is the default primary key unless specified otherwise
    item_id = models.IntegerField(db_column='Id', primary_key=True) # Explicitly define if PK is not 'id'
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, null=True, blank=True)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, null=True, blank=True) # Assuming UOMBasic directly from tblDG_Item_Master or via Unit_Master lookup
    
    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.manf_desc or f"Item {self.item_id}"

class ProcessMaster(models.Model):
    process_id = models.IntegerField(db_column='Id', primary_key=True)
    process_name = models.CharField(db_column='ProcessName', max_length=255, null=True, blank=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Process Master'
        verbose_name_plural = 'Process Masters'

    def __str__(self):
        return self.process_name or f"Process {self.process_id}"

class Employee(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is char/varchar
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name or f"Employee {self.emp_id}"

# Main models for Job Scheduling
class JobScheduleMaster(models.Model):
    # Id is the default primary key
    job_no = models.CharField(db_column='JobNo', max_length=4, unique=True, null=True, blank=True)
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    sys_time = models.TimeField(db_column='SysTime', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId', null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, null=True, blank=True)
    item_id = models.IntegerField(db_column='ItemId', null=True) # Raw ItemId, not FK

    class Meta:
        managed = False
        db_table = 'tblMS_JobShedule_Master'
        verbose_name = 'Job Schedule Master'
        verbose_name_plural = 'Job Schedule Masters'

    def __str__(self):
        return self.job_no or f"Job {self.pk}"

class JobScheduleDetail(models.Model):
    # Id is the default primary key
    master = models.IntegerField(db_column='MId', null=True) # Raw MasterId, not FK
    shift = models.IntegerField(db_column='Shift', null=True)
    machine_id = models.IntegerField(db_column='MachineId', null=True) # Raw MachineId
    type = models.IntegerField(db_column='Type', null=True)
    from_date = models.DateField(db_column='FromDate', null=True, blank=True)
    to_date = models.DateField(db_column='ToDate', null=True, blank=True)
    from_time = models.TimeField(db_column='FromTime', null=True, blank=True)
    to_time = models.TimeField(db_column='ToTime', null=True, blank=True)
    process = models.IntegerField(db_column='Process', null=True) # Raw Process ID
    qty = models.FloatField(db_column='Qty', null=True)
    incharge = models.CharField(db_column='Incharge', max_length=50, null=True, blank=True) # Employee ID
    operator = models.CharField(db_column='Operator', max_length=50, null=True, blank=True) # Employee ID
    batch_no = models.FloatField(db_column='BatchNo', null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_JobSchedule_Details'
        verbose_name = 'Job Schedule Detail'
        verbose_name_plural = 'Job Schedule Details'

    def __str__(self):
        return f"Detail for Job {self.master} - Machine {self.machine_id}"

    # Business logic: Check if machine is available for this schedule slot
    @classmethod
    def is_machine_available(cls, machine_id, from_datetime, to_datetime, exclude_id=None):
        """
        Checks if a machine is available for the given time slot.
        Considers both tblMS_JobSchedule_Details and tblMS_JobSchedule_Details_Temp.
        """
        # Ensure datetimes are timezone-aware if the DB uses timezone
        # For simplicity, assuming naive datetimes as per ASP.NET code
        
        # Convert date and time to datetime objects for comparison
        from_dt_check = from_datetime
        to_dt_check = to_datetime

        # Query existing schedules for the machine
        # Check permanent schedules
        permanent_schedules = cls.objects.filter(
            machine_id=machine_id,
        ).exclude(id=exclude_id) # Exclude current temp record if updating

        for schedule in permanent_schedules:
            schedule_from_dt = schedule.from_date_time()
            schedule_to_dt = schedule.to_date_time()

            # Check for overlap: [start1, end1] overlaps [start2, end2] if (start1 < end2 and end1 > start2)
            if schedule_from_dt < to_dt_check and schedule_to_dt > from_dt_check:
                return False # Overlap found

        # Check temporary schedules (from JobScheduleDetailTemp)
        temp_schedules = JobScheduleDetailTemp.objects.filter(
            machine_id=machine_id,
        ).exclude(id=exclude_id)

        for schedule in temp_schedules:
            schedule_from_dt = schedule.from_date_time()
            schedule_to_dt = schedule.to_date_time()

            if schedule_from_dt < to_dt_check and schedule_to_dt > from_dt_check:
                return False # Overlap found

        return True # No overlap

    # Helper methods to get datetime objects from date and time fields
    def from_date_time(self):
        if self.from_date and self.from_time:
            return models.DateTimeField().to_python(f"{self.from_date} {self.from_time}")
        return None

    def to_date_time(self):
        if self.to_date and self.to_time:
            return models.DateTimeField().to_python(f"{self.to_date} {self.to_time}")
        return None

    @property
    def machine_name(self):
        try:
            return ItemMaster.objects.get(item_id=self.machine_id).manf_desc
        except ItemMaster.DoesNotExist:
            return "N/A"

    @property
    def process_name(self):
        try:
            process_obj = ProcessMaster.objects.get(process_id=self.process)
            return f"[{process_obj.symbol}] {process_obj.process_name}" if process_obj.symbol else process_obj.process_name
        except ProcessMaster.DoesNotExist:
            return "N/A"

    @property
    def type_display(self):
        return "Fresh" if self.type == 0 else "Rework" if self.type == 1 else "Select"

    @property
    def shift_display(self):
        return "Day" if self.shift == 0 else "Night" if self.shift == 1 else "Select"

    @property
    def incharge_name(self):
        try:
            # Assuming Incharge stores EmpId (string)
            return Employee.objects.get(emp_id=self.incharge).employee_name
        except Employee.DoesNotExist:
            return "N/A"

    @property
    def operator_name(self):
        try:
            # Assuming Operator stores EmpId (string)
            return Employee.objects.get(emp_id=self.operator).employee_name
        except Employee.DoesNotExist:
            return "N/A"


class JobScheduleDetailTemp(models.Model):
    # Id is the default primary key
    comp_id = models.IntegerField(db_column='CompId', null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True)
    shift = models.IntegerField(db_column='Shift', null=True)
    type = models.IntegerField(db_column='Type', null=True)
    from_date = models.DateField(db_column='FromDate', null=True, blank=True)
    to_date = models.DateField(db_column='ToDate', null=True, blank=True)
    from_time = models.TimeField(db_column='FromTime', null=True, blank=True)
    to_time = models.TimeField(db_column='ToTime', null=True, blank=True)
    process = models.IntegerField(db_column='Process', null=True) # Raw Process ID
    qty = models.FloatField(db_column='Qty', null=True)
    incharge = models.CharField(db_column='Incharge', max_length=50, null=True, blank=True) # Employee ID
    operator = models.CharField(db_column='Operator', max_length=50, null=True, blank=True) # Employee ID
    machine_id = models.IntegerField(db_column='MachineId', null=True) # Raw MachineId
    item_id = models.IntegerField(db_column='ItemId', null=True) # Main ItemId
    batch_no = models.FloatField(db_column='BatchNo', null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_JobSchedule_Details_Temp'
        verbose_name = 'Temporary Job Schedule Detail'
        verbose_name_plural = 'Temporary Job Schedule Details'

    def __str__(self):
        return f"Temp Detail {self.pk} for Machine {self.machine_id}"

    # Business logic: Helper methods for display and datetime conversion
    def from_date_time(self):
        if self.from_date and self.from_time:
            return models.DateTimeField().to_python(f"{self.from_date} {self.from_time}")
        return None

    def to_date_time(self):
        if self.to_date and self.to_time:
            return models.DateTimeField().to_python(f"{self.to_date} {self.to_time}")
        return None

    @property
    def machine_name(self):
        try:
            return ItemMaster.objects.get(item_id=self.machine_id).manf_desc
        except ItemMaster.DoesNotExist:
            return "N/A"

    @property
    def process_name(self):
        try:
            process_obj = ProcessMaster.objects.get(process_id=self.process)
            return f"[{process_obj.symbol}] {process_obj.process_name}" if process_obj.symbol else process_obj.process_name
        except ProcessMaster.DoesNotExist:
            return "N/A"

    @property
    def type_display(self):
        return "Fresh" if self.type == 0 else "Rework" if self.type == 1 else "Select"

    @property
    def shift_display(self):
        return "Day" if self.shift == 0 else "Night" if self.shift == 1 else "Select"

    @property
    def incharge_name(self):
        try:
            return Employee.objects.get(emp_id=self.incharge).employee_name
        except Employee.DoesNotExist:
            return "N/A"

    @property
    def operator_name(self):
        try:
            return Employee.objects.get(emp_id=self.operator).employee_name
        except Employee.DoesNotExist:
            return "N/A"

    # Business logic for `AddToTemp` validation and saving
    def save_to_temp_with_validation(self, comp_id, session_id, item_id):
        # Apply the complex validation logic from AddToTemp C# method
        self.comp_id = comp_id
        self.session_id = session_id
        self.item_id = item_id

        if not self.from_date or not self.to_date or not self.from_time or not self.to_time:
            raise ValueError("All date and time fields are required.")

        from_dt = self.from_date_time()
        to_dt = self.to_date_time()

        if not from_dt or not to_dt:
             raise ValueError("Invalid date/time format.")

        if from_dt > to_dt:
            raise ValueError("From Date/Time should be less than To Date/Time.")
        elif from_dt == to_dt and from_dt.time() >= to_dt.time():
            raise ValueError("From Time should be less than To Time when dates are same.")

        # Check machine availability using JobScheduleDetail.is_machine_available
        if not JobScheduleDetail.is_machine_available(self.machine_id, from_dt, to_dt, self.pk):
            # This logic needs to mirror the exact check for 'LastDate' and 'LastTime'
            # The C# code checks for a specific "busy upto" message.
            # We'll need to replicate that specific behavior.
            last_schedule = JobScheduleDetail.objects.filter(
                machine_id=self.machine_id
            ).order_by('-to_date', '-to_time').first() or \
            JobScheduleDetailTemp.objects.filter(
                machine_id=self.machine_id
            ).order_by('-to_date', '-to_time').first()
            
            if last_schedule:
                last_dt = last_schedule.to_date_time()
                raise ValueError(f"Machine is busy upto {last_dt.strftime('%d-%m-%Y, %H:%M %p')}.")
            else:
                # Fallback for unexpected scenarios
                raise ValueError("Machine is currently unavailable or busy within the selected timeframe.")
            
        super().save() # Save the temporary record if validation passes


class BomMaster(models.Model):
    # Simplified for lookup, actual fields will be more complex
    p_id = models.IntegerField(db_column='PId', null=True)
    c_id = models.IntegerField(db_column='CId', null=True)
    item_id = models.IntegerField(db_column='ItemId', null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, null=True, blank=True)
    qty = models.FloatField(db_column='Qty', null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    # Placeholder for complex BOMTreeQty calculation
    @classmethod
    def get_bom_tree_qty(cls, wo_no, p_id, c_id):
        # This function needs to replicate the C# fun.BOMTreeQty logic.
        # For a full migration, this would involve a recursive CTE or equivalent ORM logic.
        # Placeholder returns a dummy value for now.
        return 10.0 # Placeholder for actual BOM qty calculation

class WorkOrderMaster(models.Model):
    # Simplified for lookup
    wo_no = models.CharField(db_column='WONo', primary_key=True, max_length=255)
    batches = models.FloatField(db_column='Batches', null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

```

#### 4.2 Forms (`machinery_schedule/forms.py`)

This form is for the input row (`GridView1`). It will handle validation and dynamic options for dropdowns.

```python
from django import forms
from django.db.models import Q
from .models import JobScheduleDetailTemp, ItemMaster, ProcessMaster, Employee
from datetime import datetime, time

class JobScheduleDetailTempForm(forms.ModelForm):
    machine = forms.ChoiceField(
        choices=[('', 'Select')],
        label="Machine Name",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/machinery_schedule/schedule/new/machine_select/', # HTMX endpoint
            'hx-trigger': 'change',
            'hx-target': '#process-time-container', # Update a specific div
            'hx-swap': 'innerHTML',
        })
    )
    process = forms.ChoiceField(
        choices=[('', 'Select')],
        label="Process",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    type = forms.ChoiceField(
        choices=[('Select', 'Select'), ('0', 'Fresh'), ('1', 'Rework')],
        label="Type",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    shift = forms.ChoiceField(
        choices=[('Select', 'Select'), ('0', 'Day'), ('1', 'Night')],
        label="Shift",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    batch_no = forms.ChoiceField(
        choices=[('', 'Select')],
        label="Batch No",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    qty = forms.FloatField(
        label="Batch Qty",
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    from_date = forms.DateField(
        label="From Date",
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'placeholder': 'DD-MM-YYYY'}),
        input_formats=['%d-%m-%Y'] # For parsing DD-MM-YYYY
    )
    to_date = forms.DateField(
        label="To Date",
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'placeholder': 'DD-MM-YYYY'}),
        input_formats=['%d-%m-%Y']
    )
    from_time = forms.TimeField(
        label="From Time",
        widget=forms.TimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm timepicker', 'placeholder': 'HH:MM AM/PM'}),
        input_formats=['%H:%M %p', '%I:%M %p'] # For parsing HH:MM AM/PM or I:MM AM/PM
    )
    to_time = forms.TimeField(
        label="To Time",
        widget=forms.TimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm timepicker', 'placeholder': 'HH:MM AM/PM'}),
        input_formats=['%H:%M %p', '%I:%M %p']
    )
    incharge = forms.CharField(
        label="Incharge",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm autocomplete',
            'hx-get': '/machinery_schedule/schedule/new/autocomplete_employee/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#incharge-autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            'data-employee-id': '' # To store actual ID
        })
    )
    operator = forms.CharField(
        label="Operator",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm autocomplete',
            'hx-get': '/machinery_schedule/schedule/new/autocomplete_employee/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#operator-autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            'data-employee-id': '' # To store actual ID
        })
    )

    class Meta:
        model = JobScheduleDetailTemp
        fields = [
            'machine', 'process', 'type', 'shift', 'batch_no', 'qty',
            'from_date', 'to_date', 'from_time', 'to_time',
            'incharge', 'operator'
        ]

    def __init__(self, *args, **kwargs):
        self.work_order_no = kwargs.pop('work_order_no', None)
        self.item_id = kwargs.pop('item_id', None)
        super().__init__(*args, **kwargs)

        # Populate static choices
        self.fields['machine'].choices = [('', 'Select')] + list(ItemMaster.objects.filter(
            Q(pk__in=MachineMaster.objects.values_list('machine_id', flat=True)) # Assuming MachineMaster links to ItemMaster
            # Further filters based on CompId/FinYearId might be needed, but not in current ASP.NET for machine list.
        ).values_list('item_id', 'manf_desc'))

        # Populate batch numbers
        if self.work_order_no:
            try:
                # Assumes WorkOrderMaster has a 'batches' field for number of batches
                from .models import WorkOrderMaster
                work_order = WorkOrderMaster.objects.get(wo_no=self.work_order_no)
                num_batches = int(work_order.batches) if work_order.batches else 0
                self.fields['batch_no'].choices = [('', 'Select')] + [(str(i), str(i)) for i in range(1, num_batches + 1)]
            except WorkOrderMaster.DoesNotExist:
                self.fields['batch_no'].choices = [('', 'Select')]
        else:
            self.fields['batch_no'].choices = [('', 'Select')]

        # Set initial process choices if machine is already selected (e.g., on form reload)
        if self.initial.get('machine'):
            self._update_process_choices(self.initial['machine'])

        # Set initial values for incharge/operator for display
        if self.initial.get('incharge'):
            try:
                emp = Employee.objects.get(emp_id=self.initial['incharge'])
                self.fields['incharge'].initial = f"{emp.employee_name} [{emp.emp_id}]"
                self.fields['incharge'].widget.attrs['data-employee-id'] = emp.emp_id
            except Employee.DoesNotExist:
                pass
        if self.initial.get('operator'):
            try:
                emp = Employee.objects.get(emp_id=self.initial['operator'])
                self.fields['operator'].initial = f"{emp.employee_name} [{emp.emp_id}]"
                self.fields['operator'].widget.attrs['data-employee-id'] = emp.emp_id
            except Employee.DoesNotExist:
                pass

    def _update_process_choices(self, machine_id):
        # This method is called by HTMX for dynamic updates
        from .models import MachineMaster # Assuming MachineMaster links machine to processes
        try:
            machine_master = MachineMaster.objects.get(item_id=machine_id)
            # Filter processes based on MachineMaster.MId (which is machine_master.pk here)
            # And exclude processes already scheduled for the current item in temp table
            scheduled_processes = JobScheduleDetailTemp.objects.filter(
                machine_id=machine_id,
                item_id=self.item_id, # The main item being scheduled
            ).values_list('process', flat=True)

            process_choices = [('', 'Select')] + list(ProcessMaster.objects.filter(
                pk__in=machine_master.get_related_process_ids(), # Assuming get_related_process_ids() method on MachineMaster
            ).exclude(process_id__in=scheduled_processes).values_list('process_id', 'process_name'))
            self.fields['process'].choices = process_choices
        except MachineMaster.DoesNotExist:
            self.fields['process'].choices = [('', 'Select')]

    def clean_incharge(self):
        incharge_val = self.cleaned_data['incharge']
        # Extract ID from "Name [ID]" format
        if '[' in incharge_val and ']' in incharge_val:
            emp_id = incharge_val[incharge_val.rfind('[')+1:incharge_val.rfind(']')]
            if Employee.objects.filter(emp_id=emp_id).exists():
                return emp_id
        # Fallback if format is not "Name [ID]" or ID is invalid, try direct lookup
        try:
            emp = Employee.objects.get(employee_name=incharge_val)
            return emp.emp_id
        except Employee.DoesNotExist:
            raise forms.ValidationError("Invalid Incharge selected.")

    def clean_operator(self):
        operator_val = self.cleaned_data['operator']
        if '[' in operator_val and ']' in operator_val:
            emp_id = operator_val[operator_val.rfind('[')+1:operator_val.rfind(']')]
            if Employee.objects.filter(emp_id=emp_id).exists():
                return emp_id
        try:
            emp = Employee.objects.get(employee_name=operator_val)
            return emp.emp_id
        except Employee.DoesNotExist:
            raise forms.ValidationError("Invalid Operator selected.")

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        from_time = cleaned_data.get('from_time')
        to_time = cleaned_data.get('to_time')
        
        # Combine date and time for comparison
        from_dt = None
        to_dt = None

        if from_date and from_time:
            from_dt = datetime.combine(from_date, from_time)
        if to_date and to_time:
            to_dt = datetime.combine(to_date, to_time)

        if from_dt and to_dt:
            if from_dt > to_dt:
                self.add_error(None, "From Date/Time should be less than or equal to To Date/Time.")
            elif from_dt == to_dt and from_dt.time() >= to_dt.time():
                self.add_error(None, "From Time should be less than To Time when dates are the same.")

        # Required fields validation (ASP.NET's RequiredFieldValidator equivalent)
        required_fields = ['machine', 'process', 'type', 'shift', 'batch_no', 'qty', 'from_date', 'to_date', 'from_time', 'to_time', 'incharge', 'operator']
        for field_name in required_fields:
            if not cleaned_data.get(field_name) or cleaned_data.get(field_name) == 'Select':
                self.add_error(field_name, "This field is required.")

        return cleaned_data

# Placeholder for MachineMaster, assuming it maps ItemMaster.Id to ProcessMaster.Id
class MachineMaster(models.Model):
    # This model needs to reflect tblMS_Master and tblMS_Process joining.
    # For managed=False, we'll assume a direct representation or
    # a method to get related process IDs.
    item_id = models.IntegerField(db_column='ItemId', primary_key=True) # Assuming ItemId is the primary key for machine
    
    class Meta:
        managed = False
        db_table = 'tblMS_Master' # Placeholder for Machine Master
        verbose_name = 'Machine Configuration'
        verbose_name_plural = 'Machine Configurations'

    def get_related_process_ids(self):
        # This method simulates the join between tblMS_Master and tblMS_Process
        # For a full migration, tblMS_Process would be a model with a ForeignKey to MachineMaster
        # Here, it directly queries based on MId (which is self.item_id)
        from django.db import connection
        with connection.cursor() as cursor:
            # Assuming tblMS_Process has MId and PId columns
            cursor.execute("SELECT PId FROM tblMS_Process WHERE MId = %s", [self.item_id])
            process_ids = [row[0] for row in cursor.fetchall()]
        return process_ids

```

#### 4.3 Views (`machinery_schedule/views.py`)

We'll use a combination of `TemplateView` for the main page and `View` subclasses for HTMX endpoints.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, get_object_or_404, redirect
from django.db import transaction
from django.db.models import Max
from datetime import datetime
import json
import uuid # For SessionId if not user-based

from .models import (
    JobScheduleDetailTemp, JobScheduleMaster, JobScheduleDetail,
    ItemMaster, ProcessMaster, Employee, BomMaster, WorkOrderMaster,
    MachineMaster # For dynamic process lookup
)
from .forms import JobScheduleDetailTempForm

class ScheduleNewItemsView(TemplateView):
    template_name = 'machinery_schedule/schedule_new_items.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get query parameters from URL
        item_id = self.request.GET.get('Item')
        wo_no = self.request.GET.get('WONo')
        schedule_type = self.request.GET.get('Type') # '0' or '1'
        
        # Simulating session variables
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not set
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not set
        
        # Store context for later use in forms/HTMX views
        self.request.session['current_item_id'] = item_id
        self.request.session['current_wo_no'] = wo_no
        self.request.session['current_schedule_type'] = schedule_type
        self.request.session['current_comp_id'] = comp_id
        self.request.session['current_fin_year_id'] = fin_year_id

        # 1. PrintItem() equivalent
        item_details = {}
        if item_id and wo_no:
            try:
                # Based on the ASP.NET code, this query is quite complex
                # We'll need a simplified lookup or a more robust BOM model
                # Assuming tblDG_BOM_Master directly contains ItemId and WONo
                # For `BOMTreeQty`, calling a static method on BomMaster
                bom_entry = BomMaster.objects.filter(
                    item_id=item_id,
                    wo_no=wo_no,
                    # comp_id=comp_id, # ASP.NET filter not present in BomMaster lookup
                    # fin_year_id__lte=fin_year_id # ASP.NET filter not present in BomMaster lookup
                ).first()
                
                if bom_entry:
                    item_master = ItemMaster.objects.get(item_id=bom_entry.item_id)
                    item_details['ItemCode'] = item_master.item_code
                    item_details['Description'] = item_master.manf_desc
                    item_details['UOM'] = item_master.uom_basic # Directly from item master in this simplified view
                    
                    # Call the BOMTreeQty function from the model
                    item_details['BomQty'] = BomMaster.get_bom_tree_qty(
                        wo_no=wo_no, p_id=bom_entry.p_id, c_id=bom_entry.c_id
                    )
                item_details['WoNo'] = wo_no
            except (ItemMaster.DoesNotExist, BomMaster.DoesNotExist):
                messages.error(self.request, "Item or Work Order details not found.")
        
        context['item_details'] = item_details
        
        # Initial form for adding new details (GridView1 equivalent)
        # Pass work_order_no and item_id to the form to populate batch_no and filter processes
        context['add_form'] = JobScheduleDetailTempForm(
            work_order_no=wo_no, 
            item_id=item_id
        )

        return context

class JobScheduleDetailTempTableHTMX(View):
    """
    HTMX endpoint to render the display grid (GridView2 equivalent).
    """
    def get(self, request, *args, **kwargs):
        comp_id = request.session.get('current_comp_id')
        session_id = request.session.session_key # Django's session ID
        item_id = request.session.get('current_item_id')

        # Filter temporary details for the current user session, company, and item
        temp_details = JobScheduleDetailTemp.objects.filter(
            comp_id=comp_id, 
            session_id=session_id,
            item_id=item_id
        ).order_by('id') # Ensure consistent order

        return render(request, 'machinery_schedule/_display_schedule_table.html', {
            'temp_schedule_details': temp_details
        })

class JobScheduleAddTempDetailHTMX(View):
    """
    HTMX endpoint to add a new schedule detail to temporary table (GridView1 "Add" button equivalent).
    """
    def post(self, request, *args, **kwargs):
        item_id = request.session.get('current_item_id')
        wo_no = request.session.get('current_wo_no')
        comp_id = request.session.get('current_comp_id')
        fin_year_id = request.session.get('current_fin_year_id')
        session_id = request.session.session_key # Django's session ID

        form = JobScheduleDetailTempForm(request.POST, work_order_no=wo_no, item_id=item_id)
        if form.is_valid():
            try:
                temp_detail = form.save(commit=False)
                # Manually assign fields not in form
                temp_detail.comp_id = comp_id
                temp_detail.session_id = session_id
                temp_detail.item_id = item_id
                temp_detail.machine_id = int(form.cleaned_data['machine']) # Ensure machine_id is int
                temp_detail.process = int(form.cleaned_data['process']) # Ensure process is int
                temp_detail.type = int(form.cleaned_data['type'])
                temp_detail.shift = int(form.cleaned_data['shift'])
                temp_detail.batch_no = float(form.cleaned_data['batch_no'])

                # Save with validation logic in model's save_to_temp_with_validation method
                temp_detail.save_to_temp_with_validation(comp_id, session_id, item_id)
                messages.success(request, 'Schedule item added temporarily.')
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshTempScheduleTable'}) # Signal HTMX to refresh
            except ValueError as e:
                messages.error(request, f"Validation Error: {e}")
            except Exception as e:
                messages.error(request, f"Error adding schedule: {e}")
        else:
            # Re-render the form with errors if validation fails
            messages.error(request, 'Please correct the errors below.')

        # If form is invalid or validation fails during save, render the form again with errors
        return render(request, 'machinery_schedule/_input_schedule_row.html', {
            'add_form': form
        })

class JobScheduleDeleteTempDetailHTMX(View):
    """
    HTMX endpoint to delete a temporary schedule detail (GridView2 "Delete" button equivalent).
    """
    def delete(self, request, pk, *args, **kwargs):
        comp_id = request.session.get('current_comp_id')
        session_id = request.session.session_key

        temp_detail = get_object_or_404(JobScheduleDetailTemp, pk=pk, comp_id=comp_id, session_id=session_id)
        temp_detail.delete()
        messages.success(request, 'Schedule item removed.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshTempScheduleTable'}) # Signal HTMX to refresh

class JobScheduleSubmitHTMX(View):
    """
    HTMX endpoint to finalize the schedule (btnSubmit_Click equivalent).
    """
    def post(self, request, *args, **kwargs):
        item_id = request.session.get('current_item_id')
        wo_no = request.session.get('current_wo_no')
        comp_id = request.session.get('current_comp_id')
        fin_year_id = request.session.get('current_fin_year_id')
        session_id = request.session.session_key

        temp_details = JobScheduleDetailTemp.objects.filter(
            comp_id=comp_id, 
            session_id=session_id,
            item_id=item_id
        )

        if not temp_details.exists():
            messages.warning(request, "No records found to proceed.")
            return HttpResponse(status=200) # Or 204 if you don't want to show a message via HX-Swap

        try:
            with transaction.atomic():
                # Generate JobNo
                last_job_no_str = JobScheduleMaster.objects.filter(
                    comp_id=comp_id, fin_year_id=fin_year_id
                ).aggregate(Max('job_no'))['job_no__max']
                
                if last_job_no_str:
                    next_job_no = int(last_job_no_str) + 1
                else:
                    next_job_no = 1
                job_no = f"{next_job_no:04d}" # Format to 4 digits (e.g., 0001)

                # Create JobScheduleMaster entry
                master_entry = JobScheduleMaster.objects.create(
                    sys_date=datetime.now().date(),
                    sys_time=datetime.now().time(),
                    session_id=session_id,
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    job_no=job_no,
                    wo_no=wo_no,
                    item_id=item_id,
                )

                # Transfer details from temp to permanent
                for temp_detail in temp_details:
                    JobScheduleDetail.objects.create(
                        master=master_entry.pk, # Link to the new master entry
                        shift=temp_detail.shift,
                        machine_id=temp_detail.machine_id,
                        type=temp_detail.type,
                        from_date=temp_detail.from_date,
                        to_date=temp_detail.to_date,
                        from_time=temp_detail.from_time,
                        to_time=temp_detail.to_time,
                        process=temp_detail.process,
                        qty=temp_detail.qty,
                        incharge=temp_detail.incharge,
                        operator=temp_detail.operator,
                        batch_no=temp_detail.batch_no,
                    )
                
                # Clear temporary details for this session/company/item
                temp_details.delete()

            messages.success(request, f"Job Schedule {job_no} submitted successfully!")
            
            # Redirect to the next page as per ASP.NET logic
            # This is a full page redirect, so no HX-Trigger is needed for current page refresh
            redirect_url = reverse_lazy('schedule_new_details') # Assuming this URL exists
            # Append query parameters as in ASP.NET
            redirect_url += f"?WONo={wo_no}&Type={request.session.get('current_schedule_type')}&Item={item_id}&ModId=15&SubModId=69"
            return HttpResponse(status=200, headers={'HX-Redirect': redirect_url}) # HTMX way to redirect
        except Exception as e:
            messages.error(request, f"Failed to submit job schedule: {e}")
            return HttpResponse(status=400) # Indicate an error

class JobScheduleCancelHTMX(View):
    """
    HTMX endpoint to cancel the schedule input (Btncancel_Click equivalent).
    """
    def post(self, request, *args, **kwargs):
        # Redirect logic from ASP.NET code
        wo_no = request.session.get('current_wo_no')
        item_id = request.session.get('current_item_id')
        schedule_type = request.session.get('current_schedule_type')
        
        redirect_url = reverse_lazy('schedule_new_details') # Assuming this URL exists
        redirect_url += f"?WONo={wo_no}&Type={schedule_type}&ModId=15&SubModId=69"
        
        # Clear temporary details for this session/company/item on cancel if needed (ASP.NET doesn't explicitly do this)
        comp_id = request.session.get('current_comp_id')
        session_id = request.session.session_key
        JobScheduleDetailTemp.objects.filter(comp_id=comp_id, session_id=session_id, item_id=item_id).delete()
        messages.info(request, "Job scheduling cancelled.")

        return HttpResponse(status=200, headers={'HX-Redirect': redirect_url})

class AutocompleteEmployeeHTMX(View):
    """
    HTMX endpoint for employee auto-completion (GetCompletionList equivalent).
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('prefixText', '')
        comp_id = request.session.get('current_comp_id')
        
        employees = Employee.objects.filter(
            employee_name__icontains=query,
            # CompId filter from ASP.NET is on tblHR_OfficeStaff, add if available on Employee model
            # For simplicity, assuming Employee model uses CompId, but it's not a field above.
            # If CompId is a field on Employee model, add: comp_id=comp_id
        ).values('emp_id', 'employee_name')[:10] # Limit results as per ASP.NET

        results = []
        for emp in employees:
            results.append(f"{emp['employee_name']} [{emp['emp_id']}]")
        
        # HTMX requires a simple string response for `hx-target` to be a dropdown/datalist
        # Or a partial HTML snippet if rendering suggestions directly
        # For simplicity with the provided text input, returning JSON which Alpine.js can handle.
        # If using a datalist, simply join results by newline or render a list.
        # ASP.NET returned a string array, so JSON is a good fit.
        return JsonResponse(results, safe=False)

class MachineSelectHTMX(View):
    """
    HTMX endpoint to dynamically update process dropdown and suggest from_time
    based on selected machine (DrpMachine_SelectedIndexChanged equivalent).
    """
    def post(self, request, *args, **kwargs):
        machine_id = request.POST.get('machine')
        item_id = request.session.get('current_item_id') # The main item being scheduled
        comp_id = request.session.get('current_comp_id')
        
        process_choices = [('', 'Select')]
        suggested_from_time = ''
        
        if machine_id and machine_id != 'Select':
            machine_id = int(machine_id)
            try:
                # Get processes for the selected machine
                machine_master = MachineMaster.objects.get(item_id=machine_id)
                # Filter processes based on MachineMaster.MId (which is machine_master.pk here)
                # And exclude processes already scheduled for the current item in temp table
                # The ASP.NET query was: "tblPln_Process_Master.Id not in (Select Process from tblMS_JobSchedule_Details_Temp where CompId='" + CompId + "' And ItemId='" + itemId + "')"
                scheduled_processes = JobScheduleDetailTemp.objects.filter(
                    machine_id=machine_id,
                    item_id=item_id,
                    comp_id=comp_id,
                ).values_list('process', flat=True)

                available_processes = ProcessMaster.objects.filter(
                    process_id__in=machine_master.get_related_process_ids(),
                ).exclude(process_id__in=scheduled_processes)

                process_choices.extend(list(available_processes.values_list('process_id', 'process_name')))
                
                # Logic for suggesting From Time
                # C# logic: look at last ToTime in tblMS_JobSchedule_Details_Temp then tblMS_JobSchedule_Details for the machine
                last_temp_schedule = JobScheduleDetailTemp.objects.filter(
                    machine_id=machine_id
                ).order_by('-to_date', '-to_time').first()

                last_permanent_schedule = JobScheduleDetail.objects.filter(
                    machine_id=machine_id
                ).order_by('-to_date', '-to_time').first()

                # Prioritize temp schedule
                last_schedule = last_temp_schedule if last_temp_schedule else last_permanent_schedule
                
                if last_schedule and last_schedule.to_time:
                    # ASP.NET `fun.TimeSelectorDatabase1` likely formats this.
                    # We will return it in HH:MM AM/PM format.
                    suggested_from_time = last_schedule.to_time.strftime('%I:%M %p') # e.g., 03:30 PM

            except MachineMaster.DoesNotExist:
                pass # No processes found for this machine
        
        # Render just the process dropdown and potentially update the From Time field
        # We need to send back HTML for the elements we want to replace
        # This will be a partial for the form fields
        add_form = JobScheduleDetailTempForm(
            initial={
                'machine': machine_id, 
                'process': '', 
                'from_time': suggested_from_time
            },
            work_order_no=request.session.get('current_wo_no'),
            item_id=item_id
        )
        # Manually set choices for process since it's dynamic
        add_form.fields['process'].choices = process_choices

        return render(request, 'machinery_schedule/_machine_select_update.html', {
            'form': add_form,
            'suggested_from_time': suggested_from_time,
        })
```

#### 4.4 Templates (`machinery_schedule/templates/machinery_schedule/`)

We'll define the main page and partials for HTMX.

`schedule_new_items.html` (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 class="text-2xl font-bold mb-4 style2 bg-blue-700 p-2 text-white rounded-t-lg">Job-Scheduling Input-New</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-semibold">Item Code:</span> <span class="font-bold">{{ item_details.ItemCode|default:"N/A" }}</span>
            </div>
            <div>
                <span class="font-semibold">UOM:</span> <span class="font-bold">{{ item_details.UOM|default:"N/A" }}</span>
                <span class="ml-8 font-semibold">BOM Qty:</span> <span class="font-bold">{{ item_details.BomQty|default:"N/A" }}</span>
            </div>
            <div>
                <span class="font-semibold">Description:</span> <span class="font-bold">{{ item_details.Description|default:"N/A" }}</span>
            </div>
            <div>
                <span class="font-semibold">WoNo:</span> <span class="font-bold">{{ item_details.WoNo|default:"N/A" }}</span>
            </div>
        </div>
    </div>

    <!-- Section for adding new schedule items (GridView1 equivalent) -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-xl font-semibold mb-4">Add New Schedule Item</h3>
        <div id="add-schedule-form-container" 
             hx-trigger="load, refreshAddForm from:body" 
             hx-get="{% url 'schedule_new_items_add_form_partial' %}"
             hx-swap="innerHTML">
            <!-- Initial form will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading input form...</p>
            </div>
        </div>
    </div>

    <!-- Section for existing temporary schedule items (GridView2 equivalent) -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-xl font-semibold mb-4">Current Schedule Entries</h3>
        <div id="temp-schedule-table-container"
             hx-trigger="load, refreshTempScheduleTable from:body"
             hx-get="{% url 'schedule_new_items_temp_table' %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading temporary schedule...</p>
            </div>
        </div>
    </div>

    <div class="flex justify-center space-x-4">
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md transition duration-300"
            hx-post="{% url 'schedule_new_items_submit' %}"
            hx-indicator="#submit-indicator"
            hx-confirm="Are you sure you want to finalize and submit all scheduled items?"
            hx-swap="none"
            >
            Submit
            <span id="submit-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
        </button>
        <button 
            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded-md transition duration-300"
            hx-post="{% url 'schedule_new_items_cancel' %}"
            hx-confirm="Are you sure you want to cancel? All unsaved temporary entries will be lost."
            hx-swap="none"
            >
            Cancel
        </button>
    </div>
</div>

<!-- Modal for delete confirmation (optional, could be in _display_schedule_table.html too) -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me"
     x-data="{ showModal: false }"
     x-show="showModal"
     @refresh-modal.window="showModal = true; $nextTick(() => { document.getElementById('modalContent').focus(); })"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0 scale-90"
     x-transition:enter-end="opacity-100 scale-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100 scale-100"
     x-transition:leave-end="opacity-0 scale-90">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full outline-none" tabindex="-1">
        <!-- Content loaded by HTMX will go here -->
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Initialize Alpine.js (if not already done by base.html)
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here unless for more complex UI state
    });

    // Datepicker initialization for elements with class 'datepicker'
    document.addEventListener('htmx:load', function(event) {
        // Flatpickr setup
        flatpickr(".datepicker", {
            dateFormat: "d-m-Y",
            allowInput: true,
            altInput: true,
            altFormat: "DD-MM-YYYY",
            onReady: function(selectedDates, dateStr, instance) {
                // For HTMX loaded content, ensure focus is set correctly
                if (event.detail.elt) {
                    const input = instance.input;
                    if (input.dataset.initialLoadFocus === 'true') {
                        input.focus();
                        input.select();
                        delete input.dataset.initialLoadFocus;
                    }
                }
            }
        });
        
        // Timepicker (using simple text input for now, enhance with a library if needed)
        // You might use an Alpine.js component here for a custom time picker or integrate a library
        // For input type="time", browsers often provide a UI. For HH:MM AM/PM, it's custom.
        // A simple pattern regex can be used for basic validation.
        flatpickr(".timepicker", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i K", // HH:MM AM/PM format
            altInput: true,
            altFormat: "hh:mm K", // Display HH:MM AM/PM
            time_24hr: false,
            minuteIncrement: 1,
            defaultDate: "09:00 AM", // A sensible default
            onReady: function(selectedDates, dateStr, instance) {
                if (event.detail.elt) {
                    const input = instance.input;
                    if (input.dataset.initialLoadFocus === 'true') {
                        input.focus();
                        input.select();
                        delete input.dataset.initialLoadFocus;
                    }
                }
            }
        });

        // Autocomplete setup (simple example, could be enhanced with actual dropdown/list)
        // This is handled by hx-target directly for now.
        // If results come back as JSON array, Alpine.js would process it.
        // Example for storing Employee ID for form submission:
        document.querySelectorAll('.autocomplete').forEach(input => {
            input.addEventListener('input', function() {
                this.dataset.employeeId = ''; // Clear stored ID on change
            });
            input.addEventListener('blur', function() {
                // When focus leaves, try to set the hidden ID
                const value = this.value;
                const match = value.match(/\[(.*?)\]$/);
                if (match) {
                    this.dataset.employeeId = match[1];
                }
            });
        });
    });

    // Listen for custom events to handle messages from Django
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.getResponseHeader('HX-Trigger')) {
            const triggers = JSON.parse(evt.detail.xhr.getResponseHeader('HX-Trigger'));
            if (triggers.messages) {
                triggers.messages.forEach(msg => {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = `p-3 rounded-md mb-3 ${msg.tags === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
                    alertDiv.textContent = msg.message;
                    document.getElementById('messages-container').appendChild(alertDiv);
                    setTimeout(() => alertDiv.remove(), 5000); // Remove after 5 seconds
                });
            }
        }
    });

    // Initialize DataTable after the table partial is loaded
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'temp-schedule-table-container') {
            $('#tempScheduleTable').DataTable({
                "pageLength": 8,
                "lengthMenu": [[8, 15, 25, -1], [8, 15, 25, "All"]]
            });
        }
    });
</script>
{% endblock %}
```

`_input_schedule_row.html` (Partial for adding new schedule item)

```html
<form hx-post="{% url 'schedule_new_items_add' %}" hx-swap="outerHTML" hx-target="#add-schedule-form-container">
    {% csrf_token %}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {% for field in add_form %}
        <div class="field-wrapper">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">{{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}</label>
            {% if field.name == 'process' %}
                <div id="process-time-container">
                    {{ field }}
                    {% if suggested_from_time %}
                        <input type="hidden" name="suggested_from_time" value="{{ suggested_from_time }}">
                        <script>
                            // Alpine.js or vanilla JS to update the from_time field
                            document.addEventListener('htmx:afterSwap', function(evt) {
                                const fromTimeInput = document.getElementById('id_from_time');
                                const suggestedTime = document.querySelector('input[name="suggested_from_time"]');
                                if (fromTimeInput && suggestedTime && suggestedTime.value) {
                                    fromTimeInput._flatpickr.setDate(suggestedTime.value, false); // For Flatpickr
                                    // Or for plain input: fromTimeInput.value = suggestedTime.value;
                                }
                            }, { once: true }); // Run once after this partial is swapped in
                        </script>
                    {% endif %}
                </div>
            {% elif field.name == 'incharge' or field.name == 'operator' %}
                <div class="relative">
                    {{ field }}
                    <div id="{{ field.name }}-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full mt-1">
                        <!-- Autocomplete suggestions will be loaded here -->
                    </div>
                </div>
            {% else %}
                {{ field }}
            {% endif %}
            {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
    </div>

    <div class="flex justify-center mt-6">
        <button type="submit" 
                class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-6 rounded-md transition duration-300"
                hx-indicator="#add-row-indicator">
            Add
            <span id="add-row-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
        </button>
    </div>
</form>
{% if add_form.non_field_errors %}
    <div class="text-red-500 text-sm mt-4">
        {{ add_form.non_field_errors }}
    </div>
{% endif %}
```

`_display_schedule_table.html` (Partial for displaying temporary schedule items)

```html
<table id="tempScheduleTable" class="min-w-full bg-white border border-gray-300 rounded-lg shadow-sm">
    <thead>
        <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <th class="py-3 px-4 border-b border-gray-200">SN</th>
            <th class="py-3 px-4 border-b border-gray-200">Machine Name</th>
            <th class="py-3 px-4 border-b border-gray-200">Process</th>
            <th class="py-3 px-4 border-b border-gray-200">Type</th>
            <th class="py-3 px-4 border-b border-gray-200">Shift</th>
            <th class="py-3 px-4 border-b border-gray-200">Batch No</th>
            <th class="py-3 px-4 border-b border-gray-200">Batch Qty</th>
            <th class="py-3 px-4 border-b border-gray-200">From Date</th>
            <th class="py-3 px-4 border-b border-gray-200">To Date</th>
            <th class="py-3 px-4 border-b border-gray-200">From Time</th>
            <th class="py-3 px-4 border-b border-gray-200">To Time</th>
            <th class="py-3 px-4 border-b border-gray-200">Incharge</th>
            <th class="py-3 px-4 border-b border-gray-200">Operator</th>
            <th class="py-3 px-4 border-b border-gray-200">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if temp_schedule_details %}
            {% for detail in temp_schedule_details %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.machine_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.process_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.type_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.shift_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.batch_no|floatformat:"0" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.qty|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.from_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.to_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.from_time|time:"h:i A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.to_time|time:"h:i A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.incharge_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.operator_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-300"
                        hx-delete="{% url 'schedule_new_items_delete' detail.pk %}"
                        hx-confirm="Are you sure you want to delete this schedule item?"
                        hx-swap="none">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="14" class="py-4 text-center text-gray-500 text-lg">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

`_machine_select_update.html` (Partial for machine selection response)

```html
{# This partial swaps into #process-time-container to update process dropdown and optionally set from_time #}
<div id="process-time-container">
    <label for="{{ form.process.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Process <span class="text-red-500">*</span></label>
    {{ form.process }}
    {% if form.process.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.process.errors }}</p>
    {% endif %}
    {% if suggested_from_time %}
        <input type="hidden" name="suggested_from_time" value="{{ suggested_from_time }}">
        <script>
            // Alpine.js or vanilla JS to update the from_time field
            document.addEventListener('htmx:afterSwap', function(evt) {
                const fromTimeInput = document.getElementById('id_from_time');
                const suggestedTime = document.querySelector('input[name="suggested_from_time"]');
                if (fromTimeInput && suggestedTime && suggestedTime.value) {
                    fromTimeInput._flatpickr.setDate(suggestedTime.value, false); // For Flatpickr
                }
            }, { once: true });
        </script>
    {% endif %}
</div>
```

#### 4.5 URLs (`machinery_schedule/urls.py`)

Define all the necessary URL patterns for the main page and HTMX endpoints.

```python
from django.urls import path
from .views import (
    ScheduleNewItemsView, 
    JobScheduleDetailTempTableHTMX, 
    JobScheduleAddTempDetailHTMX, 
    JobScheduleDeleteTempDetailHTMX,
    JobScheduleSubmitHTMX,
    JobScheduleCancelHTMX,
    AutocompleteEmployeeHTMX,
    MachineSelectHTMX
)
from .forms import JobScheduleDetailTempForm # Import form to pass to view for initial rendering

urlpatterns = [
    # Main page for job scheduling input
    path('schedule/new/', ScheduleNewItemsView.as_view(), name='schedule_new_items'),

    # HTMX endpoints for the input form (GridView1 equivalent)
    path('schedule/new/add/', JobScheduleAddTempDetailHTMX.as_view(), name='schedule_new_items_add'),
    path('schedule/new/add_form_partial/', lambda request: render(request, 'machinery_schedule/_input_schedule_row.html', {
        'add_form': JobScheduleDetailTempForm(
            work_order_no=request.session.get('current_wo_no'),
            item_id=request.session.get('current_item_id')
        )
    }), name='schedule_new_items_add_form_partial'),

    # HTMX endpoint for the temporary schedule display table (GridView2 equivalent)
    path('schedule/new/temp_table/', JobScheduleDetailTempTableHTMX.as_view(), name='schedule_new_items_temp_table'),
    path('schedule/new/delete/<int:pk>/', JobScheduleDeleteTempDetailHTMX.as_view(), name='schedule_new_items_delete'),

    # HTMX endpoints for global actions
    path('schedule/new/submit/', JobScheduleSubmitHTMX.as_view(), name='schedule_new_items_submit'),
    path('schedule/new/cancel/', JobScheduleCancelHTMX.as_view(), name='schedule_new_items_cancel'),

    # HTMX endpoints for dynamic form elements
    path('schedule/new/autocomplete_employee/', AutocompleteEmployeeHTMX.as_view(), name='autocomplete_employee'),
    path('schedule/new/machine_select/', MachineSelectHTMX.as_view(), name='machine_select'),
    
    # Placeholder for redirect target URL (assuming this exists)
    path('schedule/new/details/', TemplateView.as_view(template_name='machinery_schedule/schedule_new_details.html'), name='schedule_new_details'),
]

```

#### 4.6 Tests (`machinery_schedule/tests.py`)

Comprehensive tests for models, forms, and views to ensure functionality and data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, time, datetime
from unittest.mock import patch

from .models import (
    JobScheduleDetailTemp, JobScheduleMaster, JobScheduleDetail,
    ItemMaster, ProcessMaster, Employee, BomMaster, WorkOrderMaster,
    MachineMaster
)
from .forms import JobScheduleDetailTempForm

class JobScheduleModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary dependent data for tests
        ItemMaster.objects.create(item_id=1, manf_desc='Machine A', item_code='M001', uom_basic='PCS')
        ItemMaster.objects.create(item_id=2, manf_desc='Main Item X', item_code='X001', uom_basic='KGS')
        ProcessMaster.objects.create(process_id=101, process_name='Cutting', symbol='C')
        ProcessMaster.objects.create(process_id=102, process_name='Finishing', symbol='F')
        Employee.objects.create(emp_id='EMP001', employee_name='John Doe')
        Employee.objects.create(emp_id='EMP002', employee_name='Jane Smith')
        WorkOrderMaster.objects.create(wo_no='WO2023-001', batches=5)

        # Assuming MachineMaster links ItemMaster.item_id to processes
        cls.machine_a_master = MachineMaster.objects.create(item_id=1)
        # We need to mock or define how get_related_process_ids works
        with patch.object(MachineMaster, 'get_related_process_ids') as mock_method:
            mock_method.return_value = [101, 102]
            cls.machine_a_master.get_related_process_ids() # Call it once to register mock

        # Initial permanent job schedules for overlap checks
        JobScheduleDetail.objects.create(
            pk=1, machine_id=1, from_date=date(2024, 7, 10), from_time=time(9, 0),
            to_date=date(2024, 7, 10), to_time=time(17, 0), process=101, qty=10,
            incharge='EMP001', operator='EMP002', batch_no=1
        )
        JobScheduleDetail.objects.create(
            pk=2, machine_id=1, from_date=date(2024, 7, 11), from_time=time(9, 0),
            to_date=date(2024, 7, 11), to_time=time(17, 0), process=101, qty=10,
            incharge='EMP001', operator='EMP002', batch_no=1
        )
        
        # Patch BOMTreeQty for BomMaster
        with patch.object(BomMaster, 'get_bom_tree_qty') as mock_method:
            mock_method.return_value = 100.0


    def test_job_schedule_detail_temp_creation(self):
        temp_detail = JobScheduleDetailTemp.objects.create(
            comp_id=1, session_id='testsession', shift=0, type=0,
            from_date=date(2024, 7, 15), to_date=date(2024, 7, 15),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='EMP001', operator='EMP002', machine_id=1, item_id=2, batch_no=2
        )
        self.assertEqual(temp_detail.qty, 50.0)
        self.assertEqual(temp_detail.machine_name, 'Machine A')
        self.assertEqual(temp_detail.process_name, '[C] Cutting')
        self.assertEqual(temp_detail.type_display, 'Fresh')
        self.assertEqual(temp_detail.incharge_name, 'John Doe')

    def test_is_machine_available_no_overlap(self):
        from_dt = datetime(2024, 7, 12, 9, 0)
        to_dt = datetime(2024, 7, 12, 17, 0)
        self.assertTrue(JobScheduleDetail.is_machine_available(1, from_dt, to_dt))

    def test_is_machine_available_overlap_with_permanent(self):
        # Overlaps with the already created permanent schedule on 2024-07-10
        from_dt = datetime(2024, 7, 10, 10, 0)
        to_dt = datetime(2024, 7, 10, 11, 0)
        self.assertFalse(JobScheduleDetail.is_machine_available(1, from_dt, to_dt))

    def test_is_machine_available_overlap_with_temp(self):
        JobScheduleDetailTemp.objects.create(
            comp_id=1, session_id='another_session', shift=0, type=0,
            from_date=date(2024, 7, 13), to_date=date(2024, 7, 13),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='EMP001', operator='EMP002', machine_id=1, item_id=2, batch_no=3
        )
        from_dt = datetime(2024, 7, 13, 10, 0)
        to_dt = datetime(2024, 7, 13, 11, 0)
        self.assertFalse(JobScheduleDetail.is_machine_available(1, from_dt, to_dt))

    def test_save_to_temp_with_validation_success(self):
        temp_detail = JobScheduleDetailTemp(
            shift=0, type=0,
            from_date=date(2024, 7, 16), to_date=date(2024, 7, 16),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='EMP001', operator='EMP002', machine_id=1, batch_no=1
        )
        temp_detail.save_to_temp_with_validation(1, 'testsession', 2)
        self.assertIsNotNone(temp_detail.pk)

    def test_save_to_temp_with_validation_overlap_raises_error(self):
        # Overlaps with initial permanent schedule
        temp_detail = JobScheduleDetailTemp(
            shift=0, type=0,
            from_date=date(2024, 7, 10), to_date=date(2024, 7, 10),
            from_time=time(10, 0), to_time=time(11, 0), process=101, qty=10,
            incharge='EMP001', operator='EMP002', machine_id=1, batch_no=1
        )
        with self.assertRaises(ValueError) as cm:
            temp_detail.save_to_temp_with_validation(1, 'testsession', 2)
        self.assertIn("Machine is busy upto", str(cm.exception))


class JobScheduleFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        ItemMaster.objects.create(item_id=1, manf_desc='Machine Alpha', item_code='MA', uom_basic='PCS')
        ItemMaster.objects.create(item_id=2, manf_desc='Main Product', item_code='MP', uom_basic='EA')
        ProcessMaster.objects.create(process_id=101, process_name='Assembly', symbol='A')
        Employee.objects.create(emp_id='E001', employee_name='Alice')
        WorkOrderMaster.objects.create(wo_no='WO_TEST', batches=3)
        MachineMaster.objects.create(item_id=1) # Mock get_related_process_ids later

        with patch.object(MachineMaster, 'get_related_process_ids') as mock_method:
            mock_method.return_value = [101] # Machine Alpha can do Assembly
            MachineMaster.objects.get(item_id=1).get_related_process_ids()


    def test_form_initialization_with_work_order(self):
        form = JobScheduleDetailTempForm(work_order_no='WO_TEST', item_id=2)
        self.assertIn(('1', '1'), form.fields['batch_no'].choices)
        self.assertIn(('3', '3'), form.fields['batch_no'].choices)
        self.assertNotIn(('4', '4'), form.fields['batch_no'].choices)
        self.assertIn(('1', 'Machine Alpha'), form.fields['machine'].choices)

    def test_form_valid_data(self):
        data = {
            'machine': '1',
            'process': '101',
            'type': '0',
            'shift': '0',
            'batch_no': '1',
            'qty': 100.0,
            'from_date': '15-07-2024',
            'to_date': '15-07-2024',
            'from_time': '09:00 AM',
            'to_time': '17:00 PM',
            'incharge': 'Alice [E001]',
            'operator': 'Alice [E001]',
        }
        form = JobScheduleDetailTempForm(data, work_order_no='WO_TEST', item_id=2)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['incharge'], 'E001') # Ensure ID extracted
        self.assertEqual(form.cleaned_data['from_date'], date(2024, 7, 15))

    def test_form_invalid_dates(self):
        data = {
            'machine': '1', 'process': '101', 'type': '0', 'shift': '0', 'batch_no': '1', 'qty': 100.0,
            'from_date': '16-07-2024', 'to_date': '15-07-2024', # To date before From date
            'from_time': '09:00 AM', 'to_time': '17:00 PM',
            'incharge': 'Alice [E001]', 'operator': 'Alice [E001]',
        }
        form = JobScheduleDetailTempForm(data, work_order_no='WO_TEST', item_id=2)
        self.assertFalse(form.is_valid())
        self.assertIn("From Date/Time should be less than or equal to To Date/Time.", form.errors['__all__'][0])

    def test_form_invalid_times_same_day(self):
        data = {
            'machine': '1', 'process': '101', 'type': '0', 'shift': '0', 'batch_no': '1', 'qty': 100.0,
            'from_date': '15-07-2024', 'to_date': '15-07-2024',
            'from_time': '17:00 PM', 'to_time': '09:00 AM', # To time before From time
            'incharge': 'Alice [E001]', 'operator': 'Alice [E001]',
        }
        form = JobScheduleDetailTempForm(data, work_order_no='WO_TEST', item_id=2)
        self.assertFalse(form.is_valid())
        self.assertIn("From Time should be less than To Time when dates are the same.", form.errors['__all__'][0])

    def test_form_required_fields(self):
        form = JobScheduleDetailTempForm({}, work_order_no='WO_TEST', item_id=2)
        self.assertFalse(form.is_valid())
        self.assertIn('This field is required.', form.errors['machine'][0])
        self.assertIn('This field is required.', form.errors['qty'][0])


class JobScheduleViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session variables
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1
        self.client.session.save() # Needed for session_key
        
        # Create dependent data for views
        ItemMaster.objects.create(item_id=1, manf_desc='Machine Alpha', item_code='MA', uom_basic='PCS')
        ItemMaster.objects.create(item_id=2, manf_desc='Main Product X', item_code='MPX', uom_basic='KGS')
        ProcessMaster.objects.create(process_id=101, process_name='Assembly', symbol='A')
        ProcessMaster.objects.create(process_id=102, process_name='Welding', symbol='W')
        Employee.objects.create(emp_id='E001', employee_name='Alice')
        Employee.objects.create(emp_id='E002', employee_name='Bob')
        WorkOrderMaster.objects.create(wo_no='WO_TEST_VIEWS', batches=3)
        self.machine_master_1 = MachineMaster.objects.create(item_id=1)
        
        # Patch methods that depend on complex external logic or database joins
        with patch.object(MachineMaster, 'get_related_process_ids') as mock_get_processes:
            mock_get_processes.return_value = [101, 102] # Machine 1 can do process 101, 102
            self.machine_master_1.get_related_process_ids()

        with patch.object(BomMaster, 'get_bom_tree_qty') as mock_bom_qty:
            mock_bom_qty.return_value = 123.45

        with patch.object(JobScheduleDetail, 'is_machine_available') as mock_machine_available:
            mock_machine_available.return_value = True # Assume machine always available for tests

    def test_schedule_new_items_view_get(self):
        response = self.client.get(reverse('schedule_new_items'), {'Item': '2', 'WONo': 'WO_TEST_VIEWS'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_schedule/schedule_new_items.html')
        self.assertContains(response, 'Main Product X')
        self.assertContains(response, 'WO_TEST_VIEWS')
        self.assertContains(response, '123.45') # Mocked BOM Qty

    def test_job_schedule_add_temp_detail_htmx_post_success(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_comp_id'] = 1
        self.client.session['current_fin_year_id'] = 1
        self.client.session.save()

        data = {
            'machine': '1', 'process': '101', 'type': '0', 'shift': '0', 'batch_no': '1', 'qty': 50.0,
            'from_date': '17-07-2024', 'to_date': '17-07-2024',
            'from_time': '09:00 AM', 'to_time': '17:00 PM',
            'incharge': 'Alice [E001]', 'operator': 'Bob [E002]',
        }
        response = self.client.post(reverse('schedule_new_items_add'), data, HTTP_HX_REQUEST='true', HTTP_HX_TARGET='add-schedule-form-container')
        self.assertEqual(response.status_code, 204) # Success, no content
        self.assertEqual(JobScheduleDetailTemp.objects.count(), 1)
        self.assertIn('refreshTempScheduleTable', response.headers['HX-Trigger'])

    def test_job_schedule_add_temp_detail_htmx_post_invalid(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_comp_id'] = 1
        self.client.session['current_fin_year_id'] = 1
        self.client.session.save()

        data = { # Missing required fields
            'machine': '1', 'process': '101', 'type': '0', 'shift': '0', 'batch_no': '1', 'qty': '',
            'from_date': '', 'to_date': '', 'from_time': '', 'to_time': '',
            'incharge': 'Alice [E001]', 'operator': 'Bob [E002]',
        }
        response = self.client.post(reverse('schedule_new_items_add'), data, HTTP_HX_REQUEST='true', HTTP_HX_TARGET='add-schedule-form-container')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'machinery_schedule/_input_schedule_row.html')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(JobScheduleDetailTemp.objects.count(), 0)

    def test_job_schedule_detail_temp_table_htmx_get(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_comp_id'] = 1
        self.client.session.save()
        
        JobScheduleDetailTemp.objects.create(
            comp_id=1, session_id=self.client.session.session_key, shift=0, type=0,
            from_date=date(2024, 7, 17), to_date=date(2024, 7, 17),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='E001', operator='E002', machine_id=1, item_id=2, batch_no=1
        )
        response = self.client.get(reverse('schedule_new_items_temp_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_schedule/_display_schedule_table.html')
        self.assertContains(response, 'Machine Alpha')
        self.assertContains(response, '50.0')
        self.assertContains(response, 'Delete')

    def test_job_schedule_delete_temp_detail_htmx_delete(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_comp_id'] = 1
        self.client.session.save()
        
        temp_detail = JobScheduleDetailTemp.objects.create(
            comp_id=1, session_id=self.client.session.session_key, shift=0, type=0,
            from_date=date(2024, 7, 17), to_date=date(2024, 7, 17),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='E001', operator='E002', machine_id=1, item_id=2, batch_no=1
        )
        response = self.client.delete(reverse('schedule_new_items_delete', args=[temp_detail.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(JobScheduleDetailTemp.objects.count(), 0)
        self.assertIn('refreshTempScheduleTable', response.headers['HX-Trigger'])

    def test_job_schedule_submit_htmx_post_success(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_schedule_type'] = '0'
        self.client.session['current_comp_id'] = 1
        self.client.session['current_fin_year_id'] = 1
        self.client.session.save()
        
        JobScheduleDetailTemp.objects.create(
            comp_id=1, session_id=self.client.session.session_key, shift=0, type=0,
            from_date=date(2024, 7, 17), to_date=date(2024, 7, 17),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='E001', operator='E002', machine_id=1, item_id=2, batch_no=1
        )
        response = self.client.post(reverse('schedule_new_items_submit'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(JobScheduleMaster.objects.count(), 1)
        self.assertEqual(JobScheduleDetail.objects.count(), 1)
        self.assertEqual(JobScheduleDetailTemp.objects.count(), 0) # Temp cleared
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn('WONo=WO_TEST_VIEWS&Type=0&Item=2&ModId=15&SubModId=69', response.headers['HX-Redirect'])

    def test_job_schedule_submit_htmx_post_no_records(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_comp_id'] = 1
        self.client.session['current_fin_year_id'] = 1
        self.client.session.save()
        
        response = self.client.post(reverse('schedule_new_items_submit'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        # Check if messages framework was used to add a warning
        messages_received = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_received), 1)
        self.assertEqual(str(messages_received[0]), "No records found to proceed.")
        self.assertEqual(JobScheduleMaster.objects.count(), 0)
        self.assertEqual(JobScheduleDetail.objects.count(), 0)

    def test_job_schedule_cancel_htmx_post(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_schedule_type'] = '0'
        self.client.session['current_comp_id'] = 1
        self.client.session['current_fin_year_id'] = 1
        self.client.session.save()
        
        # Create a temp record to ensure it's cleared
        JobScheduleDetailTemp.objects.create(
            comp_id=1, session_id=self.client.session.session_key, shift=0, type=0,
            from_date=date(2024, 7, 17), to_date=date(2024, 7, 17),
            from_time=time(9, 0), to_time=time(17, 0), process=101, qty=50.0,
            incharge='E001', operator='E002', machine_id=1, item_id=2, batch_no=1
        )

        response = self.client.post(reverse('schedule_new_items_cancel'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn('WONo=WO_TEST_VIEWS&Type=0&ModId=15&SubModId=69', response.headers['HX-Redirect'])
        self.assertEqual(JobScheduleDetailTemp.objects.count(), 0) # Temp cleared

    def test_autocomplete_employee_htmx_get(self):
        self.client.session['current_comp_id'] = 1
        self.client.session.save()
        response = self.client.get(reverse('autocomplete_employee'), {'prefixText': 'al'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('Alice [E001]', json.loads(response.content.decode()))
        self.assertNotIn('Bob [E002]', json.loads(response.content.decode()))

    def test_machine_select_htmx_post(self):
        self.client.session['current_item_id'] = '2'
        self.client.session['current_wo_no'] = 'WO_TEST_VIEWS'
        self.client.session['current_comp_id'] = 1
        self.client.session.save()

        # Create a JobScheduleDetail record to test suggested_from_time
        JobScheduleDetail.objects.create(
            machine_id=1, from_date=date(2024, 7, 10), from_time=time(9, 0),
            to_date=date(2024, 7, 10), to_time=time(18, 30), process=101, qty=10,
            incharge='E001', operator='E002', batch_no=1
        )

        data = {'machine': '1'}
        response = self.client.post(reverse('machine_select'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_schedule/_machine_select_update.html')
        # Check if process options are updated
        self.assertContains(response, '<option value="101">Assembly</option>')
        self.assertContains(response, '<option value="102">Welding</option>')
        # Check if suggested from time is included (e.g., from the hidden input)
        self.assertContains(response, '<input type="hidden" name="suggested_from_time" value="06:30 PM">')

```