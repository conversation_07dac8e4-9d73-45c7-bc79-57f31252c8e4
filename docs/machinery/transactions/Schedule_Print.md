## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Analysis of Provided ASP.NET Code:

The provided ASP.NET code for `Schedule_Print.aspx` and its code-behind `Schedule_Print.aspx.cs` is exceptionally minimal. It primarily consists of boilerplate content placeholders in the `.aspx` file and an empty `Page_Load` method in the C# code-behind. This means there are no explicit database interactions, UI controls, or business logic visible in the provided snippets.

Given this lack of specific information, we will proceed by **inferring** a plausible `Schedule` module structure, as indicated by the file name `Schedule_Print`. This allows us to demonstrate a complete modernization process, focusing on best practices for a typical business application.

**Business Value of Modernization:**
Transitioning this (currently placeholder) ASP.NET page to a Django application offers significant business advantages:
*   **Future-Proofing:** Moving away from legacy ASP.NET Web Forms to a modern, actively developed framework like Django ensures long-term maintainability and access to contemporary features and security updates.
*   **Improved Agility:** Django's rapid development capabilities, coupled with HTMX and Alpine.js for frontend interactivity, enable quicker iteration and deployment of new features without complex JavaScript frameworks.
*   **Cost Efficiency:** Python's widespread adoption and Django's robust ecosystem reduce development time and skill acquisition costs compared to maintaining legacy systems.
*   **Scalability:** Django is inherently scalable, capable of handling increased user loads and data volumes as your business grows.
*   **Enhanced User Experience:** HTMX and Alpine.js provide a snappier, more interactive user interface that feels like a single-page application without the complexity, leading to better user satisfaction.

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code does not contain explicit database schema information, we will infer a common schema for a "Schedule" entity, which would typically be managed in an ERP system.

**Inferred Table Name:** `tbl_schedules`

**Inferred Columns:**
*   `schedule_id` (Primary Key, Integer)
*   `title` (String, e.g., Task Name, Meeting Title)
*   `description` (Text, detailed explanation)
*   `start_datetime` (DateTime, start of the schedule/event)
*   `end_datetime` (DateTime, end of the schedule/event)
*   `location` (String, e.g., 'Conference Room A', 'Remote', nullable)
*   `status` (String, e.g., 'Planned', 'InProgress', 'Completed', 'Canceled')
*   `created_at` (DateTime, timestamp for record creation)
*   `updated_at` (DateTime, timestamp for last modification)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Based on the inferred `Schedule` entity, we will assume standard CRUD (Create, Read, Update, Delete) operations are required for managing schedules.

*   **Create (Add New Schedule):** A user can add a new schedule entry by providing a title, description, start/end times, location, and initial status.
*   **Read (View Schedules):** A user can view a list of all schedules, likely paginated and searchable, with options to view details.
*   **Update (Edit Schedule):** A user can modify existing schedule details.
*   **Delete (Remove Schedule):** A user can remove a schedule entry.
*   **Validation Logic:** Basic validation for required fields, ensuring `end_datetime` is not before `start_datetime`.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Given the inferred backend functionality, we envision the following UI components:

*   **Schedule List View:**
    *   A table (similar to ASP.NET GridView) displaying `title`, `start_datetime`, `end_datetime`, `location`, and `status`.
    *   Action buttons/links for "Edit" and "Delete" for each schedule entry.
    *   A "Add New Schedule" button to open a form for creation.
    *   Client-side search, sort, and pagination via DataTables.
*   **Schedule Form (Add/Edit):**
    *   Text inputs for `title`, `location`.
    *   Textarea for `description`.
    *   DateTime pickers for `start_datetime` and `end_datetime`.
    *   Dropdown list for `status`.
    *   "Save" and "Cancel" buttons.
    *   This form will be loaded dynamically into a modal using HTMX.
*   **Delete Confirmation Dialog:**
    *   A simple modal with a confirmation message and "Confirm Delete" / "Cancel" buttons.

---

## Step 4: Generate Django Code

### 4.1 Models (`machinery_transactions/models.py`)

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The `Schedule` model maps to the `tbl_schedules` table. It includes methods for business logic, making it a 'fat model'.

```python
from django.db import models
from django.utils import timezone

class Schedule(models.Model):
    # Primary key, assuming auto-increment in the existing database
    id = models.AutoField(db_column='schedule_id', primary_key=True)
    title = models.CharField(max_length=255, db_column='title', verbose_name='Schedule Title')
    description = models.TextField(db_column='description', blank=True, verbose_name='Details')
    start_datetime = models.DateTimeField(db_column='start_datetime', verbose_name='Start Time')
    end_datetime = models.DateTimeField(db_column='end_datetime', verbose_name='End Time')
    location = models.CharField(max_length=255, db_column='location', blank=True, null=True, verbose_name='Location')
    status = models.CharField(
        max_length=50,
        db_column='status',
        choices=[
            ('Planned', 'Planned'),
            ('InProgress', 'In Progress'),
            ('Completed', 'Completed'),
            ('Canceled', 'Canceled'),
        ],
        default='Planned',
        verbose_name='Status'
    )
    created_at = models.DateTimeField(db_column='created_at', auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(db_column='updated_at', auto_now=True, verbose_name='Updated At')

    class Meta:
        managed = False  # Set to True if Django manages the schema
        db_table = 'tbl_schedules'
        verbose_name = 'Schedule'
        verbose_name_plural = 'Schedules'
        ordering = ['start_datetime']

    def __str__(self):
        return f"{self.title} ({self.start_datetime.strftime('%Y-%m-%d %H:%M')})"

    def save(self, *args, **kwargs):
        """
        Custom save method to ensure timestamps are handled correctly if
        auto_now_add and auto_now are not fully trusted for existing DB.
        This is typically handled by auto_now_add/auto_now.
        """
        if not self.pk:
            self.created_at = timezone.now()
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)

    def is_past_due(self):
        """Business logic: Check if the schedule has ended and is not completed."""
        return self.end_datetime < timezone.now() and self.status not in ['Completed', 'Canceled']

    def duration_minutes(self):
        """Business logic: Calculate the duration of the schedule in minutes."""
        if self.start_datetime and self.end_datetime:
            return (self.end_datetime - self.start_datetime).total_seconds() / 60
        return 0
```

### 4.2 Forms (`machinery_transactions/forms.py`)

**Task:** Define a Django form for user input for `Schedule`.

**Instructions:**
A `ModelForm` is used, with custom widgets to apply Tailwind CSS classes and a custom clean method for validation.

```python
from django import forms
from .models import Schedule
from django.core.exceptions import ValidationError

class ScheduleForm(forms.ModelForm):
    class Meta:
        model = Schedule
        fields = ['title', 'description', 'start_datetime', 'end_datetime', 'location', 'status']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'start_datetime': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'end_datetime': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'title': 'Schedule Title',
            'description': 'Details',
            'start_datetime': 'Start Time',
            'end_datetime': 'End Time',
            'location': 'Location',
            'status': 'Status',
        }

    def clean(self):
        """
        Custom validation to ensure end_datetime is not before start_datetime.
        """
        cleaned_data = super().clean()
        start_datetime = cleaned_data.get('start_datetime')
        end_datetime = cleaned_data.get('end_datetime')

        if start_datetime and end_datetime and end_datetime < start_datetime:
            raise ValidationError(
                {'end_datetime': "End time cannot be before start time."}
            )
        return cleaned_data

```

### 4.3 Views (`machinery_transactions/views.py`)

**Task:** Implement CRUD operations using CBVs and a partial view for DataTables.

**Instructions:**
Views are kept thin, delegating business logic and complex validation to the model and form. HTMX headers are handled for dynamic responses.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Schedule
from .forms import ScheduleForm

class ScheduleListView(ListView):
    model = Schedule
    template_name = 'machinery_transactions/schedule/list.html'
    context_object_name = 'schedules'

class ScheduleTablePartialView(ListView):
    model = Schedule
    template_name = 'machinery_transactions/schedule/_schedule_table.html'
    context_object_name = 'schedules' # Use plural for list context

    def get_queryset(self):
        # Example of applying basic filtering if needed, though DataTables handles it client-side
        return Schedule.objects.all().order_by('start_datetime')


class ScheduleCreateView(CreateView):
    model = Schedule
    form_class = ScheduleForm
    template_name = 'machinery_transactions/schedule/_schedule_form.html' # Use partial template for modal
    success_url = reverse_lazy('schedule_list') # Fallback, HTMX usually handles the refresh

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Schedule added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX request: return 204 No Content and trigger a refresh on the client
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScheduleList' # Custom HTMX event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        # HTMX will swap the form itself, so just render the form template again
        return self.render_to_response(self.get_context_data(form=form))


class ScheduleUpdateView(UpdateView):
    model = Schedule
    form_class = ScheduleForm
    template_name = 'machinery_transactions/schedule/_schedule_form.html' # Use partial template for modal
    success_url = reverse_lazy('schedule_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Schedule updated successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX request: return 204 No Content and trigger a refresh on the client
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScheduleList'
                }
            )
        return response

    def form_invalid(self, form):
        # HTMX will swap the form itself, so just render the form template again
        return self.render_to_response(self.get_context_data(form=form))


class ScheduleDeleteView(DeleteView):
    model = Schedule
    template_name = 'machinery_transactions/schedule/_schedule_confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('schedule_list') # Fallback

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Schedule deleted successfully.')
        if request.headers.get('HX-Request'):
            # HTMX request: return 204 No Content and trigger a refresh on the client
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScheduleList'
                }
            )
        return response

    def get(self, request, *args, **kwargs):
        # For HTMX requests, just render the partial template
        return self.render_to_response(self.get_context_data())

```

### 4.4 Templates (`machinery_transactions/schedule/`)

**Task:** Create templates for each view, leveraging HTMX and Alpine.js for dynamic interactions.

**Instructions:**
Templates follow DRY principles by extending `core/base.html` and using partials for modal content.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Schedules{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Schedules Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'schedule_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Schedule
        </button>
    </div>

    {# Success/Error messages from Django messages framework #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} p-3 mb-4 rounded-md shadow-sm {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% endif %}" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <div id="scheduleTable-container"
         hx-trigger="load, refreshScheduleList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'schedule_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading schedules...</p>
        </div>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Example Alpine.js component usage if more complex UI state is needed
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });

    // Custom event listener to close modal after HTMX form submission
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { // Status 204 is sent for successful HTMX operations
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // For Alpine or simple JS closing
                modal.classList.add('hidden'); // For Tailwind hidden class
            }
        }
    });

    // Optional: DataTables re-initialization on HTMX swap
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.target.id === 'scheduleTable-container') {
            $('#scheduleTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance before re-initialization
                "responsive": true
            });
        }
    });
</script>
{% endblock %}
```

#### `_schedule_table.html` (Partial for HTMX)

```html
<div class="overflow-x-auto">
    <table id="scheduleTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in schedules %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.title }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.start_datetime|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.end_datetime|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.location|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        {% if obj.status == 'Completed' %}bg-green-100 text-green-800
                        {% elif obj.status == 'Canceled' %}bg-red-100 text-red-800
                        {% elif obj.status == 'InProgress' %}bg-blue-100 text-blue-800
                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'schedule_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md transition duration-300 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'schedule_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No schedules found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization for the loaded content
    // This script will re-run every time the partial is swapped in.
    // The htmx:afterOnLoad event in list.html manages this more robustly.
    // However, if loading this partial standalone or without the parent list.html,
    // this direct initialization would be necessary. For full page reloads,
    // the document.ready in base.html would handle it.
    // For HTMX, we need to ensure this is run *after* the content is in the DOM.
    // The list.html `htmx:afterOnLoad` listener handles this for the main table.
</script>
```

#### `_schedule_form.html` (Partial for HTMX Add/Edit)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {% if form.instance.pk %}Edit Schedule{% else %}Add New Schedule{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}

        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors|first }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Schedule
            </button>
        </div>
    </form>
    {# Optional loading indicator #}
    <div id="form-indicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        <p class="mt-2 text-white ml-3">Saving...</p>
    </div>
</div>
```

#### `_schedule_confirm_delete.html` (Partial for HTMX Delete)

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the schedule: <strong class="text-blue-600">{{ schedule.title }}</strong>?</p>
    <p class="text-red-500 mb-6 font-medium">This action cannot be undone.</p>

    <form hx-post="{% url 'schedule_delete' schedule.pk %}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out mr-4"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            type="submit"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
            Confirm Delete
        </button>
    </form>
    {# Optional loading indicator #}
    <div id="delete-indicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-50 flex items-center justify-center z-50">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        <p class="mt-2 text-white ml-3">Deleting...</p>
    </div>
</div>
```

### 4.5 URLs (`machinery_transactions/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are structured for clarity and maintainability, including specific endpoints for HTMX partials.

```python
from django.urls import path
from .views import (
    ScheduleListView,
    ScheduleTablePartialView, # New view for HTMX partial
    ScheduleCreateView,
    ScheduleUpdateView,
    ScheduleDeleteView
)

urlpatterns = [
    # Main list page
    path('schedule/', ScheduleListView.as_view(), name='schedule_list'),

    # HTMX partial endpoint for the table content (for refreshing)
    path('schedule/table/', ScheduleTablePartialView.as_view(), name='schedule_table'),

    # CRUD operations, accessible via HTMX for modal forms
    path('schedule/add/', ScheduleCreateView.as_view(), name='schedule_add'),
    path('schedule/edit/<int:pk>/', ScheduleUpdateView.as_view(), name='schedule_edit'),
    path('schedule/delete/<int:pk>/', ScheduleDeleteView.as_view(), name='schedule_delete'),
]

```

### 4.6 Tests (`machinery_transactions/tests.py`)

**Task:** Write comprehensive tests for the model and views.

**Instructions:**
Unit tests for the `Schedule` model cover field properties and custom methods. Integration tests use `django.test.Client` to simulate HTTP requests, including HTMX-specific headers, to verify view behavior and template usage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Schedule
from .forms import ScheduleForm
import datetime

class ScheduleModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.schedule1 = Schedule.objects.create(
            title='Team Meeting',
            description='Discuss Q1 Performance',
            start_datetime=timezone.now() + datetime.timedelta(days=1, hours=9),
            end_datetime=timezone.now() + datetime.timedelta(days=1, hours=10),
            location='Conference Room A',
            status='Planned'
        )
        cls.schedule2 = Schedule.objects.create(
            title='Project Alpha Review',
            description='Final review before launch',
            start_datetime=timezone.now() - datetime.timedelta(days=5, hours=14),
            end_datetime=timezone.now() - datetime.timedelta(days=5, hours=15),
            location='Online',
            status='Completed'
        )
        cls.schedule3 = Schedule.objects.create(
            title='Cancelled Event',
            description='Event that was canceled',
            start_datetime=timezone.now() + datetime.timedelta(days=10),
            end_datetime=timezone.now() + datetime.timedelta(days=10, hours=1),
            location='Office',
            status='Canceled'
        )
        cls.schedule4_past_due = Schedule.objects.create(
            title='Overdue Task',
            description='This task was not completed on time.',
            start_datetime=timezone.now() - datetime.timedelta(days=2),
            end_datetime=timezone.now() - datetime.timedelta(days=1),
            location='Remote',
            status='InProgress' # Not completed or canceled
        )

    def test_schedule_creation(self):
        obj = Schedule.objects.get(id=self.schedule1.id)
        self.assertEqual(obj.title, 'Team Meeting')
        self.assertEqual(obj.location, 'Conference Room A')
        self.assertEqual(obj.status, 'Planned')
        self.assertIsNotNone(obj.created_at)
        self.assertIsNotNone(obj.updated_at)
        self.assertLessEqual(obj.created_at, timezone.now())
        self.assertLessEqual(obj.updated_at, timezone.now())

    def test_title_label(self):
        field_label = self.schedule1._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'Schedule Title')

    def test_description_label(self):
        field_label = self.schedule1._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Details')

    def test_str_representation(self):
        expected_str = f"{self.schedule1.title} ({self.schedule1.start_datetime.strftime('%Y-%m-%d %H:%M')})"
        self.assertEqual(str(self.schedule1), expected_str)

    def test_is_past_due_method(self):
        self.assertFalse(self.schedule1.is_past_due()) # Future schedule
        self.assertFalse(self.schedule2.is_past_due()) # Completed schedule
        self.assertFalse(self.schedule3.is_past_due()) # Canceled schedule
        self.assertTrue(self.schedule4_past_due.is_past_due()) # Past due and in progress

    def test_duration_minutes_method(self):
        # Calculate duration for schedule1, should be 60 minutes (1 hour)
        duration = self.schedule1.duration_minutes()
        self.assertEqual(duration, 60.0)

        # Test with no end_datetime (though model makes it non-nullable)
        # If it were nullable: self.schedule1.end_datetime = None -> 0
        schedule_no_duration = Schedule(title="No End Time", start_datetime=timezone.now())
        schedule_no_duration.start_datetime = None # Simulate case where start or end might be null
        self.assertEqual(schedule_no_duration.duration_minutes(), 0)


class ScheduleViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views testing
        cls.schedule = Schedule.objects.create(
            title='Test Schedule',
            description='Description for test schedule',
            start_datetime=timezone.now() + datetime.timedelta(days=2),
            end_datetime=timezone.now() + datetime.timedelta(days=2, hours=1),
            location='Test Location',
            status='Planned'
        )
        cls.htmx_headers = {'HTTP_HX_REQUEST': 'true'}

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('schedule_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/list.html')
        self.assertContains(response, 'Schedules Management') # Check for title
        # Initial list view will load a spinner, not the actual table content directly
        self.assertContains(response, 'Loading schedules...')

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('schedule_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/_schedule_table.html')
        self.assertTrue('schedules' in response.context)
        self.assertContains(response, self.schedule.title) # Ensure schedule is in the table

    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('schedule_add'), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/_schedule_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add New Schedule')

    def test_create_view_post_htmx_success(self):
        new_title = 'New Event'
        data = {
            'title': new_title,
            'description': 'Description for new event',
            'start_datetime': (timezone.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%dT%H:%M'),
            'end_datetime': (timezone.now() + datetime.timedelta(days=3, hours=2)).strftime('%Y-%m-%dT%H:%M'),
            'location': 'Online',
            'status': 'Planned',
        }
        response = self.client.post(reverse('schedule_add'), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshScheduleList')
        self.assertTrue(Schedule.objects.filter(title=new_title).exists())

    def test_create_view_post_htmx_invalid(self):
        initial_count = Schedule.objects.count()
        data = {
            'title': '', # Invalid: missing title
            'description': 'Description',
            'start_datetime': (timezone.now() + datetime.timedelta(days=3)).strftime('%Y-%m-%dT%H:%M'),
            'end_datetime': (timezone.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%dT%H:%M'), # Invalid: end before start
            'location': 'Location',
            'status': 'Planned',
        }
        response = self.client.post(reverse('schedule_add'), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/_schedule_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End time cannot be before start time.')
        self.assertEqual(Schedule.objects.count(), initial_count) # No new object created

    def test_update_view_get_htmx(self):
        response = self.client.get(reverse('schedule_edit', args=[self.schedule.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/_schedule_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.title, self.schedule.title)
        self.assertContains(response, 'Edit Schedule')

    def test_update_view_post_htmx_success(self):
        updated_title = 'Updated Test Schedule'
        data = {
            'title': updated_title,
            'description': self.schedule.description,
            'start_datetime': self.schedule.start_datetime.strftime('%Y-%m-%dT%H:%M'),
            'end_datetime': self.schedule.end_datetime.strftime('%Y-%m-%dT%H:%M'),
            'location': self.schedule.location,
            'status': 'InProgress',
        }
        response = self.client.post(reverse('schedule_edit', args=[self.schedule.pk]), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshScheduleList')
        self.schedule.refresh_from_db()
        self.assertEqual(self.schedule.title, updated_title)
        self.assertEqual(self.schedule.status, 'InProgress')

    def test_update_view_post_htmx_invalid(self):
        original_title = self.schedule.title
        data = {
            'title': '', # Invalid
            'description': self.schedule.description,
            'start_datetime': self.schedule.start_datetime.strftime('%Y-%m-%dT%H:%M'),
            'end_datetime': (self.schedule.start_datetime - datetime.timedelta(days=1)).strftime('%Y-%m-%dT%H:%M'), # Invalid
            'location': self.schedule.location,
            'status': self.schedule.status,
        }
        response = self.client.post(reverse('schedule_edit', args=[self.schedule.pk]), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/_schedule_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End time cannot be before start time.')
        self.schedule.refresh_from_db()
        self.assertEqual(self.schedule.title, original_title) # Title should not have changed

    def test_delete_view_get_htmx(self):
        response = self.client.get(reverse('schedule_delete', args=[self.schedule.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_transactions/schedule/_schedule_confirm_delete.html')
        self.assertTrue('schedule' in response.context)
        self.assertEqual(response.context['schedule'], self.schedule)
        self.assertContains(response, f'delete the schedule: {self.schedule.title}')

    def test_delete_view_post_htmx_success(self):
        schedule_to_delete = Schedule.objects.create(
            title='Temporary Delete',
            description='This will be deleted',
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + datetime.timedelta(hours=1),
            location='Test',
            status='Planned'
        )
        initial_count = Schedule.objects.count()
        response = self.client.post(reverse('schedule_delete', args=[schedule_to_delete.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshScheduleList')
        self.assertFalse(Schedule.objects.filter(pk=schedule_to_delete.pk).exists())
        self.assertEqual(Schedule.objects.count(), initial_count - 1)

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views adhere to the following:

*   **HTMX for Dynamic Updates:**
    *   `list.html` uses `hx-get` on `scheduleTable-container` with `hx-trigger="load, refreshScheduleList from:body"` to load and refresh the DataTables content.
    *   Buttons for "Add New Schedule", "Edit", and "Delete" use `hx-get` to fetch the respective forms/confirmations into the `#modalContent` target.
    *   Form submissions in `_schedule_form.html` and `_schedule_confirm_delete.html` use `hx-post` with `hx-swap="none"` (as the view returns 204 No Content for success) and `hx-indicator` for loading feedback.
    *   Views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshScheduleList'})` on successful CRUD operations, triggering the `scheduleTable-container` to reload.

*   **Alpine.js for UI State Management:**
    *   The `#modal` element in `list.html` uses a simple `_` (hyperscript) attribute for showing/hiding the modal, responding to clicks on the button (`add .is-active to #modal`) and clicks outside the modal (`on click if event.target.id == 'modal' remove .is-active from me`). This keeps the modal logic simple and tied directly to the DOM without verbose JavaScript.
    *   An `htmx:afterSwap` event listener is added in `list.html` to automatically close the modal upon a successful HTMX form submission (indicated by HTTP status 204).

*   **DataTables for List Views:**
    *   The `_schedule_table.html` partial contains the HTML structure for the DataTables table.
    *   A JavaScript block within `list.html` (in `extra_js` and specifically attached to `htmx:afterOnLoad` for the `scheduleTable-container`) re-initializes DataTables after the table content is loaded via HTMX, ensuring proper functionality (search, sort, pagination) even with dynamic content updates. The `destroy: true` option is crucial for re-initialization.

*   **No Full Page Reloads:** All CRUD operations and list refreshes are handled dynamically using HTMX, providing a smooth, SPA-like user experience without complex client-side routing.

## Final Notes

*   This modernization plan provides a complete, runnable Django application for a conceptual `Schedule` module, based on the inferences from the ASP.NET file name.
*   All placeholders like `[APP_NAME]` have been replaced with `machinery_transactions` based on the ASP.NET namespace.
*   The `managed = False` in the `Schedule` model's `Meta` class indicates that Django will not attempt to create or modify the `tbl_schedules` table in the database; it assumes the table already exists. This is critical for migrating existing data.
*   The provided CSS classes assume Tailwind CSS is configured in your Django project's `core/base.html`.
*   Remember to add `machinery_transactions` to your `INSTALLED_APPS` in `settings.py` and include its `urls.py` in your project's main `urls.py` for the application to be accessible.