## ASP.NET to Django Conversion Script: Modernization Plan for Preventive/Breakdown Maintenance Edit

This document outlines a comprehensive plan to modernize the ASP.NET `PMBM_Edit_Details.aspx` application to a robust Django-based solution. Our approach emphasizes AI-assisted automation, adhering to the "Fat Model, Thin View" philosophy, and leveraging modern frontend technologies like HTMX and Alpine.js for dynamic, efficient user interfaces.

### Business Value Proposition

Migrating this critical "Preventive/Breakdown Maintenance Edit" functionality to Django offers several key business benefits:

1.  **Enhanced Maintainability & Scalability:** Django's structured framework and Python's readability will significantly reduce the complexity of future updates and allow the application to handle increased data volumes and user traffic more efficiently.
2.  **Improved User Experience:** By adopting HTMX and Alpine.js, the application will provide a highly responsive interface with partial page updates, eliminating full page reloads for common actions like form submissions and data table interactions. This leads to a snappier and more modern feel.
3.  **Cost Efficiency:** Leveraging open-source technologies like Django, PostgreSQL (recommended database), HTMX, and Alpine.js reduces licensing costs associated with proprietary Microsoft technologies. The standardized approach also streamlines development and reduces debugging time.
4.  **Future-Proofing:** Moving away from legacy ASP.NET ensures the application remains compatible with modern web standards and security practices, extending its operational lifespan and reducing technical debt.
5.  **Simplified Development:** The use of conversational AI to guide this migration process, along with clear, modular Django components, simplifies development and allows for faster iteration cycles.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several tables. Based on `SqlDataAdapter` fills and `fun.select` statements, we identify the following:

*   **`tblMS_PMBM_Master`**: This is the primary table for the maintenance record itself.
    *   Columns: `Id` (PK), `MachineId` (FK), `PMBM` (Maintenance Type, likely int: 0=Preventive, 1=Breakdown), `FromDate`, `ToDate`, `FromTime`, `ToTime`, `NameOfAgency`, `NameOfEngineer`, `NextPMDueOn`, `Remarks`, `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`.
*   **`tblMS_PMBM_Details`**: Stores the spare parts used for a specific maintenance record.
    *   Columns: `Id` (PK), `MId` (FK to `tblMS_PMBM_Master`), `SpareId` (FK), `Qty` (Availed Quantity).
*   **`tblMS_Master`**: Represents the machine itself.
    *   Columns: `Id` (PK), `ItemId` (FK), `Model`, `Make`, `Capacity`, `Location`, `CompId`, `FinYearId`.
*   **`tblDG_Item_Master`**: Details about general items/spares.
    *   Columns: `Id` (PK), `ItemCode`, `ManfDesc` (Description/Name), `UOMBasic` (FK to `Unit_Master`), `StockQty`, `CId` (FK to `tblDG_Category_Master`), `CompId`.
*   **`Unit_Master`**: Defines units of measurement.
    *   Columns: `Id` (PK), `Symbol`.
*   **`tblMS_Spares`**: Seems to define required spares per machine.
    *   Columns: `Id` (PK), `MId` (FK to `tblMS_Master`), `ItemId` (FK to `tblDG_Item_Master`), `Qty` (Required Quantity).

**Inferred Data Types:**
*   `Id`, `MachineId`, `MId`, `SpareId`, `ItemId`, `UOMBasic`, `CId`, `CompId`, `FinYearId`: Integer (PKs/FKs)
*   `PMBM`: Integer (Boolean-like or choice)
*   `FromDate`, `ToDate`, `NextPMDueOn`, `SysDate`: Date
*   `FromTime`, `ToTime`, `SysTime`: Time
*   `NameOfAgency`, `NameOfEngineer`, `Remarks`, `SessionId`, `Model`, `Make`, `Capacity`, `Location`, `ItemCode`, `ManfDesc`, `Symbol`: String/Text
*   `Qty`, `StockQty`: Decimal/Float

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**

*   **Read (R):**
    *   `Page_Load`: This method is responsible for fetching and displaying existing data.
        *   Retrieves `PMBMId` from query string.
        *   Fetches `MachineId` from `tblMS_PMBM_Master` using `PMBMId`.
        *   Fetches `ItemId` from `tblMS_Master` using `MachineId`.
        *   Retrieves machine details (`Model`, `Make`, `Capacity`, `Location`) from `tblMS_Master`.
        *   Retrieves item details (`ItemCode`, `UOMBasic`, `ManfDesc`) from `tblDG_Item_Master` and `Unit_Master`.
        *   Retrieves main maintenance details (`FromDate`, `ToDate`, `NameOfAgency`, `NameOfEngineer`, `NextPMDueOn`, `Remarks`, `PMBM`, `FromTime`, `ToTime`) from `tblMS_PMBM_Master`.
        *   Calls `LoadDataSpareMaster()` to populate the `GridView5`.
    *   `LoadDataSpareMaster()`:
        *   Fetches all required spares for the `itemId` (machine's primary item) from `tblMS_Spares`.
        *   For each required spare, fetches its `ItemCode`, `ManfDesc`, `UOMBasic`, `StockQty` from `tblDG_Item_Master` and `Unit_Master`.
        *   Then, it checks `tblMS_PMBM_Details` to see if a specific spare has already been *availed* for the current `PMBMId`, and populates `txtQty` and `chkSpare` accordingly.

*   **Update (U):**
    *   `btnProceed_Click`: This is the core update logic.
        *   Updates the `tblMS_PMBM_Master` record with new `FromDate`, `ToDate`, `FromTime`, `ToTime`, `NameOfAgency`, `NameOfEngineer`, `NextPMDueOn`, `Remarks`, `PMBM`, `SysDate`, `SysTime`, `SessionId`.
        *   Iterates through `GridView5` (spare parts).
            *   **Upsert (`tblMS_PMBM_Details`):** If `chkSpare` is checked and `txtQty` has valid numeric input:
                *   Checks if a record for the `SpareId` already exists in `tblMS_PMBM_Details` for the current `PMBMId`.
                *   If exists, updates the `Qty`.
                *   If not exists, inserts a new record with `MId`, `SpareId`, `Qty`.
            *   **Delete (`tblMS_PMBM_Details`):** If `chkSpare` is *unchecked* but a record for that `SpareId` *exists* in `tblMS_PMBM_Details` for the current `PMBMId`, the record is deleted.

*   **Delete (D):**
    *   Implicit deletion of `tblMS_PMBM_Details` records if a spare was previously selected but is now unchecked. No explicit full record deletion for `PMBM_Master` is shown on this page.

*   **Validation:**
    *   Date format validation (`RegularExpressionValidator`).
    *   Required field validation (`RequiredFieldValidator`).
    *   Numeric validation for `txtQty` (`RegularExpressionValidator`).
    *   Server-side validation in `btnProceed_Click` checks `txtFromDate`, `txtToDate`, `txtNameOfAgency`, `txtNameOfEngineer`, `txtNextPMDueOn`, `txtRemarks` for emptiness and valid dates.
    *   Server-side validation also checks `txtQty` for numeric validity if `chkSpare` is checked.
    *   `ValidateTextBox()` client-side visibility control for `ReqQty`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Main Form (Machine & Maintenance Details):**
    *   **Labels:** `lblItemCode`, `lblUOM`, `lblName`, `lblModel`, `lblMake`, `lblCapacity`, `lblLocation` – Display machine details. These are read-only.
    *   **Dropdown:** `DDLMaintenance` – For `PMBM` type (Preventive/Breakdown).
    *   **Text Boxes:** `txtFromDate`, `txtToDate`, `txtNextPMDueOn` (with CalendarExtender) – Date inputs. `txtNameOfAgency`, `txtNameOfEngineer`, `txtRemarks` – Text inputs.
    *   **Time Selectors:** `TSFromTime`, `TSToTime` – Time inputs.

*   **Spare Parts Grid (`GridView5`):**
    *   Displays a list of potential spare parts.
    *   **Columns:** `SN` (Row index), `Item Code`, `Description`, `Unit`, `Stock Qty`, `Required Qty`. These are display-only labels.
    *   **Interactive Elements:**
        *   `chkSpare` (CheckBox): To select if a spare was availed.
        *   `txtQty` (TextBox): To input the "Availed Qty" for selected spares.
    *   **Hidden Field:** `lblId` – Holds the `SpareId` for each row, essential for backend processing.

*   **Buttons:**
    *   `btnProceed` (Text: "Update"): Triggers the update logic.
    *   `BtnCancel` (Text: "Cancel"): Redirects to the list page.

**Mapping to Django Components:**

*   **Labels:** Rendered directly from model object attributes in templates.
*   **Dropdown:** `forms.ChoiceField` or `forms.ModelChoiceField` in a Django form, rendered with `forms.Select`.
*   **Text Boxes:** `forms.DateField`, `forms.CharField` in a Django form, rendered with `forms.TextInput` widgets. Date pickers will be integrated via Alpine.js or a simple JS library.
*   **Time Selectors:** `forms.TimeField` in a Django form, rendered with `forms.TextInput` widgets. Custom formatting or Alpine.js component will handle time selection.
*   **Spare Parts Grid:** This is the most complex part. It will be a `ModelFormSet` for `MaintenanceSpareUsed` records. The `MachineSpareRequirement` model will provide the baseline data for the grid (Item details, required quantity, stock quantity). The `txtQty` and `chkSpare` will correspond to fields in the formset. The grid itself will be rendered as a table in the template, powered by DataTables for presentation. HTMX will be used for dynamic loading and interaction of the grid.
*   **Buttons:** Standard HTML buttons with `hx-post`, `hx-get`, `hx-swap`, `hx-trigger` attributes for HTMX-driven interactions.

### Step 4: Generate Django Code

We will create a Django application named `machinery` to house these components.

#### 4.1 Models (`machinery/models.py`)

```python
from django.db import models
from django.utils import timezone

class Unit(models.Model):
    """
    Corresponds to tbl_Unit_Master.
    Defines units of measurement.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Item(models.Model):
    """
    Corresponds to tblDG_Item_Master.
    Details about general items/spares.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, unique=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic')
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=10, decimal_places=2, default=0)
    cid = models.IntegerField(db_column='CId') # Assuming CId is a simple integer, could be FK to CategoryMaster
    comp_id = models.IntegerField(db_column='CompId', default=0) # Company ID from session

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class Machine(models.Model):
    """
    Corresponds to tblMS_Master.
    Represents the machine itself.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId') # Link to its general item definition
    model = models.CharField(db_column='Model', max_length=100, blank=True, null=True)
    make = models.CharField(db_column='Make', max_length=100, blank=True, null=True)
    capacity = models.CharField(db_column='Capacity', max_length=100, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', default=0) # Company ID from session
    fin_year_id = models.IntegerField(db_column='FinYearId', default=0) # Financial Year ID from session

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'

    def __str__(self):
        return f"{self.item.manf_desc} ({self.item.item_code})"

class MaintenanceRecord(models.Model):
    """
    Corresponds to tblMS_PMBM_Master.
    Stores the preventive/breakdown maintenance records.
    """
    MAINTENANCE_TYPES = (
        (0, 'Preventive'),
        (1, 'Breakdown'),
    )

    id = models.IntegerField(db_column='Id', primary_key=True)
    machine = models.ForeignKey(Machine, models.DO_NOTHING, db_column='MachineId')
    pmbm = models.IntegerField(db_column='PMBM', choices=MAINTENANCE_TYPES, default=0)
    from_date = models.DateField(db_column='FromDate')
    to_date = models.DateField(db_column='ToDate')
    from_time = models.TimeField(db_column='FromTime')
    to_time = models.TimeField(db_column='ToTime')
    name_of_agency = models.CharField(db_column='NameOfAgency', max_length=255)
    name_of_engineer = models.CharField(db_column='NameOfEngineer', max_length=255)
    next_pm_due_on = models.DateField(db_column='NextPMDueOn')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # User ID/Session ID
    comp_id = models.IntegerField(db_column='CompId', default=0) # Company ID from session
    fin_year_id = models.IntegerField(db_column='FinYearId', default=0) # Financial Year ID from session

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Master'
        verbose_name = 'Maintenance Record'
        verbose_name_plural = 'Maintenance Records'

    def __str__(self):
        return f"Maintenance for {self.machine.item.manf_desc} (ID: {self.id})"

    def get_machine_details(self):
        """
        Retrieves comprehensive machine details for display.
        This demonstrates business logic within the model.
        """
        machine = self.machine
        item = machine.item
        return {
            'item_code': item.item_code,
            'uom': item.uom_basic.symbol,
            'name': item.manf_desc,
            'model': machine.model,
            'make': machine.make,
            'capacity': machine.capacity,
            'location': machine.location,
            'stock_qty': item.stock_qty,
        }

    def get_required_spares(self):
        """
        Retrieves all spares typically associated with this machine,
        along with their required quantities.
        Corresponds to the first part of LoadDataSpareMaster.
        """
        return MachineSpareRequirement.objects.filter(machine=self.machine).select_related('item', 'item__uom_basic')

class MaintenanceSpareUsed(models.Model):
    """
    Corresponds to tblMS_PMBM_Details.
    Stores the spare parts *actually used* for a specific maintenance record.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    maintenance_record = models.ForeignKey(MaintenanceRecord, models.DO_NOTHING, db_column='MId')
    spare_item = models.ForeignKey(Item, models.DO_NOTHING, db_column='SpareId')
    qty = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Details'
        verbose_name = 'Maintenance Spare Used'
        verbose_name_plural = 'Maintenance Spares Used'
        unique_together = (('maintenance_record', 'spare_item'),) # Ensure a spare is listed once per record

    def __str__(self):
        return f"{self.spare_item.manf_desc} ({self.qty})"

    def get_full_details(self):
        """
        Combines spare usage data with master item details.
        """
        return {
            'id': self.id,
            'item_code': self.spare_item.item_code,
            'description': self.spare_item.manf_desc,
            'unit': self.spare_item.uom_basic.symbol,
            'stock_qty': self.spare_item.stock_qty,
            'availed_qty': self.qty,
            'is_selected': True, # For UI convenience
        }

class MachineSpareRequirement(models.Model):
    """
    Corresponds to tblMS_Spares.
    Defines general spare requirements for a machine type.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    machine = models.ForeignKey(Machine, models.DO_NOTHING, db_column='MId')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2) # Required Qty

    class Meta:
        managed = False
        db_table = 'tblMS_Spares'
        verbose_name = 'Machine Spare Requirement'
        verbose_name_plural = 'Machine Spare Requirements'

    def __str__(self):
        return f"{self.item.manf_desc} required for {self.machine.item.manf_desc}"

```

#### 4.2 Forms (`machinery/forms.py`)

```python
from django import forms
from django.forms import inlineformset_factory
from .models import MaintenanceRecord, MaintenanceSpareUsed, Item
import datetime

class MaintenanceRecordForm(forms.ModelForm):
    """
    Form for the main maintenance record details.
    """
    from_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'x-data': '', # Alpine.js hook
            'x-ref': 'from_date_input' # Alpine.js ref for datepicker init
        }),
        input_formats=['%d-%m-%Y'], # Allow dd-MM-yyyy format
        error_messages={'invalid': 'Enter date in dd-MM-yyyy format.'}
    )
    to_date = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'x-data': '',
            'x-ref': 'to_date_input'
        }),
        input_formats=['%d-%m-%Y'],
        error_messages={'invalid': 'Enter date in dd-MM-yyyy format.'}
    )
    next_pm_due_on = forms.DateField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
            'x-data': '',
            'x-ref': 'next_pm_due_on_input'
        }),
        input_formats=['%d-%m-%Y'],
        error_messages={'invalid': 'Enter date in dd-MM-yyyy format.'}
    )
    from_time = forms.TimeField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm timepicker',
            'placeholder': 'HH:MM AM/PM'
        }),
        input_formats=['%H:%M %p', '%H:%M'], # Allow 12hr and 24hr formats
        error_messages={'invalid': 'Enter time in HH:MM AM/PM format.'}
    )
    to_time = forms.TimeField(
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm timepicker',
            'placeholder': 'HH:MM AM/PM'
        }),
        input_formats=['%H:%M %p', '%H:%M'],
        error_messages={'invalid': 'Enter time in HH:MM AM/PM format.'}
    )

    class Meta:
        model = MaintenanceRecord
        fields = [
            'pmbm', 'from_date', 'to_date', 'from_time', 'to_time',
            'name_of_agency', 'name_of_engineer', 'next_pm_due_on', 'remarks'
        ]
        widgets = {
            'pmbm': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'name_of_agency': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Name of Agency'}),
            'name_of_engineer': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Name of Engineer'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20', 'rows': 3, 'placeholder': 'Remarks'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        
        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', 'To Date cannot be earlier than From Date.')
        
        # Add similar logic for time validation if needed (e.g., to_time after from_time on same day)
        # Note: Original ASP.NET had individual date/time validation but not cross-field
        return cleaned_data

class MaintenanceSpareUsedForm(forms.ModelForm):
    """
    Form for a single spare part used in maintenance.
    Used within a formset.
    """
    # These fields are for display only, not directly part of the form's data
    item_code = forms.CharField(required=False, widget=forms.HiddenInput())
    description = forms.CharField(required=False, widget=forms.HiddenInput())
    unit = forms.CharField(required=False, widget=forms.HiddenInput())
    stock_qty = forms.DecimalField(required=False, widget=forms.HiddenInput())
    required_qty = forms.DecimalField(required=False, widget=forms.HiddenInput()) # From MachineSpareRequirement

    # This field is the actual input for the availed quantity
    qty = forms.DecimalField(
        required=False, # Make it optional if checkbox is unchecked
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'box3 block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Availed Qty',
            'x-model': 'item.availedQty', # Alpine.js binding for reactivity
            'x-bind:disabled': '!item.isSelected' # Disable if not selected
        })
    )
    # Checkbox to indicate if this spare is selected/used
    is_selected = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-checkbox h-5 w-5 text-indigo-600',
            'x-model': 'item.isSelected', # Alpine.js binding for reactivity
            'x-on:change': 'if (!item.isSelected) { item.availedQty = ""; }' # Clear qty if unchecked
        })
    )
    
    # Hidden field to store the actual Item ID (SpareId in DB)
    spare_item_id = forms.IntegerField(widget=forms.HiddenInput())

    class Meta:
        model = MaintenanceSpareUsed
        fields = ['id', 'qty', 'spare_item'] # 'id' for existing records, 'qty' for input, 'spare_item' for saving FK
        # Note: 'spare_item' from the model needs to be handled via `spare_item_id` in `save()`
        
    def __init__(self, *args, **kwargs):
        # Initial data for display fields, used for rendering the grid
        self.item_data = kwargs.pop('item_data', None)
        super().__init__(*args, **kwargs)

        if self.item_data:
            # Populate fields for display in the template
            self.fields['item_code'].initial = self.item_data.get('item_code')
            self.fields['description'].initial = self.item_data.get('description')
            self.fields['unit'].initial = self.item_data.get('unit')
            self.fields['stock_qty'].initial = self.item_data.get('stock_qty')
            self.fields['required_qty'].initial = self.item_data.get('required_qty')
            self.fields['spare_item_id'].initial = self.item_data.get('spare_item_id')
            self.fields['is_selected'].initial = self.item_data.get('is_selected', False)
            self.fields['qty'].initial = self.item_data.get('availed_qty', '') # Availed Qty


    def clean(self):
        cleaned_data = super().clean()
        is_selected = cleaned_data.get('is_selected')
        qty = cleaned_data.get('qty')

        # Custom validation: If selected, quantity must be provided and valid
        if is_selected and (qty is None or qty == ''):
            self.add_error('qty', 'Required.')
        elif is_selected and qty is not None and qty <= 0:
            self.add_error('qty', 'Must be a positive number.')

        return cleaned_data

    def save(self, commit=True):
        # Override save to handle the spare_item_id and is_selected logic
        instance = super().save(commit=False)
        is_selected = self.cleaned_data.get('is_selected')
        qty = self.cleaned_data.get('qty')
        spare_item_id = self.cleaned_data.get('spare_item_id')

        if is_selected and qty is not None and qty > 0:
            # Spare is selected and quantity is valid, create or update
            instance.spare_item = Item.objects.get(id=spare_item_id)
            instance.qty = qty
            if commit:
                instance.save()
            return instance
        elif instance.pk and not is_selected:
            # Spare was previously selected but now unchecked, delete it
            if commit:
                instance.delete()
            return None # Indicate deletion
        return None # If not selected or invalid qty, and no existing record, do nothing.

# Formset for handling multiple spare parts
MaintenanceSpareUsedFormSet = inlineformset_factory(
    MaintenanceRecord,
    MaintenanceSpareUsed,
    form=MaintenanceSpareUsedForm,
    extra=0, # No extra blank forms by default
    can_delete=False, # Deletion handled by custom save logic of individual form
    fields=['qty'] # These are the only actual fields to be saved to DB
)

```

#### 4.3 Views (`machinery/views.py`)

```python
from django.views.generic import UpdateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction
from django.forms import formset_factory

from .models import MaintenanceRecord, MaintenanceSpareUsed, MachineSpareRequirement, Item
from .forms import MaintenanceRecordForm, MaintenanceSpareUsedFormSet, MaintenanceSpareUsedForm

class MaintenanceRecordListView(ListView):
    """
    Displays a list of all maintenance records.
    (This view is not explicitly shown in ASP.NET but is implied as the navigation target).
    """
    model = MaintenanceRecord
    template_name = 'machinery/maintenancerecord/list.html'
    context_object_name = 'maintenance_records'

    def get_queryset(self):
        # Assuming comp_id and fin_year_id from session/user context
        # For demonstration, use a placeholder, in real app these would be from request.user
        comp_id = 1 # self.request.session.get('compid', 1)
        fin_year_id = 1 # self.request.session.get('finyear', 1)
        return MaintenanceRecord.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).select_related('machine', 'machine__item')

class MaintenanceRecordTablePartialView(MaintenanceRecordListView):
    """
    Renders only the table content for HTMX requests.
    """
    template_name = 'machinery/maintenancerecord/_maintenance_record_table.html'

class MaintenanceRecordEditView(UpdateView):
    """
    Handles the editing of a single maintenance record and its associated spare parts.
    This corresponds to the main functionality of the ASP.NET page.
    """
    model = MaintenanceRecord
    form_class = MaintenanceRecordForm
    template_name = 'machinery/maintenancerecord/form.html' # Main form template, rendered as partial
    success_url = reverse_lazy('maintenance_record_list') # Redirects to list page on full page load

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        maintenance_record = self.get_object() # The MaintenanceRecord instance being edited

        # Add machine details to context, similar to ASP.NET labels
        context['machine_details'] = maintenance_record.get_machine_details()

        # Prepare initial data for the spare parts formset
        # This combines required spares with currently availed spares, similar to LoadDataSpareMaster
        required_spares = maintenance_record.get_required_spares()
        availed_spares_map = {
            detail.spare_item_id: detail for detail in MaintenanceSpareUsed.objects.filter(maintenance_record=maintenance_record)
        }

        initial_spare_data = []
        for req_spare in required_spares:
            item = req_spare.item
            availed_detail = availed_spares_map.get(item.id)
            
            initial_spare_data.append({
                'id': availed_detail.id if availed_detail else None, # PK for existing availed spare
                'item_code': item.item_code,
                'description': item.manf_desc,
                'unit': item.uom_basic.symbol,
                'stock_qty': item.stock_qty,
                'required_qty': req_spare.qty, # Quantity from tblMS_Spares
                'spare_item_id': item.id, # The actual FK to Item model
                'qty': availed_detail.qty if availed_detail else '', # Availed Quantity
                'is_selected': bool(availed_detail), # Checkbox state
            })

        # Create the formset, passing initial data to individual forms via `item_data`
        # Using a custom formset to pass extra data
        MaintenanceSpareUsedFormSet = formset_factory(
            MaintenanceSpareUsedForm,
            extra=0,
            can_delete=False, # Handled by custom form save
            fields=['qty']
        )
        # Pass `form_kwargs` to pass additional context to each form instance
        # This will set the `item_data` on each form to be used in its __init__
        context['spare_formset'] = MaintenanceSpareUsedFormSet(
            initial=[{'item_data': data} for data in initial_spare_data],
            prefix='spares_formset'
        )

        return context

    def form_valid(self, form):
        maintenance_record = form.save(commit=False)
        # Assign session/system related fields
        maintenance_record.sys_date = timezone.now().date()
        maintenance_record.sys_time = timezone.now().time()
        maintenance_record.session_id = self.request.session.get('username', 'system_user') # Assuming username in session
        maintenance_record.comp_id = self.request.session.get('compid', 1)
        maintenance_record.fin_year_id = self.request.session.get('finyear', 1)
        
        # Save the main record first to ensure it has a PK if it's a new record (though this is UpdateView)
        # For UpdateView, maintenance_record already has a PK.
        
        # Process the spare parts formset
        # We need to manually initialize the formset with POST data and current instance
        # The formset needs to be bound to the instance for its save() method to work correctly
        # The formset is passed the original MaintenanceRecord instance.
        
        # Manually create the formset with post data and initial data to reconstruct state
        formset = formset_factory(
            MaintenanceSpareUsedForm,
            extra=0,
            can_delete=False,
            fields=['qty']
        )(self.request.POST, prefix='spares_formset')
        
        # Manually populate item_data for each form in the formset for validation/display
        # This is crucial because formset won't automatically know about the hidden display fields
        # Re-fetch the initial_spare_data structure to properly initialize formset forms
        required_spares = maintenance_record.get_required_spares()
        availed_spares_map = {
            detail.spare_item_id: detail for detail in MaintenanceSpareUsed.objects.filter(maintenance_record=maintenance_record)
        }
        initial_spare_data = []
        for req_spare in required_spares:
            item = req_spare.item
            availed_detail = availed_spares_map.get(item.id)
            initial_spare_data.append({
                'id': availed_detail.id if availed_detail else None,
                'item_code': item.item_code,
                'description': item.manf_desc,
                'unit': item.uom_basic.symbol,
                'stock_qty': item.stock_qty,
                'required_qty': req_spare.qty,
                'spare_item_id': item.id,
                'qty': availed_detail.qty if availed_detail else '',
                'is_selected': bool(availed_detail),
            })

        # Update each form instance with its item_data
        for i, form_in_formset in enumerate(formset.forms):
            # Only set item_data if it exists (i.e. if there are enough forms in formset)
            if i < len(initial_spare_data):
                form_in_formset.item_data = initial_spare_data[i]
                form_in_formset.fields['is_selected'].initial = initial_spare_data[i]['is_selected']
                form_in_formset.fields['qty'].initial = initial_spare_data[i]['qty']
                form_in_formset.fields['spare_item_id'].initial = initial_spare_data[i]['spare_item_id']

        if formset.is_valid():
            with transaction.atomic():
                # Save the main maintenance record
                maintenance_record.save()
                
                # Process the spare parts formset
                for spare_form in formset:
                    if spare_form.cleaned_data.get('spare_item_id'): # Ensure it's a valid spare row
                        # Manually assign the FK for MaintenanceSpareUsed
                        instance = spare_form.save(commit=False)
                        if instance: # If the form didn't result in deletion
                            instance.maintenance_record = maintenance_record
                            instance.save()
            
            messages.success(self.request, 'Maintenance Record updated successfully.')
            
            # HTMX response for modal closing and list refresh
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # No Content
                    headers={
                        'HX-Trigger': 'refreshMaintenanceRecordList, closeModal'
                    }
                )
            return super().form_valid(form) # Fallback for non-HTMX requests
        else:
            # Formset is invalid, re-render the form with errors
            # Reconstruct the context data as in get_context_data
            context = self.get_context_data()
            context['form'] = form
            context['spare_formset'] = formset # Pass the bound formset with errors
            
            if self.request.headers.get('HX-Request'):
                return render(self.request, self.template_name, context)
            return self.render_to_response(context)

    def form_invalid(self, form):
        # Handle invalid main form
        context = self.get_context_data()
        context['form'] = form # Pass the form with errors
        
        # Ensure spare_formset is also part of context if needed
        # It's usually empty for invalid main form if formset processing happens after
        # For our case, we need to manually process spare formset in form_valid/invalid
        # This will render errors from main form, and then we'll process spare formset in form_valid
        
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, context)
        return super().form_invalid(form)


# Helper view to load the form directly via HTMX into a modal
class MaintenanceRecordEditModalView(MaintenanceRecordEditView):
    template_name = 'machinery/maintenancerecord/_maintenance_record_form.html' # Use partial template

```

#### 4.4 Templates (`machinery/templates/machinery/maintenancerecord/`)

**`list.html`** (Main list page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Maintenance Records</h2>
        <!-- No direct add button on this specific page, as it's an edit page from ASP.NET.
             If there was an "Add New" on the ASP.NET parent list, it would go here. -->
    </div>
    
    <!-- Messages (Django messages framework) -->
    {% if messages %}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 rounded-md {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="maintenanceRecordTable-container"
         hx-trigger="load, refreshMaintenanceRecordList from:body"
         hx-get="{% url 'maintenance_record_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Maintenance Records...</p>
        </div>
    </div>
    
    <!-- Modal for form and delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false"
         x-on:add-modal.window="showModal = true"
         x-init="$watch('$el.classList.contains(\'is-active\')', value => showModal = value)">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-auto"
             @click.away="showModal = false"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            <!-- Content loaded here by HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('formModal', () => ({
            showModal: false,
            // Logic for datepickers can be added here if needed, or via external JS
            init() {
                // Initialize flatpickr for date inputs
                // For HTMX loaded content, you might need to re-initialize after swap
                // This is a global listener for htmx:afterSwap to re-init
                document.body.addEventListener('htmx:afterSwap', function(event) {
                    if (event.detail.target.id === 'modalContent') {
                        // Re-initialize datepickers if the modal was swapped
                        if (typeof flatpickr !== 'undefined') {
                            flatpickr(".datepicker", {
                                dateFormat: "d-m-Y",
                                altInput: true,
                                altFormat: "d-m-Y",
                                allowInput: true,
                            });
                        }
                         // Re-initialize timepickers
                        if (typeof tempusDominus !== 'undefined') {
                            document.querySelectorAll('.timepicker').forEach(el => {
                                new tempusDominus.TempusDominus(el, {
                                    display: {
                                        components: {
                                            date: false,
                                            month: false,
                                            year: false,
                                            clock: true,
                                            hours: true,
                                            minutes: true,
                                            seconds: true,
                                            useTwentyfourHour: false,
                                        },
                                        buttons: {
                                            today: false,
                                            clear: false,
                                            close: false,
                                        }
                                    },
                                    localization: {
                                        locale: 'en',
                                        format: 'HH:mm A'
                                    }
                                });
                            });
                        }
                    }
                });

                // HTMX events to toggle modal
                this.$el.addEventListener('htmx:beforeSwap', (evt) => {
                    if (evt.detail.xhr.status === 204) {
                        this.showModal = false; // Close modal on 204 (success)
                        document.dispatchEvent(new CustomEvent('close-modal')); // Trigger Alpine event
                    } else if (evt.detail.requestConfig.verb === 'get' && evt.detail.target.id === 'modalContent') {
                        this.showModal = true; // Open modal when content is loaded
                        document.dispatchEvent(new CustomEvent('add-modal')); // Trigger Alpine event
                    }
                });
            }
        }));
    });
</script>
<!-- Flatpickr (Date picker) and Tempus Dominus (Time picker) CDN -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/tempus-dominus@6.7.13/dist/js/tempus-dominus.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tempus-dominus@6.7.13/dist/css/tempus-dominus.min.css">
{% endblock %}
```

**`_maintenance_record_table.html`** (Partial for DataTables)

```html
<table id="maintenanceRecordTable" class="min-w-full bg-white table-auto">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engineer</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for record in maintenance_records %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ record.machine.item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ record.machine.item.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ record.get_pmbm_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ record.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ record.to_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ record.name_of_engineer }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'maintenance_record_edit' record.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-gray-500">No maintenance records found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#maintenanceRecordTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "ordering": true, // Enable sorting
        "searching": true, // Enable search box
        "paging": true // Enable pagination
    });
});
</script>
```

**`_maintenance_record_form.html`** (Partial for the Edit form)

```html
<div class="p-6" x-data="{
    spareItems: {{ spare_formset.initial|json_script:"initialSpareItems"|safe }},
    init() {
        this.spareItems = JSON.parse(document.getElementById('initialSpareItems').textContent);
        // Initialize datepickers if not already done by global event
        if (typeof flatpickr !== 'undefined') {
            flatpickr(".datepicker", {
                dateFormat: "d-m-Y",
                altInput: true,
                altFormat: "d-m-Y",
                allowInput: true,
            });
        }
        if (typeof tempusDominus !== 'undefined') {
            document.querySelectorAll('.timepicker').forEach(el => {
                new tempusDominus.TempusDominus(el, {
                    display: {
                        components: {
                            date: false,
                            month: false,
                            year: false,
                            clock: true,
                            hours: true,
                            minutes: true,
                            seconds: true,
                            useTwentyfourHour: false,
                        },
                        buttons: {
                            today: false,
                            clear: false,
                            close: false,
                        }
                    },
                    localization: {
                        locale: 'en',
                        format: 'HH:mm A'
                    }
                });
            });
        }
    }
}">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Maintenance Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-6">
            <!-- Machine Details -->
            <div class="bg-gray-100 p-4 rounded-md shadow-sm">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Machine Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Machine Code:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.item_code }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">UOM:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.uom }}</p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Name:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Model:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.model }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Make:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.make }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Capacity:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.capacity }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Location:</label>
                        <p class="mt-1 text-sm text-gray-900 font-bold">{{ machine_details.location }}</p>
                    </div>
                </div>
            </div>

            <!-- Maintenance Details Form Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.pmbm.id_for_label }}" class="block text-sm font-medium text-gray-700">Maintenance Type</label>
                    {{ form.pmbm }}
                    {% if form.pmbm.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pmbm.errors }}</p>{% endif %}
                </div>
                <div></div> {# Spacer for layout #}
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.from_time.id_for_label }}" class="block text-sm font-medium text-gray-700">From Time</label>
                    {{ form.from_time }}
                    {% if form.from_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_time.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.to_time.id_for_label }}" class="block text-sm font-medium text-gray-700">To Time</label>
                    {{ form.to_time }}
                    {% if form.to_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_time.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.name_of_agency.id_for_label }}" class="block text-sm font-medium text-gray-700">Name of Agency</label>
                    {{ form.name_of_agency }}
                    {% if form.name_of_agency.errors %}<p class="text-red-500 text-xs mt-1">{{ form.name_of_agency.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.name_of_engineer.id_for_label }}" class="block text-sm font-medium text-gray-700">Name of Engineer</label>
                    {{ form.name_of_engineer }}
                    {% if form.name_of_engineer.errors %}<p class="text-red-500 text-xs mt-1">{{ form.name_of_engineer.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.next_pm_due_on.id_for_label }}" class="block text-sm font-medium text-gray-700">Next PM Due On</label>
                    {{ form.next_pm_due_on }}
                    {% if form.next_pm_due_on.errors %}<p class="text-red-500 text-xs mt-1">{{ form.next_pm_due_on.errors }}</p>{% endif %}
                </div>
                <div></div> {# Spacer #}
                <div class="md:col-span-2">
                    <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                    {{ form.remarks }}
                    {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                    <p class="text-red-500 text-xs mt-1">* PM excluding holiday</p>
                </div>
            </div>

            <!-- Spare Parts Section (Formset) -->
            <div class="bg-gray-100 p-4 rounded-md shadow-sm mt-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Spares</h4>
                {{ spare_formset.management_form }}
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border-collapse">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Required Qty</th>
                                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Availed Qty</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for form in spare_formset %}
                            <tr x-data="{ item: spareItems[{{ forloop.counter0 }}] }">
                                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                                <td class="py-2 px-4 border-b border-gray-200 text-center">
                                    {{ form.is_selected }}
                                    {{ form.id }} {# Hidden PK for existing items #}
                                    {{ form.spare_item }} {# Hidden FK to Item for new items #}
                                </td>
                                <td class="py-2 px-4 border-b border-gray-200">{{ form.item_code.value }}</td>
                                <td class="py-2 px-4 border-b border-gray-200">{{ form.description.value }}</td>
                                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ form.unit.value }}</td>
                                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ form.stock_qty.value }}</td>
                                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ form.required_qty.value }}</td>
                                <td class="py-2 px-4 border-b border-gray-200 text-center">
                                    {{ form.qty }}
                                    {{ form.spare_item_id }} {# Hidden field to pass item ID #}
                                    {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="py-4 text-center text-gray-500">No spare parts defined for this machine.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`machinery/urls.py`)

```python
from django.urls import path
from .views import (
    MaintenanceRecordListView, 
    MaintenanceRecordEditView, 
    MaintenanceRecordTablePartialView,
    MaintenanceRecordEditModalView
)

urlpatterns = [
    # Main list view (implicitly navigated to from ASP.NET's PMBM_Edit.aspx)
    path('maintenance-records/', MaintenanceRecordListView.as_view(), name='maintenance_record_list'),
    
    # HTMX endpoint for the DataTables partial
    path('maintenance-records/table/', MaintenanceRecordTablePartialView.as_view(), name='maintenance_record_table'),

    # Edit form (main view and HTMX modal target)
    path('maintenance-records/edit/<int:pk>/', MaintenanceRecordEditView.as_view(), name='maintenance_record_edit'),
    
    # HTMX modal content endpoint for the edit form
    path('maintenance-records/edit/<int:pk>/modal/', MaintenanceRecordEditModalView.as_view(), name='maintenance_record_edit_modal'),
]
```

#### 4.6 Tests (`machinery/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time, timedelta

from .models import Unit, Item, Machine, MaintenanceRecord, MaintenanceSpareUsed, MachineSpareRequirement

class BaseModelTest(TestCase):
    """Base class for setting up common test data."""
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 1
        cls.session_id = 'testuser'

        # Create base data
        cls.unit = Unit.objects.create(id=1, symbol='NOS')
        cls.item_machine = Item.objects.create(id=101, item_code='MCH001', manf_desc='Drilling Machine', uom_basic=cls.unit, stock_qty=1, comp_id=cls.comp_id)
        cls.item_spare1 = Item.objects.create(id=201, item_code='SPR001', manf_desc='Drill Bit', uom_basic=cls.unit, stock_qty=10, comp_id=cls.comp_id)
        cls.item_spare2 = Item.objects.create(id=202, item_code='SPR002', manf_desc='Lubricant Oil', uom_basic=cls.unit, stock_qty=5, comp_id=cls.comp_id)

        cls.machine = Machine.objects.create(
            id=1, item=cls.item_machine, model='XYZ-200', make='BrandA', capacity='100Kg', location='Shop Floor',
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

        MachineSpareRequirement.objects.create(id=1, machine=cls.machine, item=cls.item_spare1, qty=2)
        MachineSpareRequirement.objects.create(id=2, machine=cls.machine, item=cls.item_spare2, qty=1)

        cls.maintenance_record = MaintenanceRecord.objects.create(
            id=1, machine=cls.machine, pmbm=0, from_date=date(2023, 1, 1), to_date=date(2023, 1, 5),
            from_time=time(9, 0, 0), to_time=time(17, 0, 0), name_of_agency='ABC Services',
            name_of_engineer='John Doe', next_pm_due_on=date(2024, 1, 1), remarks='Initial PM',
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id=cls.session_id,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

        MaintenanceSpareUsed.objects.create(
            id=1, maintenance_record=cls.maintenance_record, spare_item=cls.item_spare1, qty=1.5
        )

class ModelUnitTests(BaseModelTest):
    def test_unit_creation(self):
        self.assertEqual(self.unit.symbol, 'NOS')
        self.assertEqual(str(self.unit), 'NOS')

    def test_item_creation(self):
        self.assertEqual(self.item_machine.item_code, 'MCH001')
        self.assertEqual(self.item_machine.uom_basic, self.unit)
        self.assertEqual(str(self.item_machine), 'MCH001 - Drilling Machine')

    def test_machine_creation(self):
        self.assertEqual(self.machine.item, self.item_machine)
        self.assertEqual(self.machine.model, 'XYZ-200')
        self.assertEqual(str(self.machine), 'Drilling Machine (MCH001)')

    def test_maintenance_record_creation(self):
        self.assertEqual(self.maintenance_record.machine, self.machine)
        self.assertEqual(self.maintenance_record.pmbm, 0)
        self.assertEqual(str(self.maintenance_record), f"Maintenance for {self.machine.item.manf_desc} (ID: {self.maintenance_record.id})")

    def test_maintenance_record_get_machine_details(self):
        details = self.maintenance_record.get_machine_details()
        self.assertEqual(details['item_code'], 'MCH001')
        self.assertEqual(details['uom'], 'NOS')
        self.assertEqual(details['name'], 'Drilling Machine')
        self.assertEqual(details['model'], 'XYZ-200')

    def test_maintenance_record_get_required_spares(self):
        required_spares = self.maintenance_record.get_required_spares()
        self.assertEqual(required_spares.count(), 2)
        self.assertTrue(required_spares.filter(item=self.item_spare1).exists())
        self.assertTrue(required_spares.filter(item=self.item_spare2).exists())

    def test_maintenance_spare_used_creation(self):
        spare_used = MaintenanceSpareUsed.objects.get(id=1)
        self.assertEqual(spare_used.maintenance_record, self.maintenance_record)
        self.assertEqual(spare_used.spare_item, self.item_spare1)
        self.assertEqual(float(spare_used.qty), 1.5) # Convert Decimal to float for comparison
        self.assertEqual(str(spare_used), 'Drill Bit (1.50)')

    def test_maintenance_spare_used_get_full_details(self):
        spare_used = MaintenanceSpareUsed.objects.get(id=1)
        details = spare_used.get_full_details()
        self.assertEqual(details['item_code'], 'SPR001')
        self.assertEqual(details['availed_qty'], 1.5)
        self.assertTrue(details['is_selected'])

class ViewIntegrationTests(BaseModelTest):
    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        self.client.session['username'] = self.session_id
        self.client.session.save()

    def test_maintenance_record_list_view(self):
        response = self.client.get(reverse('maintenance_record_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/maintenancerecord/list.html')
        self.assertIn('maintenance_records', response.context)
        self.assertEqual(response.context['maintenance_records'].count(), 1)
        self.assertEqual(response.context['maintenance_records'].first(), self.maintenance_record)

    def test_maintenance_record_table_partial_view(self):
        response = self.client.get(reverse('maintenance_record_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/maintenancerecord/_maintenance_record_table.html')
        self.assertIn('maintenance_records', response.context)
        self.assertEqual(response.context['maintenance_records'].count(), 1)

    def test_maintenance_record_edit_view_get(self):
        url = reverse('maintenance_record_edit', args=[self.maintenance_record.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/maintenancerecord/form.html') # Main form template
        self.assertIn('form', response.context)
        self.assertIn('machine_details', response.context)
        self.assertIn('spare_formset', response.context)
        self.assertEqual(response.context['form'].instance, self.maintenance_record)
        
        # Check initial state of spare formset
        spare_formset = response.context['spare_formset']
        self.assertEqual(len(spare_formset), 2) # Both required spares
        self.assertTrue(spare_formset[0].initial['is_selected']) # Drill Bit was availed
        self.assertEqual(float(spare_formset[0].initial['qty']), 1.5)
        self.assertFalse(spare_formset[1].initial['is_selected']) # Lubricant Oil was not availed

    def test_maintenance_record_edit_modal_view_get_htmx(self):
        url = reverse('maintenance_record_edit_modal', args=[self.maintenance_record.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/maintenancerecord/_maintenance_record_form.html') # Partial template
        self.assertIn('form', response.context)

    def test_maintenance_record_edit_view_post_success(self):
        url = reverse('maintenance_record_edit', args=[self.maintenance_record.id])
        new_agency = 'New Agency LLC'
        new_engineer = 'Jane Smith'
        new_remarks = 'Updated remarks after 2nd PM'

        # Simulate form data for main record
        form_data = {
            'pmbm': '1', # Change to Breakdown
            'from_date': '01-01-2023',
            'to_date': '06-01-2023',
            'from_time': '09:30 AM',
            'to_time': '05:30 PM',
            'name_of_agency': new_agency,
            'name_of_engineer': new_engineer,
            'next_pm_due_on': '01-01-2025',
            'remarks': new_remarks,
        }

        # Simulate form data for spare parts formset
        # Management form data (must be present for formsets)
        form_data['spares_formset-TOTAL_FORMS'] = '2'
        form_data['spares_formset-INITIAL_FORMS'] = '2'
        form_data['spares_formset-MIN_NUM_FORMS'] = '0'
        form_data['spares_formset-MAX_NUM_FORMS'] = '1000' # Arbitrary max

        # First spare (Drill Bit) - already existed, update qty, keep selected
        form_data['spares_formset-0-id'] = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).id # Existing PK
        form_data['spares_formset-0-is_selected'] = 'on'
        form_data['spares_formset-0-qty'] = '2.0' # Updated quantity
        form_data['spares_formset-0-spare_item_id'] = str(self.item_spare1.id) # Hidden item ID

        # Second spare (Lubricant Oil) - was not selected, now select and add qty
        form_data['spares_formset-1-id'] = '' # New record, no PK yet
        form_data['spares_formset-1-is_selected'] = 'on'
        form_data['spares_formset-1-qty'] = '0.5' # New quantity
        form_data['spares_formset-1-spare_item_id'] = str(self.item_spare2.id) # Hidden item ID

        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        
        # Check HTMX response (204 No Content, with trigger)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaintenanceRecordList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

        # Verify main record update
        self.maintenance_record.refresh_from_db()
        self.assertEqual(self.maintenance_record.name_of_agency, new_agency)
        self.assertEqual(self.maintenance_record.name_of_engineer, new_engineer)
        self.assertEqual(self.maintenance_record.remarks, new_remarks)
        self.assertEqual(self.maintenance_record.pmbm, 1) # Changed to Breakdown

        # Verify spare parts updates
        spare1_used = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        )
        self.assertEqual(float(spare1_used.qty), 2.0)

        spare2_used = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare2
        )
        self.assertEqual(float(spare2_used.qty), 0.5)

    def test_maintenance_record_edit_view_post_deselect_spare(self):
        url = reverse('maintenance_record_edit', args=[self.maintenance_record.id])

        # Initially, spare1 (Drill Bit) is used. Let's deselect it.
        # Create another spare used that we will keep selected
        MaintenanceSpareUsed.objects.create(
            id=2, maintenance_record=self.maintenance_record, spare_item=self.item_spare2, qty=0.75
        )

        form_data = {
            'pmbm': '0',
            'from_date': '01-01-2023',
            'to_date': '01-01-2023',
            'from_time': '09:00 AM',
            'to_time': '17:00 PM',
            'name_of_agency': 'ABC Services',
            'name_of_engineer': 'John Doe',
            'next_pm_due_on': '01-01-2024',
            'remarks': 'Initial PM',
        }

        form_data['spares_formset-TOTAL_FORMS'] = '2'
        form_data['spares_formset-INITIAL_FORMS'] = '2'
        form_data['spares_formset-MIN_NUM_FORMS'] = '0'
        form_data['spares_formset-MAX_NUM_FORMS'] = '1000'

        # First spare (Drill Bit) - was selected, now deselect it
        form_data['spares_formset-0-id'] = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).id
        form_data['spares_formset-0-is_selected'] = '' # No 'on' means unchecked
        form_data['spares_formset-0-qty'] = '' # Empty quantity (will be cleared by Alpine.js in real app)
        form_data['spares_formset-0-spare_item_id'] = str(self.item_spare1.id)

        # Second spare (Lubricant Oil) - keep selected
        form_data['spares_formset-1-id'] = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare2
        ).id
        form_data['spares_formset-1-is_selected'] = 'on'
        form_data['spares_formset-1-qty'] = '0.75'
        form_data['spares_formset-1-spare_item_id'] = str(self.item_spare2.id)
        
        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        
        # Verify Drill Bit spare is deleted
        self.assertFalse(MaintenanceSpareUsed.objects.filter(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).exists())

        # Verify Lubricant Oil spare is still there
        self.assertTrue(MaintenanceSpareUsed.objects.filter(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare2
        ).exists())

    def test_maintenance_record_edit_view_post_invalid_spare_qty(self):
        url = reverse('maintenance_record_edit', args=[self.maintenance_record.id])

        form_data = {
            'pmbm': '0',
            'from_date': '01-01-2023',
            'to_date': '01-01-2023',
            'from_time': '09:00 AM',
            'to_time': '17:00 PM',
            'name_of_agency': 'ABC Services',
            'name_of_engineer': 'John Doe',
            'next_pm_due_on': '01-01-2024',
            'remarks': 'Initial PM',
        }
        form_data['spares_formset-TOTAL_FORMS'] = '2'
        form_data['spares_formset-INITIAL_FORMS'] = '2'
        form_data['spares_formset-MIN_NUM_FORMS'] = '0'
        form_data['spares_formset-MAX_NUM_FORMS'] = '1000'

        form_data['spares_formset-0-id'] = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).id
        form_data['spares_formset-0-is_selected'] = 'on'
        form_data['spares_formset-0-qty'] = '' # Invalid: selected but no qty
        form_data['spares_formset-0-spare_item_id'] = str(self.item_spare1.id)

        form_data['spares_formset-1-id'] = ''
        form_data['spares_formset-1-is_selected'] = ''
        form_data['spares_formset-1-qty'] = ''
        form_data['spares_formset-1-spare_item_id'] = str(self.item_spare2.id)

        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should render the form with errors
        self.assertTemplateUsed(response, 'machinery/maintenancerecord/form.html')
        self.assertIn('spare_formset', response.context)
        
        spare_formset = response.context['spare_formset']
        self.assertFalse(spare_formset.is_valid())
        self.assertIn('Required.', spare_formset[0].errors['qty'])
        
        # Verify that the database was not updated due to formset errors
        self.maintenance_record.refresh_from_db()
        self.assertEqual(self.maintenance_record.remarks, 'Initial PM') # Should not be updated
        self.assertTrue(MaintenanceSpareUsed.objects.filter(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).exists()) # Should still exist with original qty
        self.assertFalse(MaintenanceSpareUsed.objects.filter(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare2
        ).exists()) # Should not be created

    def test_maintenance_record_edit_view_post_invalid_main_form(self):
        url = reverse('maintenance_record_edit', args=[self.maintenance_record.id])

        form_data = {
            'pmbm': '0',
            'from_date': '01-01-2023',
            'to_date': '31-12-2022', # Invalid: To date before From date
            'from_time': '09:00 AM',
            'to_time': '17:00 PM',
            'name_of_agency': 'ABC Services',
            'name_of_engineer': 'John Doe',
            'next_pm_due_on': '01-01-2024',
            'remarks': 'Updated remarks',
        }
        form_data['spares_formset-TOTAL_FORMS'] = '2'
        form_data['spares_formset-INITIAL_FORMS'] = '2'
        form_data['spares_formset-MIN_NUM_FORMS'] = '0'
        form_data['spares_formset-MAX_NUM_FORMS'] = '1000'

        form_data['spares_formset-0-id'] = MaintenanceSpareUsed.objects.get(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).id
        form_data['spares_formset-0-is_selected'] = 'on'
        form_data['spares_formset-0-qty'] = '1.0'
        form_data['spares_formset-0-spare_item_id'] = str(self.item_spare1.id)

        form_data['spares_formset-1-id'] = ''
        form_data['spares_formset-1-is_selected'] = ''
        form_data['spares_formset-1-qty'] = ''
        form_data['spares_formset-1-spare_item_id'] = str(self.item_spare2.id)

        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should render the form with errors
        self.assertTemplateUsed(response, 'machinery/maintenancerecord/form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('To Date cannot be earlier than From Date.', response.context['form'].errors['to_date'])
        
        # Verify that the database was not updated
        self.maintenance_record.refresh_from_db()
        self.assertEqual(self.maintenance_record.remarks, 'Initial PM') # Should not be updated
        self.assertTrue(MaintenanceSpareUsed.objects.filter(
            maintenance_record=self.maintenance_record, spare_item=self.item_spare1
        ).exists()) # Should still exist with original qty

```

### Step 5: HTMX and Alpine.js Integration

The templates and views already integrate HTMX for:
*   Loading the main list view (`maintenance_record_list`) and refreshing its content (`maintenance_record_table`) dynamically.
*   Loading the edit form (`_maintenance_record_form.html`) into a modal via `hx-get`.
*   Submitting the form (`hx-post`) and handling success (HTTP 204 with `HX-Trigger`) or error (re-render form with errors).

Alpine.js is used for:
*   Controlling the visibility and behavior of the modal (`x-data`, `x-show`, `x-on`).
*   Managing the state of individual spare part rows in the formset (e.g., `is_selected` checkbox and `availedQty` input disabling/clearing). This is managed by binding the `item` object for each row in Alpine.js and linking `x-model` directives.
*   Initializing external libraries like `flatpickr` (for dates) and `tempusDominus` (for times) upon HTMX content swap using `htmx:afterSwap` event listeners.

DataTables is integrated by:
*   Including the necessary CDN links in the `base.html` (as per instructions, not shown here).
*   Calling `$('#maintenanceRecordTable').DataTable()` in the `_maintenance_record_table.html` partial after the table is loaded, ensuring client-side searching, sorting, and pagination are enabled.

### Final Notes

This comprehensive plan transforms the ASP.NET `PMBM_Edit_Details` functionality into a modern Django application.
*   **DRY Principle:** Model methods like `get_machine_details` and `get_required_spares` encapsulate business logic, keeping views thin. Template partials promote reusability.
*   **Separation of Concerns:** Clear boundaries between models (data & business logic), forms (validation & input handling), views (request processing), and templates (presentation).
*   **Automation Focus:** The modular design with clear responsibilities and the use of declarative frameworks like Django, HTMX, and Alpine.js make this blueprint highly suitable for AI-assisted code generation and systematic migration processes. The tests provide a quantifiable measure of success and ensure functional correctness.
*   **User Experience:** The use of HTMX and Alpine.js ensures a highly interactive and responsive user interface, minimizing full page reloads and providing immediate feedback.