## ASP.NET to Django Conversion Script: Job Scheduling Output Edit

This document outlines a comprehensive modernization plan for the provided ASP.NET application, transitioning it to a robust Django-based solution. The focus is on leveraging modern Django 5.0+ features, a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for enhanced list presentation. This plan emphasizes automation-driven approaches to reduce manual coding effort and human error.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database tables and their implied columns:

**Primary Table for this page:**
*   **`tblMS_JobCompletion`**
    *   `Id` (PK)
    *   `MId` (FK to `tblMS_JobShedule_Master.Id`)
    *   `DId` (FK to `tblMS_JobSchedule_Details.Id`)
    *   `OutputQty` (double)
    *   `UOM` (FK to `Unit_Master.Id`)

**Related Tables:**
*   **`tblMS_JobShedule_Master`**
    *   `Id` (PK)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `WONo` (string)
    *   `CompId` (int)
    *   `FinYearId` (int)
*   **`tblMS_JobSchedule_Details`**
    *   `Id` (PK)
    *   `MId` (FK to `tblMS_JobShedule_Master.Id`)
    *   `MachineId` (FK to `tblDG_Item_Master.Id`)
    *   `Process` (FK to `tblPln_Process_Master.Id`)
    *   `Type` (int, 0=Fresh, 1=Rework)
    *   `Shift` (int, 0=Day, 1=Night)
    *   `Qty` (double)
    *   `FromDate` (datetime)
    *   `ToDate` (datetime)
    *   `FromTime` (string)
    *   `ToTime` (string)
    *   `Operator` (string, likely `EmpId` FK to `tblHR_OfficeStaff.EmpId`)
    *   `BatchNo` (string)
    *   `Released` (int, 0/1)
    *   `ReleasedDate` (datetime)
    *   `ReleasedTime` (string)
    *   `ReleasedBy` (string, `SId`/username)
*   **`tblDG_Item_Master`**
    *   `Id` (PK)
    *   `ItemCode` (string)
    *   `ManfDesc` (string)
    *   `UOMBasic` (FK to `Unit_Master.Id`)
    *   `CompId` (int)
    *   `FinYearId` (int)
*   **`Unit_Master`**
    *   `Id` (PK)
    *   `Symbol` (string)
*   **`tblPln_Process_Master`**
    *   `Id` (PK)
    *   `Symbol` (string)
    *   `ProcessName` (string)
*   **`tblHR_OfficeStaff`**
    *   `EmpId` (PK, string) - inferred from usage as `Operator`
    *   `EmployeeName` (string)
    *   `CompId` (int)
*   **`tblDG_BOM_Master`**
    *   `PId` (int)
    *   `CId` (int)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `WONo` (string)
    *   `Qty` (double)
    *   `CompId` (int)
    *   `FinYearId` (int)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily performs **Read** and **Update** operations:

*   **Read (Display):**
    *   Fetches high-level item details (`Item Code`, `Description`, `UOM`, `BOM Qty`, `WoNo`) related to `tblMS_JobShedule_Master` and `tblDG_BOM_Master`.
    *   Populates a data grid (`GridView1`) with entries from `tblMS_JobCompletion` and extensive joins with `tblMS_JobSchedule_Details`, `tblDG_Item_Master`, `tblPln_Process_Master`, `tblHR_OfficeStaff`, and `Unit_Master`.
*   **Update (Edit):**
    *   Allows editing of `OutputQty` and `UOM` for `tblMS_JobCompletion` records.
*   **Update (Release/Unrelease):**
    *   Allows changing the `Released` status of records in `tblMS_JobSchedule_Details`, along with `ReleasedDate`, `ReleasedTime`, and `ReleasedBy`.
*   **Pagination:** The `GridView` supports pagination.
*   **Validation:** Basic required field and regex validation for `OutputQty` and `UOM`.

### Step 3: Infer UI Components

The ASP.NET page uses:
*   `asp:Label` for displaying static/read-only information (`lblItemCode`, `lblunit`, `lblBomqty`, `lblDesc`, `lblWoNo`).
*   `asp:GridView` for tabular data display with in-line editing.
    *   `LinkButton` for `Edit`, `Update`, `Cancel`, `Release`, `Unrelease` actions within the grid.
    *   `asp:TextBox` (`TxtoutputQty`) for editing output quantity.
    *   `asp:DropDownList` (`DrpUnit`) for selecting UOM during edit.
    *   `asp:RequiredFieldValidator` and `asp:RegularExpressionValidator` for input validation.
*   `asp:Button` (`Btncancel`) for navigation.

The page uses a master page (`MasterPage.master`) and includes CSS (`yui-datatable.css`, `StyleSheet.css`) and JavaScript (`loadingNotifier.js`, `PopUpMsg.js`). The `yui-datatable.css` suggests a pre-existing datatable library.

### Step 4: Generate Django Code

We will create a Django app named `machinery`.

#### 4.1 Models (`machinery/models.py`)

This section defines the Django models that map to the existing database tables. We set `managed = False` and `db_table` to ensure Django uses the existing schema without attempting to create or modify tables. Business logic like `release` and `unrelease` actions are included as model methods.

```python
from django.db import models
from django.utils import timezone

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey('Unit', models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ProcessMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    process_name = models.CharField(db_column='ProcessName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Process Master'
        verbose_name_plural = 'Process Masters'

    def __str__(self):
        return f"[{self.symbol}] {self.process_name}"

class OfficeStaff(models.Model):
    # Assuming EmpId is the primary key and char field based on Operator column usage
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) 
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class JobScheduleMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_JobShedule_Master'
        verbose_name = 'Job Schedule Master'
        verbose_name_plural = 'Job Schedule Masters'

    def __str__(self):
        return f"WO: {self.wo_no} - Item: {self.item.item_code if self.item else 'N/A'}"

    # Business logic to get related BOM details (simplified for this example)
    def get_bom_details(self, bom_master_class):
        # This mocks the complex BOMTreeQty logic.
        # In a real scenario, this would involve recursive queries on BOMMaster.
        bom_entry = bom_master_class.objects.filter(
            item_id=self.item_id, wono=self.wo_no,
            comp_id=self.comp_id, fin_year_id=self.fin_year_id
        ).first()

        bom_qty = 1.0 # Placeholder for fun.BOMTreeQty
        if bom_entry:
            # Here, implement the actual BOMTreeQty logic from the C# fun class if available.
            # For demonstration, we'll just use the Qty from the BOM entry if it exists.
            bom_qty = bom_entry.qty

        return {
            'item_code': self.item.item_code if self.item else 'N/A',
            'description': self.item.manf_desc if self.item else 'N/A',
            'uom': self.item.uom_basic.symbol if self.item and self.item.uom_basic else 'N/A',
            'bom_qty': bom_qty,
            'wo_no': self.wo_no,
        }

class JobScheduleDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(JobScheduleMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    machine = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='MachineId', related_name='job_schedule_details_machine', blank=True, null=True)
    process = models.ForeignKey(ProcessMaster, models.DO_NOTHING, db_column='Process', blank=True, null=True)
    type_code = models.IntegerField(db_column='Type', blank=True, null=True) # 0=Fresh, 1=Rework
    shift_code = models.IntegerField(db_column='Shift', blank=True, null=True) # 0=Day, 1=Night
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    from_date = models.DateField(db_column='FromDate', blank=True, null=True)
    to_date = models.DateField(db_column='ToDate', blank=True, null=True)
    from_time = models.CharField(db_column='FromTime', max_length=50, blank=True, null=True)
    to_time = models.CharField(db_column='ToTime', max_length=50, blank=True, null=True)
    operator_emp_id = models.CharField(db_column='Operator', max_length=50, blank=True, null=True) # Stored as EmpId
    batch_no = models.CharField(db_column='BatchNo', max_length=50, blank=True, null=True)
    released = models.IntegerField(db_column='Released', blank=True, null=True) # 0=False, 1=True
    released_date = models.DateField(db_column='ReleasedDate', blank=True, null=True)
    released_time = models.CharField(db_column='ReleasedTime', max_length=50, blank=True, null=True)
    released_by = models.CharField(db_column='ReleasedBy', max_length=255, blank=True, null=True) # Username

    class Meta:
        managed = False
        db_table = 'tblMS_JobSchedule_Details'
        verbose_name = 'Job Schedule Detail'
        verbose_name_plural = 'Job Schedule Details'

    def __str__(self):
        return f"Detail ID: {self.id} - {self.machine.manf_desc if self.machine else 'N/A'}"

    @property
    def machine_name(self):
        return self.machine.manf_desc if self.machine else 'N/A'

    @property
    def process_display(self):
        return self.process.process_name if self.process else 'N/A'
    
    @property
    def type_display(self):
        return "Fresh" if self.type_code == 0 else "Rework" if self.type_code == 1 else "Unknown"

    @property
    def shift_display(self):
        return "Day" if self.shift_code == 0 else "Night" if self.shift_code == 1 else "Unknown"
    
    @property
    def operator_name(self):
        try:
            return OfficeStaff.objects.get(emp_id=self.operator_emp_id).employee_name
        except OfficeStaff.DoesNotExist:
            return 'N/A'

    def release_job(self, released_by_username):
        if self.released == 0:
            self.released = 1
            self.released_date = timezone.now().date()
            self.released_time = timezone.now().strftime('%H:%M:%S') # Assuming HH:MM:SS format
            self.released_by = released_by_username
            self.save()
            return True
        return False

    def unrelease_job(self):
        if self.released == 1:
            self.released = 0
            self.released_date = None
            self.released_time = ''
            self.released_by = ''
            self.save()
            return True
        return False

class JobCompletion(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Changed MId to master, DId to detail for clearer FK relationships
    master = models.ForeignKey(JobScheduleMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    detail = models.ForeignKey(JobScheduleDetail, models.DO_NOTHING, db_column='DId', blank=True, null=True)
    output_qty = models.FloatField(db_column='OutputQty', blank=True, null=True)
    uom = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOM', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_JobCompletion'
        verbose_name = 'Job Completion'
        verbose_name_plural = 'Job Completions'

    def __str__(self):
        return f"Completion ID: {self.id} - Qty: {self.output_qty}"

    # Business logic for updating output quantity and UOM
    def update_output(self, new_output_qty, new_uom_id):
        self.output_qty = new_output_qty
        self.uom_id = new_uom_id
        self.save()

class BOMMaster(models.Model):
    # Inferring primary key for consistency, adjust if DB has a different PK.
    # Assuming PId, CId, ItemId, WONo form a unique composite key or there's an internal PK.
    # For now, using a simple ID. If no explicit PK, Django will add one.
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming an auto-incrementing PK if not explicit
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for {self.item.manf_desc if self.item else 'N/A'} (WO: {self.wono})"

```

#### 4.2 Forms (`machinery/forms.py`)

We need a form specifically for updating `JobCompletion` records. The `Unit` dropdown is dynamically populated in the `__init__` method.

```python
from django import forms
from .models import JobCompletion, Unit

class JobCompletionForm(forms.ModelForm):
    # Add a field for the UOM dropdown
    uom_choice = forms.ModelChoiceField(
        queryset=Unit.objects.all().order_by('symbol'),
        empty_label="Select",
        label="UOM",
        required=True,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = JobCompletion
        fields = ['output_qty', 'uom_choice']
        widgets = {
            'output_qty': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Output Quantity'
            }),
        }
        labels = {
            'output_qty': 'Output Qty',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial value for uom_choice if an instance exists
        if self.instance and self.instance.uom:
            self.fields['uom_choice'].initial = self.instance.uom
        
    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.uom = self.cleaned_data['uom_choice']
        if commit:
            instance.save()
        return instance

    def clean_output_qty(self):
        output_qty = self.cleaned_data['output_qty']
        if output_qty is not None and output_qty < 0:
            raise forms.ValidationError("Output Quantity must be a positive number.")
        return output_qty

```

#### 4.3 Views (`machinery/views.py`)

Views are kept thin, delegating complex logic to models and using HTMX for dynamic content. A separate `TablePartialView` is created for HTMX to fetch and render only the table.

```python
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404, render, redirect
from django.db.models import F # For selecting related fields efficiently
from .models import JobCompletion, JobScheduleMaster, JobScheduleDetail, BOMMaster, ItemMaster, Unit, ProcessMaster, OfficeStaff
from .forms import JobCompletionForm
import logging

logger = logging.getLogger(__name__)

class JobCompletionOutputEditListView(ListView):
    model = JobCompletion
    template_name = 'machinery/jobcompletion/list.html'
    context_object_name = 'job_completions'
    paginate_by = 15 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Retrieve parameters from query string, similar to Request.QueryString in ASP.NET
        # itemId = Request.QueryString["id"] -> MId from JobScheduleMaster
        # WONo = Request.QueryString["WONo"]
        item_master_id = self.request.GET.get('id')
        wo_no = self.request.GET.get('WONo')

        # Assuming CompId and FinYearId from session are now part of Django's request context
        # Or should be retrieved from the user's profile/settings if applicable.
        # For this example, we'll assume they are available or pass them in a robust system.
        # Placeholder for CompId and FinYearId based on application context
        comp_id = 1 # Example: self.request.user.company_id if user has company context
        fin_year_id = 1 # Example: current_financial_year()

        if not item_master_id or not wo_no:
            logger.warning("Missing 'id' or 'WONo' in query string for JobCompletionOutputEditListView.")
            return JobCompletion.objects.none() # Return empty queryset if parameters are missing

        try:
            # First, get JobScheduleMaster based on the passed ID (which is JobScheduleMaster.Id)
            job_master = JobScheduleMaster.objects.filter(
                id=item_master_id, comp_id=comp_id, wo_no=wo_no
            ).first()

            if not job_master:
                logger.warning(f"JobScheduleMaster not found for id={item_master_id}, wo_no={wo_no}")
                return JobCompletion.objects.none()

            # Filter JobCompletion by MId (JobScheduleMaster.Id) and order
            # Use select_related to efficiently fetch related objects for display
            queryset = JobCompletion.objects.filter(
                master=job_master
            ).select_related(
                'detail__master__item', 'detail__machine', 'detail__process', 
                'detail__uom', 'detail__operator_emp_id', 'uom'
            ).order_by('-id') # OrderBy Id Desc matches ASP.NET

            return queryset
        except Exception as e:
            logger.error(f"Error fetching JobCompletion list: {e}")
            return JobCompletion.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_master_id = self.request.GET.get('id')
        wo_no = self.request.GET.get('WONo')
        comp_id = 1 # Example: self.request.user.company_id
        fin_year_id = 1 # Example: current_financial_year()
        
        job_master_header = None
        bom_details = {}

        if item_master_id and wo_no:
            job_master_header = JobScheduleMaster.objects.filter(
                id=item_master_id, comp_id=comp_id, wo_no=wo_no
            ).select_related('item__uom_basic').first() # Eagerly load related Item and UOM

            if job_master_header:
                # Use the model method to encapsulate the BOM logic
                bom_details = job_master_header.get_bom_details(BOMMaster) # Pass BOMMaster class

        context['job_master_header'] = job_master_header
        context['bom_details'] = bom_details
        return context

class JobCompletionOutputEditTablePartialView(ListView):
    model = JobCompletion
    template_name = 'machinery/jobcompletion/_jobcompletion_table.html'
    context_object_name = 'job_completions'

    def get_queryset(self):
        # Re-use queryset logic from the main ListView
        item_master_id = self.request.GET.get('id')
        wo_no = self.request.GET.get('WONo')
        comp_id = 1 
        fin_year_id = 1 
        
        if not item_master_id or not wo_no:
            return JobCompletion.objects.none()

        try:
            job_master = JobScheduleMaster.objects.filter(
                id=item_master_id, comp_id=comp_id, wo_no=wo_no
            ).first()

            if not job_master:
                return JobCompletion.objects.none()

            return JobCompletion.objects.filter(
                master=job_master
            ).select_related(
                'detail__master__item', 'detail__machine', 'detail__process', 
                'detail__operator_emp_id', 'uom'
            ).order_by('-id')
        except Exception as e:
            logger.error(f"Error fetching JobCompletion table data: {e}")
            return JobCompletion.objects.none()


class JobCompletionOutputEditUpdateView(UpdateView):
    model = JobCompletion
    form_class = JobCompletionForm
    template_name = 'machinery/jobcompletion/_jobcompletion_form.html'
    # success_url will be handled by HX-Trigger response

    def get_object(self, queryset=None):
        # The ASP.NET code uses DataKeyNames="outputId" and `lbloutputId`.
        # This maps to JobCompletion.Id.
        output_id = self.kwargs.get('pk') 
        return get_object_or_404(JobCompletion, pk=output_id)

    def form_valid(self, form):
        # Call the model method to update output quantity and UOM
        instance = form.save(commit=False)
        instance.update_output(form.cleaned_data['output_qty'], form.cleaned_data['uom_choice'].id)
        
        messages.success(self.request, 'Job completion record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success and no HTML to swap
                headers={
                    'HX-Trigger': 'refreshJobCompletionList' # Custom event to refresh the list
                }
            )
        return super().form_valid(form) # Fallback for non-HTMX requests

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class JobScheduleDetailActionView(View):
    # This view handles Release/Unrelease actions via HTMX
    # It updates JobScheduleDetail, not JobCompletion directly
    
    def post(self, request, pk, action):
        job_detail = get_object_or_404(JobScheduleDetail, pk=pk)
        current_username = request.user.username if request.user.is_authenticated else 'System' # SId in ASP.NET

        if action == 'release':
            if job_detail.release_job(current_username):
                messages.success(request, 'Job scheduled detail released successfully.')
            else:
                messages.error(request, 'Job scheduled detail is already released.')
        elif action == 'unrelease':
            if job_detail.unrelease_job():
                messages.success(request, 'Job scheduled detail unreleased successfully.')
            else:
                messages.error(request, 'Job scheduled detail is not released.')
        else:
            raise Http404("Invalid action specified.")

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshJobCompletionList' # Refresh the main list
                }
            )
        return redirect(reverse_lazy('jobcompletion_list')) # Fallback redirect

```

#### 4.4 Templates (`machinery/templates/machinery/jobcompletion/`)

Templates are designed with HTMX for dynamic loading of the table and forms within a modal. DataTables is initialized via a script within the partial table template.

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
        <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Job-Scheduling Output-Edit
            </h3>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Item Code</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1 font-bold">{{ bom_details.item_code }}</dd>
                    <dt class="text-sm font-medium text-gray-500">UOM</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1 font-bold">{{ bom_details.uom }}</dd>
                </div>
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1 font-bold">{{ bom_details.description }}</dd>
                    <dt class="text-sm font-medium text-gray-500">BOM Qty</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1 font-bold">{{ bom_details.bom_qty }}</dd>
                </div>
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">WoNo</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1 font-bold">{{ bom_details.wo_no }}</dd>
                    <dt class="text-sm font-medium text-gray-500"></dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-1"></dd>
                </div>
            </dl>
        </div>
    </div>

    <div id="jobCompletionTable-container"
         hx-trigger="load, refreshJobCompletionList from:body"
         hx-get="{% url 'jobcompletion_table_partial' %}?id={{ request.GET.id }}&WONo={{ request.GET.WONo }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Job Completion data...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full mx-auto"
             @click.stop> <!-- Prevent clicks inside modal from closing it -->
            <!-- Content loaded by HTMX -->
        </div>
    </div>

    <div class="mt-6 flex justify-center">
        <a href="{% url 'machinery:schedule_output_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init is handled by base.html, just ensure we have access to the modal state
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.add('is-active'); // Add active class for visibility
                modal.classList.remove('hidden'); // Remove hidden class
                // Initialize Alpine if content was loaded
                if (window.Alpine) {
                    window.Alpine.initTree(modal);
                }
            }
        }
    });

    document.addEventListener('htmx:beforeRequest', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // When a form submission returns 204, it means success, close modal
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // Remove active class
                modal.classList.add('hidden'); // Add hidden class
            }
        }
    });

    // Event listener for closing modal via cancel button
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            closeModal() {
                const modal = document.getElementById('modal');
                if (modal) {
                    modal.classList.remove('is-active');
                    modal.classList.add('hidden');
                }
            }
        }));
    });
</script>
{% endblock %}
```

##### `_jobcompletion_table.html` (Partial for DataTables)

```html
<table id="jobCompletionTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Batch No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Batch Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operator</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">O/p Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in job_completions %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs {% if obj.detail.released == 1 %}hidden{% endif %}"
                    hx-get="{% url 'jobcompletion_edit' pk=obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs {% if obj.detail.released == 1 %}hidden{% endif %}"
                    hx-post="{% url 'job_detail_action' pk=obj.detail.pk action='release' %}"
                    hx-confirm="Are you sure you want to Release this item?"
                    hx-swap="none"
                    hx-trigger="click">
                    Release
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs {% if obj.detail.released == 0 %}hidden{% endif %}"
                    hx-post="{% url 'job_detail_action' pk=obj.detail.pk action='unrelease' %}"
                    hx-confirm="Are you sure you want to Unrelease this item?"
                    hx-swap="none"
                    hx-trigger="click">
                    Unrelease
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.detail.machine_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.detail.process_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.type_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.shift_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.batch_no|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.qty|floatformat:"2" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.from_date|date:"d/m/Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.to_date|date:"d/m/Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.from_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.detail.to_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.detail.operator_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.output_qty|floatformat:"2" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.uom.symbol if obj.uom else 'N/A' }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="15" class="py-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Destroy existing DataTable instance before re-initializing to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#jobCompletionTable')) {
        $('#jobCompletionTable').DataTable().destroy();
    }
    $('#jobCompletionTable').DataTable({
        "pageLength": 15, // Matches ASP.NET GridView PageSize
        "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [1] } // Disable sorting on Actions column
        ]
    });
</script>
```

##### `_jobcompletion_form.html` (Partial for Edit Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit Job Completion Output{% else %}Add Job Completion Output{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`machinery/urls.py`)

This file defines the URL patterns for the `machinery` application.

```python
from django.urls import path
from .views import JobCompletionOutputEditListView, JobCompletionOutputEditUpdateView, JobCompletionOutputEditTablePartialView, JobScheduleDetailActionView

app_name = 'machinery' # Namespace for this app

urlpatterns = [
    # Main list view for Job Completion Output Edit
    path('schedule-output-edit/', JobCompletionOutputEditListView.as_view(), name='schedule_output_edit_list'),
    
    # HTMX endpoint for the DataTables partial view
    path('schedule-output-edit/table/', JobCompletionOutputEditTablePartialView.as_view(), name='jobcompletion_table_partial'),
    
    # HTMX endpoint for editing a specific JobCompletion record (loads form into modal)
    path('schedule-output-edit/edit/<int:pk>/', JobCompletionOutputEditUpdateView.as_view(), name='jobcompletion_edit'),

    # HTMX endpoint for Release/Unrelease actions on JobScheduleDetail
    path('job-detail-action/<int:pk>/<str:action>/', JobScheduleDetailActionView.as_view(), name='job_detail_action'),

    # This is a placeholder for the original ASP.NET cancel redirect URL.
    # In a real system, you'd link this to the appropriate Django URL.
    path('schedule-output/', JobCompletionOutputEditListView.as_view(), name='schedule_output_list'), # Example, points back to this page for now
]

```

#### 4.6 Tests (`machinery/tests.py`)

Comprehensive tests cover model methods and view functionality, ensuring data integrity and correct behavior, especially for HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import JobCompletion, JobScheduleMaster, JobScheduleDetail, ItemMaster, Unit, ProcessMaster, OfficeStaff, BOMMaster

class ModelSetupMixin:
    """Helper mixin to set up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        # Create base data required for all tests
        cls.comp_id = 1
        cls.fin_year_id = 1
        cls.current_username = 'testuser'

        cls.unit_ea = Unit.objects.create(id=1, symbol='EA')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')

        cls.item_machine = ItemMaster.objects.create(id=101, item_code='M001', manf_desc='Assembly Machine', uom_basic=cls.unit_ea, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.item_product = ItemMaster.objects.create(id=102, item_code='P001', manf_desc='Finished Product A', uom_basic=cls.unit_ea, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)

        cls.process_assem = ProcessMaster.objects.create(id=201, symbol='ASM', process_name='Assembly')
        cls.process_paint = ProcessMaster.objects.create(id=202, symbol='PNT', process_name='Painting')

        cls.operator1 = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=cls.comp_id)
        cls.operator2 = OfficeStaff.objects.create(emp_id='EMP002', employee_name='Jane Smith', comp_id=cls.comp_id)

        cls.job_master_1 = JobScheduleMaster.objects.create(id=1, item=cls.item_product, wo_no='WO-12345', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.job_master_2 = JobScheduleMaster.objects.create(id=2, item=cls.item_product, wo_no='WO-67890', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)

        cls.job_detail_1 = JobScheduleDetail.objects.create(
            id=1, master=cls.job_master_1, machine=cls.item_machine, process=cls.process_assem,
            type_code=0, shift_code=0, qty=100.0, from_date=timezone.now().date(), to_date=timezone.now().date(),
            from_time='08:00', to_time='17:00', operator_emp_id=cls.operator1.emp_id, batch_no='B1', released=0
        )
        cls.job_detail_2 = JobScheduleDetail.objects.create(
            id=2, master=cls.job_master_1, machine=cls.item_machine, process=cls.process_paint,
            type_code=1, shift_code=1, qty=50.0, from_date=timezone.now().date(), to_date=timezone.now().date(),
            from_time='20:00', to_time='05:00', operator_emp_id=cls.operator2.emp_id, batch_no='B2', released=1,
            released_date=timezone.now().date(), released_time='18:00:00', released_by='prevuser'
        )
        
        cls.job_completion_1 = JobCompletion.objects.create(
            id=1, master=cls.job_master_1, detail=cls.job_detail_1, output_qty=95.0, uom=cls.unit_ea
        )
        cls.job_completion_2 = JobCompletion.objects.create(
            id=2, master=cls.job_master_1, detail=cls.job_detail_2, output_qty=48.0, uom=cls.unit_kg
        )

        cls.bom_master = BOMMaster.objects.create(
            p_id=1, c_id=1, item=cls.item_product, wono=cls.job_master_1.wo_no, qty=1.0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

class JobCompletionModelTest(ModelSetupMixin, TestCase):
    def test_job_completion_creation(self):
        self.assertEqual(JobCompletion.objects.count(), 2)
        obj = JobCompletion.objects.get(id=1)
        self.assertEqual(obj.output_qty, 95.0)
        self.assertEqual(obj.uom.symbol, 'EA')
        self.assertEqual(obj.master.wo_no, 'WO-12345')
        self.assertEqual(obj.detail.process_display, 'Assembly')

    def test_job_completion_update_output_method(self):
        obj = JobCompletion.objects.get(id=1)
        initial_qty = obj.output_qty
        initial_uom = obj.uom

        obj.update_output(100.0, self.unit_kg.id)
        obj.refresh_from_db()
        self.assertEqual(obj.output_qty, 100.0)
        self.assertEqual(obj.uom.symbol, 'KG')
        self.assertNotEqual(obj.output_qty, initial_qty)
        self.assertNotEqual(obj.uom, initial_uom)

class JobScheduleDetailModelTest(ModelSetupMixin, TestCase):
    def test_job_detail_properties(self):
        detail = JobScheduleDetail.objects.get(id=1)
        self.assertEqual(detail.machine_name, 'Assembly Machine')
        self.assertEqual(detail.process_display, 'Assembly')
        self.assertEqual(detail.type_display, 'Fresh')
        self.assertEqual(detail.shift_display, 'Day')
        self.assertEqual(detail.operator_name, 'John Doe')

    def test_release_job(self):
        detail = JobScheduleDetail.objects.get(id=1) # Initially not released
        self.assertEqual(detail.released, 0)
        
        # Test successful release
        result = detail.release_job(self.current_username)
        self.assertTrue(result)
        detail.refresh_from_db()
        self.assertEqual(detail.released, 1)
        self.assertEqual(detail.released_by, self.current_username)
        self.assertIsNotNone(detail.released_date)
        self.assertIsNotNone(detail.released_time)

        # Test releasing an already released job
        result_again = detail.release_job(self.current_username)
        self.assertFalse(result_again) # Should return False as it's already released

    def test_unrelease_job(self):
        detail = JobScheduleDetail.objects.get(id=2) # Initially released
        self.assertEqual(detail.released, 1)
        
        # Test successful unrelease
        result = detail.unrelease_job()
        self.assertTrue(result)
        detail.refresh_from_db()
        self.assertEqual(detail.released, 0)
        self.assertEqual(detail.released_by, '')
        self.assertIsNone(detail.released_date)
        self.assertEqual(detail.released_time, '')

        # Test unrelease an already unreleased job
        result_again = detail.unrelease_job()
        self.assertFalse(result_again) # Should return False as it's already unreleased

class JobScheduleMasterModelTest(ModelSetupMixin, TestCase):
    def test_get_bom_details(self):
        details = self.job_master_1.get_bom_details(BOMMaster)
        self.assertEqual(details['item_code'], self.item_product.item_code)
        self.assertEqual(details['description'], self.item_product.manf_desc)
        self.assertEqual(details['uom'], self.unit_ea.symbol)
        self.assertEqual(details['wo_no'], self.job_master_1.wo_no)
        # Note: bom_qty is mocked as 1.0 in the example, would be actual calculation in real code
        self.assertEqual(details['bom_qty'], 1.0) 

class JobCompletionOutputEditViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Mock user authentication if needed for `request.user.username`
        # self.client.force_login(User.objects.create_user(username=self.current_username))

    def test_list_view_get_success(self):
        url = reverse('machinery:schedule_output_edit_list') + f'?id={self.job_master_1.id}&WONo={self.job_master_1.wo_no}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobcompletion/list.html')
        self.assertIn('job_completions', response.context)
        self.assertIn('bom_details', response.context)
        self.assertEqual(len(response.context['job_completions']), 2) # Both completions for job_master_1

    def test_list_view_missing_params(self):
        url = reverse('machinery:schedule_output_edit_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200) # Still 200, but queryset should be empty
        self.assertTemplateUsed(response, 'machinery/jobcompletion/list.html')
        self.assertEqual(len(response.context['job_completions']), 0)

    def test_table_partial_view_get_success(self):
        url = reverse('machinery:jobcompletion_table_partial') + f'?id={self.job_master_1.id}&WONo={self.job_master_1.wo_no}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobcompletion/_jobcompletion_table.html')
        self.assertIn('job_completions', response.context)
        self.assertEqual(len(response.context['job_completions']), 2)

    def test_update_view_get_success(self):
        obj = self.job_completion_1
        url = reverse('machinery:jobcompletion_edit', args=[obj.pk])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobcompletion/_jobcompletion_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success_htmx(self):
        obj = self.job_completion_1
        url = reverse('machinery:jobcompletion_edit', args=[obj.pk])
        data = {
            'output_qty': 99.0,
            'uom_choice': self.unit_kg.id,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success status
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshJobCompletionList')

        obj.refresh_from_db()
        self.assertEqual(obj.output_qty, 99.0)
        self.assertEqual(obj.uom, self.unit_kg)

    def test_update_view_post_invalid_data_htmx(self):
        obj = self.job_completion_1
        url = reverse('machinery:jobcompletion_edit', args=[obj.pk])
        data = {
            'output_qty': -5.0, # Invalid quantity
            'uom_choice': self.unit_ea.id,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'machinery/jobcompletion/_jobcompletion_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('output_qty', response.context['form'].errors)

    def test_job_detail_action_release_htmx(self):
        detail_to_release = self.job_detail_1 # Initially not released
        self.assertEqual(detail_to_release.released, 0)
        url = reverse('machinery:job_detail_action', args=[detail_to_release.pk, 'release'])
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshJobCompletionList')
        detail_to_release.refresh_from_db()
        self.assertEqual(detail_to_release.released, 1)

    def test_job_detail_action_unrelease_htmx(self):
        detail_to_unrelease = self.job_detail_2 # Initially released
        self.assertEqual(detail_to_unrelease.released, 1)
        url = reverse('machinery:job_detail_action', args=[detail_to_unrelease.pk, 'unrelease'])
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshJobCompletionList')
        detail_to_unrelease.refresh_from_db()
        self.assertEqual(detail_to_unrelease.released, 0)

    def test_job_detail_action_invalid_action(self):
        url = reverse('machinery:job_detail_action', args=[self.job_detail_1.pk, 'invalid_action'])
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Should raise Http404

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Content:**
    *   The `jobCompletionTable-container` div in `list.html` uses `hx-get` to load the `_jobcompletion_table.html` partial, which contains the DataTables setup. `hx-trigger="load, refreshJobCompletionList from:body"` ensures the table loads on page load and refreshes whenever the `refreshJobCompletionList` custom event is triggered (e.g., after a successful update/release/unrelease).
    *   Edit buttons in `_jobcompletion_table.html` use `hx-get` to fetch the `_jobcompletion_form.html` into the `#modalContent` div, opening the modal.
    *   Form submissions in `_jobcompletion_form.html` use `hx-post` to send data back to the `UpdateView`. Upon success (HTTP 204 No Content), the `HX-Trigger` header `refreshJobCompletionList` is sent to refresh the main table.
    *   Release/Unrelease buttons use `hx-post` to a dedicated `JobScheduleDetailActionView`. They also trigger `refreshJobCompletionList` upon success.
    *   `hx-confirm` is used for action confirmation, replacing the ASP.NET `confirmationUpdate()` JavaScript.

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses Alpine.js (`x-data="{ showModal: false }" x-show="showModal"`) to manage its visibility.
    *   HTMX triggers JavaScript to add/remove classes (`is-active`, `hidden`) on the modal to show/hide it. The `htmx:afterSwap` and `htmx:beforeRequest` event listeners in `list.html` handle these transitions.
    *   A simple Alpine.js data component `modalController` with a `closeModal` function is provided for a cancel button to close the modal directly.

*   **DataTables for List Views:**
    *   The `_jobcompletion_table.html` partial includes a `<script>` tag to initialize DataTables on the loaded table. This script runs every time the partial is swapped in by HTMX, ensuring the table is correctly initialized even after dynamic updates. It also includes `destroy()` to prevent re-initialization errors.
    *   `pageLength` is set to 15, matching the original ASP.NET GridView.
    *   `columnDefs` is used to disable sorting on the "Actions" column.

*   **No Custom JavaScript (Beyond HTMX/Alpine/DataTables setup):** All interactions are handled by HTMX attributes or basic Alpine.js directives, eliminating the need for separate, complex JavaScript files.

### Final Notes

*   **Placeholder Logic:** The `fun.BOMTreeQty` and session variable handling (`CompId`, `FinYearId`, `SId`) were simplified or mocked. In a real migration, the exact business logic for `BOMTreeQty` would be translated into a Python method within `BOMMaster` or a dedicated service, and session/context variables would be integrated via Django's authentication system or custom middleware.
*   **Error Handling:** Basic error logging is included. More robust error handling (e.g., user-friendly messages for database errors) should be implemented.
*   **Authentication/Authorization:** The current plan assumes `request.user.username` is available. A full migration would include setting up Django's authentication system and relevant permissions.
*   **Styling:** Tailwind CSS classes are used for modern, responsive styling. Ensure Tailwind CSS is correctly set up and compiled in your Django project.
*   **URL Parameter Handling:** The plan uses `request.GET.get('id')` and `request.GET.get('WONo')` to retrieve URL parameters, directly mirroring the `Request.QueryString` behavior.
*   **Redirects:** The original `Btncancel_Click` redirects to `schedule_Output_Edit.aspx`. In Django, this would be a `reverse_lazy` to the corresponding list view.
*   **Security:** Django's ORM and CSRF protection inherently provide much better security than the raw SQL concatenations seen in the ASP.NET code.