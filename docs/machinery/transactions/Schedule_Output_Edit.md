## ASP.NET to Django Conversion Script: Job Scheduling Output-Edit Modernization

This plan outlines the automated conversion of your ASP.NET "Job Scheduling Output-Edit" module to a modern Django application. Our approach leverages conversational AI to guide the migration process, focusing on automation, efficient design patterns like 'Fat Model, Thin View', and a highly interactive frontend with HTMX and Alpine.js.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include base.html template code in your output - assume it already exists and is extended.
*   Focus ONLY on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

### AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the ASP.NET code-behind, we identify several tables and their columns involved in fetching the job schedule data:

*   **`tblMS_JobShedule_Master` (Main Table):**
    *   `Id` (Primary Key, integer)
    *   `SessionId` (varchar/string, maps to employee who generated the schedule)
    *   `JobNo` (varchar/string)
    *   `ItemId` (integer, foreign key to `tblDG_Item_Master`)
    *   `SysDate` (datetime/date, system date of generation)
    *   `FinYearId` (integer, foreign key to `tblFinancial_master`)
    *   `WONo` (varchar/string, Work Order Number)
    *   `CompId` (integer, Company ID)

*   **`tblDG_Item_Master`:**
    *   `Id` (Primary Key, integer)
    *   `ItemCode` (varchar/string)

*   **`tblFinancial_master`:**
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (varchar/string)

*   **`tblHR_OfficeStaff`:**
    *   `EmpId` (Primary Key, varchar/string, corresponds to `SessionId` in `tblMS_JobShedule_Master`)
    *   `Title` (varchar/string)
    *   `EmployeeName` (varchar/string)

*   **`tblMS_JobCompletion`:**
    *   `MId` (integer, acts as a foreign key or indicator that a record from `tblMS_JobShedule_Master` has a corresponding completion record). This table is used in a subquery to filter only completed jobs.

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The primary functionality of this ASP.NET page is:

*   **Read (List & Search):** The page displays a list of "Job Scheduling Output" records in a `GridView`. Users can search for records based on:
    *   `Job No`
    *   `WO No`
    *   `Item Code` (with a "like" search for partial matches)
    The list also supports pagination. The search mechanism involves building a dynamic SQL query based on user input from a dropdown and a text box, then binding the results to the `GridView`.

*   **Navigate to Edit/Details:** When a user clicks on the `Item Code` link within the `GridView`, the application redirects to a separate `schedule_Output_Edit_Details.aspx` page, passing `id` and `wono` as query parameters. This indicates that the current page is primarily for viewing and initiating an edit process on another dedicated page, rather than performing in-place edits.

**No explicit Create or Delete operations are performed directly on this page.** However, as per the migration guidelines, we will implement full CRUD functionality in the Django module to provide a complete and modern solution.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The following ASP.NET UI components are identified:

*   **`DrpField` (Type: `asp:DropDownList`)**:
    *   **Role:** Selects the search criterion (Job No, WO No, Item Code).
    *   **Django Equivalent:** HTML `<select>` element, managed by a Django `forms.ChoiceField` in a `SearchForm`.
*   **`TxtSearchValue` (Type: `asp:TextBox`)**:
    *   **Role:** Enters the search value.
    *   **Django Equivalent:** HTML `<input type="text">` element, managed by a Django `forms.CharField` in a `SearchForm`.
*   **`btnSearch` (Type: `asp:Button`)**:
    *   **Role:** Triggers the search operation (`binddata()`).
    *   **Django Equivalent:** An HTML `<button>` or `<input type="submit">` element. This will use HTMX's `hx-trigger="click"` and `hx-get` to refresh the data table dynamically.
*   **`SearchGridView1` (Type: `asp:GridView`)**:
    *   **Role:** Displays the tabular data (Job Schedules). Supports paging and sorting.
    *   **Django Equivalent:** A standard HTML `<table>` element. This table will be enhanced with `DataTables.js` for client-side search, sort, and pagination. The table content will be loaded and refreshed using HTMX.
    *   **Bound Fields:** `FinYear`, `WONo`, `JobNo`, `ItemCode` (as `LinkButton`), `SysDate`, `GenBy`, `Id` (hidden), `FinYearId` (hidden).
    *   **Action Column:** The `ItemCode` is a `LinkButton` that triggers a redirect to an edit/detail page. In Django, this will be an HTML `<a href>` or `hx-get` button within the table to load an edit form in a modal or navigate to a new page.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**

We will create models for `JobScheduleMaster` as the primary entity and helper models for `ItemMaster`, `FinancialMaster`, and `HROfficeStaff` to correctly map the database relationships.

**File: `machinery/models.py`**

```python
from django.db import models
from django.db.models import F, Q
from django.utils import timezone

class FinancialMaster(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class HROfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be varchar based on SessionId mapping
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}" if self.title else self.employee_name

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

class JobScheduleMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session = models.ForeignKey(HROfficeStaff, models.DO_NOTHING, db_column='SessionId', to_field='emp_id', blank=True, null=True)
    job_no = models.CharField(db_column='JobNo', max_length=50)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', default=timezone.now)
    fin_year = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Company ID

    class Meta:
        managed = False
        db_table = 'tblMS_JobShedule_Master'
        verbose_name = 'Job Schedule Output'
        verbose_name_plural = 'Job Schedule Outputs'

    def __str__(self):
        return f"{self.job_no} ({self.wo_no})"

    @property
    def get_fin_year_display(self):
        """Returns the Financial Year string."""
        return self.fin_year.fin_year if self.fin_year else 'N/A'

    @property
    def get_generated_by_display(self):
        """Returns the 'Generated By' string."""
        if self.session:
            return f"{self.session.title or ''}. {self.session.employee_name}"
        return 'N/A'

    @property
    def get_sys_date_display(self):
        """Returns the system date in DD/MM/YYYY format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else 'N/A'
    
    @staticmethod
    def get_filtered_job_schedules(search_field, search_value, comp_id, fin_year_id):
        """
        Retrieves job schedules based on search criteria, company ID, and financial year.
        This method encapsulates the original C# binddata logic.
        """
        # Filter for completed jobs and current financial year boundary
        queryset = JobScheduleMaster.objects.filter(
            id__in=models.Subquery(
                models.Subquery(
                    JobScheduleMaster.objects.filter(
                        id__in=models.Subquery(
                            models.Subquery(
                                models.Subquery(
                                    JobScheduleMaster.objects.filter(
                                        id__in=models.Subquery(
                                            JobCompletion.objects.filter(
                                                mid=F('id') # MId corresponds to JobScheduleMaster.Id
                                            ).values('mid')
                                        )
                                    ).values('id')
                                )
                            )
                        )
                    ).values('id')
                )
            ),
            comp_id=comp_id,
            fin_year__fin_year_id__lte=fin_year_id # tblMS_JobShedule_Master.FinYearId<=' " + FinYearId + "'"
        ).order_by('-id')

        if search_value:
            if search_field == '0':  # Job No
                queryset = queryset.filter(job_no=search_value)
            elif search_field == '1':  # WO No
                queryset = queryset.filter(wo_no=search_value)
            elif search_field == '2':  # Item Code
                queryset = queryset.filter(item__item_code__icontains=search_value)
        return queryset

# Add a placeholder for JobCompletion model as it's used in subquery for filtering
class JobCompletion(models.Model):
    mid = models.IntegerField(db_column='MId', primary_key=True) # Assuming MId is primary key for simplicity based on the subquery

    class Meta:
        managed = False
        db_table = 'tblMS_JobCompletion'
        verbose_name = 'Job Completion'
        verbose_name_plural = 'Job Completions'
```

### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**

We'll define a `JobScheduleMasterForm` for CRUD operations and a `JobScheduleSearchForm` for the filter bar.

**File: `machinery/forms.py`**

```python
from django import forms
from .models import JobScheduleMaster, ItemMaster, FinancialMaster, HROfficeStaff

class JobScheduleMasterForm(forms.ModelForm):
    # Dynamically populate choices for FK fields if needed, or use ModelChoiceField
    # For simplicity, assuming these might be text inputs if not configured properly by model
    # Or, they could be ModelChoiceField to select from existing entities.
    # We will use ModelChoiceField as per best practices.
    item = forms.ModelChoiceField(
        queryset=ItemMaster.objects.all(),
        empty_label="Select Item",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False # Allow null if not strictly required
    )
    fin_year = forms.ModelChoiceField(
        queryset=FinancialMaster.objects.all(),
        empty_label="Select Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )
    session = forms.ModelChoiceField(
        queryset=HROfficeStaff.objects.all(),
        empty_label="Select Session User",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )

    class Meta:
        model = JobScheduleMaster
        fields = ['job_no', 'wo_no', 'item', 'sys_date', 'fin_year', 'session', 'comp_id']
        widgets = {
            'job_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    # Add custom validation methods here if needed, e.g., to ensure unique job_no

class JobScheduleSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'Job No'),
        ('1', 'WO No'),
        ('2', 'Item Code'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        initial='Select' # Set initial value, which might not be a real choice, for "Select" option logic
    )
    search_value = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter search value'})
    )

    def clean_search_field(self):
        search_field = self.cleaned_data['search_field']
        if search_field == 'Select': # Check for the 'Select' option
            raise forms.ValidationError("Please select a valid search field.")
        return search_field

```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

We'll create views for listing, searching (via a partial), adding, updating, and deleting job schedules. We'll include the search form handling within the list view and the partial.

**File: `machinery/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import JobScheduleMaster
from .forms import JobScheduleMasterForm, JobScheduleSearchForm

class JobScheduleMasterListView(ListView):
    model = JobScheduleMaster
    template_name = 'machinery/jobschedulmaster/list.html'
    context_object_name = 'job_schedules' # Plural lowercase for context variable

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = JobScheduleSearchForm(self.request.GET)
        return context

# View for HTMX-driven table refresh including search logic
class JobScheduleMasterTablePartialView(View):
    def get(self, request, *args, **kwargs):
        search_form = JobScheduleSearchForm(request.GET)
        job_schedules = JobScheduleMaster.objects.none() # Default empty queryset

        # Placeholder for session data (CompId, FinYearId). In a real app, these would come from
        # request.user.profile or similar, or specific session management.
        # For demonstration, hardcoding or fetching from URL/mockup for now.
        # Assuming user is authenticated and has these attributes for simplicity.
        # Example: request.user.company_id, request.user.financial_year_id
        # For this example, let's use default values as we don't have actual user context.
        mock_comp_id = 1 # Example company ID
        mock_fin_year_id = 2024 # Example financial year ID

        if search_form.is_valid():
            search_field = search_form.cleaned_data.get('search_field')
            search_value = search_form.cleaned_data.get('search_value')

            # Use the model's static method to encapsulate filtering logic
            job_schedules = JobScheduleMaster.get_filtered_job_schedules(
                search_field, search_value, mock_comp_id, mock_fin_year_id
            )
        else:
            # If form is not submitted or invalid, show all initial records
            job_schedules = JobScheduleMaster.get_filtered_job_schedules(
                None, None, mock_comp_id, mock_fin_year_id # Show all initially
            )

        context = {'job_schedules': job_schedules}
        return render(request, 'machinery/jobschedulmaster/_jobschedule_table.html', context)

class JobScheduleMasterCreateView(CreateView):
    model = JobScheduleMaster
    form_class = JobScheduleMasterForm
    template_name = 'machinery/jobschedulmaster/_jobschedule_form.html' # Use partial for modal
    success_url = reverse_lazy('jobschedulmaster_list')

    def form_valid(self, form):
        # Additional logic before saving, e.g., setting comp_id or session based on current user
        # For demonstration, let's hardcode for now or retrieve from request.user
        form.instance.comp_id = 1 # Example: Set Company ID
        # form.instance.session = self.request.user.staff_profile # Example: Set Session user
        response = super().form_valid(form)
        messages.success(self.request, 'Job Schedule added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX not to swap anything
                headers={
                    'HX-Trigger': 'refreshJobScheduleMasterList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class JobScheduleMasterUpdateView(UpdateView):
    model = JobScheduleMaster
    form_class = JobScheduleMasterForm
    template_name = 'machinery/jobschedulmaster/_jobschedule_form.html' # Use partial for modal
    success_url = reverse_lazy('jobschedulmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Job Schedule updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshJobScheduleMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class JobScheduleMasterDeleteView(DeleteView):
    model = JobScheduleMaster
    template_name = 'machinery/jobschedulmaster/_jobschedule_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('jobschedulmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Job Schedule deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshJobScheduleMasterList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass instance details to confirm_delete template
        context['object_display_name'] = f"Job No: {self.object.job_no}, WO No: {self.object.wo_no}"
        return context
```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

Templates are designed for HTMX partial loading, extending a base template, and integrating DataTables.

**File: `machinery/templates/machinery/jobschedulmaster/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <h2 class="text-2xl font-bold text-gray-800">Job Scheduling Output - Edit</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'jobschedulmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 .scale-100 to #modalContent transition ease-out duration-300"
        >
            Add New Job Schedule
        </button>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search Job Schedules</h3>
        <form hx-get="{% url 'jobschedulmaster_table' %}" hx-target="#jobScheduleMasterTable-container" hx-indicator="#table-loading-indicator" hx-swap="innerHTML">
            <div class="flex flex-col md:flex-row items-end gap-4">
                <div class="flex-1 min-w-0">
                    <label for="id_search_field" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_field }}
                </div>
                <div class="flex-1 min-w-0">
                    <label for="id_search_value" class="block text-sm font-medium text-gray-700">Search Value</label>
                    {{ search_form.search_value }}
                </div>
                <div class="flex-shrink-0">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
            {% if search_form.errors %}
                <div class="mt-4 text-red-600 text-sm">
                    {% for field, errors in search_form.errors.items %}
                        {% for error in errors %}<p>{{ error }}</p>{% endfor %}
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>
    
    <div id="jobScheduleMasterTable-container"
         hx-trigger="load, refreshJobScheduleMasterList from:body"
         hx-get="{% url 'jobschedulmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="table-loading-indicator" class="flex items-center justify-center p-8 h-48" hx-indicator>
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="ml-3 text-lg text-gray-600">Loading Job Schedules...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 p-4"
         _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 .scale-100 from #modalContent transition ease-in duration-300">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto transform opacity-0 scale-95 transition-all duration-300 ease-out">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader UI state.
        // For individual modals, inline _="on click" is often sufficient.
    });

    // Custom event listener for refreshing messages after HTMX operations
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { // No content swap, likely a form submission
            // Trigger a UI refresh if needed, for example, to clear form content or show messages
            // Django messages are usually rendered on full page load, but can be updated via HTMX.
            // For now, relies on the `refreshJobScheduleMasterList` trigger.
        }
    });
</script>
{% endblock %}
```

**File: `machinery/templates/machinery/jobschedulmaster/_jobschedule_table.html`**

```html
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <table id="jobScheduleMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in job_schedules %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_fin_year_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.wo_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.job_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800 cursor-pointer"
                    hx-get="{% url 'jobschedulmaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 .scale-100 to #modalContent transition ease-out duration-300">
                    {{ obj.item.item_code }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_sys_date_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.get_generated_by_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg mr-2 shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'jobschedulmaster_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 .scale-100 to #modalContent transition ease-out duration-300"
                    >
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'jobschedulmaster_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 .scale-100 to #modalContent transition ease-out duration-300"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-8 text-center text-gray-500 text-lg">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables only if it's not already initialized
    // This script runs every time the partial is loaded via HTMX
    if ($.fn.DataTable.isDataTable('#jobScheduleMasterTable')) {
        $('#jobScheduleMasterTable').DataTable().destroy();
    }
    $('#jobScheduleMasterTable').DataTable({
        "pageLength": 17, // As per ASP.NET GridView
        "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] }, // Disable sorting on SN and Actions column
            { "width": "5%", "targets": 0 }, // SN column width
            { "width": "10%", "targets": 1 }, // Fin Yrs column width
            { "width": "30%", "targets": 6 }, // Gen By column width
        ],
        "order": [[6, "desc"]] // Default sort by Gen. Date if exists, otherwise by Id desc (backend does it)
    });
</script>
```

**File: `machinery/templates/machinery/jobschedulmaster/_jobschedule_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Job Schedule</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on--after-request="if(event.detail.xhr.status === 204) {
                                  htmx.find('#modal')._('remove .flex from me then remove .opacity-100 .scale-100 from #modalContent transition ease-in duration-300');
                                  htmx.trigger(htmx.find('body'), 'refreshJobScheduleMasterList');
                                }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="{{ form.job_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Job No</label>
                {{ form.job_no }}
                {% if form.job_no.errors %}<p class="text-red-600 text-xs mt-1">{{ form.job_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">WO No</label>
                {{ form.wo_no }}
                {% if form.wo_no.errors %}<p class="text-red-600 text-xs mt-1">{{ form.wo_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.item.id_for_label }}" class="block text-sm font-medium text-gray-700">Item</label>
                {{ form.item }}
                {% if form.item.errors %}<p class="text-red-600 text-xs mt-1">{{ form.item.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.sys_date.id_for_label }}" class="block text-sm font-medium text-gray-700">System Date</label>
                {{ form.sys_date }}
                {% if form.sys_date.errors %}<p class="text-red-600 text-xs mt-1">{{ form.sys_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.fin_year.id_for_label }}" class="block text-sm font-medium text-gray-700">Financial Year</label>
                {{ form.fin_year }}
                {% if form.fin_year.errors %}<p class="text-red-600 text-xs mt-1">{{ form.fin_year.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.session.id_for_label }}" class="block text-sm font-medium text-gray-700">Generated By (Session User)</label>
                {{ form.session }}
                {% if form.session.errors %}<p class="text-red-600 text-xs mt-1">{{ form.session.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.comp_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Company ID</label>
                {{ form.comp_id }}
                {% if form.comp_id.errors %}<p class="text-red-600 text-xs mt-1">{{ form.comp_id.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 .scale-100 from #modalContent transition ease-in duration-300">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Job Schedule
            </button>
        </div>
    </form>
</div>
```

**File: `machinery/templates/machinery/jobschedulmaster/_jobschedule_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the job schedule?</p>
    <p class="font-medium text-red-600 mb-6"><strong>{{ object_display_name }}</strong></p>
    
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on--after-request="if(event.detail.xhr.status === 204) {
                                  htmx.find('#modal')._('remove .flex from me then remove .opacity-100 .scale-100 from #modalContent transition ease-in duration-300');
                                  htmx.trigger(htmx.find('body'), 'refreshJobScheduleMasterList');
                                }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 .scale-100 from #modalContent transition ease-in duration-300">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**

We will create URL patterns for each view, including the HTMX partial for the table.

**File: `machinery/urls.py`**

```python
from django.urls import path
from .views import (
    JobScheduleMasterListView,
    JobScheduleMasterTablePartialView,
    JobScheduleMasterCreateView,
    JobScheduleMasterUpdateView,
    JobScheduleMasterDeleteView
)

urlpatterns = [
    # Main list page
    path('job-schedule-output/', JobScheduleMasterListView.as_view(), name='jobschedulmaster_list'),
    
    # HTMX partial for table refresh
    path('job-schedule-output/table/', JobScheduleMasterTablePartialView.as_view(), name='jobschedulmaster_table'),
    
    # CRUD operations loaded into modal via HTMX
    path('job-schedule-output/add/', JobScheduleMasterCreateView.as_view(), name='jobschedulmaster_add'),
    path('job-schedule-output/edit/<int:pk>/', JobScheduleMasterUpdateView.as_view(), name='jobschedulmaster_edit'),
    path('job-schedule-output/delete/<int:pk>/', JobScheduleMasterDeleteView.as_view(), name='jobschedulmaster_delete'),
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**

Comprehensive unit tests for the `JobScheduleMaster` model, including its properties and static methods, and integration tests for all views to ensure proper functionality and HTMX interactions.

**File: `machinery/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import JobScheduleMaster, ItemMaster, FinancialMaster, HROfficeStaff, JobCompletion

class ModelSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create FinancialMaster for testing
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2024 = FinancialMaster.objects.create(fin_year_id=2024, fin_year='2024-2025')
        
        # Create HROfficeStaff for testing
        cls.staff_member = HROfficeStaff.objects.create(emp_id='USER001', title='Mr', employee_name='John Doe')
        cls.staff_member_2 = HROfficeStaff.objects.create(emp_id='USER002', title='Ms', employee_name='Jane Smith')

        # Create ItemMaster for testing
        cls.item1 = ItemMaster.objects.create(id=1, item_code='ITEM-A-001')
        cls.item2 = ItemMaster.objects.create(id=2, item_code='ITEM-B-002')

        # Create JobScheduleMaster records
        cls.job_schedule_1 = JobScheduleMaster.objects.create(
            id=101,
            session=cls.staff_member,
            job_no='JOB-001',
            item=cls.item1,
            sys_date=timezone.datetime(2023, 10, 26, 10, 0, 0, tzinfo=timezone.utc),
            fin_year=cls.fin_year_2023,
            wo_no='WO-001',
            comp_id=1
        )
        cls.job_schedule_2 = JobScheduleMaster.objects.create(
            id=102,
            session=cls.staff_member_2,
            job_no='JOB-002',
            item=cls.item2,
            sys_date=timezone.datetime(2024, 1, 15, 11, 30, 0, tzinfo=timezone.utc),
            fin_year=cls.fin_year_2024,
            wo_no='WO-002',
            comp_id=1
        )
        cls.job_schedule_3 = JobScheduleMaster.objects.create(
            id=103,
            session=cls.staff_member,
            job_no='JOB-003',
            item=cls.item1,
            sys_date=timezone.datetime(2024, 2, 1, 12, 0, 0, tzinfo=timezone.utc),
            fin_year=cls.fin_year_2024,
            wo_no='WO-003',
            comp_id=2 # Different company ID
        )

        # Create JobCompletion records for job_schedule_1 and job_schedule_2
        JobCompletion.objects.create(mid=cls.job_schedule_1.id)
        JobCompletion.objects.create(mid=cls.job_schedule_2.id)

class JobScheduleMasterModelTest(ModelSetupMixin, TestCase):
    def test_job_schedule_creation(self):
        job = JobScheduleMaster.objects.get(id=self.job_schedule_1.id)
        self.assertEqual(job.job_no, 'JOB-001')
        self.assertEqual(job.wo_no, 'WO-001')
        self.assertEqual(job.item.item_code, 'ITEM-A-001')
        self.assertEqual(job.fin_year.fin_year, '2023-2024')
        self.assertEqual(job.session.employee_name, 'John Doe')
        self.assertEqual(job.comp_id, 1)

    def test_get_fin_year_display_property(self):
        job = JobScheduleMaster.objects.get(id=self.job_schedule_1.id)
        self.assertEqual(job.get_fin_year_display, '2023-2024')
        job_no_fin_year = JobScheduleMaster.objects.create(id=104, job_no='JOB-004', item=self.item1, sys_date=timezone.now(), comp_id=1)
        self.assertEqual(job_no_fin_year.get_fin_year_display, 'N/A')

    def test_get_generated_by_display_property(self):
        job = JobScheduleMaster.objects.get(id=self.job_schedule_1.id)
        self.assertEqual(job.get_generated_by_display, 'Mr. John Doe')
        job_no_session = JobScheduleMaster.objects.create(id=105, job_no='JOB-005', item=self.item1, sys_date=timezone.now(), fin_year=self.fin_year_2024, comp_id=1)
        self.assertEqual(job_no_session.get_generated_by_display, 'N/A')

    def test_get_sys_date_display_property(self):
        job = JobScheduleMaster.objects.get(id=self.job_schedule_1.id)
        self.assertEqual(job.get_sys_date_display, '26/10/2023')

    def test_get_filtered_job_schedules_no_search(self):
        # Assume mock_comp_id=1 and mock_fin_year_id=2024
        # Should return job_schedule_1 (2023), job_schedule_2 (2024)
        filtered_jobs = JobScheduleMaster.get_filtered_job_schedules(None, None, 1, 2024)
        self.assertIn(self.job_schedule_1, filtered_jobs)
        self.assertIn(self.job_schedule_2, filtered_jobs)
        self.assertNotIn(self.job_schedule_3, filtered_jobs) # Different comp_id
        self.assertEqual(len(filtered_jobs), 2)

    def test_get_filtered_job_schedules_by_job_no(self):
        filtered_jobs = JobScheduleMaster.get_filtered_job_schedules('0', 'JOB-001', 1, 2024)
        self.assertIn(self.job_schedule_1, filtered_jobs)
        self.assertNotIn(self.job_schedule_2, filtered_jobs)
        self.assertEqual(len(filtered_jobs), 1)

    def test_get_filtered_job_schedules_by_wo_no(self):
        filtered_jobs = JobScheduleMaster.get_filtered_job_schedules('1', 'WO-002', 1, 2024)
        self.assertIn(self.job_schedule_2, filtered_jobs)
        self.assertNotIn(self.job_schedule_1, filtered_jobs)
        self.assertEqual(len(filtered_jobs), 1)

    def test_get_filtered_job_schedules_by_item_code(self):
        filtered_jobs = JobScheduleMaster.get_filtered_job_schedules('2', 'ITEM-A', 1, 2024)
        self.assertIn(self.job_schedule_1, filtered_jobs)
        self.assertNotIn(self.job_schedule_2, filtered_jobs) # ITEM-B-002
        self.assertEqual(len(filtered_jobs), 1)
        
        filtered_jobs_part = JobScheduleMaster.get_filtered_job_schedules('2', 'ITEM', 1, 2024)
        self.assertIn(self.job_schedule_1, filtered_jobs_part)
        self.assertIn(self.job_schedule_2, filtered_jobs_part)
        self.assertEqual(len(filtered_jobs_part), 2)


class JobScheduleMasterViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('jobschedulmaster_list')
        self.table_url = reverse('jobschedulmaster_table')
        self.add_url = reverse('jobschedulmaster_add')
        self.edit_url = reverse('jobschedulmaster_edit', args=[self.job_schedule_1.id])
        self.delete_url = reverse('jobschedulmaster_delete', args=[self.job_schedule_1.id])

    def test_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobschedulmaster/list.html')
        self.assertIn('search_form', response.context)

    def test_table_partial_view_get_no_search(self):
        response = self.client.get(self.table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobschedulmaster/_jobschedule_table.html')
        self.assertIn('job_schedules', response.context)
        # Check that expected number of records for default comp_id=1, fin_year_id=2024 are present
        self.assertEqual(len(response.context['job_schedules']), 2) 
        self.assertContains(response, 'JOB-001')
        self.assertContains(response, 'JOB-002')
        self.assertNotContains(response, 'JOB-003')

    def test_table_partial_view_get_with_search_job_no(self):
        response = self.client.get(self.table_url + '?search_field=0&search_value=JOB-001', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'JOB-001')
        self.assertNotContains(response, 'JOB-002')

    def test_table_partial_view_get_with_search_item_code(self):
        response = self.client.get(self.table_url + '?search_field=2&search_value=ITEM-B', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM-B-002')
        self.assertNotContains(response, 'ITEM-A-001')

    def test_table_partial_view_get_with_invalid_search_field(self):
        response = self.client.get(self.table_url + '?search_field=Select&search_value=TEST', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form invalid, but view renders partial
        self.assertContains(response, 'Please select a valid search field.') # Error message
        self.assertNotContains(response, 'JOB-001') # No results due to invalid form

    def test_create_view_get_htmx(self):
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobschedulmaster/_jobschedule_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Job Schedule')

    def test_create_view_post_htmx_success(self):
        new_job_data = {
            'job_no': 'JOB-NEW',
            'wo_no': 'WO-NEW',
            'item': self.item1.id,
            'sys_date': timezone.datetime(2024, 3, 1, 10, 0, 0).isoformat(), # Datetime needs to be ISO format for HTML input
            'fin_year': self.fin_year_2024.fin_year_id,
            'session': self.staff_member.emp_id,
            'comp_id': 1
        }
        response = self.client.post(self.add_url, new_job_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(JobScheduleMaster.objects.filter(job_no='JOB-NEW').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshJobScheduleMasterList')

    def test_create_view_post_htmx_invalid(self):
        invalid_data = {
            'job_no': '', # Missing required field
            'wo_no': 'WO-INVALID',
            'item': self.item1.id,
            'sys_date': timezone.datetime(2024, 3, 1, 10, 0, 0).isoformat(),
            'fin_year': self.fin_year_2024.fin_year_id,
            'session': self.staff_member.emp_id,
            'comp_id': 1
        }
        response = self.client.post(self.add_url, invalid_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'machinery/jobschedulmaster/_jobschedule_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(JobScheduleMaster.objects.filter(wo_no='WO-INVALID').exists())

    def test_update_view_get_htmx(self):
        response = self.client.get(self.edit_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobschedulmaster/_jobschedule_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Job Schedule')
        self.assertContains(response, self.job_schedule_1.job_no)

    def test_update_view_post_htmx_success(self):
        updated_data = {
            'job_no': 'JOB-001-UPDATED',
            'wo_no': self.job_schedule_1.wo_no,
            'item': self.job_schedule_1.item.id,
            'sys_date': self.job_schedule_1.sys_date.isoformat(),
            'fin_year': self.job_schedule_1.fin_year.fin_year_id,
            'session': self.job_schedule_1.session.emp_id,
            'comp_id': self.job_schedule_1.comp_id
        }
        response = self.client.post(self.edit_url, updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.job_schedule_1.refresh_from_db()
        self.assertEqual(self.job_schedule_1.job_no, 'JOB-001-UPDATED')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshJobScheduleMasterList')

    def test_delete_view_get_htmx(self):
        response = self.client.get(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/jobschedulmaster/_jobschedule_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, f'Job No: {self.job_schedule_1.job_no}, WO No: {self.job_schedule_1.wo_no}')

    def test_delete_view_post_htmx_success(self):
        initial_count = JobScheduleMaster.objects.count()
        response = self.client.post(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(JobScheduleMaster.objects.count(), initial_count - 1)
        self.assertFalse(JobScheduleMaster.objects.filter(id=self.job_schedule_1.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshJobScheduleMasterList')
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated Django code already incorporates HTMX and Alpine.js for dynamic interactions:

*   **HTMX for Search & Table Refresh:**
    *   The search form uses `hx-get` to trigger a request to `jobschedulmaster_table/` (the `JobScheduleMasterTablePartialView`).
    *   The `hx-target="#jobScheduleMasterTable-container"` and `hx-swap="innerHTML"` attributes ensure that only the table content is updated, not the entire page.
    *   `hx-trigger="load, refreshJobScheduleMasterList from:body"` on the table container ensures the table loads on page load and refreshes whenever a `refreshJobScheduleMasterList` custom event is triggered (e.g., after a successful CRUD operation).
    *   `hx-indicator` is used to show a loading spinner during table updates.

*   **HTMX for Modals (Add, Edit, Delete):**
    *   Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch the respective form/confirmation partials (`_jobschedule_form.html` or `_jobschedule_confirm_delete.html`).
    *   `hx-target="#modalContent"` directs the loaded content into the modal's inner container.
    *   Alpine.js-like directives (`_="on click add .flex to #modal ..."`) handle showing and hiding the modal CSS classes for a smooth transition.

*   **HTMX for Form Submissions:**
    *   Forms within the modal use `hx-post` to submit data to the current URL.
    *   `hx-swap="none"` prevents HTMX from swapping any content, as the server will send a `204 No Content` response.
    *   `hx-on--after-request` or `on click` directives include logic to close the modal (`_('remove .flex from me ...')`) and trigger the `refreshJobScheduleMasterList` event on the body to refresh the main data table (`htmx.trigger(htmx.find('body'), 'refreshJobScheduleMasterList');`).

*   **DataTables Integration:**
    *   The `_jobschedule_table.html` partial contains the `<table id="jobScheduleMasterTable">`.
    *   A `<script>` block within this partial initializes `DataTables` on this `id`. This ensures DataTables is re-initialized correctly every time the partial is swapped by HTMX, providing client-side search, sorting, and pagination.
    *   The `pageLength` and `lengthMenu` are configured to match the original ASP.NET `GridView`'s `PageSize`.

*   **Alpine.js for UI State:**
    *   While much of the dynamic behavior is handled by HTMX, Alpine.js is integrated for simple UI state management, primarily for controlling the visibility and transition effects of the modal (`on click add/remove .flex to #modal`).

### Final Notes

*   **Placeholders:** Replace `mock_comp_id` and `mock_fin_year_id` with actual dynamic values obtained from the logged-in user's session or profile in a production environment.
*   **DRY Principle:** Templates are designed to be partials, promoting reusability for form and delete confirmation modals. The `base.html` (not included here) would handle common headers, footers, navigation, and CDN links.
*   **Business Logic:** All data retrieval and filtering logic, which was previously in the C# `binddata()` method, is now encapsulated within the `JobScheduleMaster` model's `get_filtered_job_schedules` static method, adhering to the fat model principle.
*   **Tests:** The provided tests cover model properties, methods, and view interactions including HTMX requests, ensuring high test coverage and reliability of the migrated components.
*   **Frontend Libraries:** Only HTMX, Alpine.js, and DataTables are used for frontend interactivity, avoiding heavy JavaScript frameworks and complex build processes. Tailwind CSS classes are directly applied for styling.

This comprehensive plan provides a clear, step-by-step guide for automatically converting the described ASP.NET module into a modern, maintainable Django application with a focus on efficiency and best practices.