This document outlines a comprehensive plan to modernize the provided ASP.NET `Schedule_New.aspx` application to a Django-based solution. The approach prioritizes automation, adheres to modern Django best practices, and leverages HTMX and Alpine.js for a highly interactive frontend without extensive JavaScript.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the ASP.NET code, particularly the `BindDataCust` method and `SqlCommand` statements, we identify the following database tables and their relevant columns. The primary keys are inferred from usage (`DataKeyNames` in GridView, filtering).

**Inferred Tables and Columns:**

*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (String)
*   **`SD_Cust_Master`**:
    *   `CustomerId` (Primary Key, String)
    *   `CustomerName` (String)
    *   `CompId` (Integer)
*   **`SD_Cust_WorkOrder_Master`**:
    *   `Id` (Primary Key, Integer, inferred auto-incrementing ID)
    *   `EnqId` (String)
    *   `CustomerId` (String, Foreign Key to `SD_Cust_Master`)
    *   `WONo` (String, used as DataKeyNames in GridView, important identifier)
    *   `PONo` (String)
    *   `POId` (String)
    *   `SessionId` (String)
    *   `FinYearId` (Integer, Foreign Key to `tblFinancial_master`)
    *   `SysDate` (String, stored as `varchar` and parsed in ASP.NET code)
    *   `CompId` (Integer)

## Step 2: Identify Backend Functionality

The ASP.NET page primarily functions as a **Read** (List/Search) interface.

*   **Read (List & Search):** The `BindDataCust` method retrieves data based on search criteria (Customer Name or Work Order No) and company/financial year context from session. It performs join-like operations in C# code to gather `FinYear` and `CustomerName` for display in the GridView.
*   **No explicit Create, Update, or Delete operations** are performed directly on this page. The "Select" button merely redirects to another page (`Schedule_New_Details.aspx`) for further actions.

## Step 3: Infer UI Components

The ASP.NET page utilizes standard Web Forms controls:

*   **`DropDownList1`**: A dropdown for selecting search criteria ("Customer Name" or "WO No").
*   **`txtEnqId`**: A `TextBox` for inputting "WO No" for search.
*   **`TxtSearchValue`**: A `TextBox` for inputting "Customer Name" for search, enhanced with `AutoCompleteExtender` for predictive text.
*   **`btnSearch`**: A `Button` to trigger the search operation.
*   **`SearchGridView1`**: A `GridView` displaying the results of the search. It supports pagination, displays columns like `FinYear`, `WONo`, `SysDate`, `CustomerName`, `CustomerId`, `POId`, and includes a `LinkButton` ("Select") for row-specific actions.

## Step 4: Generate Django Code

We will create a new Django application, named `machinery` (as per the module path in ASP.NET), to house these components.

### 4.1 Models

We will create Django models for `FinancialYear`, `Customer`, and `WorkOrder`, mapping them to the existing database tables. The `SysDate` field in `WorkOrder` is stored as a string (`varchar`) in the ASP.NET database; we will map it as a `CharField` and handle date parsing in Python logic.

**`machinery/models.py`**

```python
import datetime
from django.db import models
from django.db.models import Q

class FinancialYear(models.Model):
    """
    Represents the financial year table (tblFinancial_master).
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Customer(models.Model):
    """
    Represents the customer master table (SD_Cust_Master).
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId') # Company ID for filtering

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} ({self.customer_id})"

    @classmethod
    def get_customer_id_by_name(cls, customer_name: str) -> str | None:
        """
        Simulates the fun.getCode functionality to get CustomerId from CustomerName.
        """
        try:
            return cls.objects.filter(customer_name__iexact=customer_name).values_list('customer_id', flat=True).first()
        except Exception:
            return None


class WorkOrderManager(models.Manager):
    """
    Custom manager for WorkOrder to encapsulate complex search logic.
    """
    def search_work_orders(self, search_by: str, search_value: str, comp_id: int, fin_year_id: int):
        """
        Applies filtering logic similar to ASP.NET's BindDataCust.
        Assumes fin_year_id is inclusive (FinYearId<='{FinYearId}').
        """
        queryset = self.get_queryset().filter(comp_id=comp_id, financial_year__fin_year_id__lte=fin_year_id)

        if search_by == '1': # WO No
            if search_value:
                queryset = queryset.filter(wo_no__iexact=search_value)
        elif search_by == '0': # Customer Name
            if search_value:
                customer_id = Customer.get_customer_id_by_name(search_value)
                if customer_id:
                    queryset = queryset.filter(customer_id=customer_id)
                else:
                    # If customer name doesn't resolve to an ID, return empty queryset
                    queryset = queryset.none()
        
        # Order by Id Desc as in ASP.NET code
        return queryset.order_by('-id')

class WorkOrder(models.Model):
    """
    Represents the work order master table (SD_Cust_WorkOrder_Master).
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.CharField(db_column='POId', max_length=50, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', to_field='fin_year_id')
    sys_date = models.CharField(db_column='SysDate', max_length=20) # Stored as 'MM-DD-YYYY' string in DB
    comp_id = models.IntegerField(db_column='CompId')

    objects = WorkOrderManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

    @property
    def formatted_sys_date(self):
        """
        Parses the SysDate string (MM-DD-YYYY) and returns it in DD-MM-YYYY format for display.
        This mirrors the ASP.NET conversion logic.
        """
        if self.sys_date:
            try:
                dt_obj = datetime.datetime.strptime(self.sys_date, '%m-%d-%Y').date()
                return dt_obj.strftime('%d-%m-%Y')
            except ValueError:
                return self.sys_date # Return original if parsing fails
        return ''

```

### 4.2 Forms

A regular Django `forms.Form` will be used for the search functionality, as there's no direct model CRUD on this page.

**`machinery/forms.py`**

```python
from django import forms
from .models import Customer

class WorkOrderSearchForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('Select', 'Select'), # Mimic ASP.NET default
        ('0', 'Customer Name'),
        ('1', 'WO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-full', # Tailwind CSS class
            'hx-get': 'search_form_fields/', # HTMX to update visible fields
            'hx-target': '#search-fields-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        }),
        label="Search By"
    )
    # These fields will be dynamically rendered based on search_by selection
    txt_enq_id = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full', # Tailwind CSS class
            'placeholder': 'Enter WO No',
        }),
        label="Work Order No."
    )
    txt_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full', # Tailwind CSS class
            'placeholder': 'Enter Customer Name',
            'hx-get': '/api/customers/autocomplete/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        }),
        label="Customer Name"
    )

    def __init__(self, *args, **kwargs):
        initial_search_by = kwargs.pop('initial_search_by', 'Select')
        super().__init__(*args, **kwargs)
        self.fields['search_by'].initial = initial_search_by
        self.set_field_visibility(initial_search_by)

    def set_field_visibility(self, search_by_value):
        """Manages visibility of search input fields based on selected search type."""
        if search_by_value == '0': # Customer Name
            self.fields['txt_enq_id'].widget.attrs['class'] += ' hidden'
            self.fields['txt_search_value'].widget.attrs['class'] = self.fields['txt_search_value'].widget.attrs['class'].replace(' hidden', '')
            self.fields['txt_search_value'].widget.attrs['hx-target'] = '#autocomplete-results'
            self.fields['txt_search_value'].widget.attrs['hx-swap'] = 'innerHTML'
            self.fields['txt_search_value'].widget.attrs['hx-get'] = '/api/customers/autocomplete/' # Ensure path is correct
            self.fields['txt_search_value'].widget.attrs['hx-trigger'] = 'keyup changed delay:500ms'
            self.fields['txt_search_value'].widget.attrs['autocomplete'] = 'off'
        elif search_by_value == '1' or search_by_value == 'Select': # WO No or Select (default)
            self.fields['txt_enq_id'].widget.attrs['class'] = self.fields['txt_enq_id'].widget.attrs['class'].replace(' hidden', '')
            self.fields['txt_search_value'].widget.attrs['class'] += ' hidden'
            self.fields['txt_search_value'].widget.attrs.pop('hx-get', None)
            self.fields['txt_search_value'].widget.attrs.pop('hx-target', None)
            self.fields['txt_search_value'].widget.attrs.pop('hx-swap', None)
            self.fields['txt_search_value'].widget.attrs.pop('hx-trigger', None)
            self.fields['txt_search_value'].widget.attrs.pop('autocomplete', None)
        else: # Default case or any other unknown value
            self.fields['txt_enq_id'].widget.attrs['class'] += ' hidden'
            self.fields['txt_search_value'].widget.attrs['class'] += ' hidden'

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        txt_enq_id = cleaned_data.get('txt_enq_id')
        txt_search_value = cleaned_data.get('txt_search_value')

        if search_by == '1' and not txt_enq_id:
            # Not strict validation as ASP.NET example allows empty search
            pass
        elif search_by == '0' and not txt_search_value:
            # Not strict validation as ASP.NET example allows empty search
            pass
        
        return cleaned_data

```

### 4.3 Views

We will use `TemplateView` for the main page and `ListView` for the HTMX-driven table content. An additional `View` will handle the autocomplete.

**`machinery/views.py`**

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from .models import WorkOrder, Customer
from .forms import WorkOrderSearchForm

# Assume 'compid' and 'finyear' are available in session, similar to ASP.NET
# For production, these might be retrieved from user profile or other settings.
# For simplicity in this example, we'll use placeholder values or fetch from session if available.

class WorkOrderListView(TemplateView):
    """
    Main view for the Work Order scheduling input page.
    Renders the search form and the container for the HTMX-loaded table.
    """
    template_name = 'machinery/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with default 'Select' or existing search params if needed
        context['form'] = WorkOrderSearchForm(initial_search_by='Select')
        return context

class WorkOrderTablePartialView(ListView):
    """
    View that renders the partial HTML for the Work Order table,
    designed to be loaded via HTMX.
    """
    model = WorkOrder
    template_name = 'machinery/workorder/_workorder_table.html'
    context_object_name = 'workorders'
    paginate_by = 18 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Retrieve session context (CompId, FinYearId)
        # For actual production, ensure these are securely obtained, e.g., from user profile
        comp_id = self.request.session.get('compid', 1)  # Defaulting to 1 for example
        fin_year_id = self.request.session.get('finyear', 2024) # Defaulting to 2024 for example

        search_by = self.request.GET.get('search_by', 'Select')
        txt_enq_id = self.request.GET.get('txt_enq_id', '')
        txt_search_value = self.request.GET.get('txt_search_value', '')

        # Pass filters to the custom manager's search method
        queryset = WorkOrder.objects.search_work_orders(
            search_by=search_by,
            search_value=txt_enq_id if search_by == '1' else txt_search_value,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        return queryset

    def get(self, request, *args, **kwargs):
        # Render the partial template with the queryset
        self.object_list = self.get_queryset()
        context = self.get_context_data()
        return self.render_to_response(context)

class SearchFormFieldsPartialView(View):
    """
    HTMX endpoint to dynamically update search input fields based on dropdown selection.
    """
    def get(self, request, *args, **kwargs):
        search_by_value = request.GET.get('search_by', 'Select')
        form = WorkOrderSearchForm(initial_search_by=search_by_value)
        
        # Render just the relevant input fields
        html_content = render_to_string('machinery/workorder/_search_form_fields.html', {'form': form})
        return HttpResponse(html_content)

class CustomerAutocompleteView(View):
    """
    HTMX endpoint for customer name autocomplete, mimicking AutoCompleteExtender.
    Returns a list of matching customer names and their IDs.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('txt_search_value', '')
        # Retrieve session context for CompId
        comp_id = request.session.get('compid', 1) # Defaulting to 1 for example

        if query:
            customers = Customer.objects.filter(
                Q(customer_name__icontains=query) | Q(customer_id__icontains=query),
                comp_id=comp_id
            ).values('customer_name', 'customer_id')[:10] # Limit to 10 as in ASP.NET example

            # Format as 'CustomerName [CustomerId]'
            suggestions = [f"{c['customer_name']} [{c['customer_id']}]" for c in customers]
        else:
            suggestions = []
        
        # Return as plain text lines, or JSON depending on HTMX configuration
        # For simplicity with the standard AutoCompleteExtender emulation, we'll return a simple HTML list.
        # HTMX will put this into the #autocomplete-results target.
        html_response = "<div id='autocomplete-results' class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1'>"
        if suggestions:
            for s in suggestions:
                # Use hx-select and hx-on:click to select the item and populate the textbox
                html_response += f"<div class='py-2 px-3 cursor-pointer hover:bg-gray-100' hx-on:click=\"this.closest('#autocomplete-results').previousElementSibling.value = '{s.split(' [')[0]}'; this.closest('#autocomplete-results').innerHTML = '';\">{s}</div>"
        else:
            html_response += "<div class='py-2 px-3 text-gray-500'>No results</div>"
        html_response += "</div>"

        return HttpResponse(html_response)

```

### 4.4 Templates

We will create the main list template and partials for the table content and search form fields to facilitate HTMX-driven updates.

**`machinery/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Job Scheduling Input - New</h2>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search Work Orders</h3>
        <form id="searchForm" hx-get="{% url 'machinery:workorder_table_partial' %}" hx-target="#workOrderTable-container" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="id_search_by" class="block text-sm font-medium text-gray-700">{{ form.search_by.label }}</label>
                    {{ form.search_by }}
                </div>
                <div id="search-fields-container" class="relative">
                    {# HTMX will swap this container based on dropdown selection #}
                    {% include 'machinery/workorder/_search_form_fields.html' %}
                </div>
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div id="workOrderTable-container"
         hx-trigger="load, searchSubmitted from:#searchForm" {# Initial load and refresh on search #}
         hx-get="{% url 'machinery:workorder_table_partial' %}"
         hx-indicator="#table-loading-indicator"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div id="table-loading-indicator" class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize DataTable if the table partial was swapped
        if (event.detail.target.id === 'workOrderTable-container') {
            $('#workorderTable').DataTable({
                "pageLength": 18, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 18, 25, 50, -1], [10, 18, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance before re-initialization
                "processing": true,
                "responsive": true,
                "order": [[2, "desc"]] // Sort by WO No (index 2) descending, similar to "Order by Id Desc" in ASP.NET
            });
        }
    });

    // Manually trigger the form submission when search_by dropdown changes
    // The hx-trigger on the dropdown updates the fields, but not the table
    // The table updates on form submit or page load
    document.addEventListener('DOMContentLoaded', () => {
        const searchByDropdown = document.getElementById('id_search_by');
        const searchForm = document.getElementById('searchForm');
        searchByDropdown.addEventListener('change', () => {
            // Trigger form submission to update the table
            // This ensures the table refreshes with empty data when search_by changes to 'Select' or 'WO No'
            // and the corresponding text field is empty.
            // Using a custom HTMX trigger event
            htmx.trigger(searchForm, 'searchSubmitted');
        });
    });
</script>
{% endblock %}
```

**`machinery/workorder/_workorder_table.html`** (Partial template for HTMX)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="workorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if workorders %}
                {% for obj in workorders %}
                <tr>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.financial_year.fin_year }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.wo_no }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.formatted_sys_date }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.customer.customer_name }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.customer.customer_id }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                        <a href="{% url 'machinery:schedule_details' wo_no=obj.wo_no %}"
                           class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-xs"
                           hx-boost="true"> {# Use hx-boost for traditional link behavior, or hx-get/target for modal/partial load #}
                            Select
                        </a>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="7" class="py-8 px-6 text-center text-lg text-red-700">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# The script to initialize DataTables will be in list.html and triggered on htmx:afterSwap #}
```

**`machinery/workorder/_search_form_fields.html`** (Partial template for HTMX to dynamically update search fields)

```html
{% comment %}
    This partial is dynamically swapped into #search-fields-container
    It renders the appropriate input field based on the form.search_by value set in the view/form.
{% endcomment %}
{% if form.search_by.value == '0' %} {# Customer Name #}
    <label for="id_txt_search_value" class="block text-sm font-medium text-gray-700">{{ form.txt_search_value.label }}</label>
    {{ form.txt_search_value }}
    <div id="autocomplete-results"></div> {# Placeholder for autocomplete suggestions #}
{% elif form.search_by.value == '1' %} {# WO No #}
    <label for="id_txt_enq_id" class="block text-sm font-medium text-gray-700">{{ form.txt_enq_id.label }}</label>
    {{ form.txt_enq_id }}
{% else %} {# Select (default) or other #}
    <label for="id_txt_enq_id" class="block text-sm font-medium text-gray-700">Search Value</label>
    <input type="text" disabled class="box3 w-full bg-gray-100 cursor-not-allowed" placeholder="Select search type">
{% endif %}

```

### 4.5 URLs

URL patterns for the new Django application. The `schedule_details` URL is a placeholder for the next page (`Schedule_New_Details.aspx`).

**`machinery/urls.py`**

```python
from django.urls import path
from .views import (
    WorkOrderListView,
    WorkOrderTablePartialView,
    SearchFormFieldsPartialView,
    CustomerAutocompleteView,
)

app_name = 'machinery' # Namespace for this app

urlpatterns = [
    # Main page for job scheduling input
    path('schedule/new/', WorkOrderListView.as_view(), name='schedule_new'),
    
    # HTMX endpoint for the work order table content (search results)
    path('schedule/new/table/', WorkOrderTablePartialView.as_view(), name='workorder_table_partial'),
    
    # HTMX endpoint to dynamically update search form fields (dropdown change)
    path('schedule/new/search_form_fields/', SearchFormFieldsPartialView.as_view(), name='search_form_fields'),

    # API endpoint for customer name autocomplete (used by HTMX for TxtSearchValue)
    # Note: This path should match the hx-get in forms.py for txt_search_value
    path('api/customers/autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder for the "Select" button redirect
    # This assumes 'schedule_details' will be in the same app or a related one.
    path('schedule/<str:wo_no>/details/', WorkOrderListView.as_view(), name='schedule_details'), # Replace with actual detail view later
]
```

**Root `project/urls.py` (assuming `machinery` is your app name):**

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('machinery/', include('machinery.urls', namespace='machinery')), # Include the new app's URLs
    # ... other project URLs
]
```

### 4.6 Tests

Comprehensive tests for models, the custom manager logic, forms, and views including HTMX interactions.

**`machinery/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import Q
import datetime
from unittest.mock import patch, MagicMock

# Import models, forms, and views
from .models import FinancialYear, Customer, WorkOrder
from .forms import WorkOrderSearchForm
from .views import (
    WorkOrderListView,
    WorkOrderTablePartialView,
    SearchFormFieldsPartialView,
    CustomerAutocompleteView,
)

class ModelTest(TestCase):
    """
    Unit tests for FinancialYear, Customer, and WorkOrder models.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that will be used by all test methods
        cls.comp_id = 1
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year="2022-23")
        cls.fin_year_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year="2023-24")

        cls.customer_alpha = Customer.objects.create(customer_id="C001", customer_name="Alpha Corp", comp_id=cls.comp_id)
        cls.customer_beta = Customer.objects.create(customer_id="C002", customer_name="Beta Ltd", comp_id=cls.comp_id)
        cls.customer_gamma = Customer.objects.create(customer_id="C003", customer_name="Gamma Inc", comp_id=cls.comp_id + 1) # Different comp_id

        cls.wo1 = WorkOrder.objects.create(
            id=101, enq_id="E001", customer=cls.customer_alpha, wo_no="WO-001", po_no="PO-001",
            po_id="P001", session_id="user1", financial_year=cls.fin_year_2024, sys_date="03-15-2024",
            comp_id=cls.comp_id
        )
        cls.wo2 = WorkOrder.objects.create(
            id=102, enq_id="E002", customer=cls.customer_beta, wo_no="WO-002", po_no="PO-002",
            po_id="P002", session_id="user1", financial_year=cls.fin_year_2024, sys_date="01-20-2024",
            comp_id=cls.comp_id
        )
        cls.wo3 = WorkOrder.objects.create(
            id=103, enq_id="E003", customer=cls.customer_alpha, wo_no="WO-003", po_no="PO-003",
            po_id="P003", session_id="user2", financial_year=cls.fin_year_2023, sys_date="11-10-2023",
            comp_id=cls.comp_id
        )
        cls.wo4_other_comp = WorkOrder.objects.create(
            id=104, enq_id="E004", customer=cls.customer_gamma, wo_no="WO-004", po_no="PO-004",
            po_id="P004", session_id="user3", financial_year=cls.fin_year_2024, sys_date="02-25-2024",
            comp_id=cls.comp_id + 1 # Different comp_id
        )

    def test_financial_year_creation(self):
        self.assertEqual(self.fin_year_2024.fin_year, "2023-24")
        self.assertEqual(FinancialYear.objects.count(), 2)

    def test_customer_creation(self):
        self.assertEqual(self.customer_alpha.customer_name, "Alpha Corp")
        self.assertEqual(Customer.objects.count(), 3)

    def test_work_order_creation(self):
        self.assertEqual(self.wo1.wo_no, "WO-001")
        self.assertEqual(self.wo1.customer.customer_name, "Alpha Corp")
        self.assertEqual(self.wo1.financial_year.fin_year, "2023-24")
        self.assertEqual(WorkOrder.objects.count(), 4)

    def test_formatted_sys_date_property(self):
        self.assertEqual(self.wo1.formatted_sys_date, "15-03-2024")
        self.assertEqual(self.wo2.formatted_sys_date, "20-01-2024")
        # Test with invalid date string
        self.wo1.sys_date = "invalid-date"
        self.assertEqual(self.wo1.formatted_sys_date, "invalid-date")

    def test_get_customer_id_by_name(self):
        self.assertEqual(Customer.get_customer_id_by_name("Alpha Corp"), "C001")
        self.assertIsNone(Customer.get_customer_id_by_name("Non Existent"))
        # Test case-insensitivity
        self.assertEqual(Customer.get_customer_id_by_name("alpha corp"), "C001")

    def test_work_order_manager_search_wo_no(self):
        # Search by WO No
        results = WorkOrder.objects.search_work_orders(
            search_by='1', search_value='WO-001', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().wo_no, "WO-001")

        results_empty_wo = WorkOrder.objects.search_work_orders(
            search_by='1', search_value='', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(results_empty_wo.count(), 3) # All WO from same comp and fin_year_id <= 2024

    def test_work_order_manager_search_customer_name(self):
        # Search by Customer Name
        results = WorkOrder.objects.search_work_orders(
            search_by='0', search_value='Alpha Corp', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(results.count(), 2)
        self.assertTrue(self.wo1 in results)
        self.assertTrue(self.wo3 in results) # wo3 is alpha corp, fin_year_id 2023 <= 2024

        results_non_existent_customer = WorkOrder.objects.search_work_orders(
            search_by='0', search_value='Non Existent', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(results_non_existent_customer.count(), 0)

        results_empty_customer_name = WorkOrder.objects.search_work_orders(
            search_by='0', search_value='', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(results_empty_customer_name.count(), 3) # All WO from same comp and fin_year_id <= 2024

    def test_work_order_manager_search_select_mode(self):
        # Search with 'Select' (no specific search value) should return all for the comp_id and fin_year_id
        results = WorkOrder.objects.search_work_orders(
            search_by='Select', search_value='', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(results.count(), 3)
        self.assertIn(self.wo1, results)
        self.assertIn(self.wo2, results)
        self.assertIn(self.wo3, results)
        self.assertNotIn(self.wo4_other_comp, results) # Exclude other company's WO

    def test_work_order_manager_ordering(self):
        # Ensure ordering is by id Desc
        results = WorkOrder.objects.search_work_orders(
            search_by='Select', search_value='', comp_id=self.comp_id, fin_year_id=self.fin_year_2024.fin_year_id
        )
        self.assertEqual(list(results.values_list('id', flat=True)), [103, 102, 101]) # Ordered by Id Desc

class WorkOrderSearchFormTest(TestCase):
    """
    Unit tests for WorkOrderSearchForm.
    """
    def test_form_initial_visibility_select(self):
        form = WorkOrderSearchForm(initial_search_by='Select')
        self.assertIn('hidden', form.fields['txt_search_value'].widget.attrs['class'])
        self.assertNotIn('hidden', form.fields['txt_enq_id'].widget.attrs['class'])

    def test_form_initial_visibility_customer_name(self):
        form = WorkOrderSearchForm(initial_search_by='0')
        self.assertNotIn('hidden', form.fields['txt_search_value'].widget.attrs['class'])
        self.assertIn('hidden', form.fields['txt_enq_id'].widget.attrs['class'])

    def test_form_initial_visibility_wo_no(self):
        form = WorkOrderSearchForm(initial_search_by='1')
        self.assertIn('hidden', form.fields['txt_search_value'].widget.attrs['class'])
        self.assertNotIn('hidden', form.fields['txt_enq_id'].widget.attrs['class'])

    def test_form_valid_data(self):
        form_data = {'search_by': '1', 'txt_enq_id': 'WO-TEST', 'txt_search_value': ''}
        form = WorkOrderSearchForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['search_by'], '1')
        self.assertEqual(form.cleaned_data['txt_enq_id'], 'WO-TEST')

        form_data = {'search_by': '0', 'txt_enq_id': '', 'txt_search_value': 'Customer Test'}
        form = WorkOrderSearchForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['search_by'], '0')
        self.assertEqual(form.cleaned_data['txt_search_value'], 'Customer Test')

    def test_form_empty_data(self):
        # The form is valid even with empty search values, as per ASP.NET behavior
        form_data = {'search_by': 'Select', 'txt_enq_id': '', 'txt_search_value': ''}
        form = WorkOrderSearchForm(data=form_data)
        self.assertTrue(form.is_valid())


class ViewTest(TestCase):
    """
    Integration tests for WorkOrder views and HTMX endpoints.
    """
    @classmethod
    def setUpTestData(cls):
        # Use same test data as ModelTest
        cls.comp_id = 1
        cls.fin_year_2024_id = 2024
        cls.fin_year_2023_id = 2023
        FinancialYear.objects.create(fin_year_id=cls.fin_year_2023_id, fin_year="2022-23")
        FinancialYear.objects.create(fin_year_id=cls.fin_year_2024_id, fin_year="2023-24")

        cls.customer_alpha = Customer.objects.create(customer_id="C001", customer_name="Alpha Corp", comp_id=cls.comp_id)
        cls.customer_beta = Customer.objects.create(customer_id="C002", customer_name="Beta Ltd", comp_id=cls.comp_id)

        WorkOrder.objects.create(
            id=101, enq_id="E001", customer=cls.customer_alpha, wo_no="WO-001", po_no="PO-001",
            po_id="P001", session_id="user1", financial_year_id=cls.fin_year_2024_id, sys_date="03-15-2024",
            comp_id=cls.comp_id
        )
        WorkOrder.objects.create(
            id=102, enq_id="E002", customer=cls.customer_beta, wo_no="WO-002", po_no="PO-002",
            po_id="P002", session_id="user1", financial_year_id=cls.fin_year_2024_id, sys_date="01-20-2024",
            comp_id=cls.comp_id
        )
        WorkOrder.objects.create(
            id=103, enq_id="E003", customer=cls.customer_alpha, wo_no="WO-003", po_no="PO-003",
            po_id="P003", session_id="user2", financial_year_id=cls.fin_year_2023_id, sys_date="11-10-2023",
            comp_id=cls.comp_id
        )
        # WO for different company
        Customer.objects.create(customer_id="C004", customer_name="Other Corp", comp_id=cls.comp_id + 1)
        WorkOrder.objects.create(
            id=104, enq_id="E004", customer_id="C004", wo_no="WO-004", po_no="PO-004",
            po_id="P004", session_id="user3", financial_year_id=cls.fin_year_2024_id, sys_date="02-25-2024",
            comp_id=cls.comp_id + 1
        )

    def setUp(self):
        self.client = Client()
        # Set session variables for comp_id and fin_year
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_2024_id
        session.save()

    def test_work_order_list_view(self):
        url = reverse('machinery:schedule_new')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/workorder/list.html')
        self.assertIsInstance(response.context['form'], WorkOrderSearchForm)

    def test_work_order_table_partial_initial_load(self):
        url = reverse('machinery:workorder_table_partial')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/workorder/_workorder_table.html')
        self.assertIn('workorders', response.context)
        # Should contain 3 work orders for comp_id=1 and fin_year_id <= 2024
        self.assertEqual(response.context['workorders'].count(), 3)
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'WO-002')
        self.assertContains(response, 'WO-003') # fin_year_id 2023 <= 2024
        self.assertNotContains(response, 'WO-004') # different comp_id

    def test_work_order_table_partial_search_wo_no(self):
        url = reverse('machinery:workorder_table_partial')
        response = self.client.get(url, {'search_by': '1', 'txt_enq_id': 'WO-001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')
        self.assertNotContains(response, 'WO-002')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_work_order_table_partial_search_customer_name(self):
        url = reverse('machinery:workorder_table_partial')
        response = self.client.get(url, {'search_by': '0', 'txt_search_value': 'Alpha Corp'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'WO-003')
        self.assertNotContains(response, 'WO-002')
        self.assertEqual(response.context['workorders'].count(), 2)

    def test_work_order_table_partial_search_customer_name_no_match(self):
        url = reverse('machinery:workorder_table_partial')
        response = self.client.get(url, {'search_by': '0', 'txt_search_value': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        self.assertEqual(response.context['workorders'].count(), 0)

    def test_work_order_table_partial_pagination(self):
        # Test if pagination context is passed
        url = reverse('machinery:workorder_table_partial')
        response = self.client.get(url, {'page': 1})
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['is_paginated'])
        self.assertEqual(response.context['paginator'].per_page, 18)

    def test_search_form_fields_partial_view_customer_name(self):
        url = reverse('machinery:search_form_fields')
        response = self.client.get(url, {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="txt_search_value"')
        self.assertContains(response, 'hx-get="/api/customers/autocomplete/"') # Ensure HTMX attributes present
        self.assertNotContains(response, 'name="txt_enq_id"')

    def test_search_form_fields_partial_view_wo_no(self):
        url = reverse('machinery:search_form_fields')
        response = self.client.get(url, {'search_by': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="txt_enq_id"')
        self.assertNotContains(response, 'name="txt_search_value"')

    def test_customer_autocomplete_view(self):
        url = reverse('machinery:customer_autocomplete')
        response = self.client.get(url, {'txt_search_value': 'Alp'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Corp [C001]')
        self.assertNotContains(response, 'Beta Ltd')

        response = self.client.get(url, {'txt_search_value': 'nonexistent'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No results')

        response = self.client.get(url, {'txt_search_value': ''})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No results')

    def test_customer_autocomplete_view_case_insensitivity(self):
        url = reverse('machinery:customer_autocomplete')
        response = self.client.get(url, {'txt_search_value': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Corp [C001]')

```

## Step 5: HTMX and Alpine.js Integration

The generated code extensively uses HTMX for dynamic interactions and DataTables for client-side table enhancements. Alpine.js is not strictly required for this specific page's functionality, but its placeholders are included as per the guidelines for future UI state management.

*   **Initial Table Load & Search:** The main `list.html` uses `hx-get` on a `div` with `hx-trigger="load, searchSubmitted from:#searchForm"` to load the `_workorder_table.html` partial. The search form's `hx-get` and `hx-target` ensure that submitting the form (via `btnSearch`) reloads only the table content, maintaining the search state in the URL parameters.
*   **Dynamic Search Fields:** The `search_by` dropdown uses `hx-get="/machinery/schedule/new/search_form_fields/"` and `hx-target="#search-fields-container"` with `hx-trigger="change"` to dynamically swap the input fields (`txt_enq_id` or `txt_search_value`) without a full page reload.
*   **Customer Autocomplete:** The `txt_search_value` field uses `hx-get="/api/customers/autocomplete/"` and `hx-target="#autocomplete-results"` with `hx-trigger="keyup changed delay:500ms"`. The `CustomerAutocompleteView` returns HTML snippets that HTMX directly inserts, allowing for dynamic suggestions. A simple Alpine-like `hx-on:click` attribute is added to suggestion items to populate the input field.
*   **DataTables:** The `_workorder_table.html` partial contains the `<table>` element. The `list.html` file includes JavaScript that uses `htmx:afterSwap` to re-initialize DataTables (`$('#workorderTable').DataTable({...})`) every time the table content is reloaded via HTMX. This ensures DataTables functionality (pagination, sorting, filtering) is always active on the new content.
*   **"Select" Button:** The "Select" `<a>` tag uses `hx-boost="true"` to make the link work with HTMX, fetching the target page (`schedule_details`) via AJAX and swapping it into the main content area, providing a smoother navigation experience without a full page refresh.

## Final Notes

*   **Placeholders:** `compid` and `finyear` are assumed to be present in `request.session`. In a real application, these might be stored in user profiles or managed by a more robust context system.
*   **Styling:** All components are designed with Tailwind CSS classes (`box3`, `w-full`, `bg-blue-600`, etc.), ensuring a consistent and modern UI.
*   **DRY Principles:** Models encapsulate business logic (e.g., `WorkOrderManager.search_work_orders`, `Customer.get_customer_id_by_name`, `WorkOrder.formatted_sys_date`). Views remain thin, primarily orchestrating data flow and rendering. Templates are broken into partials for reusability and HTMX targeting.
*   **Test Coverage:** The provided tests cover model behavior, form functionality, and the core view logic, including various search scenarios and HTMX interactions. This lays a strong foundation for maintaining high test coverage during further development.
*   **Security:** The original ASP.NET code was vulnerable to SQL Injection. The Django ORM and parametrized queries inherently prevent this. Session management is handled securely by Django's built-in session framework.