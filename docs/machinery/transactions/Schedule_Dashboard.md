## ASP.NET to Django Conversion Script: Machine Schedule Dashboard

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

### Conversion Steps:

This ASP.NET page acts as a read-only dashboard for machine schedule details, displaying data from multiple joined tables based on a selected date and query parameters (`MachineId`, `ProcessId`). The modernization plan will focus on rebuilding this dashboard with Django, HTMX, Alpine.js, and DataTables, prioritizing automation and clear separation of concerns.

## Step 1: Extract Database Schema

**Analysis:** The ASP.NET code extensively uses SQL queries to fetch data from several tables. The `fillgrid` method and `RadCalendar1_DayRender` provide explicit table and column names.

**Identified Tables and Key Columns:**

*   **`tblMS_JobShedule_Master` (Django Model: `JobScheduleMaster`)**
    *   `Id` (int, PK)
    *   `JobNo` (string)
    *   `WONo` (string)
    *   `ItemId` (int, FK to `tblDG_Item_Master` for the main item of the job)
    *   `CompId` (int)
    *   `FinYearId` (int)
*   **`tblMS_JobSchedule_Details` (Django Model: `JobScheduleDetail`)**
    *   `Id` (int, PK)
    *   `MId` (int, FK to `tblMS_JobShedule_Master`)
    *   `MachineId` (int, FK to `tblDG_Item_Master` for the machine itself)
    *   `FromDate` (datetime)
    *   `ToDate` (datetime)
    *   `FromTime` (string, potentially time field)
    *   `ToTime` (string, potentially time field)
    *   `Qty` (double, maps to `InputQty` in GridView)
    *   `Operator` (string, maps to `EmpId` in `tblHR_OfficeStaff`)
    *   `Process` (string, maps to `Id` in `tblPln_Process_Master`)
    *   `Shift` (string, `0` for Day, `1` for Night)
    *   `Released` (string, indicates if released)
*   **`tblMS_JobCompletion` (Django Model: `JobCompletion`)**
    *   `Id` (int, PK, inferred, not explicitly `Id` in provided query)
    *   `MId` (int, FK to `tblMS_JobShedule_Master`)
    *   `DId` (int, FK to `tblMS_JobSchedule_Details`)
    *   `OutputQty` (double)
    *   `UOM` (int, FK to `Unit_Master`)
*   **`tblDG_Item_Master` (Django Model: `ItemMaster`)**
    *   `Id` (int, PK)
    *   `ManfDesc` (string, used for both machine name and item description)
    *   `ItemCode` (string)
    *   `StockQty` (double, maps to `BomQty` in GridView)
    *   `CompId` (int)
    *   `FinYearId` (int)
*   **`Unit_Master` (Django Model: `UnitMaster`)**
    *   `Id` (int, PK)
    *   `Symbol` (string)
*   **`tblHR_OfficeStaff` (Django Model: `OfficeStaff`)**
    *   `EmpId` (string, PK, inferred, potentially `Id` or similar)
    *   `EmployeeName` (string)
    *   `CompId` (int)
*   **`tblPln_Process_Master` (Django Model: `ProcessMaster`)**
    *   `Id` (string, PK)
    *   `ProcessName` (string)

## Step 2: Identify Backend Functionality

**Core Functionality:**
*   **Read (Display Schedule Data):** The primary function is to display a schedule dashboard. This involves fetching `JobScheduleMaster`, `JobScheduleDetail`, and `JobCompletion` records, then joining them with `ItemMaster` (for machine and item details), `UnitMaster`, `OfficeStaff`, and `ProcessMaster` to assemble the `GridView` data.
*   **Filtering:** Data is filtered by `MachineId`, `ProcessId` (from query string) and `SelectedDate` (from RadCalendar).
*   **Calendar Highlighting:** The calendar days are colored based on:
    *   Weekends (Red)
    *   Days with active schedules (Orange)
    *   Days past a schedule's `ToDate` (Pale Green)

**No explicit CRUD for schedule entries on this page:** The ASP.NET code shows no forms for adding, editing, or deleting schedule entries directly from this dashboard. The "Cancel" button performs a redirection. Therefore, the Django solution will primarily focus on `ListView` (or `TemplateView` with a custom data fetcher) capabilities.

## Step 3: Infer UI Components

The ASP.NET page combines a header, a machine name display, a color legend, a calendar, and a data grid.

*   **Header:** Simple static text and a dynamically updated machine name.
*   **Color Legend:** Three static textboxes with background colors (Red, Orange, Green) and descriptions.
*   **RadCalendar:** A date picker with custom day rendering for visual schedule representation. This will be replaced with a custom HTML calendar component powered by HTMX/Alpine.js.
*   **GridView:** A tabular display of schedule details with columns: SN, WO No, Job No, Item Code, Description, Qty, UOM, Shift, I/p Qty, O/p Qty, From Date, To Date, From Time, To Time, Process, Operator. This will be replaced by a DataTables-enhanced HTML table.
*   **Buttons:** A "Cancel" button for navigation.

## Step 4: Generate Django Code

We will create a Django app named `machinery` to encapsulate this module.

### 4.1 Models (`machinery/models.py`)

We'll define Django models for each identified database table with `managed = False` as per the instruction for existing database mapping. We'll also add a custom manager to `JobScheduleDetail` to encapsulate the complex data retrieval logic required for the dashboard grid.

```python
from django.db import models
from django.db.models import F, Value, Q, Case, When, CharField
from django.db.models.functions import Concat, Cast
from django.utils import timezone
from datetime import date, timedelta

# --- Custom Manager for Dashboard Data ---
class JobScheduleDetailManager(models.Manager):
    def get_dashboard_data(self, machine_id, process_id, selected_date, comp_id, fin_year_id):
        # Base query joining necessary tables to replicate the ASP.NET fillgrid logic
        # Note: In a real scenario, date comparisons might need to be more robust
        # based on exact database column types (e.g., date vs datetime)
        
        # SQL equivalent logic:
        # 1. Get all scheduled dates for the given process and machine
        #    tblMS_JobSchedule_Details.Process='" + Id + "' And tblMS_JobShedule_Master.Id=tblMS_JobSchedule_Details.MId And tblMS_JobShedule_Master.CompId='" + CompId + "' And tblMS_JobSchedule_Details.Released!='1'
        # 2. Iterate through each day between FromDate and ToDate for each schedule.
        # 3. If a day matches the selected_date, then fetch the detailed grid data.

        # For simplicity, we'll try to filter directly by the selected_date for jobs that span it.
        # This might not be 100% equivalent to the day-by-day iteration in C#, but it's a common
        # Django ORM pattern for range queries.

        # The C# code first finds relevant FromDate/ToDate ranges, then iterates day by day,
        # and only if a day matches RadCalendar1.SelectedDate, it runs the second, more complex query.
        # We need to replicate this logic: Find schedules that *cover* the selected date.

        today_start = selected_date
        today_end = selected_date + timedelta(days=1) - timedelta(microseconds=1) # End of day

        # Filter schedules that overlap with the selected_date
        relevant_details = self.filter(
            Q(from_date__lte=selected_date) & Q(to_date__gte=selected_date),
            machine_id=machine_id,
            process=process_id,
            released='0' # '1' means released, so '0' means not released
        ).select_related(
            'master', 'machine_item', 'process_master', 'operator_staff'
        ).prefetch_related(
            'jobcompletion_set__uom_master' # Prefetch related JobCompletion and its UOM
        ).order_by('-master__id') # Order by MasterId desc

        dashboard_rows = []
        for detail in relevant_details:
            # Replicate the logic where a JobCompletion record is linked
            job_completion = detail.jobcompletion_set.filter(
                mid=detail.mid, did=detail.id
            ).first() # Assuming one completion per detail, or just take the first

            if job_completion:
                # Reconstruct the row structure as seen in the C# DataTable
                row = {
                    'Id': detail.master.id,
                    'WONo': detail.master.wono,
                    'JobNo': detail.master.job_no,
                    'ItemCode': detail.master.item_master.item_code if detail.master.item_master else '',
                    'ManfDesc': detail.master.item_master.manf_desc if detail.master.item_master else '',
                    'BomQty': detail.master.item_master.stock_qty if detail.master.item_master else 0.0,
                    'UOM': job_completion.uom_master.symbol if job_completion.uom_master else '',
                    'InputQty': detail.qty,
                    'OutputQty': job_completion.output_qty,
                    'FromDate': detail.from_date.strftime('%d-%m-%Y'), # Format as DD-MM-YYYY
                    'ToDate': detail.to_date.strftime('%d-%m-YYYY'), # Format as DD-MM-YYYY
                    'FromTime': detail.from_time,
                    'ToTime': detail.to_time,
                    'Operator': detail.operator_staff.employee_name if detail.operator_staff else '',
                    'Process': detail.process_master.process_name if detail.process_master else '',
                    'Shift': 'Day' if detail.shift == '0' else 'Night' if detail.shift == '1' else '',
                }
                dashboard_rows.append(row)
        return dashboard_rows
    
    def get_calendar_highlight_dates(self, machine_id, process_id, comp_id, fin_year_id):
        # Fetch all relevant FromDate and ToDate ranges for the calendar highlighting
        # tblMS_JobSchedule_Details.Process='" + Id + "' And tblMS_JobCompletion.DId= tblMS_JobSchedule_Details.Id And tblMS_JobShedule_Master.Id=tblMS_JobSchedule_Details.MId And tblMS_JobSchedule_Details.Released!='1'
        # Order by tblMS_JobSchedule_Details.FromDate Asc
        
        # Filter for schedules related to the machine and process, not released
        schedules = self.filter(
            machine_id=machine_id,
            process=process_id,
            released='0'
        ).select_related('master').order_by('from_date') # Order by FromDate Asc

        # A set to store all busy dates
        busy_dates = set()
        # A list to store the ToDate for green highlighting (available after schedule)
        to_dates = []

        for schedule in schedules:
            current_date = schedule.from_date
            while current_date <= schedule.to_date:
                busy_dates.add(current_date)
                current_date += timedelta(days=1)
            to_dates.append(schedule.to_date)
        
        return busy_dates, to_dates # Returns a set of busy dates and list of ToDates

# --- Core ERP Models (Managed = False) ---

class JobScheduleMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    job_no = models.CharField(db_column='JobNo', max_length=255, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster for the job's item
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    # Manual relationships for managed=False
    item_master = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='ItemId', related_name='job_masters', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_JobShedule_Master'
        verbose_name = 'Job Schedule Master'
        verbose_name_plural = 'Job Schedule Masters'

    def __str__(self):
        return f"{self.job_no} ({self.wono})"

class JobScheduleDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId', blank=True, null=True) # FK to JobScheduleMaster
    machine_id = models.IntegerField(db_column='MachineId', blank=True, null=True) # FK to ItemMaster (for the machine)
    from_date = models.DateField(db_column='FromDate', blank=True, null=True)
    to_date = models.DateField(db_column='ToDate', blank=True, null=True)
    from_time = models.CharField(db_column='FromTime', max_length=50, blank=True, null=True)
    to_time = models.CharField(db_column='ToTime', max_length=50, blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    operator = models.CharField(db_column='Operator', max_length=50, blank=True, null=True) # FK to OfficeStaff
    process = models.CharField(db_column='Process', max_length=50, blank=True, null=True) # FK to ProcessMaster
    shift = models.CharField(db_column='Shift', max_length=10, blank=True, null=True) # '0' for Day, '1' for Night
    released = models.CharField(db_column='Released', max_length=10, blank=True, null=True) # '1' means released, '0' means not

    # Manual relationships for managed=False
    master = models.ForeignKey(JobScheduleMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', blank=True, null=True)
    machine_item = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='MachineId', related_name='scheduled_details', blank=True, null=True)
    operator_staff = models.ForeignKey('OfficeStaff', on_delete=models.DO_NOTHING, db_column='Operator', related_name='scheduled_jobs', to_field='emp_id', blank=True, null=True) # Assuming EmpId is unique or the target field
    process_master = models.ForeignKey('ProcessMaster', on_delete=models.DO_NOTHING, db_column='Process', related_name='scheduled_details', to_field='id', blank=True, null=True) # Assuming Id is unique or the target field

    objects = JobScheduleDetailManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblMS_JobSchedule_Details'
        verbose_name = 'Job Schedule Detail'
        verbose_name_plural = 'Job Schedule Details'

    def __str__(self):
        return f"Detail for {self.master.job_no if self.master else 'N/A'} (Machine: {self.machine_item.manf_desc if self.machine_item else 'N/A'})"


class JobCompletion(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Inferred PK
    mid = models.IntegerField(db_column='MId', blank=True, null=True) # FK to JobScheduleMaster
    did = models.IntegerField(db_column='DId', blank=True, null=True) # FK to JobScheduleDetail
    output_qty = models.FloatField(db_column='OutputQty', blank=True, null=True)
    uom = models.IntegerField(db_column='UOM', blank=True, null=True) # FK to UnitMaster

    # Manual relationships for managed=False
    master = models.ForeignKey(JobScheduleMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='completions', blank=True, null=True)
    detail = models.ForeignKey(JobScheduleDetail, on_delete=models.DO_NOTHING, db_column='DId', related_name='jobcompletion_set', blank=True, null=True)
    uom_master = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOM', related_name='completions', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMS_JobCompletion'
        verbose_name = 'Job Completion'
        verbose_name_plural = 'Job Completions'

    def __str__(self):
        return f"Completion for {self.master.job_no if self.master else 'N/A'}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.manf_desc or self.item_code

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is PK
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class ProcessMaster(models.Model):
    id = models.CharField(db_column='Id', primary_key=True, max_length=50) # Assuming Id is PK and string
    process_name = models.CharField(db_column='ProcessName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Process Master'
        verbose_name_plural = 'Process Masters'

    def __str__(self):
        return self.process_name
```

### 4.2 Forms (`machinery/forms.py`)

No forms are needed for this dashboard page, as it is a read-only display.

```python
# No forms required for this read-only dashboard view.
# If CRUD functionality were added later, forms would be defined here.
```

### 4.3 Views (`machinery/views.py`)

We'll use a `TemplateView` for the main dashboard and a `TemplateView` for the HTMX-loaded table partial.

```python
from django.views.generic import TemplateView, ListView
from django.shortcuts import get_object_or_404
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from datetime import date, timedelta
from django.utils import timezone
from django.urls import reverse_lazy

from .models import ItemMaster, JobScheduleDetail, ProcessMaster, JobScheduleMaster # Import all relevant models

# Helper to get global session-like variables (replace with actual session/user context in production)
def get_global_context():
    # In a real ERP, CompId and FinYearId would likely come from user session,
    # tenant context, or a global configuration linked to the authenticated user.
    # For now, we'll use placeholder values.
    return {
        'comp_id': 1, # Placeholder
        'fin_year_id': 2024, # Placeholder
        'username': 'admin' # Placeholder
    }

class MachineScheduleDashboardView(TemplateView):
    template_name = 'machinery/schedule_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        global_ctx = get_global_context()
        comp_id = global_ctx['comp_id']
        fin_year_id = global_ctx['fin_year_id']
        
        # Get machine_id and process_id from URL query parameters
        # (simulating Request.QueryString)
        machine_id_param = self.request.GET.get('MachineId')
        process_id_param = self.request.GET.get('ProcessId')
        selected_date_str = self.request.GET.get('SelectedDate', timezone.now().strftime('%Y-%m-%d')) # YYYY-MM-DD
        
        selected_date = timezone.datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        
        context['selected_date'] = selected_date.strftime('%Y-%m-%d') # For JS calendar to initialize
        context['display_date'] = selected_date.strftime('%d %B %Y') # For display

        machine_name = "N/A"
        try:
            if machine_id_param:
                machine_item = ItemMaster.objects.get(id=int(machine_id_param), comp_id=comp_id, fin_year_id__lte=fin_year_id)
                machine_name = machine_item.manf_desc
        except ItemMaster.DoesNotExist:
            pass # Machine not found

        context['machine_id'] = machine_id_param
        context['process_id'] = process_id_param
        context['machine_name'] = machine_name
        
        # Determine calendar highlighting
        if machine_id_param and process_id_param:
            busy_dates, to_dates = JobScheduleDetail.objects.get_calendar_highlight_dates(
                machine_id=int(machine_id_param), 
                process_id=process_id_param, 
                comp_id=comp_id, 
                fin_year_id=fin_year_id
            )
            context['busy_dates'] = [d.strftime('%Y-%m-%d') for d in busy_dates]
            context['to_dates_for_green'] = [d.strftime('%Y-%m-%d') for d in to_dates]
        else:
            context['busy_dates'] = []
            context['to_dates_for_green'] = []

        return context

class MachineScheduleTablePartialView(TemplateView):
    template_name = 'machinery/_schedule_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        global_ctx = get_global_context()
        comp_id = global_ctx['comp_id']
        fin_year_id = global_ctx['fin_year_id']

        machine_id_param = self.request.GET.get('machine_id')
        process_id_param = self.request.GET.get('process_id')
        selected_date_str = self.request.GET.get('selected_date')

        if not all([machine_id_param, process_id_param, selected_date_str]):
            # If parameters are missing, return empty data or an error state
            context['schedule_data'] = []
            return context
        
        try:
            selected_date = timezone.datetime.strptime(selected_date_str, '%Y-%m-%d').date()
            machine_id = int(machine_id_param)
            process_id = process_id_param
        except (ValueError, TypeError):
            context['schedule_data'] = []
            return context # Handle invalid date or ID
        
        # Use the custom manager to get dashboard data
        schedule_data = JobScheduleDetail.objects.get_dashboard_data(
            machine_id=machine_id,
            process_id=process_id,
            selected_date=selected_date,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        context['schedule_data'] = schedule_data
        return context

# View to handle the "Cancel" button redirection
class ScheduleProcessDashboardRedirectView(TemplateView):
    # This view simulates the BtnCancel_Click logic
    def get(self, request, *args, **kwargs):
        machine_id = request.GET.get('Id') # Matches ASP.NET QueryString "Id"
        # Assuming ModId and SubModId are fixed or derived from context
        mod_id = 15 # Placeholder
        sub_mod_id = 70 # Placeholder
        # Construct the URL for the target dashboard
        # This assumes 'schedule_process_dashboard' is another Django URL
        redirect_url = reverse_lazy('schedule_process_dashboard') + f'?Id={machine_id}&ModId={mod_id}&SubModId={sub_mod_id}'
        return HttpResponse(status=204, headers={'HX-Redirect': redirect_url}) # For HTMX redirection
```

### 4.4 Templates (`machinery/templates/machinery/`)

We'll need `schedule_dashboard.html` for the main page, `_calendar_partial.html` for the calendar, and `_schedule_table.html` for the DataTables content.

**`schedule_dashboard.html`**
This is the main dashboard page. It will initially load the machine name and calendar. The schedule table will be loaded via HTMX.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <table align="left" cellpadding="0" cellspacing="0" class="w-full">
        <tr>
            <td align="left" class="fontcsswhite h-20 bg-blue-700 text-white font-bold px-4 py-2" scope="col" valign="middle">
                &nbsp;<b>Machine Schedule Details</b>
            </td>
        </tr>
        <tr>
            <td align="left">
                <div class="p-4 bg-white rounded-lg shadow">
                    <h3 class="text-xl font-semibold mb-4">Machine Details</h3>
                    <table class="w-full">
                        <tr>
                            <td class="h-8 align-middle text-gray-700">
                                &#160;<b>Schedule for :</b>&#160;<span class="font-bold">{{ machine_name }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                <span class="text-red-500">* Days shown in</span> 
                                <span class="inline-block w-4 h-4 bg-red-500 border border-red-500 rounded-sm align-middle mx-1"></span>&#160;weekends.&#160;&#160; 
                                <span class="inline-block w-4 h-4 bg-orange-500 border border-orange-500 rounded-sm align-middle mx-1"></span><span class="text-orange-500">Machine&nbsp; Busy .</span><span class="text-red-500">&nbsp; 
                                <span class="inline-block w-4 h-4 bg-green-500 border border-green-500 rounded-sm align-middle mx-1"></span>&#160;</span><span class="text-green-500">Machine&nbsp; Available.</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="pt-4">
                                <!-- Calendar will be loaded here via HTMX -->
                                <div id="calendar-container"
                                     hx-trigger="load, reloadCalendar from:body"
                                     hx-get="{% url 'machinery:calendar_partial' %}?machine_id={{ machine_id }}&process_id={{ process_id }}&selected_date={{ selected_date }}"
                                     hx-swap="innerHTML">
                                    <div class="text-center">
                                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                        <p class="mt-2">Loading Calendar...</p>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="height:10px;"></td>
                        </tr>
                        <tr>
                            <td class="pt-4">
                                <!-- DataTables will be loaded here via HTMX -->
                                <div id="schedule-table-container"
                                     hx-trigger="load, reloadScheduleTable from:body"
                                     hx-get="{% url 'machinery:schedule_table_partial' %}?machine_id={{ machine_id }}&process_id={{ process_id }}&selected_date={{ selected_date }}"
                                     hx-swap="innerHTML">
                                    <div class="text-center">
                                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                        <p class="mt-2">Loading Schedule Data...</p>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td align="center" class="h-10 valign-middle pt-4">
                                <button id="BtnCancel" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                        hx-get="{% url 'machinery:schedule_process_dashboard_redirect' %}?Id={{ machine_id }}" hx-swap="none">
                                    Cancel
                                </button>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</div>
{% endblock %}

{% block extra_js %}
<script type="text/javascript">
    document.addEventListener('alpine:init', () => {
        Alpine.data('calendar', (initialDate, busyDates, toDatesForGreen) => ({
            currentDate: new Date(initialDate),
            busyDates: new Set(busyDates),
            toDatesForGreen: new Set(toDatesForGreen),
            
            get monthName() {
                return this.currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });
            },

            get firstDayOfMonth() {
                const tempDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
                return tempDate.getDay(); // 0 for Sunday, 1 for Monday
            },

            get daysInMonth() {
                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0).getDate();
            },

            get calendarDays() {
                const days = [];
                const numDays = this.daysInMonth;
                const firstDay = this.firstDayOfMonth;

                // Empty leading days
                for (let i = 0; i < firstDay; i++) {
                    days.push({ day: '', date: null, classes: '' });
                }

                // Actual days of the month
                for (let i = 1; i <= numDays; i++) {
                    const date = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), i);
                    let classes = 'p-2 border border-gray-200 text-center cursor-pointer hover:bg-blue-100';
                    let isWeekend = date.getDay() === 0; // Sunday

                    // Calendar highlighting logic (ASP.NET RadCalendar1_DayRender equivalent)
                    const isoDate = date.toISOString().split('T')[0];
                    let isBusy = this.busyDates.has(isoDate);
                    let isAfterToDate = false;
                    
                    // Check if date is after any 'ToDate' and is today or future
                    const today = new Date();
                    today.setHours(0, 0, 0, 0); // Normalize to start of day

                    if (date >= today) { // Only highlight future/current available dates
                        for (const toDateStr of this.toDatesForGreen) {
                            const toDate = new Date(toDateStr);
                            if (date > toDate && !this.busyDates.has(isoDate)) { // Only if not busy
                                isAfterToDate = true;
                                break;
                            }
                        }
                    }

                    if (isWeekend) {
                        classes += ' bg-red-100 text-red-700 font-bold'; // Red for weekends
                    }
                    if (isBusy) {
                        classes = classes.replace('bg-blue-100', ''); // Remove hover background if busy
                        classes += ' bg-orange-200 text-orange-700 font-bold'; // Orange for busy
                    }
                    if (isAfterToDate) {
                         classes = classes.replace('bg-blue-100', ''); // Remove hover background if available
                         classes += ' bg-green-200 text-green-700 font-bold'; // Pale green for available after schedule
                    }
                    if (isoDate === new Date().toISOString().split('T')[0]) {
                        classes += ' border-blue-500 border-2'; // Highlight today
                    }
                    
                    days.push({ day: i, date: isoDate, classes: classes });
                }

                return days;
            },

            prevMonth() {
                this.currentDate.setMonth(this.currentDate.getMonth() - 1);
                this.currentDate = new Date(this.currentDate); // Trigger reactivity
                this.updateDashboard(this.currentDate.toISOString().split('T')[0]);
            },

            nextMonth() {
                this.currentDate.setMonth(this.currentDate.getMonth() + 1);
                this.currentDate = new Date(this.currentDate); // Trigger reactivity
                this.updateDashboard(this.currentDate.toISOString().split('T')[0]);
            },

            selectDay(dateStr) {
                if (!dateStr) return;
                this.updateDashboard(dateStr);
            },

            updateDashboard(newDateStr) {
                // Update URL parameters for HTMX
                const url = new URL(window.location.href);
                url.searchParams.set('SelectedDate', newDateStr);
                url.searchParams.set('MachineId', '{{ machine_id }}');
                url.searchParams.set('ProcessId', '{{ process_id }}');
                history.pushState(null, '', url.toString()); // Update browser URL

                // Trigger HTMX reload for the table and calendar
                // Use setTimeout to ensure URL change is registered before triggering
                setTimeout(() => {
                    document.body.dispatchEvent(new Event('reloadScheduleTable'));
                    document.body.dispatchEvent(new Event('reloadCalendar'));
                }, 50);
            }
        }));
    });

    // Initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id === 'schedule-table-container') {
            $('#scheduleDataTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable if re-initialized
            });
        }
    });
</script>
{% endblock %}
```

**`_calendar_partial.html`**
This partial will render the calendar and be dynamically loaded by HTMX.

```html
<div x-data="calendar('{{ selected_date }}', {{ busy_dates|safe }}, {{ to_dates_for_green|safe }})" class="p-4 bg-gray-50 rounded-lg shadow">
    <div class="flex justify-between items-center mb-4">
        <button @click="prevMonth" class="px-3 py-1 rounded-md bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
            &lt;
        </button>
        <h2 class="text-lg font-bold text-gray-800" x-text="monthName"></h2>
        <button @click="nextMonth" class="px-3 py-1 rounded-md bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
            &gt;
        </button>
    </div>

    <div class="grid grid-cols-7 gap-1 text-sm">
        <div class="py-2 text-center font-semibold text-gray-600">Sun</div>
        <div class="py-2 text-center font-semibold text-gray-600">Mon</div>
        <div class="py-2 text-center font-semibold text-gray-600">Tue</div>
        <div class="py-2 text-center font-semibold text-gray-600">Wed</div>
        <div class="py-2 text-center font-semibold text-gray-600">Thu</div>
        <div class="py-2 text-center font-semibold text-gray-600">Fri</div>
        <div class="py-2 text-center font-semibold text-gray-600">Sat</div>

        <template x-for="day in calendarDays" :key="day.date">
            <div 
                :class="day.classes" 
                @click="selectDay(day.date)"
                x-text="day.day">
            </div>
        </template>
    </div>
</div>
```

**`_schedule_table.html`**
This partial will render the DataTables content and be dynamically loaded by HTMX.

```html
<table id="scheduleDataTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">I/p Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">O/p Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">From Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">To Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Process</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Operator</th>
        </tr>
    </thead>
    <tbody>
        {% if schedule_data %}
            {% for row in schedule_data %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.WONo }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.JobNo }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.ItemCode }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ row.ManfDesc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.BomQty|floatformat:"0" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.UOM }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.Shift }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.InputQty|floatformat:"0" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.OutputQty|floatformat:"0" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.FromDate }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.ToDate }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.FromTime }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.ToTime }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.Process }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ row.Operator }}</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="16" class="py-4 text-center text-red-500 font-bold text-lg">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

### 4.5 URLs (`machinery/urls.py`)

Define the URL patterns for the dashboard and its HTMX partials.

```python
from django.urls import path
from .views import MachineScheduleDashboardView, MachineScheduleTablePartialView, ScheduleProcessDashboardRedirectView

app_name = 'machinery' # Namespace for URLs

urlpatterns = [
    path('schedule-dashboard/', MachineScheduleDashboardView.as_view(), name='schedule_dashboard'),
    path('schedule-table-partial/', MachineScheduleTablePartialView.as_view(), name='schedule_table_partial'),
    # This URL is just for calendar rendering. It uses the same view as the dashboard
    # to pass necessary context (busy/to_dates).
    path('calendar-partial/', MachineScheduleDashboardView.as_view(template_name='machinery/_calendar_partial.html'), name='calendar_partial'),
    path('schedule-process-dashboard-redirect/', ScheduleProcessDashboardRedirectView.as_view(), name='schedule_process_dashboard_redirect'),
]
```

### 4.6 Tests (`machinery/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from unittest.mock import patch

from .models import (
    JobScheduleMaster, JobScheduleDetail, JobCompletion, ItemMaster,
    UnitMaster, OfficeStaff, ProcessMaster
)

# Mocking the global context for tests
@patch('machinery.views.get_global_context', return_value={'comp_id': 1, 'fin_year_id': 2024, 'username': 'testuser'})
class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.comp_id = 1
        cls.fin_year_id = 2024

        cls.machine_item = ItemMaster.objects.create(
            id=101, manf_desc='CNC Machine A', item_code='M-CNCA', stock_qty=1.0, 
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.job_item = ItemMaster.objects.create(
            id=201, manf_desc='Product Part X', item_code='P-PARTX', stock_qty=100.0, 
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.unit = UnitMaster.objects.create(id=1, symbol='Pcs')
        cls.operator = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=cls.comp_id)
        cls.process = ProcessMaster.objects.create(id='PROC01', process_name='Cutting')

        cls.job_master_1 = JobScheduleMaster.objects.create(
            id=1, job_no='JOB001', wono='WO-1001', item_id=cls.job_item.id,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.job_master_2 = JobScheduleMaster.objects.create(
            id=2, job_no='JOB002', wono='WO-1002', item_id=cls.job_item.id,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

        cls.job_detail_1 = JobScheduleDetail.objects.create(
            id=1, mid=cls.job_master_1.id, machine_id=cls.machine_item.id,
            from_date=date.today() - timedelta(days=1), to_date=date.today() + timedelta(days=1),
            from_time='09:00', to_time='17:00', qty=50.0, operator=cls.operator.emp_id,
            process=cls.process.id, shift='0', released='0' # Not released
        )
        cls.job_detail_2 = JobScheduleDetail.objects.create(
            id=2, mid=cls.job_master_2.id, machine_id=cls.machine_item.id,
            from_date=date.today() + timedelta(days=2), to_date=date.today() + timedelta(days=3),
            from_time='08:00', to_time='16:00', qty=75.0, operator=cls.operator.emp_id,
            process=cls.process.id, shift='1', released='0' # Not released
        )
        cls.job_detail_3_released = JobScheduleDetail.objects.create( # This one should be ignored for highlighting
            id=3, mid=cls.job_master_1.id, machine_id=cls.machine_item.id,
            from_date=date.today() - timedelta(days=5), to_date=date.today() - timedelta(days=4),
            from_time='08:00', to_time='16:00', qty=75.0, operator=cls.operator.emp_id,
            process=cls.process.id, shift='1', released='1' # Released
        )


        JobCompletion.objects.create(
            id=1, mid=cls.job_master_1.id, did=cls.job_detail_1.id, output_qty=48.0, uom=cls.unit.id
        )
        JobCompletion.objects.create(
            id=2, mid=cls.job_master_2.id, did=cls.job_detail_2.id, output_qty=70.0, uom=cls.unit.id
        )

    def test_job_schedule_detail_manager_get_dashboard_data(self, mock_get_global_context):
        selected_date = date.today()
        data = JobScheduleDetail.objects.get_dashboard_data(
            machine_id=self.machine_item.id,
            process_id=self.process.id,
            selected_date=selected_date,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        )
        self.assertEqual(len(data), 1) # Only job_detail_1 should be returned for today
        self.assertEqual(data[0]['JobNo'], 'JOB001')
        self.assertEqual(data[0]['ItemCode'], 'P-PARTX')
        self.assertEqual(data[0]['Shift'], 'Day')
        self.assertEqual(data[0]['InputQty'], 50.0)
        self.assertEqual(data[0]['OutputQty'], 48.0)

    def test_job_schedule_detail_manager_get_calendar_highlight_dates(self, mock_get_global_context):
        busy_dates, to_dates = JobScheduleDetail.objects.get_calendar_highlight_dates(
            machine_id=self.machine_item.id,
            process_id=self.process.id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        )
        
        # Test busy dates
        expected_busy_dates = {
            self.job_detail_1.from_date, 
            self.job_detail_1.from_date + timedelta(days=1), # date.today()
            self.job_detail_1.to_date, # date.today() + 1
            self.job_detail_2.from_date, # date.today() + 2
            self.job_detail_2.to_date # date.today() + 3
        }
        self.assertEqual(set(busy_dates), expected_busy_dates)
        
        # Test to_dates for green highlighting
        expected_to_dates = [self.job_detail_1.to_date, self.job_detail_2.to_date]
        self.assertEqual(set(to_dates), set(expected_to_dates))

class MachineScheduleDashboardViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.comp_id = 1
        cls.fin_year_id = 2024

        cls.machine_item = ItemMaster.objects.create(
            id=101, manf_desc='CNC Machine A', item_code='M-CNCA', stock_qty=1.0, 
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.job_item = ItemMaster.objects.create(
            id=201, manf_desc='Product Part X', item_code='P-PARTX', stock_qty=100.0, 
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.unit = UnitMaster.objects.create(id=1, symbol='Pcs')
        cls.operator = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=cls.comp_id)
        cls.process = ProcessMaster.objects.create(id='PROC01', process_name='Cutting')

        cls.job_master = JobScheduleMaster.objects.create(
            id=1, job_no='JOB001', wono='WO-1001', item_id=cls.job_item.id,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.job_detail = JobScheduleDetail.objects.create(
            id=1, mid=cls.job_master.id, machine_id=cls.machine_item.id,
            from_date=date.today(), to_date=date.today(),
            from_time='09:00', to_time='17:00', qty=50.0, operator=cls.operator.emp_id,
            process=cls.process.id, shift='0', released='0'
        )
        JobCompletion.objects.create(
            id=1, mid=cls.job_master.id, did=cls.job_detail.id, output_qty=48.0, uom=cls.unit.id
        )

    def setUp(self):
        self.client = Client()
        self.base_url_params = f"MachineId={self.machine_item.id}&ProcessId={self.process.id}&SelectedDate={date.today().strftime('%Y-%m-%d')}"

    @patch('machinery.views.get_global_context', return_value={'comp_id': 1, 'fin_year_id': 2024, 'username': 'testuser'})
    def test_dashboard_view(self, mock_get_global_context):
        response = self.client.get(reverse('machinery:schedule_dashboard') + f'?{self.base_url_params}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/schedule_dashboard.html')
        self.assertContains(response, 'CNC Machine A')
        self.assertContains(response, date.today().strftime('%Y-%m-%d'))
        self.assertIn(date.today().strftime('%Y-%m-%d'), response.context['busy_dates'])

    @patch('machinery.views.get_global_context', return_value={'comp_id': 1, 'fin_year_id': 2024, 'username': 'testuser'})
    def test_schedule_table_partial_view_htmx(self, mock_get_global_context):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery:schedule_table_partial') + f'?{self.base_url_params}', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_schedule_table.html')
        self.assertContains(response, 'JOB001') # Check if data is populated
        self.assertContains(response, 'P-PARTX')
        self.assertContains(response, 'scheduleDataTable') # Check for DataTables ID

    @patch('machinery.views.get_global_context', return_value={'comp_id': 1, 'fin_year_id': 2024, 'username': 'testuser'})
    def test_schedule_table_partial_view_missing_params(self, mock_get_global_context):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery:schedule_table_partial'), **headers) # No params
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')

    @patch('machinery.views.get_global_context', return_value={'comp_id': 1, 'fin_year_id': 2024, 'username': 'testuser'})
    def test_calendar_partial_view_htmx(self, mock_get_global_context):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machinery:calendar_partial') + f'?{self.base_url_params}', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_calendar_partial.html')
        self.assertContains(response, 'x-data="calendar(') # Check for Alpine.js init
        self.assertIn(date.today().strftime('%Y-%m-%d'), response.context['busy_dates'])

    def test_schedule_process_dashboard_redirect_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        machine_id = self.machine_item.id
        response = self.client.get(reverse('machinery:schedule_process_dashboard_redirect') + f'?Id={machine_id}', **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content
        # Verify HX-Redirect header
        expected_redirect_url = reverse('schedule_process_dashboard') + f'?Id={machine_id}&ModId=15&SubModId=70'
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)

```

## Step 5: HTMX and Alpine.js Integration

The design utilizes HTMX for all dynamic content loading and form submissions, and Alpine.js for local UI state management (like the custom calendar's month navigation and day selection).

*   **Initial Load:** The `schedule_dashboard.html` loads, but `_calendar_partial.html` and `_schedule_table.html` are loaded via `hx-trigger="load"` on their respective container divs. This ensures that even the initial rendering is done via HTMX, allowing for consistent data loading.
*   **Calendar Interaction:**
    *   The `_calendar_partial.html` has an Alpine.js component `x-data="calendar(...)` that handles month navigation (`prevMonth`, `nextMonth`) and day selection (`selectDay`).
    *   When a day is selected or month changes, `updateDashboard` function updates the browser's URL (to reflect the new `SelectedDate`, `MachineId`, `ProcessId`) using `history.pushState`.
    *   Crucially, `updateDashboard` then dispatches custom events (`reloadScheduleTable`, `reloadCalendar`) on the `document.body`.
    *   The `schedule-table-container` and `calendar-container` divs have `hx-trigger="load, reloadScheduleTable from:body"` and `hx-trigger="load, reloadCalendar from:body"` respectively. This means they will refetch their content via `hx-get` when the page initially loads OR when these custom events are dispatched, thus refreshing the calendar and table data.
*   **DataTables Integration:**
    *   The `_schedule_table.html` contains the `<table>` element with `id="scheduleDataTable"`.
    *   A JavaScript block after the table (within the `{% block extra_js %}` of `base.html` or in `schedule_dashboard.html`) listens for the `htmx:afterSwap` event on the `schedule-table-container`.
    *   Once the table content is swapped in, it initializes DataTables on `#scheduleDataTable`, ensuring proper client-side sorting, filtering, and pagination without full page reloads. `destroy: true` is important for re-initialization on subsequent HTMX loads.
*   **Cancel Button:** The `BtnCancel` uses `hx-get` to a dedicated `schedule_process_dashboard_redirect` view. This view responds with `HX-Redirect` header and `204 No Content` status, instructing HTMX to perform a client-side redirect to the legacy ASP.NET page (or another Django page).

## Final Notes

*   **Placeholders:** `[APP_NAME]` is `machinery`. `[MODEL_NAME]` was broken down into several specific models for the underlying database tables, and the `JobScheduleDetailManager` acts as the data aggregator for the dashboard's "logical model".
*   **DRY Templates:** `_calendar_partial.html` and `_schedule_table.html` serve as reusable components. `base.html` is assumed to handle all core CDN links (HTMX, Alpine.js, DataTables, TailwindCSS).
*   **Fat Model, Thin View:** The complex SQL query logic from ASP.NET's `fillgrid` and calendar day rendering has been encapsulated within the `JobScheduleDetailManager` methods (`get_dashboard_data`, `get_calendar_highlight_dates`). Views (`MachineScheduleDashboardView`, `MachineScheduleTablePartialView`) remain concise, primarily coordinating data fetching from models and rendering templates.
*   **Tests:** Comprehensive unit tests for the custom manager methods and integration tests for the views (including HTMX requests) ensure robustness and maintainability.
*   **Error Handling:** Basic error handling for missing query parameters or invalid dates is included. In a production system, more robust error reporting and user feedback mechanisms would be implemented.
*   **Authentication/Session:** The `get_global_context` helper function simulates session variables (`CompId`, `FinYearId`, `username`). In a real application, these would be integrated with Django's authentication system and potentially a multi-tenant setup or user profile.
*   **Time Fields:** `FromTime` and `ToTime` were `string` in ASP.NET. They are mapped to `CharField` in Django models. If they need to be treated as actual time objects for sorting/filtering, they should be converted to `models.TimeField` and parsed/formatted accordingly.
*   **Query Filtering:** The `JobScheduleDetailManager.get_dashboard_data` replicates the selected date filtering based on `FromDate` and `ToDate` ranges. The original C# code iterates day-by-day; the Django ORM approach uses range overlaps, which is generally more efficient. Validation for exact matching of behavior would be needed in a full migration.