## ASP.NET to Django Conversion Script: Job Scheduling Module

This document outlines a comprehensive plan for migrating the provided ASP.NET Job Scheduling module to a modern Django application. The focus is on leveraging AI-assisted automation techniques, adhering to a "fat model, thin view" architecture, and utilizing contemporary frontend technologies like HTMX, Alpine.js, and DataTables.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code-behind, the primary tables involved are `tblMS_JobShedule_Master`, `tblDG_Item_Master`, `tblFinancial_master`, `tblHR_OfficeStaff`, and `tblMS_JobSchedule_Details`.

**Identified Tables and Inferred Columns:**

*   **`tblMS_JobShedule_Master`** (Primary entity)
    *   `Id` (Primary Key, integer)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff.EmpId`, string/varchar)
    *   `JobNo` (varchar)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, integer)
    *   `SysDate` (date/datetime)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`, integer)
    *   `WONo` (varchar)
    *   `CompId` (integer, likely from session context)

*   **`tblDG_Item_Master`** (Lookup table for `ItemCode`)
    *   `Id` (Primary Key, integer)
    *   `ItemCode` (varchar)

*   **`tblFinancial_master`** (Lookup table for `FinYear`)
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (varchar)

*   **`tblHR_OfficeStaff`** (Lookup table for `GenBy` - Generated By)
    *   `EmpId` (Primary Key, string/varchar, referenced by `SessionId`)
    *   `Title` (varchar)
    *   `EmployeeName` (varchar)

*   **`tblMS_JobSchedule_Details`** (Existence check for `JobSchedule` records)
    *   `MId` (Foreign Key to `tblMS_JobShedule_Master.Id`, integer)
    *   *(Other columns not provided in ASP.NET code, assuming an implicit primary key)*

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements a **Read** (search and list) operation. The `schedule_Edit.aspx` page allows searching and displaying a list of "Job Schedules" based on `Job No`, `WO No`, or `Item Code`. The `GridView` provides pagination and a "Sel" command (LinkButton on `ItemCode`) that redirects to a detail/edit page (`schedule_Edit_Details.aspx`).

*   **Create:** Not directly implemented in the provided ASP.NET code for this page. The Django plan will include a `CreateView` for comprehensive CRUD functionality.
*   **Read:** Implemented through the `binddata()` method, which filters and fetches `JobSchedule` records, performs lookups for `FinYear` and `GenBy`, and binds them to `SearchGridView1`.
*   **Update:** Not directly implemented on this page, but implied by the redirection to `schedule_Edit_Details.aspx`. The Django plan will include an `UpdateView` for comprehensive CRUD functionality.
*   **Delete:** Not implemented in the provided ASP.NET code. The Django plan will include a `DeleteView` for comprehensive CRUD functionality.
*   **Validation:** Basic presence check for `TxtSearchValue` if a search field is selected.

### Step 3: Infer UI Components

The ASP.NET page uses standard Web Forms controls:
*   `DrpField` (DropDownList): Maps to a Django `forms.ChoiceField` for search criteria.
*   `TxtSearchValue` (TextBox): Maps to a Django `forms.CharField` for search input.
*   `btnSearch` (Button): Triggers data re-binding. In Django, this will be handled by HTMX form submission.
*   `SearchGridView1` (GridView): This is the main data display. It will be replaced by a Django template using a DataTables setup with HTMX for dynamic content loading. It includes columns for SN, Fin Yrs, WO No, Job No, Item Code (as a clickable link), Gen. Date, Gen. By.

### Step 4: Generate Django Code

The Django application will be named `schedule`.

#### 4.1 Models (`schedule/models.py`)

We will define Django models for `JobSchedule`, and its related lookup entities: `FinancialYear`, `Item`, and `Employee`. A placeholder `JobScheduleDetail` model is also included to reflect the original SQL's existence check. All models will use `managed = False` and `db_table` to map to existing database tables. The business logic for displaying `FinYear`, `GenBy`, and filtering will be encapsulated in the `JobSchedule` model or its manager.

```python
from django.db import models
from django.utils import timezone
from datetime import date

# Lookup Models (Managed=False for existing DB)
class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

class Employee(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be string
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name}".strip()

# Placeholder for JobScheduleDetail (reflecting `Id in (Select MId from tblMS_JobSchedule_Details )`)
class JobScheduleDetail(models.Model):
    # This assumes `tblMS_JobSchedule_Details` has its own primary key,
    # and `MId` is a regular foreign key.
    # If `MId` itself is the sole primary key, it would be models.OneToOneField
    # from JobSchedule to JobScheduleDetail, or models.ForeignKey(..., primary_key=True) here.
    # We choose a regular FK for robustness without full schema.
    mid = models.ForeignKey('JobSchedule', models.DO_NOTHING, db_column='MId', related_name='details_set')

    class Meta:
        managed = False
        db_table = 'tblMS_JobSchedule_Details'
        verbose_name = 'Job Schedule Detail'
        verbose_name_plural = 'Job Schedule Details'

    def __str__(self):
        return f"Detail for Job: {self.mid.job_no}"

# Main Model
class JobSchedule(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', blank=True, null=True)
    job_no = models.CharField(db_column='JobNo', max_length=255, blank=True, null=True)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    sys_date = models.DateField(db_column='SysDate')
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMS_JobShedule_Master'
        verbose_name = 'Job Schedule'
        verbose_name_plural = 'Job Schedules'

    def __str__(self):
        return f"Job No: {self.job_no or 'N/A'}, WO No: {self.wo_no or 'N/A'}"

    # Business logic properties for display (Fat Model)
    @property
    def fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else 'N/A'

    @property
    def generated_by_display(self):
        return str(self.session_id) if self.session_id else 'N/A'

    @property
    def system_date_display(self):
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else 'N/A'

    @classmethod
    def get_filtered_job_schedules(cls, comp_id, fin_year_id, search_field, search_value):
        """
        Retrieves filtered Job Schedule records, mimicking the ASP.NET binddata logic.
        """
        queryset = cls.objects.filter(
            comp_id=comp_id,
            financial_year__fin_year_id__lte=fin_year_id # FinYearId <= current_fin_year_id
        ).select_related('item', 'financial_year', 'session_id') # Optimize FK lookups

        # Apply the "Id in (Select MId from tblMS_JobSchedule_Details )" filter
        # This translates to checking for existence of related JobScheduleDetail entries.
        queryset = queryset.filter(details_set__isnull=False).distinct()

        if search_value:
            if search_field == '0': # Job No
                queryset = queryset.filter(job_no__iexact=search_value) # Using iexact for case-insensitive exact match
            elif search_field == '1': # WO No
                queryset = queryset.filter(wo_no__iexact=search_value) # Using iexact for case-insensitive exact match
            elif search_field == '2': # Item Code
                queryset = queryset.filter(item__item_code__icontains=search_value)

        queryset = queryset.order_by('-id') # Order by tblMS_JobShedule_Master.Id Desc

        return queryset
```

#### 4.2 Forms (`schedule/forms.py`)

A `JobScheduleSearchForm` will handle the search inputs, and a `JobScheduleForm` will be created for `Create` and `Update` operations, including necessary widgets for Tailwind CSS styling.

```python
from django import forms
from .models import JobSchedule, Item, FinancialYear, Employee

class JobScheduleSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Job No'),
        ('1', 'WO No'),
        ('2', 'Item Code'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'schedule:jobschedule_table' %}",
            'hx-target': '#jobscheduleTable-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )
    search_value = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'schedule:jobschedule_table' %}",
            'hx-target': '#jobscheduleTable-container',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )

class JobScheduleForm(forms.ModelForm):
    class Meta:
        model = JobSchedule
        fields = ['job_no', 'wo_no', 'item', 'sys_date', 'financial_year', 'session_id', 'comp_id']
        widgets = {
            'job_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'session_id': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views (`schedule/views.py`)

Views are kept thin, delegating business logic to the `JobSchedule` model's `get_filtered_job_schedules` class method. HTMX responses are managed with appropriate `HX-Trigger` headers for dynamic updates. For `CompId` and `FinYearId`, default values are used for demonstration; in a live system, these would typically be derived from the logged-in user's profile or session.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import JobSchedule, Item, FinancialYear, Employee # Ensure all models are imported
from .forms import JobScheduleForm, JobScheduleSearchForm

class JobScheduleListView(ListView):
    model = JobSchedule
    template_name = 'schedule/jobschedule/list.html'
    context_object_name = 'jobschedules'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = JobScheduleSearchForm(self.request.GET)
        return context

class JobScheduleTablePartialView(ListView):
    model = JobSchedule
    template_name = 'schedule/jobschedule/_jobschedule_table.html'
    context_object_name = 'jobschedules'
    paginate_by = 17 # Mimic ASP.NET GridView PageSize

    def get_queryset(self):
        # Default values for comp_id and fin_year_id, mimicking ASP.NET Session variables.
        # In a production environment, these would come from self.request.user or similar context.
        current_comp_id = 1 # Example: Replace with actual session/user company ID
        current_fin_year_id = 2024 # Example: Replace with actual session/user financial year ID

        form = JobScheduleSearchForm(self.request.GET)
        search_field = form['search_field'].value if form.is_valid() else None
        search_value = form['search_value'].value if form.is_valid() else None

        queryset = JobSchedule.get_filtered_job_schedules(
            comp_id=current_comp_id,
            fin_year_id=current_fin_year_id,
            search_field=search_field,
            search_value=search_value
        )
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # Ensure this view only responds to HTMX requests
        if self.request.headers.get('HX-Request'):
            return super().render_to_response(context, **response_kwargs)
        # If not HTMX, redirect to the main list page to load full UI
        return HttpResponse(status=204, headers={'HX-Location': reverse_lazy('schedule:jobschedule_list')})


class JobScheduleCreateView(CreateView):
    model = JobSchedule
    form_class = JobScheduleForm
    template_name = 'schedule/jobschedule/_jobschedule_form.html'
    success_url = reverse_lazy('schedule:jobschedule_list')

    def form_valid(self, form):
        # Populate session-dependent fields if not in form
        if not form.instance.comp_id:
            form.instance.comp_id = 1 # Example: Default company ID
        if not form.instance.session_id_id:
            # Assign a dummy employee if none is set (for testing/setup)
            # In a real app, this would be self.request.user.employee_profile or similar
            try:
                form.instance.session_id = Employee.objects.first()
            except Employee.DoesNotExist:
                form.instance.session_id = Employee.objects.create(emp_id='DUMMY_SYS_USER', employee_name='System Auto')

        response = super().form_valid(form)
        messages.success(self.request, 'Job Schedule added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, just header for HTMX
                headers={'HX-Trigger': 'refreshJobScheduleList'} # Custom event to trigger list refresh
            )
        return response

class JobScheduleUpdateView(UpdateView):
    model = JobSchedule
    form_class = JobScheduleForm
    template_name = 'schedule/jobschedule/_jobschedule_form.html'
    success_url = reverse_lazy('schedule:jobschedule_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Job Schedule updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshJobScheduleList'}
            )
        return response

class JobScheduleDeleteView(DeleteView):
    model = JobSchedule
    template_name = 'schedule/jobschedule/_jobschedule_confirm_delete.html'
    success_url = reverse_lazy('schedule:jobschedule_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Job Schedule deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshJobScheduleList'}
            )
        return response
```

#### 4.4 Templates (`schedule/templates/schedule/jobschedule/`)

Templates are designed for HTMX and Alpine.js, extending `core/base.html` and using partials for modal forms and the dynamically loaded table.

**`schedule/templates/schedule/jobschedule/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Job Schedules</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'schedule:jobschedule_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Job Schedule
        </button>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Job Schedules</h3>
        <form id="searchForm" hx-get="{% url 'schedule:jobschedule_table' %}" hx-target="#jobscheduleTable-container" hx-swap="innerHTML" hx-indicator="#search-loading-indicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.search_field.label }}
                    </label>
                    {{ search_form.search_field }}
                </div>
                <div>
                    <label for="{{ search_form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.search_value.html_name }} {# Use html_name for forms.Form fields #}
                    </label>
                    {{ search_form.search_value }}
                </div>
                <div class="md:col-span-1 flex items-end">
                    <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                    <div id="search-loading-indicator" class="htmx-indicator ml-3">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <div id="jobscheduleTable-container"
         hx-trigger="load, refreshJobScheduleList from:body"
         hx-get="{% url 'schedule:jobschedule_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Job Schedules...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader page interactions
    });
</script>
{% endblock %}
```

**`schedule/templates/schedule/jobschedule/_jobschedule_table.html`**
```html
<table id="jobscheduleTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in jobschedules %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ obj.fin_year_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ obj.wo_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ obj.job_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                <button
                    class="text-blue-600 hover:text-blue-800 font-semibold"
                    hx-get="{% url 'schedule:jobschedule_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    {{ obj.item.item_code }}
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ obj.system_date_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">{{ obj.generated_by_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'schedule:jobschedule_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'schedule:jobschedule_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-lg font-medium text-red-700">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is re-initialized after HTMX swap
    // Destroy existing DataTable instance before re-initializing to prevent errors
    if ($.fn.DataTable.isDataTable('#jobscheduleTable')) {
        $('#jobscheduleTable').DataTable().destroy();
    }
    $('#jobscheduleTable').DataTable({
        "pageLength": 17, // Mimic ASP.NET GridView PageSize
        "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
        "dom": 'lrtip', // Only length, table, info, pagination. No default search box.
        "searching": true, // Allow DataTables to perform internal search on the loaded data (though the external form handles primary search)
        "paging": true,
        "info": true,
        "ordering": true,
    });
</script>
```

**`schedule/templates/schedule/jobschedule/_jobschedule_form.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Job Schedule</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`schedule/templates/schedule/jobschedule/_jobschedule_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Job Schedule for <strong>"{{ object }}"</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`schedule/urls.py`)

URL patterns for the Job Schedule module, including paths for the main list view, the HTMX-targeted table partial, and CRUD operations.

```python
from django.urls import path
from .views import (
    JobScheduleListView,
    JobScheduleTablePartialView,
    JobScheduleCreateView,
    JobScheduleUpdateView,
    JobScheduleDeleteView,
)

app_name = 'schedule' # Namespacing for clean URL references

urlpatterns = [
    path('jobschedule/', JobScheduleListView.as_view(), name='jobschedule_list'),
    path('jobschedule/table/', JobScheduleTablePartialView.as_view(), name='jobschedule_table'), # HTMX target for table
    path('jobschedule/add/', JobScheduleCreateView.as_view(), name='jobschedule_add'),
    path('jobschedule/edit/<int:pk>/', JobScheduleUpdateView.as_view(), name='jobschedule_edit'),
    path('jobschedule/delete/<int:pk>/', JobScheduleDeleteView.as_view(), name='jobschedule_delete'),
]
```

#### 4.6 Tests (`schedule/tests.py`)

Comprehensive unit tests for each model and integration tests for all views are provided, ensuring functionality and HTMX interactions are correctly handled.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import JobSchedule, Item, FinancialYear, Employee, JobScheduleDetail
from datetime import date

# --- Model Tests ---

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024')
        FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')

    def test_financial_year_creation(self):
        fy = FinancialYear.objects.get(fin_year_id=2023)
        self.assertEqual(fy.fin_year, '2023-2024')
        self.assertEqual(str(fy), '2023-2024')

class ItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Item.objects.create(id=101, item_code='PROD-A')
        Item.objects.create(id=102, item_code='PROD-B')

    def test_item_creation(self):
        item = Item.objects.get(id=101)
        self.assertEqual(item.item_code, 'PROD-A')
        self.assertEqual(str(item), 'PROD-A')

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Employee.objects.create(emp_id='EMP-001', title='Mr', employee_name='John Doe')
        Employee.objects.create(emp_id='EMP-002', employee_name='Jane Smith') # No title

    def test_employee_creation(self):
        emp = Employee.objects.get(emp_id='EMP-001')
        self.assertEqual(str(emp), 'Mr. John Doe')
        emp2 = Employee.objects.get(emp_id='EMP-002')
        self.assertEqual(str(emp2), 'Jane Smith')

class JobScheduleModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fy_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fy_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.item_a = Item.objects.create(id=101, item_code='PROD-A')
        cls.item_b = Item.objects.create(id=102, item_code='PROD-B')
        cls.employee_admin = Employee.objects.create(emp_id='E001', title='Mr', employee_name='Admin User')

        cls.job1 = JobSchedule.objects.create(
            id=1,
            job_no='J123',
            wo_no='WO456',
            item=cls.item_a,
            sys_date=date(2024, 1, 15),
            financial_year=cls.fy_2024,
            session_id=cls.employee_admin,
            comp_id=1
        )
        JobScheduleDetail.objects.create(mid=cls.job1) # Add detail to make it appear in list

        cls.job2 = JobSchedule.objects.create(
            id=2,
            job_no='J124',
            wo_no='WO457',
            item=cls.item_b,
            sys_date=date(2024, 2, 10),
            financial_year=cls.fy_2024,
            session_id=cls.employee_admin,
            comp_id=1
        )
        # No detail for job2, so it shouldn't appear in default list

        cls.job3 = JobSchedule.objects.create(
            id=3,
            job_no='J125',
            wo_no='WO458',
            item=cls.item_a,
            sys_date=date(2023, 12, 1),
            financial_year=cls.fy_2023,
            session_id=cls.employee_admin,
            comp_id=1
        )
        JobScheduleDetail.objects.create(mid=cls.job3) # Add detail for job3

    def test_job_schedule_creation(self):
        self.assertEqual(self.job1.job_no, 'J123')
        self.assertEqual(self.job1.item.item_code, 'PROD-A')
        self.assertEqual(self.job1.financial_year.fin_year, '2024-2025')
        self.assertEqual(self.job1.session_id.employee_name, 'Admin User')

    def test_fin_year_display_property(self):
        self.assertEqual(self.job1.fin_year_display, '2024-2025')

    def test_generated_by_display_property(self):
        self.assertEqual(self.job1.generated_by_display, 'Mr. Admin User')

    def test_system_date_display_property(self):
        self.assertEqual(self.job1.system_date_display, '15/01/2024')

    def test_get_filtered_job_schedules_no_filter(self):
        # Only job1 and job3 have details and fin_year_id <= 2024
        # Ordered by -id, so job3 then job1
        queryset = JobSchedule.get_filtered_job_schedules(comp_id=1, fin_year_id=2024, search_field=None, search_value=None)
        self.assertIn(self.job1, queryset)
        self.assertNotIn(self.job2, queryset)
        self.assertIn(self.job3, queryset)
        self.assertEqual(queryset.count(), 2)
        self.assertEqual(list(queryset), [self.job3, self.job1]) # Order by -id

    def test_get_filtered_job_schedules_job_no_filter(self):
        queryset = JobSchedule.get_filtered_job_schedules(comp_id=1, fin_year_id=2024, search_field='0', search_value='J123')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first(), self.job1)

    def test_get_filtered_job_schedules_wo_no_filter(self):
        queryset = JobSchedule.get_filtered_job_schedules(comp_id=1, fin_year_id=2024, search_field='1', search_value='WO458')
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first(), self.job3)

    def test_get_filtered_job_schedules_item_code_filter(self):
        queryset = JobSchedule.get_filtered_job_schedules(comp_id=1, fin_year_id=2024, search_field='2', search_value='PROD-A')
        # Both job1 and job3 have PROD-A and details, and fin_year_id <= 2024
        self.assertEqual(queryset.count(), 2)
        self.assertIn(self.job1, queryset)
        self.assertIn(self.job3, queryset)


# --- View Tests ---

class JobScheduleViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fy_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.item_a = Item.objects.create(id=101, item_code='PROD-A')
        cls.item_b = Item.objects.create(id=102, item_code='PROD-B')
        cls.employee_admin = Employee.objects.create(emp_id='E001', title='Mr', employee_name='Admin User')
        
        cls.job1 = JobSchedule.objects.create(
            id=1,
            job_no='J123',
            wo_no='WO456',
            item=cls.item_a,
            sys_date=date(2024, 1, 15),
            financial_year=cls.fy_2024,
            session_id=cls.employee_admin,
            comp_id=1
        )
        JobScheduleDetail.objects.create(mid=cls.job1)

        cls.job2 = JobSchedule.objects.create(
            id=2,
            job_no='J124',
            wo_no='WO457',
            item=cls.item_b,
            sys_date=date(2024, 2, 10),
            financial_year=cls.fy_2024,
            session_id=cls.employee_admin,
            comp_id=1
        )
        JobScheduleDetail.objects.create(mid=cls.job2)
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('schedule:jobschedule_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'schedule/jobschedule/list.html')
        self.assertIn('search_form', response.context)
    
    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('schedule:jobschedule_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'schedule/jobschedule/_jobschedule_table.html')
        self.assertIn('jobschedules', response.context)
        self.assertEqual(response.context['jobschedules'].count(), 2)

        # Test search filter on table partial
        response = self.client.get(reverse('schedule:jobschedule_table'), {'search_field': '0', 'search_value': 'J123'}, **headers)
        self.assertEqual(response.context['jobschedules'].count(), 1)
        self.assertEqual(response.context['jobschedules'].first().job_no, 'J123')

    def test_table_partial_view_non_htmx_redirects(self):
        response = self.client.get(reverse('schedule:jobschedule_table'))
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Location', response.headers)
        self.assertEqual(response.headers['HX-Location'], reverse('schedule:jobschedule_list'))

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('schedule:jobschedule_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'schedule/jobschedule/_jobschedule_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_htmx_success(self):
        initial_count = JobSchedule.objects.count()
        data = {
            'job_no': 'J999',
            'wo_no': 'WO999',
            'item': self.item_a.id,
            'sys_date': '2024-03-20',
            'financial_year': self.fy_2024.fin_year_id,
            'session_id': self.employee_admin.emp_id,
            'comp_id': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('schedule:jobschedule_add'), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue(JobSchedule.objects.filter(job_no='J999').exists())
        self.assertEqual(JobSchedule.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshJobScheduleList')
        
    def test_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('schedule:jobschedule_edit', args=[self.job1.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'schedule/jobschedule/_jobschedule_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.job1)
        
    def test_update_view_post_htmx_success(self):
        data = {
            'job_no': 'J123_UPDATED',
            'wo_no': 'WO456_UPDATED',
            'item': self.item_a.id,
            'sys_date': '2024-01-15',
            'financial_year': self.fy_2024.fin_year_id,
            'session_id': self.employee_admin.emp_id,
            'comp_id': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('schedule:jobschedule_edit', args=[self.job1.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.job1.refresh_from_db()
        self.assertEqual(self.job1.job_no, 'J123_UPDATED')
        self.assertEqual(self.job1.wo_no, 'WO456_UPDATED')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshJobScheduleList')

    def test_delete_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('schedule:jobschedule_delete', args=[self.job1.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'schedule/jobschedule/_jobschedule_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.job1)

    def test_delete_view_post_htmx_success(self):
        initial_count = JobSchedule.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('schedule:jobschedule_delete', args=[self.job1.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(JobSchedule.objects.filter(id=self.job1.id).exists())
        self.assertEqual(JobSchedule.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshJobScheduleList')
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:** All dynamic content loading (initial table, search results, modal forms) is driven by HTMX.
    *   `hx-get` is used to fetch content (table partial, form partials).
    *   `hx-target` and `hx-swap` control where and how content is placed on the page.
    *   `hx-trigger` specifies when requests are made (e.g., `load`, `click`, `keyup changed delay:500ms`).
    *   Custom `HX-Trigger` headers (`refreshJobScheduleList`) are used by views to inform the frontend to refresh the table after successful CRUD operations.
    *   `hx-indicator` is used to show loading spinners.
*   **Alpine.js:** Manages the modal's `hidden` class based on HTMX interactions (`on click add/remove .is-active to #modal`).
*   **DataTables:** Integrated into `_jobschedule_table.html` to provide client-side searching, sorting, and pagination for the fetched data. The `dom: 'lrtip'` option is used to disable the built-in search box, as a separate HTMX-driven search form handles filtering.
*   **Frontend Logic:** All interactions are designed to be HTMX-only, avoiding traditional JavaScript fetches and DOM manipulation, ensuring a smooth, single-page application feel without the complexity of a full SPA framework.

### Final Notes

This comprehensive plan provides a blueprint for migrating the ASP.NET Job Scheduling module to Django. By focusing on automated conversion of schema to models, re-architecting business logic into fat models, and implementing modern frontend patterns with HTMX and DataTables, the transition is streamlined and results in a performant, maintainable, and highly testable Django application. This approach minimizes manual coding and provides clear, actionable steps for an AI-assisted migration process.