## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for "Job-Scheduling Output"

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET "Job-Scheduling Output" module to a modern Django-based solution. Our approach prioritizes automation, leverages the strengths of Django for robust backend logic, and implements a dynamic, responsive frontend using HTMX and Alpine.js, eliminating the need for traditional JavaScript frameworks.

### Business Benefits and Outcomes:

*   **Enhanced Performance & Scalability:** Django's efficient ORM and Python's speed will lead to faster page loads and improved application responsiveness, capable of handling increased user loads.
*   **Reduced Development & Maintenance Costs:** Python's clear syntax, Django's "batteries-included" philosophy, and adherence to DRY principles simplify future development, debugging, and ongoing maintenance.
*   **Modern User Experience:** HTMX and Alpine.js provide a highly interactive, "single-page application" feel without the complexity of traditional JavaScript, leading to a smoother user experience and improved user satisfaction.
*   **Improved Data Integrity & Security:** Django's built-in security features, form validation, and ORM protect against common web vulnerabilities, ensuring data integrity and user trust.
*   **Future-Proof Architecture:** A well-structured Django application is inherently more adaptable to new business requirements and technological advancements, extending the lifespan and value of your investment.
*   **Increased Automation Potential:** The clear separation of concerns and Python's ecosystem enable easier integration with AI-assisted automation tools for future enhancements and module development.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`machinery`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables. We will map these to Django models, assuming they exist in the legacy SQL Server database. The `CompId` (Company ID) and `FinYearId` (Financial Year ID) are frequently used for filtering, suggesting they are critical multi-tenancy or organizational context fields. `tblMS_JobCompletion_Temp` which is deleted on page load will be ignored as it represents an anti-pattern for a multi-user web application; its functionality will be absorbed into direct queries or temporary in-memory structures if needed.

**Identified Tables and Inferred Columns:**

*   **`SD_Cust_WorkOrder_Master` (for Work Order No and Customer ID)**
    *   `Id` (PK)
    *   `EnqId`
    *   `CustomerId` (FK to `SD_Cust_master`)
    *   `WONo` (Work Order Number, likely unique per `CompId`, `FinYearId`)
    *   `PONo`
    *   `POId`
    *   `FinYearId`
    *   `CompId`
*   **`SD_Cust_master` (for Customer Name)**
    *   `CustomerId` (PK)
    *   `CustomerName`
    *   `FinYearId`
    *   `CompId`
*   **`tblDG_BOM_Master` (Bill of Material details)**
    *   `Id` (PK)
    *   `PId` (Parent Item ID in BOM hierarchy)
    *   `CId` (Child Item ID in BOM hierarchy)
    *   `ItemId` (FK to `tblDG_Item_Master`)
    *   `WONo` (FK to `SD_Cust_WorkOrder_Master.WONo`)
    *   `Qty` (Quantity in BOM)
    *   `CompId`
    *   `FinYearId`
*   **`tblDG_Item_Master` (Item Master details)**
    *   `Id` (PK, referenced as `ItemId` in `tblDG_BOM_Master`)
    *   `ItemCode`
    *   `ManfDesc` (Manufacturer Description)
    *   `UOMBasic` (FK to `Unit_Master`)
*   **`Unit_Master` (Unit of Measure details)**
    *   `Id` (PK, referenced as `UOMBasic` in `tblDG_Item_Master`)
    *   `Symbol` (Unit of Measure Symbol, e.g., "KG", "PCS")
*   **`tblMS_JobShedule_Master` & `tblMS_JobSchedule_Details`**: These tables are used in a subquery to filter BOM items. Their full schema is not directly exposed for display on *this* page, but they represent a relationship that would be defined in a more complete system. For this page's scope, they will be part of the filtering logic.

### Step 2: Identify Backend Functionality

The core functionality of this ASP.NET page is a "Read" operation (displaying data) with dynamic filtering.

*   **Read (Display):**
    *   **Initial Load:** Populates the `WONo` dropdown from `SD_Cust_WorkOrder_Master`.
    *   **Work Order Selection Change:**
        *   Retrieves `CustomerName` from `SD_Cust_master` based on the selected `WONo`.
        *   Populates a data grid (`GridView2`) with Bill of Material (BOM) details. This involves a complex recursive assembly (`fun.TreeAssembly`) and quantity calculation (`fun.BOMRecurQty`) from `tblDG_BOM_Master`, `tblDG_Item_Master`, and `Unit_Master`.
*   **Navigation:**
    *   Clicking an `ItemCode` in the grid redirects to `Schedule_Output_New_Details.aspx` with `WONo` and `ItemId`. This will map to a Django `redirect` to another Django view.
*   **Business Logic Candidates for Fat Models:**
    *   `fun.TreeAssembly(WONO, CompId)`: This function builds a hierarchical list of Item IDs based on the Work Order. This logic should reside in the `BillOfMaterial` or `WorkOrder` model, perhaps as a manager method, to retrieve related BOM components.
    *   `fun.BOMRecurQty(WONO, PId, CId, 1, CompId, FinYearId)`: This is a recursive quantity calculation for BOM items. This logic is critical and must be implemented as a method on the `BillOfMaterial` model to ensure accurate quantities.

### Step 3: Infer UI Components

The ASP.NET controls will be replaced by standard HTML elements enhanced with HTMX and Alpine.js for dynamic behavior, styled with Tailwind CSS.

*   **`DrpWONO` (DropDownList):** Will be an HTML `<select>` element.
    *   **HTMX:** `hx-get` to fetch customer details and BOM table on `change`. `hx-target` to update relevant sections.
*   **`lblCustomer` (Label):** Will be an HTML `<span>` or `<p>` element.
    *   **HTMX:** Part of the dynamically updated content.
*   **`GridView2` (GridView):** Will be an HTML `<table>` element.
    *   **DataTables:** Initialized on this table for client-side features.
    *   **HTMX:** The entire table content will be fetched as a partial view on Work Order selection change.
    *   **Columns:** Each column will be rendered dynamically from the `BillOfMaterial` data.
        *   `SN`: Simple `forloop.counter` in Django template.
        *   `ItemId`: Hidden data attribute or direct access to `obj.item.id`
        *   `ItemCode`: A link (`<a>` tag) to the detail page, containing `hx-get` if the detail page is also HTMX-driven, or just a standard `href`. For now, it will be a standard `href`.
        *   `Description`, `UOM`, `BOM Qty`: Standard table cells displaying model attributes.

### Step 4: Generate Django Code

We will create a Django application named `machinery` to house this module.

#### 4.1 Models (`machinery/models.py`)

The models will map directly to the existing database tables. `CompId` and `FinYearId` are included in models where they appear in the original queries, facilitating data isolation.

```python
from django.db import models, connection
from django.db.models import F, Sum, OuterRef, Subquery

class CompAndFinYearManager(models.Manager):
    """
    Custom manager to automatically filter by CompId and FinYearId from session context.
    In a real application, these would come from request.user profile or similar.
    For demonstration, we'll use mock values or assume a context is passed.
    """
    def get_queryset(self):
        # Mocking session values for demonstration.
        # In a real app, this would be tied to the current user's organization context.
        # e.g., comp_id = self.model._thread_local.comp_id if hasattr(self.model._thread_local, 'comp_id') else 1
        # fin_year_id = self.model._thread_local.fin_year_id if hasattr(self.model._thread_local, 'fin_year_id') else 2023
        # For simplicity in this generated code, we'll assume global context can be set or
        # queries are explicitly filtered where needed.
        return super().get_queryset()

class WorkOrder(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    enquiry_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customer_id = models.IntegerField(db_column='CustomerId') # This would be FK to Customer
    wono = models.CharField(db_column='WONo', max_length=50, unique=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    objects = CompAndFinYearManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        unique_together = (('wono', 'comp_id', 'fin_year_id'),) # Inferring uniqueness

    def __str__(self):
        return self.wono

    def get_customer(self):
        """
        Retrieves the associated customer object.
        """
        try:
            return Customer.objects.get(
                customer_id=self.customer_id, 
                comp_id=self.comp_id, 
                fin_year_id=self.fin_year_id
            )
        except Customer.DoesNotExist:
            return None

class Customer(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    objects = CompAndFinYearManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'
        unique_together = (('customer_id', 'comp_id', 'fin_year_id'),)

    def __str__(self):
        return self.customer_name

class UnitOfMeasure(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class Item(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.IntegerField(db_column='UOMBasic') # FK to UnitOfMeasure
    
    # Assuming these fields based on common patterns, though not explicitly in the C# Item query.
    # If they don't exist, they can be removed or nullable=True, blank=True added.
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True) 
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code
    
    def get_uom_symbol(self):
        """Retrieves the UOM symbol for the item."""
        try:
            return UnitOfMeasure.objects.get(id=self.uom_basic).symbol
        except UnitOfMeasure.DoesNotExist:
            return 'N/A'

class BillOfMaterial(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    parent_id = models.IntegerField(db_column='PId') # Direct parent ItemId, not PK
    child_id = models.IntegerField(db_column='CId') # Direct child ItemId, not PK
    item_id = models.IntegerField(db_column='ItemId') # The actual item_id in this BOM entry
    wono = models.CharField(db_column='WONo', max_length=50) # FK to WorkOrder.wono
    qty = models.FloatField(db_column='Qty')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = CompAndFinYearManager()

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'Bill of Material Entry'
        verbose_name_plural = 'Bills of Material Entries'
        # Composite unique constraint if applicable, e.g., ('wono', 'parent_id', 'child_id', 'item_id', 'comp_id', 'fin_year_id')

    def __str__(self):
        return f"BOM for WO: {self.wono}, Item: {self.item_id}"

    @classmethod
    def get_items_for_work_order(cls, wono, comp_id, fin_year_id):
        """
        Mimics `fun.TreeAssembly` and `fillgrid` logic to get relevant BOM items
        for a given Work Order. This is a crucial business logic method.
        
        Note: The original C# `TreeAssembly` and `BOMRecurQty` are complex recursive
        functions. This implementation is a simplified mock/placeholder. 
        A proper migration would involve porting the exact SQL/logic from `clsFunctions`.
        
        The subquery involving `tblMS_JobShedule_Master` and `tblMS_JobSchedule_Details`
        is also integrated here as a filter.
        """
        # Step 1: Mimic `fun.TreeAssembly` - get a list of relevant BOM IDs.
        # This is a mock. In reality, `TreeAssembly` likely traverses a BOM structure.
        # For a flat list as per `fillgrid`, we just query directly.
        
        # Original SQL from fillgrid:
        # tblDG_BOM_Master.ItemId in (select ItemId from tblMS_JobShedule_Master where Id in (select MId from tblMS_JobSchedule_Details))
        
        # This requires models for JobScheduleMaster and JobScheduleDetail, or raw SQL.
        # For simplicity and to avoid creating full models for subqueries not directly displayed:
        # We assume the subquery results in a list of `item_ids` that are relevant.
        # For this example, we'll just fetch all BOM items for the WONo, assuming 
        # the 'TreeAssembly' logic just returned all relevant BOM_Master IDs.
        
        # This part requires specific implementation of the original fun.TreeAssembly logic.
        # For now, we'll select BOM entries where ItemId is present in JobSchedule_Master/Details.
        
        # Mock JobSchedule models for the subquery, if they are NOT to be fully defined.
        # If they are full models, use proper FK relationships.
        # Example: Mocking the subquery behavior
        try:
            # Execute raw SQL for the subquery if models aren't available or too complex
            with connection.cursor() as cursor:
                # This is a placeholder for the actual complex SQL/TreeAssembly logic
                # For demonstration, let's assume it identifies the relevant BOM entries based on ItemId
                # and the Work Order, Company, and Financial Year.
                
                # The subquery in C# was: 
                # "tblDG_BOM_Master.ItemId in (select ItemId from tblMS_JobShedule_Master where Id in (select MId from tblMS_JobSchedule_Details))"
                # This suggests ItemIds are scheduled. We will filter BOM items based on WONo first.
                
                # Simplified query to match the original fillgrid approach (excluding TreeAssembly depth logic for now)
                # and focusing on the join conditions.
                
                # The original query was:
                # SELECT tblDG_BOM_Master.PId,tblDG_BOM_Master.CId,tblDG_BOM_Master.ItemId,
                #        tblDG_Item_Master.ManfDesc,Unit_Master.Symbol As UOMBasic,tblDG_Item_Master.ItemCode,tblDG_BOM_Master.Qty
                # FROM tblDG_BOM_Master,tblDG_Item_Master,Unit_Master
                # WHERE Unit_Master.Id=tblDG_Item_Master.UOMBasic 
                #   AND tblDG_Item_Master.Id=tblDG_BOM_Master.ItemId 
                #   AND tblDG_BOM_Master.ItemId in (select ItemId from tblMS_JobShedule_Master where Id in (select MId from tblMS_JobSchedule_Details)) 
                #   AND tblDG_BOM_Master.WONo='" + WONO + "'
                #   AND tblDG_BOM_Master.Id='" + Convert.ToInt32(li[i]) + "' -- This implies iterating through IDs from TreeAssembly
                #   AND tblDG_BOM_Master.CompId='" + CompId + "'
                #   AND tblDG_BOM_Master.FinYearId<='" + FinYearId + "'"

                # Given the iteration over `li` (from `TreeAssembly`), `li` represents `tblDG_BOM_Master.Id` values.
                # `TreeAssembly` itself would be a stored procedure or complex SQL.
                
                # For a runnable example, we will simulate the result of `TreeAssembly`
                # by simply selecting BOM entries linked to the Work Order.
                # The `ItemId in (select ...)` part needs careful re-implementation if exact logic is needed.
                # For this example, we will assume all BOM items for the WONo are relevant for display.
                
                # If the TreeAssembly returns IDs from tblDG_BOM_Master, then the logic would be:
                # bom_ids = fun.TreeAssembly(wono, comp_id) # Need to implement this complex logic
                # bom_entries = cls.objects.filter(id__in=bom_ids, wono=wono, comp_id=comp_id, fin_year_id__lte=fin_year_id)
                
                # For now, a simplified direct fetch without TreeAssembly exact logic:
                bom_entries = cls.objects.filter(
                    wono=wono,
                    comp_id=comp_id,
                    fin_year_id__lte=fin_year_id
                ).select_related('item', 'item__uom') # This join implies FKs in Django models, which we need to adjust to manual joins for `managed=False`.

                # Manual join logic and data structuring because `managed=False` means no ORM FKs.
                # Replicate the C# fillgrid logic by building a list of dictionaries.
                results = []
                for entry in bom_entries:
                    try:
                        item = Item.objects.get(id=entry.item_id)
                        uom = UnitOfMeasure.objects.get(id=item.uom_basic)
                        
                        # Call the recursive quantity calculation
                        # For now, a mock. Real impl. needed.
                        calculated_qty = BillOfMaterial.calculate_recursive_qty(
                            wono, entry.parent_id, entry.child_id, 1, comp_id, fin_year_id
                        )
                        
                        results.append({
                            'item_id': item.id,
                            'item_code': item.item_code,
                            'manf_desc': item.manf_desc,
                            'symbol': uom.symbol,
                            'qty': calculated_qty, # Use the calculated quantity
                        })
                    except (Item.DoesNotExist, UnitOfMeasure.DoesNotExist):
                        # Handle cases where related item or UOM is missing
                        continue
                return results
                
        except Exception as e:
            print(f"Error in get_items_for_work_order: {e}")
            return []

    @classmethod
    def calculate_recursive_qty(cls, wono, p_id, c_id, initial_qty, comp_id, fin_year_id):
        """
        Mimics `fun.BOMRecurQty`. This is a critical business logic function
        that typically involves traversing a BOM hierarchy to calculate the total
        quantity of a component needed.

        This is a placeholder for the actual recursive logic.
        For a runnable example, we will return the initial_qty or a simple mock.
        A real implementation would involve recursive database queries or a graph traversal.
        """
        # Example of how to start actual recursive logic (pseudo-code):
        # total_qty = initial_qty
        # direct_children = cls.objects.filter(parent_id=c_id, wono=wono, comp_id=comp_id, fin_year_id__lte=fin_year_id)
        # for child_entry in direct_children:
        #     # Recursive call for the child component
        #     total_qty += cls.calculate_recursive_qty(wono, c_id, child_entry.child_id, child_entry.qty, comp_id, fin_year_id)
        # return total_qty
        
        # For simplicity, returning a mock value derived from the BOM entry's own quantity
        # In the original C#, it was `fun.BOMRecurQty(WONO, PId, CId, 1, CompId, FinYearId);`
        # This implies it might calculate based on the given PId and CId.
        
        # Let's assume for mock purposes it's just the 'Qty' from the BillOfMaterial entry 
        # relevant to the specific BOM ID (li[i]) being processed.
        # This would be `entry.qty` from `get_items_for_work_order`.
        # However, the `fun.BOMRecurQty` was called *after* fetching the row details,
        # implying it needs the PId and CId from the current BOM entry to perform its
        # recursive calculation.
        
        # For a simple runnable code, we'll return a placeholder.
        # In a real scenario, this would be a direct port of the C# `BOMRecurQty` logic.
        
        # Find the specific BOM entry for PId and CId in the context of the WONo
        try:
            bom_entry = cls.objects.filter(
                wono=wono,
                parent_id=p_id,
                child_id=c_id,
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).first()
            if bom_entry:
                return bom_entry.qty * initial_qty # Simplified example: just multiply
            return initial_qty
        except Exception:
            return initial_qty
```

#### 4.2 Forms (`machinery/forms.py`)

A simple form to handle the `WorkOrder` selection dropdown.

```python
from django import forms
from .models import WorkOrder

class WorkOrderSelectionForm(forms.Form):
    wono = forms.ModelChoiceField(
        queryset=WorkOrder.objects.none(), # Will be set in the view
        empty_label="Select Work Order No.",
        label="Work Order No",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/machinery/schedule_output/customer_and_bom_table/', # HTMX endpoint for dynamic update
            'hx-target': '#dynamic-content', # Target div for customer name and BOM table
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
        })
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)

        # Populate the dropdown based on current context
        if comp_id is not None and fin_year_id is not None:
            self.fields['wono'].queryset = WorkOrder.objects.filter(
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).order_by('-id') # Order by Id Desc
```

#### 4.3 Views (`machinery/views.py`)

We will use Class-Based Views for the main page and a specific view for HTMX-triggered updates of the customer name and BOM table.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from django.contrib import messages

from .models import WorkOrder, Customer, BillOfMaterial, Item, UnitOfMeasure
from .forms import WorkOrderSelectionForm

# Mocking session context for CompId and FinYearId for demonstration.
# In a real app, these would come from request.user or middleware.
MOCK_COMP_ID = 1
MOCK_FIN_YEAR_ID = 2023 

class ScheduleOutputListView(TemplateView):
    """
    Renders the initial Schedule Output page with the Work Order dropdown.
    """
    template_name = 'machinery/schedule_output/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass mock session values to the form for dropdown population
        context['form'] = WorkOrderSelectionForm(comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        context['wono'] = None
        context['customer_name'] = ""
        context['bom_items'] = []
        return context

class CustomerAndBOMTablePartialView(View):
    """
    Handles HTMX requests to update the customer name and BOM table
    based on the selected Work Order Number.
    """
    def get(self, request, *args, **kwargs):
        wono_value = request.GET.get('wono')
        customer_name = ""
        bom_items = []

        if wono_value and wono_value != "Select":
            try:
                # Retrieve WorkOrder object by WONo and session context
                work_order = get_object_or_404(
                    WorkOrder, 
                    wono=wono_value, 
                    comp_id=MOCK_COMP_ID, 
                    fin_year_id__lte=MOCK_FIN_YEAR_ID
                )
                
                # Get customer name
                customer = work_order.get_customer()
                if customer:
                    customer_name = customer.customer_name
                
                # Get BOM items using the fat model approach
                bom_items = BillOfMaterial.get_items_for_work_order(
                    wono_value, MOCK_COMP_ID, MOCK_FIN_YEAR_ID
                )

            except WorkOrder.DoesNotExist:
                messages.error(request, "Work Order not found for the selected criteria.")
            except Exception as e:
                messages.error(request, f"An error occurred: {e}")

        context = {
            'wono': wono_value,
            'customer_name': customer_name,
            'bom_items': bom_items,
        }
        
        # Render the partial template
        return HttpResponse(render_to_string(
            'machinery/schedule_output/_customer_info_and_bom_table.html', 
            context, 
            request=request
        ))

```

#### 4.4 Templates (`machinery/templates/machinery/schedule_output/`)

We'll create the main page template and partial templates for the dynamic content.

**`list.html` (Main page template)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row md:items-center mb-6 bg-blue-100 p-4 rounded-lg shadow-sm">
        <h2 class="text-2xl font-bold text-gray-800 flex-grow">Job-Scheduling Output - New</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="{{ form.wono.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.wono.label }}:
                </label>
                {{ form.wono }}
                {% if form.wono.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.wono.errors }}</p>
                {% endif %}
            </div>
            <div id="customer-info" class="self-end">
                <span class="block text-sm font-medium text-gray-700 mb-1">Customer Name:</span>
                <p id="lblCustomer" class="font-bold text-lg text-gray-900">{{ customer_name }}</p>
            </div>
        </div>
    </div>
    
    <div id="dynamic-content">
        {# Initial load, or when no WONo selected, will show an empty table #}
        {% include 'machinery/schedule_output/_customer_info_and_bom_table.html' %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('scheduleOutput', () => ({
            // No specific Alpine.js state management needed for this simple page yet,
            // but can be extended for local UI states like showing/hiding loaders etc.
        }));
    });
</script>
{% endblock %}
```

**`_customer_info_and_bom_table.html` (Partial for HTMX updates)**

```html
{% comment %}
    This partial template updates the customer name and the BOM table
    It is designed to be swapped into '#dynamic-content' via HTMX.
{% endcomment %}

{# This section moved to list.html directly for initial render, or could be rendered here again if needed #}
{# But for dynamic updates, the whole block is swapped. #}
{#
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div>
        <span class="block text-sm font-medium text-gray-700 mb-1">Work Order No:</span>
        <p class="font-bold text-lg text-gray-900">{{ wono|default:'None Selected' }}</p>
    </div>
    <div>
        <span class="block text-sm font-medium text-gray-700 mb-1">Customer Name:</span>
        <p id="lblCustomer" class="font-bold text-lg text-gray-900">{{ customer_name|default:'N/A' }}</p>
    </div>
</div>
#}

<div class="bg-white shadow-md rounded-lg p-6">
    {% if bom_items %}
    <div class="overflow-x-auto">
        <table id="scheduleOutputTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in bom_items %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                        <a href="{% url 'machinery:schedule_output_details' %}?WONo={{ wono }}&Item={{ item.item_id }}" 
                           class="text-blue-600 hover:text-blue-900 font-bold">
                            {{ item.item_code }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.manf_desc }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">{{ item.symbol }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-500">{{ item.qty|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="text-center py-10">
        <p class="text-gray-500 text-lg">No data to display !</p>
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables after HTMX swap
    // This script will run every time _customer_info_and_bom_table.html is loaded via HTMX
    if ($.fn.DataTable.isDataTable('#scheduleOutputTable')) {
        $('#scheduleOutputTable').DataTable().destroy(); // Destroy previous instance if any
    }
    $(document).ready(function() {
        $('#scheduleOutputTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Enable responsive design
            "pagingType": "full_numbers" // Show first, last, next, previous buttons
        });
    });
</script>
```

#### 4.5 URLs (`machinery/urls.py`)

```python
from django.urls import path
from .views import ScheduleOutputListView, CustomerAndBOMTablePartialView

app_name = 'machinery'

urlpatterns = [
    path('schedule_output/', ScheduleOutputListView.as_view(), name='schedule_output_list'),
    path('schedule_output/customer_and_bom_table/', CustomerAndBOMTablePartialView.as_view(), name='customer_and_bom_table_partial'),
    
    # Placeholder for the detail page. This would be another Django URL.
    path('schedule_output/details/', ScheduleOutputListView.as_view(), name='schedule_output_details'), # Mock URL, replace with actual detail view
]
```

#### 4.6 Tests (`machinery/tests.py`)

Comprehensive tests for models (business logic) and views (HTTP responses, HTMX interactions).

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock

from .models import WorkOrder, Customer, BillOfMaterial, Item, UnitOfMeasure, MOCK_COMP_ID, MOCK_FIN_YEAR_ID

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.uom_pcs = UnitOfMeasure.objects.create(id=1, symbol='PCS')
        cls.item1 = Item.objects.create(id=101, item_code='ITEM001', manf_desc='Product A', uom_basic=cls.uom_pcs.id)
        cls.item2 = Item.objects.create(id=102, item_code='ITEM002', manf_desc='Component B', uom_basic=cls.uom_pcs.id)
        cls.item3 = Item.objects.create(id=103, item_code='ITEM003', manf_desc='Sub-Component C', uom_basic=cls.uom_pcs.id)

        cls.customer = Customer.objects.create(customer_id=1, customer_name='Test Customer Inc.', comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        cls.work_order = WorkOrder.objects.create(
            id=1, customer_id=cls.customer.customer_id, wono='WO-2023-001',
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        cls.work_order_no_customer = WorkOrder.objects.create(
            id=2, customer_id=999, wono='WO-2023-002', # Customer 999 does not exist
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )

        # BOM Structure:
        # WO-2023-001
        #   - Item 101 (PId=0, CId=0 for top-level, or some convention)
        #     - Item 102 (PId=101, CId=102)
        #       - Item 103 (PId=102, CId=103)
        cls.bom_entry_1 = BillOfMaterial.objects.create(
            id=1, parent_id=0, child_id=0, item_id=cls.item1.id, wono=cls.work_order.wono, qty=1.0,
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        cls.bom_entry_2 = BillOfMaterial.objects.create(
            id=2, parent_id=cls.item1.id, child_id=cls.item2.id, item_id=cls.item2.id, wono=cls.work_order.wono, qty=2.0,
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        cls.bom_entry_3 = BillOfMaterial.objects.create(
            id=3, parent_id=cls.item2.id, child_id=cls.item3.id, item_id=cls.item3.id, wono=cls.work_order.wono, qty=3.0,
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        
        cls.scheduled_item_ids = [cls.item1.id, cls.item2.id, cls.item3.id] # Mocking scheduled item IDs for BOM.get_items_for_work_order

    def test_work_order_creation(self):
        self.assertEqual(self.work_order.wono, 'WO-2023-001')
        self.assertEqual(self.work_order.customer_id, self.customer.customer_id)

    def test_work_order_get_customer(self):
        customer = self.work_order.get_customer()
        self.assertIsNotNone(customer)
        self.assertEqual(customer.customer_name, 'Test Customer Inc.')
        
    def test_work_order_get_customer_not_found(self):
        customer = self.work_order_no_customer.get_customer()
        self.assertIsNone(customer)

    def test_item_get_uom_symbol(self):
        self.assertEqual(self.item1.get_uom_symbol(), 'PCS')
        
    def test_item_get_uom_symbol_not_found(self):
        # Create an item with a non-existent UOM
        item_bad_uom = Item.objects.create(id=104, item_code='BADUOM', manf_desc='Item with bad UOM', uom_basic=999)
        self.assertEqual(item_bad_uom.get_uom_symbol(), 'N/A')

    # Test BillOfMaterial.get_items_for_work_order
    @patch('machinery.models.BillOfMaterial.calculate_recursive_qty', return_value=1.0) # Mock recursive qty for simplicity
    def test_bill_of_material_get_items_for_work_order(self, mock_calc_qty):
        bom_items = BillOfMaterial.get_items_for_work_order(
            self.work_order.wono, MOCK_COMP_ID, MOCK_FIN_YEAR_ID
        )
        self.assertIsInstance(bom_items, list)
        self.assertGreater(len(bom_items), 0) # Should return items for WO-2023-001
        
        # Check if the data structure matches expected
        first_item = next((item for item in bom_items if item['item_id'] == self.item1.id), None)
        self.assertIsNotNone(first_item)
        self.assertEqual(first_item['item_code'], 'ITEM001')
        self.assertEqual(first_item['manf_desc'], 'Product A')
        self.assertEqual(first_item['symbol'], 'PCS')
        self.assertEqual(first_item['qty'], 1.0) # From mock_calc_qty

    @patch('machinery.models.BillOfMaterial.calculate_recursive_qty', return_value=1.0)
    def test_bill_of_material_get_items_for_work_order_no_match(self, mock_calc_qty):
        bom_items = BillOfMaterial.get_items_for_work_order(
            'NONEXISTENT-WO', MOCK_COMP_ID, MOCK_FIN_YEAR_ID
        )
        self.assertEqual(len(bom_items), 0)
        
    def test_bill_of_material_calculate_recursive_qty(self):
        # Test with direct BOM entry for PId and CId
        qty = BillOfMaterial.calculate_recursive_qty(
            self.work_order.wono, self.item1.id, self.item2.id, 1, MOCK_COMP_ID, MOCK_FIN_YEAR_ID
        )
        # Assuming our mock returns original qty * initial_qty for matching entry
        self.assertEqual(qty, self.bom_entry_2.qty * 1) # Expected: 2.0 * 1 = 2.0

        # Test for non-existent PId/CId
        qty_no_match = BillOfMaterial.calculate_recursive_qty(
            self.work_order.wono, 999, 888, 1, MOCK_COMP_ID, MOCK_FIN_YEAR_ID
        )
        self.assertEqual(qty_no_match, 1) # Should return initial_qty if no match

class ScheduleOutputViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Ensure test data exists for views
        self.uom_pcs = UnitOfMeasure.objects.create(id=1, symbol='PCS')
        self.item1 = Item.objects.create(id=101, item_code='ITEM001', manf_desc='Product A', uom_basic=self.uom_pcs.id)
        self.customer = Customer.objects.create(customer_id=1, customer_name='Test Customer Inc.', comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        self.work_order = WorkOrder.objects.create(
            id=1, customer_id=self.customer.customer_id, wono='WO-2023-001',
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        BillOfMaterial.objects.create(
            id=1, parent_id=0, child_id=0, item_id=self.item1.id, wono=self.work_order.wono, qty=5.0,
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )

    def test_schedule_output_list_view(self):
        response = self.client.get(reverse('machinery:schedule_output_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/schedule_output/list.html')
        self.assertContains(response, 'Job-Scheduling Output - New')
        self.assertContains(response, 'Select Work Order No.') # Check for dropdown label
        self.assertContains(response, '<table id="scheduleOutputTable"') # Check for empty table structure initially

    @patch('machinery.models.BillOfMaterial.get_items_for_work_order', return_value=[
        {'item_id': 101, 'item_code': 'ITEM001', 'manf_desc': 'Product A', 'symbol': 'PCS', 'qty': 5.0}
    ])
    def test_customer_and_bom_table_partial_view_htmx_success(self, mock_get_items):
        response = self.client.get(
            reverse('machinery:customer_and_bom_table_partial'),
            {'wono': self.work_order.wono},
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/schedule_output/_customer_info_and_bom_table.html')
        self.assertContains(response, self.customer.customer_name)
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'Product A')
        self.assertContains(response, 'PCS')
        self.assertContains(response, '5.00')
        mock_get_items.assert_called_once_with(self.work_order.wono, MOCK_COMP_ID, MOCK_FIN_YEAR_ID)

    @patch('machinery.models.BillOfMaterial.get_items_for_work_order', return_value=[])
    def test_customer_and_bom_table_partial_view_htmx_no_items(self, mock_get_items):
        # Test with a WO that yields no BOM items
        work_order_empty = WorkOrder.objects.create(
            id=3, customer_id=self.customer.customer_id, wono='WO-2023-EMPTY',
            comp_id=MOCK_COMP_ID, fin_year_id=MOCK_FIN_YEAR_ID
        )
        response = self.client.get(
            reverse('machinery:customer_and_bom_table_partial'),
            {'wono': work_order_empty.wono},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        mock_get_items.assert_called_once_with(work_order_empty.wono, MOCK_COMP_ID, MOCK_FIN_YEAR_ID)

    def test_customer_and_bom_table_partial_view_htmx_no_wono_selected(self):
        response = self.client.get(
            reverse('machinery:customer_and_bom_table_partial'),
            {'wono': 'Select'}, # Simulating 'Select' option
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'N/A') # Default customer name
        self.assertContains(response, 'No data to display !') # No items displayed
        # Assert that get_items_for_work_order was not called
        with patch('machinery.models.BillOfMaterial.get_items_for_work_order') as mock_method:
            self.client.get(
                reverse('machinery:customer_and_bom_table_partial'),
                {'wono': 'Select'},
                HTTP_HX_REQUEST='true'
            )
            mock_method.assert_not_called()

    def test_customer_and_bom_table_partial_view_htmx_invalid_wono(self):
        response = self.client.get(
            reverse('machinery:customer_and_bom_table_partial'),
            {'wono': 'INVALID_WO_NUMBER'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'N/A') # No customer name
        self.assertContains(response, 'No data to display !') # No items
        self.assertContains(response, 'Work Order not found for the selected criteria.') # Message should be present
```

### Step 5: HTMX and Alpine.js Integration

The core of the dynamic behavior in this module is driven by HTMX.

*   **Work Order Dropdown:**
    *   The `select` element for `wono` (within `WorkOrderSelectionForm`) is configured with `hx-get`, `hx-target`, `hx-swap`, and `hx-trigger="change"`.
    *   When a user selects a `WONo`, HTMX makes a GET request to `/machinery/schedule_output/customer_and_bom_table/` sending the selected `wono` as a query parameter.
    *   The `hx-target="#dynamic-content"` ensures the HTML response from the `CustomerAndBOMTablePartialView` replaces the content of the `div` with `id="dynamic-content"`.
*   **Customer Name & BOM Table Update:**
    *   The `CustomerAndBOMTablePartialView` renders the `_customer_info_and_bom_table.html` partial. This partial contains the customer name and the entire `<table>` structure for the BOM items.
    *   Upon successful HTMX swap, the new table content is inserted, and the `script` tag within the partial (which initializes DataTables) runs, ensuring the table is interactive.
*   **DataTables:**
    *   The `_customer_info_and_bom_table.html` partial includes a JavaScript block that initializes `DataTables` on the `scheduleOutputTable`. It's crucial to destroy any existing DataTable instance before re-initializing to prevent errors when the partial is swapped multiple times.
    *   This provides client-side searching, sorting, and pagination for the BOM items.
*   **Navigation Link:**
    *   The `ItemCode` link is a standard `<a>` tag. When clicked, it performs a full page navigation to the (placeholder) detail page, `machinery:schedule_output_details`, passing `WONo` and `Item` (ItemId) as query parameters, mimicking the original `Response.Redirect` behavior. If the detail page were also HTMX-driven, it would use `hx-get` and `hx-target` to load content into a modal or specific `div`.
*   **Alpine.js:**
    *   While not strictly necessary for the core functionality of this specific page (which is primarily HTMX-driven data display), Alpine.js is included in `list.html` for potential future client-side UI states or interactions. For instance, it could manage a loading spinner or dynamic messages without requiring server-side HTMX.

### Final Notes

*   **Placeholders:** `MOCK_COMP_ID` and `MOCK_FIN_YEAR_ID` in `models.py` and `views.py` are placeholders. In a real application, these values would be dynamically obtained from the authenticated user's session, profile, or a global context manager.
*   **`fun.TreeAssembly` and `fun.BOMRecurQty`:** The implementations in `BillOfMaterial.get_items_for_work_order` and `BillOfMaterial.calculate_recursive_qty` are simplified mock-ups. The actual migration of these complex recursive business logic functions, especially if they involve stored procedures or highly optimized SQL, would require a dedicated in-depth analysis and careful porting to Django ORM, raw SQL, or a custom Python library/module within the `machinery` app. This is the most complex part of the backend migration.
*   **Error Handling:** The `views.py` includes basic `try-except` blocks and `messages.error` for user feedback. More robust error handling and logging would be implemented in a production system.
*   **Security:** Ensure proper authentication and authorization middleware are configured in your Django project to protect views and data access, especially concerning `CompId` and `FinYearId` filtering.
*   **Database Connection:** Remember to configure your Django `settings.py` to connect to your existing SQL Server database, specifying the correct `ENGINE`, `NAME`, `USER`, `PASSWORD`, `HOST`, and `PORT`.