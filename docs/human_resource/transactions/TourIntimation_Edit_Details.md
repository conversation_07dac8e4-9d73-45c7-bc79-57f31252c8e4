## ASP.NET to Django Conversion Script: Tour Intimation Edit Details

This document outlines a comprehensive modernization plan for transitioning the `TourIntimation_Edit_Details.aspx` ASP.NET application to a modern Django 5.0+ solution. The focus is on leveraging AI-assisted automation by providing clear, structured instructions and complete, runnable Django code components.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
- From `SqlDataSource` and SQL commands (`SELECT`, `INSERT`, `UPDATE`, `DELETE`), identify the primary tables involved.
- Infer column names and data types based on UI bindings and data operations.

**Extracted Schema:**

*   **`tblACC_TourIntimation_Master`** (Main Tour Intimation Data)
    *   `Id` (PK, int)
    *   `TINo` (nvarchar)
    *   `EmpId` (nvarchar, maps to `tblHR_OfficeStaff.EmpId`)
    *   `WONo` (nvarchar)
    *   `BGGroupId` (int, maps to `BusinessGroup.Id`)
    *   `ProjectName` (nvarchar)
    *   `TourStartDate` (datetime)
    *   `TourStartTime` (nvarchar, stores time as "HH:MM:SS:AM/PM")
    *   `TourEndDate` (datetime)
    *   `TourEndTime` (nvarchar, stores time as "HH:MM:SS:AM/PM")
    *   `NoOfDays` (int)
    *   `NameAddressSerProvider` (nvarchar)
    *   `ContactPerson` (nvarchar)
    *   `ContactNo` (nvarchar)
    *   `Email` (nvarchar)
    *   `PlaceOfTourCountry` (int, maps to `tblHR_Country.CId`)
    *   `PlaceOfTourState` (int, maps to `tblHR_State.SId`)
    *   `PlaceOfTourCity` (int, maps to `tblHR_City.CityId`)
    *   `SysDate` (datetime)
    *   `SysTime` (nvarchar)
    *   `SessionId` (nvarchar)
    *   `CompId` (int)
    *   `FinYearId` (int)

*   **`tblACC_TourExpencessType`** (Advance Details - Expense Types)
    *   `Id` (PK, int)
    *   `Terms` (nvarchar)

*   **`tblACC_TourAdvance_Details`** (Advance Details - Amounts per Expense Type for a specific Tour Intimation)
    *   `Id` (PK, int)
    *   `MId` (FK to `tblACC_TourIntimation_Master.Id`, int)
    *   `ExpencessId` (FK to `tblACC_TourExpencessType.Id`, int)
    *   `Amount` (float)
    *   `Remarks` (nvarchar)

*   **`tblACC_TourAdvance`** (Advance Transferred To Employees for a specific Tour Intimation)
    *   `Id` (PK, int)
    *   `MId` (FK to `tblACC_TourIntimation_Master.Id`, int)
    *   `EmpId` (FK to `tblHR_OfficeStaff.EmpId`, nvarchar)
    *   `Amount` (float)
    *   `Remarks` (nvarchar)

*   **`tblHR_OfficeStaff`** (Employee Master)
    *   `EmpId` (PK, nvarchar)
    *   `EmployeeName` (nvarchar)
    *   `Title` (nvarchar)

*   **`BusinessGroup`** (Business Group Master)
    *   `Id` (PK, int)
    *   `Symbol` (nvarchar)

*   **`tblHR_Country`** (Country Master)
    *   `CId` (PK, int)
    *   `CountryName` (nvarchar)

*   **`tblHR_State`** (State Master)
    *   `SId` (PK, int)
    *   `CId` (FK to `tblHR_Country.CId`, int)
    *   `StateName` (nvarchar)

*   **`tblHR_City`** (City Master)
    *   `CityId` (PK, int)
    *   `SId` (FK to `tblHR_State.SId`, int)
    *   `CityName` (nvarchar)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic.

**Instructions:**
- Analyze C# methods (`Page_Load`, `btnSubmit_Click`, `GridView1_RowCommand`, `GridView1_RowDeleting`, `GridView1_RowUpdating`, `GetCompletionList`, `ddlPlaceOfTourCountry_SelectedIndexChanged`, `ddlPlaceOfTourState_SelectedIndexChanged`).

**Identified Functionality:**

*   **Main Form (Tour Intimation Master):**
    *   **Read (R):** On `Page_Load`, retrieves a single `TourIntimation` record by `Id` from query string and populates all main form fields.
    *   **Update (U):** On `btnSubmit_Click`, updates the existing `TourIntimation_Master` record with new values.
    *   **Logic:**
        *   Employee name auto-completion (`GetCompletionList`).
        *   WO No / BG Group selection logic (`RadioButtonWONoGroup_SelectedIndexChanged`, `WONoGroup`).
        *   Cascading dropdowns for Country, State, City (`ddlPlaceOfTourCountry_SelectedIndexChanged`, `ddlPlaceOfTourState_SelectedIndexChanged`).
        *   Date and Time parsing/formatting.
        *   Validation: Required fields, number format, date format, valid WO No, valid Employee.

*   **Advance Details (`GridView2` - `tblACC_TourAdvance_Details`):**
    *   **Read (R):** `fillgrid()` populates this grid by joining `tblACC_TourExpencessType` with `tblACC_TourAdvance_Details` for the specific `TourIntimation`.
    *   **Update (U):** `btnSubmit_Click` iterates through `GridView2` rows to update `tblACC_TourAdvance_Details` or insert new records if no prior entry existed for a given expense type.

*   **Advance Trans. To (`GridView1` - `tblACC_TourAdvance`):**
    *   **Read (R):** `FillGridAdvanceTo()` populates this grid for the specific `TourIntimation`, joining with Employee details.
    *   **Create (C):** `GridView1_RowCommand` (CommandName="Add" or "Add1") inserts new `tblACC_TourAdvance` records via the GridView footer/empty template.
    *   **Update (U):** `GridView1_RowUpdating` updates existing `tblACC_TourAdvance` records.
    *   **Delete (D):** `GridView1_RowDeleting` deletes `tblACC_TourAdvance` records.
    *   **Logic:** Employee name auto-completion for adding new advances. Pagination.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
- Identify how each control contributes to user interaction and data presentation.

**Inferred UI Components:**

*   **Master Page/Layout:** Handled by Django's template inheritance (`core/base.html`).
*   **Header/Title:** Simple HTML.
*   **Form Inputs:**
    *   Text boxes (`TextBox`): Employee Name, Project Name, WO No, No of Days, Accommodation, Contact Person, Contact No, Email, Tour Start/End Dates.
    *   Dropdowns (`DropDownList`): BG Group, Place of Tour (Country, State, City).
    *   Radio Buttons (`RadioButtonList`): WO No / BG Group.
    *   Date Pickers (`CalendarExtender`): For tour start/end dates.
    *   Time Pickers (`MKB:TimeSelector`): For tour start/end times.
*   **Tabbed Interface (`TabContainer`):** Will be implemented with HTMX and Alpine.js for client-side tab switching.
*   **Data Grids (`GridView`):**
    *   `GridView2` (Advance Details): Displays a list of expense types with editable amount and remarks. This will be a Django formset.
    *   `GridView1` (Advance Trans. To): Displays advances given to employees. This will be an HTMX-driven DataTables component with modal forms for add/edit/delete.
*   **Buttons:** "Update", "Cancel". HTMX `hx-post` for form submissions.
*   **Validation Summary/Messages:** Replaced by Django form error rendering and `django.contrib.messages` for success/error alerts.
*   **Auto-Complete:** HTMX `hx-get` to a Django view returning suggestions.

---

## Step 4: Generate Django Code

### 4.1 Models (`tour_intimation/models.py`)

**Task:** Create Django models based on the extracted database schema.

**Instructions:**
- Name models clearly (e.g., `TourIntimation` for `tblACC_TourIntimation_Master`).
- Define fields with appropriate Django types.
- Use `db_column` and `db_table` for existing database mapping.
- Set `managed = False`.
- Implement business logic as model methods.

```python
import datetime
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone # Using timezone for datetime operations

# Assuming these are core system models or will be migrated separately
# For now, we'll map them directly and treat them as external managed=False tables.
class Company(models.Model):
    CompId = models.IntegerField(primary_key=True, db_column='CompId')
    CompanyName = models.CharField(max_length=255, db_column='CompanyName')
    
    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming a table for company master
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    
    def __str__(self):
        return self.CompanyName

class FinancialYear(models.Model):
    FinYearId = models.IntegerField(primary_key=True, db_column='FinYearId')
    FinYear = models.CharField(max_length=10, db_column='FinYear')

    class Meta:
        managed = False
        db_table = 'tblFinancialYear' # Assuming a table for financial year
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.FinYear

class Employee(models.Model):
    EmpId = models.CharField(primary_key=True, max_length=50, db_column='EmpId')
    Title = models.CharField(max_length=10, db_column='Title', blank=True, null=True)
    EmployeeName = models.CharField(max_length=255, db_column='EmployeeName')
    # Add other relevant employee fields if known

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.Title}.{self.EmployeeName} [{self.EmpId}]"
    
    @classmethod
    def get_emp_id_from_full_name(cls, full_name_str):
        """
        Extracts EmpId from a string like "Title.Employee Name [EmpId]".
        Returns EmpId or None if not found/invalid format.
        """
        import re
        match = re.search(r'\[(.*?)\]$', full_name_str)
        if match:
            return match.group(1)
        return None

class BusinessGroup(models.Model):
    Id = models.IntegerField(primary_key=True, db_column='Id')
    Symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.Symbol

class Country(models.Model):
    CId = models.IntegerField(primary_key=True, db_column='CId')
    CountryName = models.CharField(max_length=100, db_column='CountryName')

    class Meta:
        managed = False
        db_table = 'tblHR_Country' # Assuming this table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.CountryName

class State(models.Model):
    SId = models.IntegerField(primary_key=True, db_column='SId')
    CId = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId', related_name='states') # DO_NOTHING for managed=False
    StateName = models.CharField(max_length=100, db_column='StateName')

    class Meta:
        managed = False
        db_table = 'tblHR_State' # Assuming this table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.StateName

class City(models.Model):
    CityId = models.IntegerField(primary_key=True, db_column='CityId')
    SId = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId', related_name='cities') # DO_NOTHING for managed=False
    CityName = models.CharField(max_length=100, db_column='CityName')

    class Meta:
        managed = False
        db_table = 'tblHR_City' # Assuming this table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.CityName

class TourIntimation(models.Model):
    # Primary Key
    Id = models.IntegerField(primary_key=True, db_column='Id') 

    # General Tour Details
    TINo = models.CharField(max_length=50, db_column='TINo', blank=True, null=True) # Tour Intimation Number, LblTINo
    EmpId = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='EmpId', related_name='tour_intimations', blank=True, null=True)
    WONo = models.CharField(max_length=50, db_column='WONo', blank=True, null=True)
    BGGroupId = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroupId', related_name='tour_intimations', default=1) # Default 1 as per ASP.NET code
    ProjectName = models.CharField(max_length=255, db_column='ProjectName')

    # Tour Dates and Times
    TourStartDate = models.DateField(db_column='TourStartDate')
    TourStartTime = models.CharField(max_length=20, db_column='TourStartTime') # Stored as 'HH:MM:SS:AM/PM' in ASP.NET
    TourEndDate = models.DateField(db_column='TourEndDate')
    TourEndTime = models.CharField(max_length=20, db_column='TourEndTime') # Stored as 'HH:MM:SS:AM/PM' in ASP.NET
    NoOfDays = models.IntegerField(db_column='NoOfDays')

    # Accommodation and Contact
    NameAddressSerProvider = models.TextField(db_column='NameAddressSerProvider')
    ContactPerson = models.CharField(max_length=255, db_column='ContactPerson')
    ContactNo = models.CharField(max_length=50, db_column='ContactNo')
    Email = models.EmailField(max_length=255, db_column='Email', blank=True, null=True)

    # Place of Tour
    PlaceOfTourCountry = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCountry', related_name='tour_intimations_by_country', blank=True, null=True)
    PlaceOfTourState = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='PlaceOfTourState', related_name='tour_intimations_by_state', blank=True, null=True)
    PlaceOfTourCity = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCity', related_name='tour_intimations_by_city', blank=True, null=True)

    # System Tracking Fields
    SysDate = models.DateField(db_column='SysDate', auto_now_add=True) # Automatically set on creation/update
    SysTime = models.CharField(max_length=20, db_column='SysTime', blank=True, null=True) # Can be updated by CTime in ASP.NET
    SessionId = models.CharField(max_length=255, db_column='SessionId', blank=True, null=True) # User's session ID
    CompId = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    FinYearId = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'

    def __str__(self):
        return self.TINo if self.TINo else f"Tour Intimation {self.Id}"

    def clean(self):
        # Business logic for TourIntimation validation (fat model)
        if self.TourStartDate and self.TourEndDate and self.TourStartDate > self.TourEndDate:
            raise ValidationError({'TourEndDate': 'Tour End Date cannot be before Tour Start Date.'})
        
        # Calculate NoOfDays based on dates if not provided or to ensure consistency
        if self.TourStartDate and self.TourEndDate:
            calculated_days = (self.TourEndDate - self.TourStartDate).days + 1
            if self.NoOfDays != calculated_days:
                # Optionally, raise an error or set NoOfDays to calculated_days
                # For now, let's just ensure it's calculated correctly upon save or form validation
                pass

    def save(self, *args, **kwargs):
        # Update system tracking fields before saving
        self.SysDate = timezone.now().date()
        self.SysTime = timezone.now().strftime("%H:%M:%S:%p")
        super().save(*args, **kwargs)

    @property
    def full_employee_name(self):
        return str(self.EmpId) if self.EmpId else ''

    @property
    def tour_start_datetime(self):
        try:
            # Parse 'HH:MM:SS:AM/PM' string
            time_str = self.TourStartTime.replace(' ', '')
            time_obj = datetime.datetime.strptime(time_str, "%I:%M:%S:%p").time()
            return datetime.datetime.combine(self.TourStartDate, time_obj)
        except (ValueError, TypeError):
            return None

    @property
    def tour_end_datetime(self):
        try:
            # Parse 'HH:MM:SS:AM/PM' string
            time_str = self.TourEndTime.replace(' ', '')
            time_obj = datetime.datetime.strptime(time_str, "%I:%M:%S:%p").time()
            return datetime.datetime.combine(self.TourEndDate, time_obj)
        except (ValueError, TypeError):
            return None


class TourExpenseType(models.Model):
    Id = models.IntegerField(primary_key=True, db_column='Id')
    Terms = models.CharField(max_length=255, db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblACC_TourExpencessType'
        verbose_name = 'Tour Expense Type'
        verbose_name_plural = 'Tour Expense Types'

    def __str__(self):
        return self.Terms

class TourAdvanceDetail(models.Model):
    Id = models.IntegerField(primary_key=True, db_column='Id')
    MId = models.ForeignKey(TourIntimation, on_delete=models.DO_NOTHING, db_column='MId', related_name='expense_details')
    ExpencessId = models.ForeignKey(TourExpenseType, on_delete=models.DO_NOTHING, db_column='ExpencessId', related_name='advance_details')
    Amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount', default=0.000) # Use DecimalField for financial data
    Remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance_Details'
        unique_together = (('MId', 'ExpencessId'),) # Ensure one entry per expense type per intimation
        verbose_name = 'Tour Advance Detail'
        verbose_name_plural = 'Tour Advance Details'

    def __str__(self):
        return f"{self.MId.TINo} - {self.ExpencessId.Terms} - {self.Amount}"
    
    def clean(self):
        if self.Amount < 0:
            raise ValidationError({'Amount': 'Amount cannot be negative.'})


class TourAdvance(models.Model):
    Id = models.IntegerField(primary_key=True, db_column='Id') # Auto-incrementing in ASP.NET
    MId = models.ForeignKey(TourIntimation, on_delete=models.DO_NOTHING, db_column='MId', related_name='advances_to_employees')
    EmpId = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='EmpId', related_name='given_advances')
    Amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount') # Use DecimalField for financial data
    Remarks = models.TextField(db_column='Remarks', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance'
        verbose_name = 'Tour Advance'
        verbose_name_plural = 'Tour Advances'

    def __str__(self):
        return f"Advance for {self.EmpId.EmployeeName} on {self.MId.TINo}"
    
    def clean(self):
        if self.Amount <= 0:
            raise ValidationError({'Amount': 'Amount must be positive.'})

```

### 4.2 Forms (`tour_intimation/forms.py`)

**Task:** Define Django forms for user input, including a ModelForm for `TourIntimation` and formsets for related models.

**Instructions:**
- Create `ModelForm` instances for the main model and inline models.
- Add appropriate widgets with Tailwind CSS classes.
- Implement custom validation logic for complex scenarios (e.g., employee name parsing, WO No validation, time parsing).

```python
from django import forms
from django.forms import inlineformset_factory
from .models import (
    TourIntimation, Employee, BusinessGroup, Country, State, City, 
    TourExpenseType, TourAdvanceDetail, TourAdvance
)
from datetime import time, datetime

class TourIntimationForm(forms.ModelForm):
    # These fields correspond to the main form inputs
    # EmpName is a display field, EmpId is the actual FK. We'll handle this in clean/save.
    employee_full_name = forms.CharField(
        max_length=255, 
        required=True, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/tour-intimation/employee-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'placeholder': 'Start typing employee name...'
        })
    )
    # Autocomplete suggestion container
    employee_suggestions = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={'id': 'employee-suggestions'}) # Hidden input to mark the container
    )

    # Radio button for WO No / BG Group logic
    wo_no_group_selection = forms.ChoiceField(
        choices=[('0', 'WO No'), ('1', 'BG Group')],
        widget=forms.RadioSelect(attrs={
            'hx-post': '/tour-intimation/toggle-wo-group/',
            'hx-target': '#wo-group-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change'
        }),
        initial='0', # Default to WO No
        label="" # No label needed for radio list itself
    )

    # Custom fields for date and time to handle separate inputs
    tour_start_date_input = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-[70px]'}),
        label="Tour Start Date",
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow both formats if needed
    )
    tour_start_time_input = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'class': 'box3 w-[70px]'}),
        label="Time"
    )
    tour_end_date_input = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-[70px]'}),
        label="Tour End Date",
        input_formats=['%Y-%m-%d', '%d-%m-%Y']
    )
    tour_end_time_input = forms.TimeField(
        widget=forms.TimeInput(attrs={'type': 'time', 'class': 'box3 w-[70px]'}),
        label="Time"
    )

    class Meta:
        model = TourIntimation
        fields = [
            'TINo', 'WONo', 'BGGroupId', 'ProjectName', 
            'tour_start_date_input', 'tour_start_time_input', 
            'tour_end_date_input', 'tour_end_time_input',
            'NoOfDays', 'NameAddressSerProvider', 'ContactPerson', 
            'ContactNo', 'Email', 'PlaceOfTourCountry', 
            'PlaceOfTourState', 'PlaceOfTourCity', 'Id' # Id for update form
        ]
        widgets = {
            'TINo': forms.TextInput(attrs={'class': 'box3 w-full', 'readonly': 'readonly'}),
            'WONo': forms.TextInput(attrs={'class': 'box3'}),
            'BGGroupId': forms.Select(attrs={'class': 'box3'}),
            'ProjectName': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'NoOfDays': forms.NumberInput(attrs={'class': 'box3', 'min': '0'}),
            'NameAddressSerProvider': forms.Textarea(attrs={'class': 'box3 w-full h-[30px]', 'rows': 2}),
            'ContactPerson': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'ContactNo': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'Email': forms.EmailInput(attrs={'class': 'box3 w-[75%]'}),
            'PlaceOfTourCountry': forms.Select(attrs={'class': 'box3', 'hx-post': '/tour-intimation/get-states/', 'hx-target': '#id_PlaceOfTourState', 'hx-swap': 'outerHTML'}),
            'PlaceOfTourState': forms.Select(attrs={'class': 'box3', 'hx-post': '/tour-intimation/get-cities/', 'hx-target': '#id_PlaceOfTourCity', 'hx-swap': 'outerHTML'}),
            'PlaceOfTourCity': forms.Select(attrs={'class': 'box3'}),
            'Id': forms.HiddenInput(), # Primary key, might be from query string, or hidden
        }
        labels = {
            'WONo': '', # Label handled by radio button list
            'BGGroupId': '', # Label handled by radio button list
            'NameAddressSerProvider': 'Name & Address of Accommodation Service Provider',
            'NoOfDays': 'No. of Days',
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for custom fields from instance
        if self.instance.pk:
            self.fields['employee_full_name'].initial = self.instance.full_employee_name
            self.fields['tour_start_date_input'].initial = self.instance.TourStartDate
            self.fields['tour_start_time_input'].initial = datetime.strptime(self.instance.TourStartTime, "%I:%M:%S:%p").time() if self.instance.TourStartTime else None
            self.fields['tour_end_date_input'].initial = self.instance.TourEndDate
            self.fields['tour_end_time_input'].initial = datetime.strptime(self.instance.TourEndTime, "%I:%M:%S:%p").time() if self.instance.TourEndTime else None
            
            # Set initial value for WO No/BG Group selection
            if self.instance.BGGroupId and self.instance.BGGroupId.Id != 1: # Assuming 1 is default/NA for WO No
                self.fields['wo_no_group_selection'].initial = '1'
            else:
                self.fields['wo_no_group_selection'].initial = '0'

        # Dynamically filter choices for dropdowns
        self.fields['BGGroupId'].queryset = BusinessGroup.objects.all()
        self.fields['PlaceOfTourCountry'].queryset = Country.objects.all()
        
        # Initial filtering for State and City based on existing instance data
        if self.instance.PlaceOfTourCountry:
            self.fields['PlaceOfTourState'].queryset = State.objects.filter(CId=self.instance.PlaceOfTourCountry.CId)
        else:
            self.fields['PlaceOfTourState'].queryset = State.objects.none()

        if self.instance.PlaceOfTourState:
            self.fields['PlaceOfTourCity'].queryset = City.objects.filter(SId=self.instance.PlaceOfTourState.SId)
        else:
            self.fields['PlaceOfTourCity'].queryset = City.objects.none()

        # Mark BGGroup related fields as not required if WO No is selected, vice versa
        self.toggle_wo_group_visibility(self.fields['wo_no_group_selection'].initial)


    def toggle_wo_group_visibility(self, selection_value):
        """
        Adjusts visibility and required status of WONo and BGGroupId based on selection.
        This is primarily for frontend hints and initial form rendering.
        Actual validation happens in clean().
        """
        if selection_value == '0': # WO No selected
            self.fields['WONo'].required = True
            self.fields['BGGroupId'].required = False
            # self.fields['BGGroupId'].widget.attrs['disabled'] = 'disabled' # For display purposes
        else: # BG Group selected
            self.fields['WONo'].required = False
            self.fields['BGGroupId'].required = True
            # self.fields['WONo'].widget.attrs['disabled'] = 'disabled' # For display purposes


    def clean(self):
        cleaned_data = super().clean()
        
        # --- Employee Name Validation ---
        employee_full_name = cleaned_data.get('employee_full_name')
        emp_id_str = Employee.get_emp_id_from_full_name(employee_full_name)
        
        if not emp_id_str:
            self.add_error('employee_full_name', 'Invalid Employee Name. Please select from suggestions.')
            return cleaned_data # Return early if employee name is invalid

        try:
            employee_obj = Employee.objects.get(EmpId=emp_id_str)
            cleaned_data['EmpId'] = employee_obj # Assign actual Employee object to EmpId
        except Employee.DoesNotExist:
            self.add_error('employee_full_name', 'Employee not found. Please select a valid employee.')

        # --- WO No / BG Group Validation ---
        wo_no_group_selection = self.data.get('wo_no_group_selection', self.fields['wo_no_group_selection'].initial)
        wo_no = cleaned_data.get('WONo')
        bg_group_id = cleaned_data.get('BGGroupId') # This is the BGGroup object

        if wo_no_group_selection == '0': # WO No selected
            if not wo_no:
                self.add_error('WONo', 'WO No is required.')
            # You would integrate `fun.CheckValidWONo` here.
            # For simplicity, we assume it's just a text field for now.
            # if not check_valid_wo_no(wo_no, self.request.session['compid'], self.request.session['finyear']):
            #     self.add_error('WONo', 'Entered WO No is not valid!')
            cleaned_data['BGGroupId'] = BusinessGroup.objects.get(Id=1) # Default BGGroup ID 1 if WO No selected
        else: # BG Group selected
            if not bg_group_id:
                self.add_error('BGGroupId', 'Business Group is required.')
            cleaned_data['WONo'] = 'NA' # Set WONo to 'NA' if BG Group selected

        # --- Date and Time Parsing and Validation ---
        start_date = cleaned_data.get('tour_start_date_input')
        start_time = cleaned_data.get('tour_start_time_input')
        end_date = cleaned_data.get('tour_end_date_input')
        end_time = cleaned_data.get('tour_end_time_input')

        if start_date and start_time:
            cleaned_data['TourStartDate'] = start_date
            cleaned_data['TourStartTime'] = start_time.strftime("%I:%M:%S:%p")
        else:
            if not start_date: self.add_error('tour_start_date_input', 'Tour Start Date is required.')
            if not start_time: self.add_error('tour_start_time_input', 'Tour Start Time is required.')

        if end_date and end_time:
            cleaned_data['TourEndDate'] = end_date
            cleaned_data['TourEndTime'] = end_time.strftime("%I:%M:%S:%p")
        else:
            if not end_date: self.add_error('tour_end_date_input', 'Tour End Date is required.')
            if not end_time: self.add_error('tour_end_time_input', 'Tour End Time is required.')

        if start_date and end_date and start_date > end_date:
            self.add_error('tour_end_date_input', 'Tour End Date cannot be before Tour Start Date.')
        
        # NoOfDays validation, ASP.NET uses `fun.NumberValidationQty`
        no_of_days = cleaned_data.get('NoOfDays')
        if no_of_days is not None and no_of_days < 0:
            self.add_error('NoOfDays', 'Number of Days cannot be negative.')

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Manually assign EmpId which was resolved in clean()
        instance.EmpId = self.cleaned_data['EmpId']
        # The WONo/BGGroupId logic is handled in clean() which modifies cleaned_data['WONo'] and cleaned_data['BGGroupId']
        instance.WONo = self.cleaned_data['WONo']
        instance.BGGroupId = self.cleaned_data['BGGroupId']
        instance.TourStartDate = self.cleaned_data['tour_start_date_input']
        instance.TourStartTime = self.cleaned_data['tour_start_time_input']
        instance.TourEndDate = self.cleaned_data['tour_end_date_input']
        instance.TourEndTime = self.cleaned_data['tour_end_time_input']

        if commit:
            instance.save()
        return instance


class TourAdvanceDetailForm(forms.ModelForm):
    class Meta:
        model = TourAdvanceDetail
        fields = ['Id', 'ExpencessId', 'Amount', 'Remarks', 'MId']
        widgets = {
            'Id': forms.HiddenInput(), # PK for existing records
            'ExpencessId': forms.HiddenInput(), # FK to expense type
            'MId': forms.HiddenInput(), # FK to main tour intimation
            'Amount': forms.NumberInput(attrs={'class': 'box3 w-full'}),
            'Remarks': forms.TextInput(attrs={'class': 'box3 w-full'}),
        }
        labels = {
            'Amount': '', # Label is in grid header
            'Remarks': '', # Label is in grid header
        }

    def clean_Amount(self):
        amount = self.cleaned_data.get('Amount')
        if amount is not None and amount < 0:
            raise forms.ValidationError("Amount cannot be negative.")
        return amount

# Formset for TourAdvanceDetail (GridView2 equivalent)
# Extra=0 because these are pre-defined expense types, we're just updating amounts/remarks.
# Can be `extra=0, can_delete=False` for simplicity, assuming they are always there.
TourAdvanceDetailFormSet = inlineformset_factory(
    TourIntimation, 
    TourAdvanceDetail, 
    form=TourAdvanceDetailForm, 
    extra=0, 
    can_delete=False,
    fields=['Id', 'ExpencessId', 'Amount', 'Remarks', 'MId']
)


class TourAdvanceForm(forms.ModelForm):
    employee_full_name = forms.CharField(
        max_length=255, 
        required=True, 
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/tour-intimation/employee-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#advance-employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'placeholder': 'Start typing employee name...'
        })
    )
    employee_suggestions = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={'id': 'advance-employee-suggestions'})
    )

    class Meta:
        model = TourAdvance
        fields = ['Id', 'MId', 'employee_full_name', 'Amount', 'Remarks']
        widgets = {
            'Id': forms.HiddenInput(),
            'MId': forms.HiddenInput(),
            'Amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'Remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
        }
        labels = {
            'employee_full_name': 'Employee Name',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['employee_full_name'].initial = self.instance.EmpId.__str__() # Pre-populate with full employee name

    def clean(self):
        cleaned_data = super().clean()
        employee_full_name = cleaned_data.get('employee_full_name')
        
        emp_id_str = Employee.get_emp_id_from_full_name(employee_full_name)
        
        if not emp_id_str:
            self.add_error('employee_full_name', 'Invalid Employee Name. Please select from suggestions.')
            return cleaned_data

        try:
            employee_obj = Employee.objects.get(EmpId=emp_id_str)
            cleaned_data['EmpId'] = employee_obj
        except Employee.DoesNotExist:
            self.add_error('employee_full_name', 'Employee not found. Please select a valid employee.')

        amount = cleaned_data.get('Amount')
        if amount is not None and amount <= 0:
            self.add_error('Amount', 'Amount must be positive.')

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.EmpId = self.cleaned_data['EmpId']
        if commit:
            instance.save()
        return instance

```

### 4.3 Views (`tour_intimation/views.py`)

**Task:** Implement CRUD and helper operations using CBVs.

**Instructions:**
- Define `UpdateView` for the main form.
- Implement helper views for autocomplete, cascading dropdowns, and dynamic updates for WO No/BG Group toggle.
- For `TourAdvance` (GridView1 equivalent), create `ListView` (for partial table), `CreateView`, `UpdateView`, `DeleteView` as HTMX endpoints.
- Keep views thin (5-15 lines) and move business logic to models/forms.
- Use `django.contrib.messages` for feedback.
- Ensure proper HTMX headers (`HX-Trigger`) for client-side updates.

```python
from django.views.generic import FormView, UpdateView, ListView, CreateView, DeleteView
from django.forms import formset_factory
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction

from .models import (
    TourIntimation, Employee, BusinessGroup, Country, State, City,
    TourExpenseType, TourAdvanceDetail, TourAdvance
)
from .forms import (
    TourIntimationForm, TourAdvanceDetailForm, TourAdvanceDetailFormSet, TourAdvanceForm
)

# Main Tour Intimation Edit View
class TourIntimationEditView(UpdateView):
    model = TourIntimation
    form_class = TourIntimationForm
    template_name = 'tour_intimation/tour_intimation_detail.html'
    success_url = reverse_lazy('tour_intimation_list') # Assuming a list view for all tour intimations

    def get_object(self, queryset=None):
        # The ASP.NET code gets 'Id' from Request.QueryString["Id"]
        tour_intimation_id = self.kwargs.get('pk')
        return get_object_or_404(self.model, Id=tour_intimation_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tour_intimation = self.get_object()
        
        # Populate TourAdvanceDetailFormSet (GridView2 equivalent)
        if self.request.POST and 'tour_expense_details_submit' in self.request.POST:
            context['expense_formset'] = TourAdvanceDetailFormSet(self.request.POST, instance=tour_intimation)
        else:
            # Prepare initial data for expense formset.
            # We need to fetch all expense types and their corresponding amounts/remarks for this intimation.
            expense_types = TourExpenseType.objects.all().order_by('Id')
            initial_data = []
            for etype in expense_types:
                detail = TourAdvanceDetail.objects.filter(MId=tour_intimation.Id, ExpencessId=etype.Id).first()
                initial_data.append({
                    'Id': detail.Id if detail else '', # If detail exists, use its ID for update, else blank for new
                    'ExpencessId': etype.Id,
                    'MId': tour_intimation.Id,
                    'Amount': detail.Amount if detail else None,
                    'Remarks': detail.Remarks if detail else '',
                })
            context['expense_formset'] = TourAdvanceDetailFormSet(instance=tour_intimation, initial=initial_data)

        # Pass WO No / BG Group selection to template for initial rendering logic
        context['wo_no_group_selection_initial'] = context['form']['wo_no_group_selection'].initial

        return context

    def form_valid(self, form):
        tour_intimation = form.instance
        expense_formset = TourAdvanceDetailFormSet(self.request.POST, instance=tour_intimation)

        if not expense_formset.is_valid():
            messages.error(self.request, 'Please correct the errors in the Advance Details tab.')
            return self.form_invalid(form) # Rerender form with errors for expense formset

        with transaction.atomic():
            # Update main TourIntimation record
            form.save() 
            
            # Update/Insert TourAdvanceDetail records from formset
            for expense_form in expense_formset:
                if expense_form.has_changed():
                    expense_detail_id = expense_form.cleaned_data.get('Id')
                    expense_type_id = expense_form.cleaned_data.get('ExpencessId').Id
                    amount = expense_form.cleaned_data.get('Amount')
                    remarks = expense_form.cleaned_data.get('Remarks')

                    # If an existing record ID is present, update it. Otherwise, create a new one.
                    if expense_detail_id:
                        # Existing record, update its values
                        TourAdvanceDetail.objects.filter(Id=expense_detail_id, MId=tour_intimation.Id, ExpencessId=expense_type_id).update(
                            Amount=amount, Remarks=remarks
                        )
                    else:
                        # New record for this expense type and tour intimation
                        # Ensure we're not creating duplicate if it already exists from a previous save
                        TourAdvanceDetail.objects.update_or_create(
                            MId=tour_intimation,
                            ExpencessId=TourExpenseType.objects.get(Id=expense_type_id),
                            defaults={'Amount': amount, 'Remarks': remarks}
                        )
            
            messages.success(self.request, 'Tour Intimation updated successfully.')

        # HTMX response for full page reload on success
        return HttpResponse(status=204, headers={'HX-Trigger': 'tourIntimationUpdated'})


# HTMX Partial View for Advance Transferred To (GridView1 equivalent)
class TourAdvanceTablePartialView(ListView):
    model = TourAdvance
    template_name = 'tour_intimation/_tour_advance_table.html'
    context_object_name = 'tour_advances'

    def get_queryset(self):
        tour_intimation_id = self.kwargs.get('pk')
        return self.model.objects.filter(MId=tour_intimation_id).order_by('-Id')

    def render_to_response(self, context, **response_kwargs):
        # Ensure that only the partial template is rendered for HTMX requests
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, context)
        return super().render_to_response(context, **response_kwargs)

# CRUD for TourAdvance (Add/Edit/Delete from GridView1)
class TourAdvanceCreateView(CreateView):
    model = TourAdvance
    form_class = TourAdvanceForm
    template_name = 'tour_intimation/_tour_advance_form.html' # Rendered in a modal
    
    def get_initial(self):
        initial = super().get_initial()
        initial['MId'] = self.kwargs.get('tour_intimation_pk') # Get MId from URL kwargs
        return initial

    def form_valid(self, form):
        # Associate with the parent TourIntimation
        tour_intimation = get_object_or_404(TourIntimation, Id=self.kwargs.get('tour_intimation_pk'))
        form.instance.MId = tour_intimation
        
        form.save()
        messages.success(self.request, 'Tour Advance added successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshTourAdvanceList' # Trigger refresh on parent page
            }
        )

    def form_invalid(self, form):
        # Render the form back for HTMX with errors
        return render(self.request, self.template_name, {'form': form}, status=400)


class TourAdvanceUpdateView(UpdateView):
    model = TourAdvance
    form_class = TourAdvanceForm
    template_name = 'tour_intimation/_tour_advance_form.html' # Rendered in a modal

    def get_object(self, queryset=None):
        # Retrieve the specific TourAdvance instance
        advance_id = self.kwargs.get('pk')
        tour_intimation_id = self.kwargs.get('tour_intimation_pk')
        return get_object_or_404(self.model, Id=advance_id, MId=tour_intimation_id)

    def form_valid(self, form):
        form.save()
        messages.success(self.request, 'Tour Advance updated successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshTourAdvanceList'
            }
        )
    
    def form_invalid(self, form):
        return render(self.request, self.template_name, {'form': form}, status=400)


class TourAdvanceDeleteView(DeleteView):
    model = TourAdvance
    template_name = 'tour_intimation/_tour_advance_confirm_delete.html' # Rendered in a modal

    def get_object(self, queryset=None):
        advance_id = self.kwargs.get('pk')
        tour_intimation_id = self.kwargs.get('tour_intimation_pk')
        return get_object_or_404(self.model, Id=advance_id, MId=tour_intimation_id)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.object.delete()
        messages.success(self.request, 'Tour Advance deleted successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshTourAdvanceList'
            }
        )
    
    def get(self, request, *args, **kwargs):
        # Just render the confirmation template for GET requests (for modal)
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return render(request, self.template_name, context)

# --- HTMX Helper Views ---

class EmployeeAutocompleteView(ListView):
    model = Employee
    context_object_name = 'employees'

    def get_queryset(self):
        query = self.request.GET.get('q', '')
        if query:
            return self.model.objects.filter(EmployeeName__icontains=query).order_by('EmployeeName')[:10]
        return self.model.objects.none()

    def render_to_response(self, context, **response_kwargs):
        # This will be rendered into a partial for HTMX
        return render(self.request, 'tour_intimation/_employee_autocomplete_results.html', context)

class GetStatesByCountryView(FormView):
    # This view will receive country ID and return states dropdown HTML
    def post(self, request, *args, **kwargs):
        country_id = request.POST.get('PlaceOfTourCountry')
        states = State.objects.none()
        if country_id:
            states = State.objects.filter(CId=country_id).order_by('StateName')
        return render(request, 'tour_intimation/_state_dropdown_options.html', {'states': states, 'selected_state_id': None})

class GetCitiesByStateView(FormView):
    # This view will receive state ID and return cities dropdown HTML
    def post(self, request, *args, **kwargs):
        state_id = request.POST.get('PlaceOfTourState')
        cities = City.objects.none()
        if state_id:
            cities = City.objects.filter(SId=state_id).order_by('CityName')
        return render(request, 'tour_intimation/_city_dropdown_options.html', {'cities': cities, 'selected_city_id': None})

class ToggleWoGroupView(FormView):
    # This view handles the radio button toggle for WO No / BG Group
    def post(self, request, *args, **kwargs):
        selection = request.POST.get('wo_no_group_selection')
        context = {
            'selection': selection,
            'business_groups': BusinessGroup.objects.all(),
            'wo_no_value': request.POST.get('WONo', ''), # Preserve current value
            'bg_group_selected_value': request.POST.get('BGGroupId', ''), # Preserve current value
        }
        return render(request, 'tour_intimation/_wo_group_toggle.html', context)

```

### 4.4 Templates (`tour_intimation/`)

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration.

**Instructions:**
- Extend `core/base.html` for main pages.
- Use partial templates for HTMX-loaded content (forms, tables, dropdowns).
- Implement DataTables for lists.
- Use HTMX attributes for dynamic interactions.
- Utilize Alpine.js for UI state (e.g., modal visibility, tab switching).

```html
{# tour_intimation/tour_intimation_detail.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    activeTab: 'main',
    isModalOpen: false,
    modalContent: ''
}">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Tour Intimation Edit - TI No: {{ object.TINo }}</h2>
    </div>

    <form method="post" class="space-y-6" hx-post="{{ request.path }}" hx-target="body" hx-swap="none" hx-indicator="#loading-spinner">
        {% csrf_token %}
        <input type="hidden" name="tour_intimation_pk" value="{{ object.Id }}"> {# Pass tour intimation PK for formset #}

        <!-- Main Form Details -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">General Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                <div>
                    <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.employee_full_name.label }}
                    </label>
                    {{ form.employee_full_name }}
                    <div id="employee-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 relative"></div>
                    {% if form.employee_full_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>{% endif %}
                </div>
                
                <div id="wo-group-container">
                    <label class="block text-sm font-medium text-gray-700">
                        WO No / BG Group
                    </label>
                    <div class="flex items-center space-x-4 mb-2">
                        {% for radio in form.wo_no_group_selection %}
                            <label class="inline-flex items-center">
                                {{ radio.tag }}
                                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if wo_no_group_selection_initial == '0' %}
                        {{ form.WONo }}
                        {% if form.WONo.errors %}<p class="text-red-500 text-xs mt-1">{{ form.WONo.errors }}</p>{% endif %}
                    {% else %}
                        {{ form.BGGroupId }}
                        {% if form.BGGroupId.errors %}<p class="text-red-500 text-xs mt-1">{{ form.BGGroupId.errors }}</p>{% endif %}
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.ProjectName.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.ProjectName.label }}
                    </label>
                    {{ form.ProjectName }}
                    {% if form.ProjectName.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ProjectName.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.PlaceOfTourCountry.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Place of Tour
                    </label>
                    {{ form.PlaceOfTourCountry }}
                    {% if form.PlaceOfTourCountry.errors %}<p class="text-red-500 text-xs mt-1">{{ form.PlaceOfTourCountry.errors }}</p>{% endif %}
                    
                    {{ form.PlaceOfTourState }}
                    {% if form.PlaceOfTourState.errors %}<p class="text-red-500 text-xs mt-1">{{ form.PlaceOfTourState.errors }}</p>{% endif %}

                    {{ form.PlaceOfTourCity }}
                    {% if form.PlaceOfTourCity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.PlaceOfTourCity.errors }}</p>{% endif %}
                </div>
                
                <div class="flex space-x-4 items-center">
                    <div>
                        <label for="{{ form.tour_start_date_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_start_date_input.label }}
                        </label>
                        {{ form.tour_start_date_input }}
                        {% if form.tour_start_date_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_start_date_input.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.tour_start_time_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_start_time_input.label }}
                        </label>
                        {{ form.tour_start_time_input }}
                        {% if form.tour_start_time_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_start_time_input.errors }}</p>{% endif %}
                    </div>
                </div>

                <div class="flex space-x-4 items-center">
                    <div>
                        <label for="{{ form.tour_end_date_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_end_date_input.label }}
                        </label>
                        {{ form.tour_end_date_input }}
                        {% if form.tour_end_date_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_end_date_input.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.tour_end_time_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.tour_end_time_input.label }}
                        </label>
                        {{ form.tour_end_time_input }}
                        {% if form.tour_end_time_input.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tour_end_time_input.errors }}</p>{% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.NoOfDays.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.NoOfDays.label }}
                    </label>
                    {{ form.NoOfDays }}
                    {% if form.NoOfDays.errors %}<p class="text-red-500 text-xs mt-1">{{ form.NoOfDays.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.NameAddressSerProvider.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.NameAddressSerProvider.label }}
                    </label>
                    {{ form.NameAddressSerProvider }}
                    {% if form.NameAddressSerProvider.errors %}<p class="text-red-500 text-xs mt-1">{{ form.NameAddressSerProvider.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.ContactPerson.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.ContactPerson.label }}
                    </label>
                    {{ form.ContactPerson }}
                    {% if form.ContactPerson.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ContactPerson.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.ContactNo.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.ContactNo.label }}
                    </label>
                    {{ form.ContactNo }}
                    {% if form.ContactNo.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ContactNo.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.Email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.Email.label }}
                    </label>
                    {{ form.Email }}
                    {% if form.Email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.Email.errors }}</p>{% endif %}
                </div>
            </div>
        </div>

        <!-- Tabs Section -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button" @click="activeTab = 'advance_details'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'advance_details', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advance_details'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Details
                    </button>
                    <button type="button" @click="activeTab = 'advance_trans_to'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'advance_trans_to', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advance_trans_to'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Advance Trans. To
                    </button>
                </nav>
            </div>

            <div x-show="activeTab === 'advance_details'" class="pt-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Advance Details</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{ expense_formset.management_form }}
                            {% for formset_form in expense_formset %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ forloop.counter }}
                                    {{ formset_form.Id }}
                                    {{ formset_form.ExpencessId }}
                                    {{ formset_form.MId }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formset_form.instance.ExpencessId.Terms }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formset_form.Amount }}
                                    {% if formset_form.Amount.errors %}<p class="text-red-500 text-xs mt-1">{{ formset_form.Amount.errors }}</p>{% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ formset_form.Remarks }}
                                    {% if formset_form.Remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ formset_form.Remarks.errors }}</p>{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% if expense_formset.non_field_errors %}<p class="text-red-500 text-xs mt-2">{{ expense_formset.non_field_errors }}</p>{% endif %}
                </div>
            </div>

            <div x-show="activeTab === 'advance_trans_to'" class="pt-4">
                <h4 class="text-md font-semibold text-gray-800 mb-3">Advance Trans. To</h4>
                <div class="flex justify-end mb-4">
                    <button
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        hx-get="{% url 'tour_intimation_advance_add' pk=object.Id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        @click="isModalOpen = true"
                    >
                        Add New Advance
                    </button>
                </div>
                <div id="tourAdvanceTable-container"
                     hx-trigger="load, refreshTourAdvanceList from:body"
                     hx-get="{% url 'tour_intimation_advance_table' pk=object.Id %}"
                     hx-swap="innerHTML">
                    <!-- DataTables for Tour Advances will be loaded here via HTMX -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit and Cancel Buttons -->
        <div class="mt-6 flex items-center justify-center space-x-4">
            <button 
                type="submit" 
                name="tour_expense_details_submit" {# Distinguish main form submission from others #}
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Update Tour Intimation
            </button>
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'tour_intimation_list' %}'"> {# Redirect to list view #}
                Cancel
            </button>
        </div>
    </form>

    <!-- Modal for Add/Edit/Delete Tour Advance -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center"
         x-show="isModalOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
         @click.self="isModalOpen = false"> {# Close modal when clicking outside content #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.away="isModalOpen = false" # This closes modal if you click outside of the modalContent
             _="on htmx:afterOnLoad remove .is-active from #modal"> {# Also remove active class if HTMX loads content into it #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            // If the response is 204 (No Content) and it came from a modal, close the modal.
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Handle formset errors if the main form submission fails
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        if (evt.detail.xhr.status === 400 && evt.detail.requestHeaders['HX-Target'] === 'body') {
            // If the main form POST returns 400 (validation error), we need to handle it.
            // By default, HTMX will swap the whole body. We prevent this and just display errors.
            // This assumes the server renders the full form back with errors.
            console.error("Form submission failed with validation errors.");
            // The backend form_invalid renders the full page including context,
            // so we don't need to do a custom swap here.
            // The main thing is that the 'tourIntimationUpdated' trigger won't fire.
        }
    });
</script>
{% endblock %}
```

```html
{# tour_intimation/_employee_autocomplete_results.html #}
{% if employees %}
<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
    {% for employee in employees %}
    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
        hx-on:click="document.getElementById('id_employee_full_name').value = '{{ employee.__str__ }}'; this.closest('ul').remove();">
        {{ employee.__str__ }}
    </li>
    {% endfor %}
</ul>
{% endif %}
```

```html
{# tour_intimation/_state_dropdown_options.html #}
<select name="PlaceOfTourState" id="id_PlaceOfTourState" 
        class="box3"
        hx-post="{% url 'tour_intimation_get_cities' %}" 
        hx-target="#id_PlaceOfTourCity" 
        hx-swap="outerHTML">
    <option value="">Select State</option>
    {% for state in states %}
        <option value="{{ state.SId }}" {% if state.SId == selected_state_id %}selected{% endif %}>{{ state.StateName }}</option>
    {% endfor %}
</select>
```

```html
{# tour_intimation/_city_dropdown_options.html #}
<select name="PlaceOfTourCity" id="id_PlaceOfTourCity" class="box3">
    <option value="">Select City</option>
    {% for city in cities %}
        <option value="{{ city.CityId }}" {% if city.CityId == selected_city_id %}selected{% endif %}>{{ city.CityName }}</option>
    {% endfor %}
</select>
```

```html
{# tour_intimation/_wo_group_toggle.html #}
<div id="wo-group-container">
    <label class="block text-sm font-medium text-gray-700">
        WO No / BG Group
    </label>
    <div class="flex items-center space-x-4 mb-2">
        {% for radio in form.wo_no_group_selection %}
            <label class="inline-flex items-center">
                {{ radio.tag }}
                <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
            </label>
        {% endfor %}
    </div>
    {% if selection == '0' %} {# WO No selected #}
        <input type="text" name="WONo" id="id_WONo" value="{{ wo_no_value }}" class="box3">
        <select name="BGGroupId" id="id_BGGroupId" class="box3 hidden" disabled>
            {# Hidden/disabled because not active #}
            {% for group in business_groups %}
                <option value="{{ group.Id }}" {% if group.Id == bg_group_selected_value %}selected{% endif %}>{{ group.Symbol }}</option>
            {% endfor %}
        </select>
    {% else %} {# BG Group selected #}
        <input type="text" name="WONo" id="id_WONo" value="{{ wo_no_value }}" class="box3 hidden" disabled>
        <select name="BGGroupId" id="id_BGGroupId" class="box3">
            {% for group in business_groups %}
                <option value="{{ group.Id }}" {% if group.Id == bg_group_selected_value %}selected{% endif %}>{{ group.Symbol }}</option>
            {% endfor %}
        </select>
    {% endif %}
</div>
```

```html
{# tour_intimation/_tour_advance_table.html #}
{# This template will be loaded by HTMX into the 'tourAdvanceTable-container' div #}
<table id="tourAdvanceTable" class="min-w-full bg-white divide-y divide-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in tour_advances %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.EmpId }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.Amount }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.Remarks }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'tour_intimation_advance_edit' tour_intimation_pk=obj.MId.Id pk=obj.Id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'tour_intimation_advance_delete' tour_intimation_pk=obj.MId.Id pk=obj.Id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists before re-initializing
        if ($.fn.DataTable.isDataTable('#tourAdvanceTable')) {
            $('#tourAdvanceTable').DataTable().destroy();
        }
        $('#tourAdvanceTable').DataTable({
            "pageLength": 9, /* As per ASP.NET GridView1.PageSize */
            "lengthMenu": [[9, 25, 50, -1], [9, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true
        });
    });
</script>
```

```html
{# tour_intimation/_tour_advance_form.html #}
{# This template is for the Add/Edit modal for Tour Advances #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Tour Advance</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        {{ form.Id }} {# Hidden ID for existing records #}
        {{ form.MId }} {# Hidden MId to link to parent TourIntimation #}

        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_full_name.label }}
                </label>
                {{ form.employee_full_name }}
                <div id="advance-employee-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 relative"></div>
                {% if form.employee_full_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.Amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.Amount.label }}
                </label>
                {{ form.Amount }}
                {% if form.Amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.Amount.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.Remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.Remarks.label }}
                </label>
                {{ form.Remarks }}
                {% if form.Remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.Remarks.errors }}</p>{% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mb-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# tour_intimation/_tour_advance_confirm_delete.html #}
{# This template is for the Delete confirmation modal for Tour Advances #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700">Are you sure you want to delete the Tour Advance for "{{ object.EmpId }}" with amount {{ object.Amount }}?</p>
    
    <form hx-delete="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`tour_intimation/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
- Create paths for the main edit view, and all HTMX partials.
- Use appropriate naming patterns and consistent URL structure.

```python
from django.urls import path
from .views import (
    TourIntimationEditView, 
    EmployeeAutocompleteView, 
    GetStatesByCountryView, 
    GetCitiesByStateView,
    ToggleWoGroupView,
    TourAdvanceTablePartialView,
    TourAdvanceCreateView,
    TourAdvanceUpdateView,
    TourAdvanceDeleteView,
)

urlpatterns = [
    # Main Tour Intimation Edit Page
    path('<int:pk>/edit/', TourIntimationEditView.as_view(), name='tour_intimation_edit'),

    # HTMX Endpoints for main form
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('get-states/', GetStatesByCountryView.as_view(), name='tour_intimation_get_states'),
    path('get-cities/', GetCitiesByStateView.as_view(), name='tour_intimation_get_cities'),
    path('toggle-wo-group/', ToggleWoGroupView.as_view(), name='tour_intimation_toggle_wo_group'),

    # HTMX Endpoints for Tour Advances (GridView1 equivalent)
    path('<int:pk>/advances/table/', TourAdvanceTablePartialView.as_view(), name='tour_intimation_advance_table'),
    path('<int:tour_intimation_pk>/advances/add/', TourAdvanceCreateView.as_view(), name='tour_intimation_advance_add'),
    path('<int:tour_intimation_pk>/advances/edit/<int:pk>/', TourAdvanceUpdateView.as_view(), name='tour_intimation_advance_edit'),
    path('<int:tour_intimation_pk>/advances/delete/<int:pk>/', TourAdvanceDeleteView.as_view(), name='tour_intimation_advance_delete'),
    
    # Placeholder for a list view if needed for redirection
    path('', TourIntimationEditView.as_view(), name='tour_intimation_list'), # Redirect to this if no id
]
```

### 4.6 Tests (`tour_intimation/tests.py`)

**Task:** Write comprehensive tests for models and views.

**Instructions:**
- Include unit tests for model methods and properties.
- Add integration tests for all views (main edit, autocomplete, cascading dropdowns, advance CRUD).
- Ensure high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import Max
from datetime import date, time
from .models import (
    Company, FinancialYear, Employee, BusinessGroup, Country, State, City,
    TourIntimation, TourExpenseType, TourAdvanceDetail, TourAdvance
)

class TourIntimationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.company = Company.objects.create(CompId=1, CompanyName='Test Co')
        cls.fin_year = FinancialYear.objects.create(FinYearId=1, FinYear='2023-2024')
        cls.employee = Employee.objects.create(EmpId='EMP001', Title='Mr', EmployeeName='John Doe')
        cls.bg_group_default = BusinessGroup.objects.create(Id=1, Symbol='Default BG')
        cls.bg_group_other = BusinessGroup.objects.create(Id=2, Symbol='Other BG')
        cls.country = Country.objects.create(CId=1, CountryName='India')
        cls.state = State.objects.create(SId=1, CId=cls.country, StateName='Karnataka')
        cls.city = City.objects.create(CityId=1, SId=cls.state, CityName='Bengaluru')
        
        # Max ID for new instances in existing DB setup
        max_id = TourIntimation.objects.all().aggregate(Max('Id'))['Id__max']
        cls.next_tour_intimation_id = (max_id or 0) + 1

        cls.tour_intimation = TourIntimation.objects.create(
            Id=cls.next_tour_intimation_id,
            TINo='TI001',
            EmpId=cls.employee,
            WONo='WO123',
            BGGroupId=cls.bg_group_default,
            ProjectName='Project Alpha',
            TourStartDate=date(2023, 10, 1),
            TourStartTime='09:00:00:AM',
            TourEndDate=date(2023, 10, 5),
            TourEndTime='05:00:00:PM',
            NoOfDays=5,
            NameAddressSerProvider='Hotel ABC, City',
            ContactPerson='Jane Doe',
            ContactNo='**********',
            Email='<EMAIL>',
            PlaceOfTourCountry=cls.country,
            PlaceOfTourState=cls.state,
            PlaceOfTourCity=cls.city,
            CompId=cls.company,
            FinYearId=cls.fin_year,
            SessionId='test_session_id',
            SysDate=date(2023, 10, 1), # Manually set for test setup, will be overwritten on save
            SysTime='09:00:00:AM' # Manually set for test setup, will be overwritten on save
        )
        
        cls.expense_type1 = TourExpenseType.objects.create(Id=101, Terms='Food')
        cls.expense_type2 = TourExpenseType.objects.create(Id=102, Terms='Travel')
        TourAdvanceDetail.objects.create(
            Id=1, MId=cls.tour_intimation, ExpencessId=cls.expense_type1, Amount=100.00, Remarks='Lunch'
        )
        TourAdvance.objects.create(
            Id=1, MId=cls.tour_intimation, EmpId=cls.employee, Amount=500.00, Remarks='Advance for travel'
        )

    def test_tour_intimation_creation(self):
        obj = TourIntimation.objects.get(Id=self.next_tour_intimation_id)
        self.assertEqual(obj.TINo, 'TI001')
        self.assertEqual(obj.EmpId, self.employee)
        self.assertEqual(obj.ProjectName, 'Project Alpha')
        self.assertEqual(obj.NoOfDays, 5)

    def test_employee_full_name_property(self):
        obj = TourIntimation.objects.get(Id=self.next_tour_intimation_id)
        self.assertEqual(obj.full_employee_name, 'Mr.John Doe [EMP001]')

    def test_tour_start_datetime_property(self):
        obj = TourIntimation.objects.get(Id=self.next_tour_intimation_id)
        expected_dt = datetime.combine(date(2023, 10, 1), time(9, 0, 0))
        self.assertEqual(obj.tour_start_datetime, expected_dt)

    def test_tour_end_datetime_property(self):
        obj = TourIntimation.objects.get(Id=self.next_tour_intimation_id)
        expected_dt = datetime.combine(date(2023, 10, 5), time(17, 0, 0))
        self.assertEqual(obj.tour_end_datetime, expected_dt)

    def test_tour_advance_detail_creation(self):
        obj = TourAdvanceDetail.objects.get(Id=1)
        self.assertEqual(obj.MId, self.tour_intimation)
        self.assertEqual(obj.ExpencessId, self.expense_type1)
        self.assertEqual(obj.Amount, 100.00)

    def test_tour_advance_creation(self):
        obj = TourAdvance.objects.get(Id=1)
        self.assertEqual(obj.MId, self.tour_intimation)
        self.assertEqual(obj.EmpId, self.employee)
        self.assertEqual(obj.Amount, 500.00)

    def test_employee_get_emp_id_from_full_name(self):
        self.assertEqual(Employee.get_emp_id_from_full_name('Mr.John Doe [EMP001]'), 'EMP001')
        self.assertIsNone(Employee.get_emp_id_from_full_name('John Doe'))
        self.assertIsNone(Employee.get_emp_id_from_full_name('Mr.John Doe [EMP001'))


class TourIntimationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(CompId=1, CompanyName='Test Co')
        cls.fin_year = FinancialYear.objects.create(FinYearId=1, FinYear='2023-2024')
        cls.employee = Employee.objects.create(EmpId='EMP001', Title='Mr', EmployeeName='John Doe')
        cls.employee_other = Employee.objects.create(EmpId='EMP002', Title='Ms', EmployeeName='Jane Smith')
        cls.bg_group_default = BusinessGroup.objects.create(Id=1, Symbol='Default BG')
        cls.bg_group_other = BusinessGroup.objects.create(Id=2, Symbol='Other BG')
        cls.country = Country.objects.create(CId=1, CountryName='India')
        cls.state = State.objects.create(SId=1, CId=cls.country, StateName='Karnataka')
        cls.city = City.objects.create(CityId=1, SId=cls.state, CityName='Bengaluru')

        max_id = TourIntimation.objects.all().aggregate(Max('Id'))['Id__max']
        cls.next_tour_intimation_id = (max_id or 0) + 1

        cls.tour_intimation = TourIntimation.objects.create(
            Id=cls.next_tour_intimation_id,
            TINo='TI001',
            EmpId=cls.employee,
            WONo='WO123',
            BGGroupId=cls.bg_group_default,
            ProjectName='Project Alpha',
            TourStartDate=date(2023, 10, 1),
            TourStartTime='09:00:00:AM',
            TourEndDate=date(2023, 10, 5),
            TourEndTime='05:00:00:PM',
            NoOfDays=5,
            NameAddressSerProvider='Hotel ABC, City',
            ContactPerson='Jane Doe',
            ContactNo='**********',
            Email='<EMAIL>',
            PlaceOfTourCountry=cls.country,
            PlaceOfTourState=cls.state,
            PlaceOfTourCity=cls.city,
            CompId=cls.company,
            FinYearId=cls.fin_year
        )
        cls.expense_type1 = TourExpenseType.objects.create(Id=101, Terms='Food')
        cls.expense_type2 = TourExpenseType.objects.create(Id=102, Terms='Travel')
        cls.expense_detail1 = TourAdvanceDetail.objects.create(
            Id=1, MId=cls.tour_intimation, ExpencessId=cls.expense_type1, Amount=100.00, Remarks='Lunch'
        )
        cls.tour_advance1 = TourAdvance.objects.create(
            Id=1, MId=cls.tour_intimation, EmpId=cls.employee, Amount=500.00, Remarks='Initial advance'
        )

    def setUp(self):
        self.client = Client()

    def test_tour_intimation_edit_view_get(self):
        response = self.client.get(reverse('tour_intimation_edit', args=[self.tour_intimation.Id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_intimation/tour_intimation_detail.html')
        self.assertContains(response, 'TI001')
        self.assertContains(response, 'Project Alpha')
        # Check form initial values
        self.assertContains(response, f'value="{self.employee.__str__()}"') # Employee name
        self.assertContains(response, 'WO No') # Radio button

    def test_tour_intimation_edit_view_post_success(self):
        data = {
            'TINo': self.tour_intimation.TINo,
            'employee_full_name': self.employee.EmpId.__str__(), # Should be full name for autocomplete
            'wo_no_group_selection': '0',
            'WONo': 'WO_UPDATED',
            'BGGroupId': self.bg_group_default.Id,
            'ProjectName': 'Project Beta',
            'tour_start_date_input': '2023-11-01',
            'tour_start_time_input': '10:00',
            'tour_end_date_input': '2023-11-03',
            'tour_end_time_input': '16:00',
            'NoOfDays': 3,
            'NameAddressSerProvider': 'New Hotel',
            'ContactPerson': 'New Contact',
            'ContactNo': '**********',
            'Email': '<EMAIL>',
            'PlaceOfTourCountry': self.country.CId,
            'PlaceOfTourState': self.state.SId,
            'PlaceOfTourCity': self.city.CityId,
            # Formset data for TourAdvanceDetail
            'touradvancedetail_set-TOTAL_FORMS': '2', # Assuming 2 expense types
            'touradvancedetail_set-INITIAL_FORMS': '1', # One existing
            'touradvancedetail_set-MIN_NUM_FORMS': '0',
            'touradvancedetail_set-MAX_NUM_FORMS': '1000',
            'touradvancedetail_set-0-Id': self.expense_detail1.Id,
            'touradvancedetail_set-0-MId': self.tour_intimation.Id,
            'touradvancedetail_set-0-ExpencessId': self.expense_type1.Id,
            'touradvancedetail_set-0-Amount': '150.00',
            'touradvancedetail_set-0-Remarks': 'Updated lunch',
            'touradvancedetail_set-1-Id': '', # New entry for expense_type2
            'touradvancedetail_set-1-MId': self.tour_intimation.Id,
            'touradvancedetail_set-1-ExpencessId': self.expense_type2.Id,
            'touradvancedetail_set-1-Amount': '200.00',
            'touradvancedetail_set-1-Remarks': 'New travel expense',
            'tour_expense_details_submit': 'Update Tour Intimation', # Mimic submit button name
        }
        headers = {'HTTP_HX_REQUEST': 'true'} # Indicate HTMX request for 204 response

        response = self.client.post(reverse('tour_intimation_edit', args=[self.tour_intimation.Id]), data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX submission
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'tourIntimationUpdated')

        updated_intimation = TourIntimation.objects.get(Id=self.tour_intimation.Id)
        self.assertEqual(updated_intimation.ProjectName, 'Project Beta')
        self.assertEqual(updated_intimation.WONo, 'WO_UPDATED')
        self.assertEqual(updated_intimation.NoOfDays, 3)
        
        # Check TourAdvanceDetail updates
        updated_expense_detail1 = TourAdvanceDetail.objects.get(Id=self.expense_detail1.Id)
        self.assertEqual(updated_expense_detail1.Amount, 150.00)
        self.assertEqual(updated_expense_detail1.Remarks, 'Updated lunch')

        new_expense_detail2 = TourAdvanceDetail.objects.get(MId=self.tour_intimation, ExpencessId=self.expense_type2)
        self.assertEqual(new_expense_detail2.Amount, 200.00)
        self.assertEqual(new_expense_detail2.Remarks, 'New travel expense')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'John'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe')
        self.assertNotContains(response, 'Jane Smith') # Should not contain unless 'q' matches

    def test_get_states_by_country_view(self):
        response = self.client.post(reverse('tour_intimation_get_states'), {'PlaceOfTourCountry': self.country.CId}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select State</option>')
        self.assertContains(response, f'<option value="{self.state.SId}">{self.state.StateName}</option>')

    def test_get_cities_by_state_view(self):
        response = self.client.post(reverse('tour_intimation_get_cities'), {'PlaceOfTourState': self.state.SId}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select City</option>')
        self.assertContains(response, f'<option value="{self.city.CityId}">{self.city.CityName}</option>')

    def test_toggle_wo_group_view(self):
        response = self.client.post(reverse('tour_intimation_toggle_wo_group'), {'wo_no_group_selection': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="BGGroupId"')
        self.assertContains(response, 'class="box3 hidden" disabled') # Check WONo is hidden

        response = self.client.post(reverse('tour_intimation_toggle_wo_group'), {'wo_no_group_selection': '0'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="WONo"')
        self.assertContains(response, 'class="box3 hidden" disabled') # Check BGGroupId is hidden


    # --- Tour Advance CRUD tests ---
    def test_tour_advance_table_partial_view(self):
        response = self.client.get(reverse('tour_intimation_advance_table', args=[self.tour_intimation.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_intimation/_tour_advance_table.html')
        self.assertContains(response, self.tour_advance1.EmpId.EmployeeName)
        self.assertContains(response, str(self.tour_advance1.Amount))

    def test_tour_advance_create_view_get(self):
        response = self.client.get(reverse('tour_intimation_advance_add', args=[self.tour_intimation.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_intimation/_tour_advance_form.html')
        self.assertContains(response, 'Add Tour Advance')

    def test_tour_advance_create_view_post_success(self):
        data = {
            'employee_full_name': self.employee_other.__str__(),
            'Amount': 750.00,
            'Remarks': 'Advance for new trip',
        }
        response = self.client.post(reverse('tour_intimation_advance_add', args=[self.tour_intimation.Id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTourAdvanceList')
        self.assertTrue(TourAdvance.objects.filter(MId=self.tour_intimation, EmpId=self.employee_other, Amount=750.00).exists())

    def test_tour_advance_create_view_post_invalid(self):
        data = {
            'employee_full_name': 'Invalid Employee [XYZ]',
            'Amount': -100.00, # Invalid amount
            'Remarks': 'Bad advance',
        }
        response = self.client.post(reverse('tour_intimation_advance_add', args=[self.tour_intimation.Id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Form invalid
        self.assertContains(response, 'Invalid Employee Name. Please select from suggestions.')
        self.assertContains(response, 'Amount must be positive.')

    def test_tour_advance_update_view_get(self):
        response = self.client.get(reverse('tour_intimation_advance_edit', args=[self.tour_intimation.Id, self.tour_advance1.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_intimation/_tour_advance_form.html')
        self.assertContains(response, 'Edit Tour Advance')
        self.assertContains(response, f'value="{self.tour_advance1.Amount}"')

    def test_tour_advance_update_view_post_success(self):
        data = {
            'employee_full_name': self.employee.__str__(),
            'Amount': 600.00,
            'Remarks': 'Updated advance details',
        }
        response = self.client.post(reverse('tour_intimation_advance_edit', args=[self.tour_intimation.Id, self.tour_advance1.Id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTourAdvanceList')
        updated_advance = TourAdvance.objects.get(Id=self.tour_advance1.Id)
        self.assertEqual(updated_advance.Amount, 600.00)
        self.assertEqual(updated_advance.Remarks, 'Updated advance details')

    def test_tour_advance_delete_view_get(self):
        response = self.client.get(reverse('tour_intimation_advance_delete', args=[self.tour_intimation.Id, self.tour_advance1.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_intimation/_tour_advance_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')

    def test_tour_advance_delete_view_post_success(self):
        response = self.client.delete(reverse('tour_intimation_advance_delete', args=[self.tour_intimation.Id, self.tour_advance1.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTourAdvanceList')
        self.assertFalse(TourAdvance.objects.filter(Id=self.tour_advance1.Id).exists())

```

## Step 5: HTMX and Alpine.js Integration

The templates and views provided above already demonstrate the integration of HTMX and Alpine.js as per the guidelines:

*   **HTMX for Dynamic Updates:**
    *   **Employee Autocomplete:** `hx-get` on `employee_full_name` input to `{% url 'employee_autocomplete' %}`. Results loaded into `#employee-suggestions`.
    *   **Cascading Dropdowns:** `hx-post` on Country/State selects to `{% url 'tour_intimation_get_states' %}` and `{% url 'tour_intimation_get_cities' %}`. Target is the subsequent dropdown (`hx-target`).
    *   **WO No / BG Group Toggle:** `hx-post` on `wo_no_group_selection` radio buttons to `{% url 'tour_intimation_toggle_wo_group' %}`. Targets the `wo-group-container` to swap visibility of `WONo` and `BGGroupId` inputs.
    *   **Tour Advance Table (`GridView1` replacement):** The `tourAdvanceTable-container` div uses `hx-trigger="load, refreshTourAdvanceList from:body"` and `hx-get="{% url 'tour_intimation_advance_table' pk=object.Id %}"`. This ensures the table loads initially and refreshes after any CRUD operation on Tour Advances triggered by `refreshTourAdvanceList` HTMX trigger.
    *   **Tour Advance CRUD (Modals):** Add/Edit/Delete buttons use `hx-get` to load forms/confirmations into the `#modalContent` div. Form submissions (POST/DELETE) within the modal use `hx-swap="none"` and send `HX-Trigger` to `refreshTourAdvanceList`, then `status=204` to close the modal.
*   **Alpine.js for UI State:**
    *   **Tabs:** `x-data="{activeTab: 'main'}"` and `@click="activeTab = 'tab_name'"` control tab visibility.
    *   **Modals:** `x-data="{isModalOpen: false}"` and `@click="isModalOpen = true"`/`@click.self="isModalOpen = false"` for modal visibility and closing behavior.
*   **DataTables:** The `_tour_advance_table.html` partial includes a JavaScript snippet that initializes DataTables on the loaded table, ensuring client-side searching, sorting, and pagination. It includes `destroy()` to prevent re-initialization issues when HTMX swaps the table.
*   **DRY Template Inheritance:** All templates extend `core/base.html` (not included here), which is assumed to contain common HTML structure, CDN links for Tailwind CSS, HTMX, Alpine.js, and jQuery (for DataTables).

---

## Final Notes

This comprehensive plan provides a robust framework for migrating the ASP.NET `TourIntimation_Edit_Details` module to Django. By strictly following the 'Fat Model, Thin View' paradigm, utilizing HTMX and Alpine.js for dynamic interfaces, and integrating DataTables for data presentation, the resulting Django application will be modern, maintainable, and highly responsive. The emphasis on automated conversion allows for systematic progression through the migration, reducing manual effort and potential for errors. The detailed code examples and test cases ensure a high-quality, production-ready solution.