## ASP.NET to Django Conversion Script: Salary Summary Report

This document outlines a strategic plan for migrating the `Salary_SAPL_Neha_Summary.aspx` ASP.NET application to a modern Django-based solution. The focus is on re-architecting the complex report generation logic into a maintainable, high-performance Django application, leveraging Django's ORM, HTMX, Alpine.js, and DataTables for a responsive and user-friendly experience.

### Business Value Proposition

Migrating this critical HR reporting functionality to Django brings significant business advantages:

1.  **Enhanced Performance & Scalability**: Django's optimized ORM and Python's efficiency will lead to faster report generation, especially as data volumes grow.
2.  **Improved User Experience**: The adoption of HTMX and Alpine.js ensures a seamless, interactive reporting interface without full page reloads, providing a desktop-like experience in the browser.
3.  **Reduced Technical Debt**: Moving away from legacy Crystal Reports and ASP.NET Web Forms eliminates dependencies on outdated technologies, making the system easier to maintain, debug, and extend.
4.  **Cost Efficiency**: Python and Django are open-source, reducing licensing costs associated with proprietary reporting tools.
5.  **Future-Proofing**: Django is a widely used, actively developed framework, ensuring long-term viability and access to a broad talent pool for future enhancements.
6.  **Centralized Business Logic**: Consolidating complex salary calculation logic within Django models promotes reusability, consistency, and easier auditing of financial rules.

### Core Migration Approach

Given that the original ASP.NET page is primarily a *report viewer* and not a CRUD interface for a single entity, the Django modernization plan will focus on:

*   **De-coupling Data Retrieval and Business Logic**: All complex salary calculations and data aggregation from the ASP.NET code-behind will be moved into dedicated Python classes and methods, adhering to the "Fat Model" principle by encapsulating business rules.
*   **Dynamic Data Presentation**: The Crystal Report viewer will be replaced with an interactive HTML table using DataTables, loaded dynamically via HTMX.
*   **Parameter Handling**: Query string parameters will be seamlessly integrated into Django views for report filtering.
*   **Automation Focus**: The generated code emphasizes patterns suitable for AI-assisted conversion, focusing on clear separation of concerns and consistent structures.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with numerous database tables to compile the salary summary report. The `SELECT` statements indicate joins across `tblHR_Salary_Master`, `tblHR_OfficeStaff`, `tblHR_Offer_Master`, `tblHR_Salary_Details`, and lookups against `tblFinancial_master`, `tblHR_Departments`, `tblHR_Designation`, `tblHR_Grade`, `tblHR_EmpType`, `tblHR_Increment_Master`, `tblHR_Offer_Accessories`, `tblHR_Increment_Accessories`, `tblHR_OTHour`, `tblHR_DutyHour`.

For the purpose of demonstrating the migration of the *report output*, we will define a conceptual `SalarySummaryRow` class to represent a single row of the generated report. For the underlying database models (which would be used for actual CRUD operations elsewhere in the ERP), we'll define key ones like `HrSalaryMaster` and `HrOfficeStaff` as `managed=False` models.

**Identified Primary Table for Django Model Template (Conceptual):**
Given the report iterates through `tblHR_Salary_Master` records, we will use this as the primary model for demonstrating the `managed=False` pattern, though the report itself aggregates from many tables.

*   **`[TABLE_NAME]`**: `tblHR_Salary_Master`
*   **`[MODEL_NAME]`**: `HrSalaryMaster`

**Key Columns Identified for `HrSalaryMaster` (from `tblHR_Salary_Master`):**
*   `Id` (Primary Key, inferred)
*   `EmpId` (int)
*   `FMonth` (int)
*   `CompId` (int)
*   `FinYearId` (int)
*   `Increment` (int)

**Derived `SalarySummaryRow` Columns (from C# `DataTable` definition):**
This is a comprehensive list of the 54 columns generated for each row of the salary summary report. These will become properties of our `SalarySummaryRow` Python class.

*   `emp_id` (string)
*   `company_id` (int)
*   `employee_name` (string)
*   `month_name` (string)
*   `year` (string)
*   `department_name` (string)
*   `designation_name` (string)
*   `status` (string)
*   `grade_name` (string)
*   `basic_salary` (double)
*   `da_allowance` (double)
*   `hra_allowance` (double)
*   `conveyance_allowance` (double)
*   `education_allowance` (double)
*   `medical_allowance` (double)
*   `sunday_pay` (double)
*   `gross_total_offered` (double)
*   `attendance_bonus` (double)
*   `special_allowance` (double)
*   `ex_gratia_calculated` (double)
*   `travelling_allowance` (double)
*   `misc_addition` (double)
*   `net_pay_before_deductions` (double)
*   `final_net_pay` (double)
*   `working_days_in_month` (double)
*   `present_days` (double)
*   `absent_days` (double)
*   `sunday_count` (double)
*   `holiday_count` (double)
*   `late_in_count` (double)
*   `coff_days` (double)
*   `half_days` (double)
*   `pl_days` (double)
*   `lwp_days` (double)
*   `pf_employee` (double)
*   `p_tax` (double)
*   `personal_loan_installment` (double)
*   `mobile_bill_deduction` (double)
*   `misc_deduction` (double)
*   `total_deductions` (double)
*   `bank_account_no` (string)
*   `current_date_dmy` (string)
*   `basic_calculated` (double)
*   `da_calculated` (double)
*   `hra_calculated` (double)
*   `conveyance_calculated` (double)
*   `education_calculated` (double)
*   `medical_calculated` (double)
*   `gross_total_calculated` (double)
*   `attendance_bonus_type` (double)
*   `attendance_bonus_amount` (double)
*   `pf_no` (string)
*   `pan_no` (string)
*   `offer_letter_path` (string)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET code primarily performs a "Read" operation by fetching, processing, and presenting salary data. There are no direct "Create", "Update", or "Delete" operations on the `Salary_SAPL_Neha_Summary.aspx` page itself. All logic revolves around data aggregation and calculation for reporting.

**Key Backend Functions to Migrate:**

*   **Data Retrieval**: Complex SQL queries joining multiple tables (`tblHR_Salary_Master`, `tblHR_OfficeStaff`, `tblHR_Offer_Master`, `tblHR_Salary_Details`, etc.) to get raw employee and salary details.
*   **Date & Financial Period Calculations**: Determining the year based on month and financial year (`fun.SalYrs`, splitting `FinYear`), calculating days in month, counting Sundays and Holidays.
*   **Offer/Increment Logic**: Determining the correct offer or increment details for an employee based on `Increment` values.
*   **Salary Component Calculations**: This is the most complex part, involving:
    *   Pro-rata calculations for `Basic`, `DA`, `HRA`, `Conveyance`, `Education`, `Medical` based on `TotalDays` (which is `DayOfMonth - (Absent - (PL + Coff))`).
    *   Calculations for PF (`fun.Pf_Cal`), Professional Tax (`fun.PTax_Cal`).
    *   Attendance Bonus calculation based on `AttBonusPer1`, `AttBonusPer2` and `AttBonusDays`.
    *   Overtime calculation (`fun.OTRate`, `fun.OTAmt`).
    *   Handling of `ExGratia`, `VehicleAllowance`, `Accessories` (CTC, TH, Both types), `Addition`, `Deduction`, `Installment`, `MobileBill`.
    *   Deriving `NetPay` and `TotalDeduct`.
*   **Utility Functions**: Re-implementing functions like `fun.GetRandomAlphaNumeric()`, `fun.CompAdd()`, `fun.FromDateDMY()`.
*   **Parameter Handling**: Retrieving `CompId`, `FinYearId`, `MonthId`, `EType`, `BGGroupId` from URL query strings or session.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses `CR:CrystalReportViewer` to display the report. There's a `Cancel` button for navigation.

**Django UI Component Mapping:**

*   **`CR:CrystalReportViewer`**: Will be replaced by a standard HTML `<table>` element enhanced with `DataTables.js` for client-side sorting, filtering, and pagination.
*   **`asp:Button ID="Cancel"`**: Will be a standard HTML `<button>` or `<a>` tag, likely using `hx-get` to navigate back to the previous report selection page.
*   **Dynamic Loading**: HTMX will be used to load the report table content dynamically, especially when parameters change (if implemented).

---

### Step 4: Generate Django Code

We will create a new Django app named `hr_salary` for this module.

#### 4.1 Models (hr_salary/models.py)

We define models for the primary tables accessed by the report. The `managed = False` is crucial for mapping to existing legacy database tables.

```python
from django.db import models
from django.utils.text import slugify
from datetime import date
from calendar import monthrange
import math

# --- Core Database Models (managed=False) ---
# These models map directly to existing database tables.
# Replace FIELD_TYPE and add all columns as per your actual database schema.

class HrSalaryMaster(models.Model):
    # Represents tblHR_Salary_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    emp_id = models.CharField(db_column='EmpId', max_length=50)
    financial_month = models.IntegerField(db_column='FMonth')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    increment = models.IntegerField(db_column='Increment', default=0)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Master'
        verbose_name = 'HR Salary Record'
        verbose_name_plural = 'HR Salary Records'

    def __str__(self):
        return f"Salary for Emp {self.emp_id} - Month {self.financial_month}/{self.financial_year_id}"


class HrOfficeStaff(models.Model):
    # Represents tblHR_OfficeStaff
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True) # Assuming EmpId is unique/PK
    user_id = models.CharField(db_column='UserID', max_length=50, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    offer_id = models.IntegerField(db_column='OfferId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId')
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50, blank=True, null=True)
    department_id = models.IntegerField(db_column='Department', blank=True, null=True)
    bg_group = models.IntegerField(db_column='BGGroup', blank=True, null=True)
    designation_id = models.IntegerField(db_column='Designation', blank=True, null=True)
    grade_id = models.IntegerField(db_column='Grade', blank=True, null=True)
    bank_account_no = models.CharField(db_column='BankAccountNo', max_length=100, blank=True, null=True)
    pf_no = models.CharField(db_column='PFNo', max_length=50, blank=True, null=True)
    pan_no = models.CharField(db_column='PANNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return self.employee_name

class HrOfferMaster(models.Model):
    # Represents tblHR_Offer_Master
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    staff_type = models.IntegerField(db_column='StaffType')
    type_of = models.IntegerField(db_column='TypeOf') # 1 for SAPL, 2 for NEHA
    salary = models.FloatField(db_column='salary') # Gross Salary
    duty_hrs = models.IntegerField(db_column='DutyHrs', blank=True, null=True)
    ot_hrs = models.IntegerField(db_column='OTHrs', blank=True, null=True)
    overtime_option = models.IntegerField(db_column='OverTime', blank=True, null=True) # 1=No, 2=Yes
    ex_gratia = models.FloatField(db_column='ExGratia', default=0)
    vehicle_allowance = models.FloatField(db_column='VehicleAllowance', default=0)
    att_bonus_per1 = models.FloatField(db_column='AttBonusPer1', default=0)
    att_bonus_per2 = models.FloatField(db_column='AttBonusPer2', default=0)
    pf_employee_option = models.IntegerField(db_column='PFEmployee', default=0) # PF employee option
    pf_company_option = models.IntegerField(db_column='PFCompany', default=0) # PF company option
    increment = models.IntegerField(db_column='Increment', default=0)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'HR Offer Master'
        verbose_name_plural = 'HR Offer Masters'

    def __str__(self):
        return f"Offer {self.offer_id} - Salary: {self.salary}"

class HrSalaryDetail(models.Model):
    # Represents tblHR_Salary_Details
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK
    master = models.ForeignKey(HrSalaryMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', blank=True, null=True)
    present = models.FloatField(db_column='Present', default=0)
    absent = models.FloatField(db_column='Absent', default=0)
    late_in = models.FloatField(db_column='LateIn', default=0)
    half_day = models.FloatField(db_column='HalfDay', default=0)
    sunday_present = models.FloatField(db_column='Sunday', default=0)
    coff = models.FloatField(db_column='Coff', default=0)
    pl = models.FloatField(db_column='PL', default=0)
    overtime_hrs = models.FloatField(db_column='OverTimeHrs', default=0)
    overtime_rate = models.FloatField(db_column='OverTimeRate', default=0)
    installment = models.FloatField(db_column='Installment', default=0)
    mobile_exe_amt = models.FloatField(db_column='MobileExeAmt', default=0)
    addition = models.FloatField(db_column='Addition', default=0)
    remarks1 = models.CharField(db_column='Remarks1', max_length=255, blank=True, null=True)
    deduction = models.FloatField(db_column='Deduction', default=0)
    remarks2 = models.CharField(db_column='Remarks2', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Details'
        verbose_name = 'HR Salary Detail'
        verbose_name_plural = 'HR Salary Details'

    def __str__(self):
        return f"Details for Salary Master {self.master_id}"

# --- Auxiliary Models (managed=False) to support lookups and calculations ---

class FinancialMaster(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True) # Assuming FinYearId is PK
    company_id = models.IntegerField(db_column='CompId')
    financial_year = models.CharField(db_column='FinYear', max_length=20) # e.g., "2023-2024"

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.financial_year

class HrDepartment(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'HR Department'
        verbose_name_plural = 'HR Departments'

    def __str__(self):
        return self.symbol

class HrDesignation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type_name = models.CharField(db_column='Type', max_length=100)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'HR Designation'
        verbose_name_plural = 'HR Designations'

    def __str__(self):
        return f"{self.type_name} [{self.symbol}]"

class HrGrade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'HR Grade'
        verbose_name_plural = 'HR Grades'

    def __str__(self):
        return self.symbol

class HrEmpType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'HR Employee Type'
        verbose_name_plural = 'HR Employee Types'

    def __str__(self):
        return self.description

class HrIncrementMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    offer = models.ForeignKey(HrOfferMaster, on_delete=models.DO_NOTHING, db_column='OfferId', related_name='increments', blank=True, null=True)
    increment = models.IntegerField(db_column='Increment', default=0)
    staff_type = models.IntegerField(db_column='StaffType')
    type_of = models.IntegerField(db_column='TypeOf') # 1 for SAPL, 2 for NEHA
    salary = models.FloatField(db_column='salary') # Gross Salary
    duty_hrs = models.IntegerField(db_column='DutyHrs', blank=True, null=True)
    ot_hrs = models.IntegerField(db_column='OTHrs', blank=True, null=True)
    overtime_option = models.IntegerField(db_column='OverTime', blank=True, null=True) # 1=No, 2=Yes
    ex_gratia = models.FloatField(db_column='ExGratia', default=0)
    vehicle_allowance = models.FloatField(db_column='VehicleAllowance', default=0)
    att_bonus_per1 = models.FloatField(db_column='AttBonusPer1', default=0)
    att_bonus_per2 = models.FloatField(db_column='AttBonusPer2', default=0)
    pf_employee_option = models.IntegerField(db_column='PFEmployee', default=0) # PF employee option
    pf_company_option = models.IntegerField(db_column='PFCompany', default=0) # PF company option

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Master'
        verbose_name = 'HR Increment Master'
        verbose_name_plural = 'HR Increment Masters'

    def __str__(self):
        return f"Increment {self.increment} for Offer {self.offer_id}"

class HrOfferAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming PK
    offer = models.ForeignKey(HrOfferMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='accessories', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', default=0)
    amount = models.FloatField(db_column='Amount', default=0)
    includes_in = models.CharField(db_column='IncludesIn', max_length=10) # 1, 2, or 3 (CTC, TH, Both)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'HR Offer Accessory'
        verbose_name_plural = 'HR Offer Accessories'

    def __str__(self):
        return f"Accessory for Offer {self.offer_id} - {self.qty}x{self.amount}"

class HrIncrementAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming PK
    increment_master = models.ForeignKey(HrIncrementMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='accessories', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', default=0)
    amount = models.FloatField(db_column='Amount', default=0)
    includes_in = models.CharField(db_column='IncludesIn', max_length=10) # 1, 2, or 3 (CTC, TH, Both)

    class Meta:
        managed = False
        db_table = 'tblHR_Increment_Accessories'
        verbose_name = 'HR Increment Accessory'
        verbose_name_plural = 'HR Increment Accessories'

    def __str__(self):
        return f"Accessory for Increment {self.increment_master_id} - {self.qty}x{self.amount}"

class HrOTHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.FloatField(db_column='Hours', default=0)

    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'HR OT Hour'
        verbose_name_plural = 'HR OT Hours'

    def __str__(self):
        return f"{self.hours} Hours"

class HrDutyHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.FloatField(db_column='Hours', default=0)

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'HR Duty Hour'
        verbose_name_plural = 'HR Duty Hours'

    def __str__(self):
        return f"{self.hours} Hours"

# --- Report Row and Generator (Python Classes for Business Logic) ---

class SalarySummaryRow:
    """
    Represents a single row of the generated salary summary report.
    This is a pure Python class, not a Django model, as its data is derived
    from multiple database tables and complex calculations.
    """
    def __init__(self, **kwargs):
        for field in [
            'emp_id', 'company_id', 'employee_name', 'month_name', 'year',
            'department_name', 'designation_name', 'status', 'grade_name',
            'basic_salary', 'da_allowance', 'hra_allowance', 'conveyance_allowance',
            'education_allowance', 'medical_allowance', 'sunday_pay', 'gross_total_offered',
            'attendance_bonus', 'special_allowance', 'ex_gratia_calculated', 'travelling_allowance',
            'misc_addition', 'net_pay_before_deductions', 'final_net_pay',
            'working_days_in_month', 'present_days', 'absent_days', 'sunday_count',
            'holiday_count', 'late_in_count', 'coff_days', 'half_days', 'pl_days',
            'lwp_days', 'pf_employee', 'p_tax', 'personal_loan_installment',
            'mobile_bill_deduction', 'misc_deduction', 'total_deductions',
            'bank_account_no', 'current_date_dmy', 'basic_calculated', 'da_calculated',
            'hra_calculated', 'conveyance_calculated', 'education_calculated',
            'medical_calculated', 'gross_total_calculated', 'attendance_bonus_type',
            'attendance_bonus_amount', 'pf_no', 'pan_no', 'offer_letter_path'
        ]:
            setattr(self, field, kwargs.get(field, 0.0 if 'double' in str(type(kwargs.get(field))) else ''))

    def __str__(self):
        return f"{self.employee_name} ({self.emp_id}) - {self.month_name} {self.year}"

class SalarySummaryGenerator:
    """
    Encapsulates the complex logic from the ASP.NET code-behind to generate
    SalarySummaryRow objects from the underlying database models.
    This class is responsible for all data retrieval, lookups, and calculations.
    """
    def __init__(self, company_id, financial_year_id, month_id, employee_type, bg_group_id, key):
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        self.month_id = month_id
        self.employee_type = employee_type # EType in ASP.NET
        self.bg_group_id = bg_group_id
        self.key = key # Used for session key in ASP.NET, not directly applicable here.

        # Pre-fetch common lookup data to avoid repeated DB hits
        self.financial_year_data = self._get_financial_year()
        self.departments_data = {d.id: d.symbol for d in HrDepartment.objects.all()}
        self.designations_data = {d.id: f"{d.type_name} [ {d.symbol} ]" for d in HrDesignation.objects.all()}
        self.grades_data = {g.id: g.symbol for g in HrGrade.objects.all()}
        self.emp_types_data = {et.id: et.description for et in HrEmpType.objects.all()}
        self.ot_hours_data = {ot.id: ot.hours for ot in HrOTHour.objects.all()}
        self.duty_hours_data = {dh.id: dh.hours for dh in HrDutyHour.objects.all()}
        self.current_date_dmy = date.today().strftime('%d/%m/%Y')

    def _get_financial_year(self):
        try:
            return FinancialMaster.objects.get(
                company_id=self.company_id,
                id=self.financial_year_id
            ).financial_year
        except FinancialMaster.DoesNotExist:
            return None

    def _get_year_from_fin_year(self, fin_year_str):
        if not fin_year_str:
            return ""
        parts = fin_year_str.split('-')
        if len(parts) == 2:
            fy = int(parts[0])
            ty = int(parts[1])
            if self.month_id in [1, 2, 3]: # Jan, Feb, Mar
                return str(ty)
            else:
                return str(fy)
        return ""

    def _get_month_name(self):
        return date(2000, self.month_id, 1).strftime('%B') if 1 <= self.month_id <= 12 else ""

    def _get_company_address(self, company_id):
        # Placeholder for fun.CompAdd(CompId) logic.
        # This would typically query a company/address table.
        # For now, return a dummy address.
        if company_id == 1:
            return "123 ERP Main Street, Tech City, 12345"
        return "Unknown Company Address"
    
    def _get_days_in_month(self):
        """Returns the number of days in the specified month and financial year (used as calendar year)."""
        year_for_month = int(self._get_year_from_fin_year(self.financial_year_data))
        return monthrange(year_for_month, self.month_id)[1]

    def _count_sundays(self, year, month):
        """Counts Sundays in a given month and year."""
        # This logic should be accurate to fun.CountSundays
        num_sundays = 0
        _, num_days = monthrange(year, month)
        for day in range(1, num_days + 1):
            if date(year, month, day).weekday() == 6:  # Monday is 0, Sunday is 6
                num_sundays += 1
        return num_sundays

    def _get_holiday_count(self, month_id, comp_id, fin_year_id):
        # Placeholder for fun.GetHoliday. This would query a holiday table.
        # Return a dummy value for demonstration.
        return 2.0 # Example: 2 holidays in the month

    def _offer_cal(self, gross_salary, component_type, factor, staff_type):
        # Placeholder for fun.Offer_Cal logic.
        # This would involve looking up percentages or fixed amounts based on component_type and staff_type.
        # E.g., Basic might be 40% of Gross, DA 20%, etc.
        # For simplicity, returning a fixed percentage based on type for demonstration.
        if component_type == 1: return gross_salary * 0.40 # Basic
        if component_type == 2: return gross_salary * 0.20 # DA
        if component_type == 3: return gross_salary * 0.15 # HRA
        if component_type == 4: return gross_salary * 0.05 # Conveyance
        if component_type == 5: return gross_salary * 0.05 # Education
        if component_type == 6: return gross_salary * 0.05 # Medical
        return 0.0

    def _pf_cal(self, gross_salary, pf_type, pf_option):
        # Placeholder for fun.Pf_Cal logic.
        # This would depend on company rules (fixed amount, percentage, capped).
        # Assuming a simple percentage for demonstration.
        if pf_option == 1: # Example PF option: 12%
            return min(gross_salary * 0.12, 1800.0) # Example cap
        return 0.0

    def _p_tax_cal(self, gross_total_income, month_str):
        # Placeholder for fun.PTax_Cal logic.
        # Professional Tax rules are state-specific and slab-based.
        # This needs to be precisely implemented based on actual rules.
        # For demonstration, a very basic example:
        if gross_total_income > 25000: return 200.0
        if gross_total_income > 15000: return 150.0
        return 0.0

    def _ot_rate(self, gross_salary, ot_hours_per_day, duty_hours_per_day, days_in_month):
        # Placeholder for fun.OTRate.
        # Often (Gross / (DutyHrs * DaysInMonth)) * OT_Factor
        if duty_hours_per_day > 0 and days_in_month > 0:
            return (gross_salary / (duty_hours_per_day * days_in_month)) * 1.5 # 1.5x for OT
        return 0.0

    def _ot_amt(self, ot_rate, actual_ot_hrs):
        return ot_rate * actual_ot_hrs

    def _get_working_days(self, fin_year_id, month_id):
        # Placeholder for fun.WorkingDays
        # This would calculate actual working days excluding Sundays/Holidays for the financial year/month
        year_for_month = int(self._get_year_from_fin_year(self.financial_year_data))
        days_in_month = monthrange(year_for_month, month_id)[1]
        num_sundays = self._count_sundays(year_for_month, month_id)
        num_holidays = self._get_holiday_count(month_id, self.company_id, fin_year_id)
        return days_in_month - num_sundays - num_holidays # Basic approximation

    def _get_random_alpha_numeric(self):
        import random
        import string
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))

    def generate_summary(self):
        """
        Generates a list of SalarySummaryRow objects based on the provided parameters
        and complex calculation logic, mimicking the ASP.NET code-behind.
        """
        summary_rows = []
        year_for_month = int(self._get_year_from_fin_year(self.financial_year_data))
        day_of_month = self._get_days_in_month()
        month_name = self._get_month_name()
        sunday_in_month = self._count_sundays(year_for_month, self.month_id)
        holiday_count = self._get_holiday_count(self.month_id, self.company_id, self.financial_year_id)

        employee_salaries = HrSalaryMaster.objects.filter(
            financial_month=self.month_id,
            company_id=self.company_id,
            financial_year_id=self.financial_year_id
        ).select_related('details').prefetch_related(
            'empsalarymaster_set', # Reverse relation from HrOfficeStaff
            'hrsalrymaster_set', # From OfferMaster
        )

        if self.bg_group_id == 1: # SAPL
            employee_staff_query = HrOfficeStaff.objects.filter(
                offer__type_of=self.employee_type
            )
        else: # NEHA or other groups
            employee_staff_query = HrOfficeStaff.objects.filter(
                offer__type_of=self.employee_type,
                bg_group=self.bg_group_id
            )
        # Join with salary master to only get staff with salary records for the month
        employee_staff_query = employee_staff_query.filter(
            emp_id__in=employee_salaries.values_list('emp_id', flat=True)
        ).select_related('offer', 'hrsalarymaster', 'department', 'designation', 'grade')

        for staff_member in employee_staff_query:
            salary_master_record = None
            try:
                # Find the corresponding salary master record for this staff member and month
                salary_master_record = employee_salaries.get(emp_id=staff_member.emp_id)
            except HrSalaryMaster.DoesNotExist:
                continue # Skip if no salary record for the month

            salary_detail = None
            if salary_master_record:
                # Check if 'details' relation exists for the salary_master_record
                # If not, it means there are no salary details, which could cause errors.
                # In ASP.NET, it's assumed DSLeave.Tables[0].Rows.Count > 0 if salary master exists.
                try:
                    salary_detail = HrSalaryDetail.objects.get(master=salary_master_record)
                except HrSalaryDetail.DoesNotExist:
                    salary_detail = HrSalaryDetail() # Use a default empty detail if none found


            dr_data = {} # Dictionary to build the SalarySummaryRow

            dr_data['emp_id'] = staff_member.emp_id
            dr_data['company_id'] = staff_member.company_id
            dr_data['employee_name'] = f"{staff_member.title}.{staff_member.employee_name}"
            dr_data['month_name'] = month_name
            dr_data['year'] = self._get_year_from_fin_year(self.financial_year_data)
            dr_data['department_name'] = self.departments_data.get(staff_member.department_id, '')
            dr_data['designation_name'] = self.designations_data.get(staff_member.designation_id, '')
            dr_data['grade_name'] = self.grades_data.get(staff_member.grade_id, '')
            dr_data['pf_no'] = staff_member.pf_no
            dr_data['pan_no'] = staff_member.pan_no

            # Offer/Increment determination
            current_offer_obj = None
            if salary_master_record.increment == staff_member.offer.increment:
                current_offer_obj = staff_member.offer
            else:
                try:
                    current_offer_obj = HrIncrementMaster.objects.get(
                        offer=staff_member.offer,
                        increment=salary_master_record.increment
                    )
                except HrIncrementMaster.DoesNotExist:
                    current_offer_obj = staff_member.offer # Fallback or error handling

            if current_offer_obj:
                dr_data['offer_letter_path'] = (
                    f"/erp/Module/HR/Transactions/OfferLetter_Print_Details.aspx?"
                    f"OfferId={staff_member.offer_id}&T=2&Key={self._get_random_alpha_numeric()}"
                    f"&Key1={self.key}&EType={self.employee_type}&MonthId={self.month_id}"
                    f"&BGGroupId={self.bg_group_id}&Increment={current_offer_obj.increment}"
                    f"&ModId=12&SubModId=25" # This URL is legacy ASP.NET, needs remapping in Django
                )
                
                gross_salary_offered = current_offer_obj.salary
                dr_data['gross_total_offered'] = gross_salary_offered

                dr_data['basic_salary'] = self._offer_cal(gross_salary_offered, 1, 1, current_offer_obj.staff_type)
                dr_data['da_allowance'] = self._offer_cal(gross_salary_offered, 2, 1, current_offer_obj.type_of)
                dr_data['hra_allowance'] = self._offer_cal(gross_salary_offered, 3, 1, current_offer_obj.type_of)
                dr_data['conveyance_allowance'] = self._offer_cal(gross_salary_offered, 4, 1, current_offer_obj.type_of)
                dr_data['education_allowance'] = self._offer_cal(gross_salary_offered, 5, 1, current_offer_obj.type_of)
                dr_data['medical_allowance'] = self._offer_cal(gross_salary_offered, 6, 1, current_offer_obj.type_of)

                emp_type_desc = self.emp_types_data.get(current_offer_obj.staff_type, '')
                if current_offer_obj.type_of == 1:
                    dr_data['status'] = f"SAPL - {emp_type_desc}"
                elif current_offer_obj.type_of == 2:
                    dr_data['status'] = f"NEHA - {emp_type_desc}"

                # Salary Details Calculations
                working_days = self._get_working_days(self.financial_year_id, self.month_id)
                present_days = salary_detail.present
                absent_days = salary_detail.absent
                pl_days = salary_detail.pl
                coff_days = salary_detail.coff
                half_days = salary_detail.half_day
                sunday_present_days = salary_detail.sunday_present
                
                att_bonus_1_per = current_offer_obj.att_bonus_per1
                att_bonus_2_per = current_offer_obj.att_bonus_per2
                ex_gratia_offered = current_offer_obj.ex_gratia
                vehicle_allowance_offered = current_offer_obj.vehicle_allowance
                addition = salary_detail.addition
                deduction = salary_detail.deduction

                # Calculate LWP and TotalDays similar to ASP.NET
                # TotalDays = DayOfMonth - (Absent - (PL + Coff));
                total_days_eligible_for_pay = day_of_month - (absent_days - (pl_days + coff_days))
                
                # AttBonusDays = Present + SundayP + HalfDay;
                att_bonus_days = present_days + sunday_present_days + half_days
                lwp_days = (day_of_month - total_days_eligible_for_pay)

                dr_data['working_days_in_month'] = working_days
                dr_data['present_days'] = present_days
                dr_data['absent_days'] = absent_days
                dr_data['sunday_count'] = sunday_in_month
                dr_data['holiday_count'] = holiday_count
                dr_data['late_in_count'] = salary_detail.late_in
                dr_data['coff_days'] = coff_days
                dr_data['half_days'] = half_days
                dr_data['pl_days'] = pl_days
                dr_data['lwp_days'] = lwp_days

                # Calculated Salary Components (pro-rata)
                dr_data['basic_calculated'] = round((dr_data['basic_salary'] * total_days_eligible_for_pay) / day_of_month)
                dr_data['da_calculated'] = round((dr_data['da_allowance'] * total_days_eligible_for_pay) / day_of_month)
                dr_data['hra_calculated'] = round((dr_data['hra_allowance'] * total_days_eligible_for_pay) / day_of_month)
                dr_data['conveyance_calculated'] = round((dr_data['conveyance_allowance'] * total_days_eligible_for_pay) / day_of_month)
                dr_data['education_calculated'] = round((dr_data['education_allowance'] * total_days_eligible_for_pay) / day_of_month)
                dr_data['medical_calculated'] = round((dr_data['medical_allowance'] * total_days_eligible_for_pay) / day_of_month)
                dr_data['gross_total_calculated'] = round(
                    dr_data['basic_calculated'] + dr_data['da_calculated'] + dr_data['hra_calculated'] +
                    dr_data['conveyance_calculated'] + dr_data['education_calculated'] + dr_data['medical_calculated']
                )

                pf_employee_calculated = self._pf_cal(dr_data['gross_total_calculated'], 1, current_offer_obj.pf_employee_option)
                dr_data['pf_employee'] = pf_employee_calculated
                
                dr_data['ex_gratia_calculated'] = round((ex_gratia_offered * total_days_eligible_for_pay) / day_of_month)
                dr_data['personal_loan_installment'] = salary_detail.installment
                dr_data['mobile_bill_deduction'] = salary_detail.mobile_exe_amt

                # Accessories Addition
                accessories_ctc = 0.0
                accessories_th = 0.0
                accessories_both = 0.0

                if isinstance(current_offer_obj, HrOfferMaster):
                    accessories_query = HrOfferAccessory.objects.filter(offer=current_offer_obj)
                elif isinstance(current_offer_obj, HrIncrementMaster):
                    accessories_query = HrIncrementAccessory.objects.filter(increment_master=current_offer_obj)
                else:
                    accessories_query = [] # No accessories if not found

                for acc in accessories_query:
                    value = acc.qty * acc.amount
                    if acc.includes_in == "1": accessories_ctc += value
                    elif acc.includes_in == "2": accessories_th += value
                    elif acc.includes_in == "3": accessories_both += value

                # Over Time
                ot_amt = 0.0
                if current_offer_obj.overtime_option == 2:
                    ot_rate_val = self._ot_rate(
                        gross_salary_offered,
                        self.ot_hours_data.get(current_offer_obj.ot_hrs, 0),
                        self.duty_hours_data.get(current_offer_obj.duty_hrs, 0),
                        day_of_month
                    )
                    ot_amt = round(self._ot_amt(ot_rate_val, salary_detail.overtime_hrs))
                
                # Att Bonus
                att_bonus_type = 0
                att_bonus_amount = 0.0
                # Replicated ASP.NET logic: AttBonusDays >= (DayOfMonth - (Holiday + SundayInMonth + 2)) && AttBonusDays < ((DayOfMonth + 2) - (Holiday + SundayInMonth))
                threshold1_lower = day_of_month - (holiday_count + sunday_in_month + 2)
                threshold1_upper = (day_of_month + 2) - (holiday_count + sunday_in_month)

                if att_bonus_days >= threshold1_lower and att_bonus_days < threshold1_upper:
                    att_bonus_type = 1
                    att_bonus_amount = round((gross_salary_offered * att_bonus_1_per) / 100)
                elif att_bonus_days >= threshold1_upper:
                    att_bonus_type = 2
                    att_bonus_amount = round((gross_salary_offered * att_bonus_2_per) / 100)
                
                dr_data['attendance_bonus_type'] = att_bonus_type
                dr_data['attendance_bonus_amount'] = att_bonus_amount

                # Misc Addition
                misc_add = round(vehicle_allowance_offered + accessories_th + accessories_both + ot_amt + addition)
                dr_data['misc_addition'] = misc_add

                # P Tax
                ptax_calculated = self._p_tax_cal(
                    dr_data['gross_total_calculated'] + att_bonus_amount + accessories_th +
                    accessories_both + dr_data['ex_gratia_calculated'] + vehicle_allowance_offered +
                    addition + ot_amt,
                    str(self.month_id).zfill(2)
                )
                dr_data['p_tax'] = ptax_calculated

                # Misc Deduction
                misc_deduct = deduction
                dr_data['misc_deduction'] = misc_deduct
                
                total_deductions = round(pf_employee_calculated + ptax_calculated + salary_detail.installment +
                                      salary_detail.mobile_exe_amt + misc_deduct)
                dr_data['total_deductions'] = total_deductions

                # Net Pay
                net_pay_before_deductions = dr_data['gross_total_calculated'] + att_bonus_amount + dr_data['ex_gratia_calculated'] + misc_add
                dr_data['net_pay_before_deductions'] = round(net_pay_before_deductions)
                
                final_net_pay = round(net_pay_before_deductions - total_deductions)
                dr_data['final_net_pay'] = final_net_pay
                
                dr_data['sunday_pay'] = sunday_present_days # Replicated from ASP.NET dr[15]
                
            dr_data['bank_account_no'] = staff_member.bank_account_no
            dr_data['current_date_dmy'] = self.current_date_dmy # from fun.FromDateDMY(fun.getCurrDate())

            summary_rows.append(SalarySummaryRow(**dr_data))

        return summary_rows

```

#### 4.2 Forms (hr_salary/forms.py)

Since this ASP.NET page is a report display and doesn't involve user input for saving/updating a single model instance, a dedicated `ModelForm` is not strictly required for its primary function. However, to adhere to the prompt's structure for CRUD operations, we provide a placeholder form for `HrSalaryMaster`. This form would be used in a separate view if `HrSalaryMaster` records were being created or updated directly.

```python
from django import forms
from .models import HrSalaryMaster

class HrSalaryMasterForm(forms.ModelForm):
    class Meta:
        model = HrSalaryMaster
        fields = ['emp_id', 'financial_month', 'company_id', 'financial_year_id', 'increment']
        widgets = {
            'emp_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_month': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'increment': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Example custom validation (if needed, based on ASP.NET logic)
    def clean_financial_month(self):
        month = self.cleaned_data['financial_month']
        if not (1 <= month <= 12):
            raise forms.ValidationError("Financial month must be between 1 and 12.")
        return month

```

#### 4.3 Views (hr_salary/views.py)

This section details the Django Class-Based Views (CBVs). For the report, we'll use a `ListView` and a partial view to render the DataTables. The generic CRUD views for `HrSalaryMaster` are included to meet the prompt's requirements, noting they would be for managing the *underlying* data, not the report itself.

```python
from django.views.generic import View, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import HrSalaryMaster, SalarySummaryGenerator, SalarySummaryRow # Import derived row class
from .forms import HrSalaryMasterForm

# --- Report Views (primary focus of this migration) ---

class SalarySummaryReportView(View):
    """
    Main view for displaying the salary summary report.
    It takes parameters from the request and uses SalarySummaryGenerator to get data.
    """
    template_name = 'hr_salary/salarysummaryrow/list.html'

    def get(self, request, *args, **kwargs):
        # Parameters from ASP.NET Request.QueryString and Session
        # In Django, sessions are handled by middleware, request.session
        # Query string parameters are in request.GET
        try:
            bg_group_id = int(request.GET.get('BGGroupId', 0))
            month_id = int(request.GET.get('MonthId', 0))
            company_id = int(request.session.get('compid', 0)) # Assuming 'compid' is in session
            fin_year_id = int(request.session.get('finyear', 0)) # Assuming 'finyear' is in session
            employee_type = int(request.GET.get('EType', 0))
            # 'Key' is ASP.NET specific for session report storage, not directly used in Django
            key = request.GET.get('Key', '')

            if not all([bg_group_id, month_id, company_id, fin_year_id, employee_type]):
                messages.error(request, "Missing essential report parameters.")
                # Redirect or show a default empty state
                context = {'salary_summaries': [], 'error_message': "Please provide all required parameters."}
                return render(request, self.template_name, context)

            generator = SalarySummaryGenerator(
                company_id=company_id,
                financial_year_id=fin_year_id,
                month_id=month_id,
                employee_type=employee_type,
                bg_group_id=bg_group_id,
                key=key # Pass for potential legacy URL generation if needed
            )
            salary_summaries = generator.generate_summary()
            
            context = {
                'salary_summaries': salary_summaries,
                'company_address': generator._get_company_address(company_id) # For report header if needed
            }
            return render(request, self.template_name, context)

        except ValueError as e:
            messages.error(request, f"Invalid parameter format: {e}")
            context = {'salary_summaries': [], 'error_message': f"Invalid parameter: {e}"}
            return render(request, self.template_name, context)
        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {e}")
            context = {'salary_summaries': [], 'error_message': f"An unexpected error occurred: {e}"}
            return render(request, self.template_name, context)

class SalarySummaryTablePartialView(View):
    """
    Renders only the DataTable content, intended for HTMX requests.
    """
    template_name = 'hr_salary/salarysummaryrow/_salarysummaryrow_table.html'

    def get(self, request, *args, **kwargs):
        try:
            bg_group_id = int(request.GET.get('BGGroupId', 0))
            month_id = int(request.GET.get('MonthId', 0))
            company_id = int(request.session.get('compid', 0))
            fin_year_id = int(request.session.get('finyear', 0))
            employee_type = int(request.GET.get('EType', 0))
            key = request.GET.get('Key', '')

            if not all([bg_group_id, month_id, company_id, fin_year_id, employee_type]):
                return HttpResponse("<p class='text-red-500'>Missing parameters for report generation.</p>")

            generator = SalarySummaryGenerator(
                company_id=company_id,
                financial_year_id=fin_year_id,
                month_id=month_id,
                employee_type=employee_type,
                bg_group_id=bg_group_id,
                key=key
            )
            salary_summaries = generator.generate_summary()
            
            context = {
                'salary_summaries': salary_summaries
            }
            return render(request, self.template_name, context)

        except ValueError:
            return HttpResponse("<p class='text-red-500'>Error: Invalid report parameters provided.</p>")
        except Exception as e:
            return HttpResponse(f"<p class='text-red-500'>Error loading report: {e}</p>")

# --- Generic CRUD Views for HrSalaryMaster (for completeness as per prompt) ---
# NOTE: These views are for demonstrating standard CRUD patterns on the HrSalaryMaster model.
# The original ASP.NET page was a report, not a CRUD interface for this specific model.
# You would use similar patterns for actual data management elsewhere in your ERP.

class HrSalaryMasterListView(ListView):
    model = HrSalaryMaster
    template_name = 'hr_salary/hrsalarymaster/list.html'
    context_object_name = 'hr_salary_masters'

class HrSalaryMasterCreateView(CreateView):
    model = HrSalaryMaster
    form_class = HrSalaryMasterForm
    template_name = 'hr_salary/hrsalarymaster/form.html'
    success_url = reverse_lazy('hr_salary_master_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'HR Salary Master record added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshHrSalaryMasterList'
                }
            )
        return response

class HrSalaryMasterUpdateView(UpdateView):
    model = HrSalaryMaster
    form_class = HrSalaryMasterForm
    template_name = 'hr_salary/hrsalarymaster/form.html'
    success_url = reverse_lazy('hr_salary_master_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'HR Salary Master record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshHrSalaryMasterList'
                }
            )
        return response

class HrSalaryMasterDeleteView(DeleteView):
    model = HrSalaryMaster
    template_name = 'hr_salary/hrsalarymaster/confirm_delete.html'
    success_url = reverse_lazy('hr_salary_master_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'HR Salary Master record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshHrSalaryMasterList'
                }
            )
        return response
```

#### 4.4 Templates (hr_salary/templates/)

Templates are organized by conceptual `MODEL_NAME` to maintain clarity. `salarysummaryrow` for the report, and `hrsalarymaster` for the generic CRUD examples.

**hr_salary/templates/hr_salary/salarysummaryrow/list.html** (Main Report Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Salary Summary Report</h2>
        <!-- Cancel button similar to ASP.NET -->
        <a href="{% url 'hr_salary_report_selection' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    {% if error_message %}
        <div class="text-red-600 text-lg text-center">{{ error_message }}</div>
    {% else %}
        <div id="salarySummaryRowTable-container"
             hx-trigger="load, refreshSalarySummaryRowList from:body"
             hx-get="{% url 'salary_summary_table' %}?BGGroupId={{ request.GET.BGGroupId }}&MonthId={{ request.GET.MonthId }}&EType={{ request.GET.EType }}&Key={{ request.GET.Key }}"
             hx-swap="innerHTML"
             class="bg-white shadow-md rounded-lg p-6">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Report Data...</p>
            </div>
        </div>
    {% endif %}
    
    <!-- Modal for form (not used on this report page, but kept for pattern consistency) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management
    });
</script>
{% endblock %}
```

**hr_salary/templates/hr_salary/salarysummaryrow/_salarysummaryrow_table.html** (Partial for HTMX)

```html
<table id="salarySummaryRowTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month/Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Offered</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Calc.</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF Emp</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PTax</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Deduct</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in salary_summaries %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.month_name }} {{ obj.year }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.department_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.designation_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.gross_total_offered|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.gross_total_calculated|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.pf_employee|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.p_tax|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.total_deductions|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right font-semibold">{{ obj.final_net_pay|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <!-- Example Action: View Offer Letter -->
                <a href="{{ obj.offer_letter_path }}" target="_blank"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                    View Offer
                </a>
                <!-- Add other report-specific actions if needed -->
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#salarySummaryRowTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "simple_numbers",
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": [12] } // Disable sorting for 'Actions' column
        ]
    });
});
</script>
```

**hr_salary/templates/hr_salary/hrsalarymaster/list.html** (Generic CRUD List for HrSalaryMaster)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">HR Salary Master Records</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'hr_salary_master_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Record
        </button>
    </div>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div id="hrSalaryMasterTable-container"
         hx-trigger="load, refreshHrSalaryMasterList from:body"
         hx-get="{% url 'hr_salary_master_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**hr_salary/templates/hr_salary/hrsalarymaster/_hrsalarymaster_table.html** (Partial for HTMX)

```html
<table id="hrSalaryMasterTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Increment</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in hr_salary_masters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.financial_month }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.company_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.financial_year_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.increment }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'hr_salary_master_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'hr_salary_master_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#hrSalaryMasterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**hr_salary/templates/hr_salary/hrsalarymaster/form.html** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} HR Salary Master Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**hr_salary/templates/hr_salary/hrsalarymaster/confirm_delete.html** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete the HR Salary Master record for Employee ID <strong>{{ object.emp_id }}</strong> (Month: {{ object.financial_month }}, Year: {{ object.financial_year_id }})?</p>
    
    <form hx-post="{% url 'hr_salary_master_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (hr_salary/urls.py)

```python
from django.urls import path
from .views import (
    SalarySummaryReportView, SalarySummaryTablePartialView,
    HrSalaryMasterListView, HrSalaryMasterCreateView, HrSalaryMasterUpdateView, HrSalaryMasterDeleteView
)

urlpatterns = [
    # URLs for the Salary Summary Report (main migration focus)
    # The 'report_selection' would be the previous page leading to this summary report.
    path('salary-report/', SalarySummaryReportView.as_view(), name='salary_summary_report'),
    path('salary-report/table/', SalarySummaryTablePartialView.as_view(), name='salary_summary_table'),
    # Dummy URL for the cancel button, assuming it goes back to a selection page
    path('salary-report/selection/', SalarySummaryTablePartialView.as_view(), name='hr_salary_report_selection'),

    # URLs for HrSalaryMaster CRUD operations (for demonstrating the pattern)
    path('hr-salary-masters/', HrSalaryMasterListView.as_view(), name='hr_salary_master_list'),
    path('hr-salary-masters/add/', HrSalaryMasterCreateView.as_view(), name='hr_salary_master_add'),
    path('hr-salary-masters/edit/<int:pk>/', HrSalaryMasterUpdateView.as_view(), name='hr_salary_master_edit'),
    path('hr-salary-masters/delete/<int:pk>/', HrSalaryMasterDeleteView.as_view(), name='hr_salary_master_delete'),
    # HTMX partial for HrSalaryMaster list
    path('hr-salary-masters/table/', HrSalaryMasterListView.as_view(template_name='hr_salary/hrsalarymaster/_hrsalarymaster_table.html'), name='hr_salary_master_table'),
]
```

#### 4.6 Tests (hr_salary/tests.py)

This section includes comprehensive tests for both the `SalarySummaryGenerator` logic and the Django views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date
from calendar import monthrange
from .models import (
    HrSalaryMaster, HrOfficeStaff, HrOfferMaster, HrSalaryDetail,
    FinancialMaster, HrDepartment, HrDesignation, HrGrade, HrEmpType,
    HrIncrementMaster, HrOfferAccessory, HrIncrementAccessory,
    HrOTHour, HrDutyHour,
    SalarySummaryRow, SalarySummaryGenerator
)

# Mock the database for unit tests on SalarySummaryGenerator
# For full integration tests, you'd populate a test database using fixtures or factories.

# --- Unit Tests for SalarySummaryGenerator and supporting logic ---
class SalarySummaryGeneratorTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock data for all related models that SalarySummaryGenerator needs
        # This will be used by the mock objects
        cls.company_id = 1
        cls.financial_year_id = 1
        cls.month_id = 7 # July
        cls.employee_type = 1 # SAPL
        cls.bg_group_id = 1
        cls.key = "ABC123DEF456"

        FinancialMaster.objects.create(id=cls.financial_year_id, company_id=cls.company_id, financial_year="2023-2024")
        HrDepartment.objects.create(id=1, symbol="HR")
        HrDesignation.objects.create(id=1, type_name="Manager", symbol="MGR")
        HrGrade.objects.create(id=1, symbol="A")
        HrEmpType.objects.create(id=1, description="Permanent")
        HrOTHour.objects.create(id=1, hours=8.0)
        HrDutyHour.objects.create(id=1, hours=8.0)

        cls.offer_master = HrOfferMaster.objects.create(
            offer_id=101, staff_type=1, type_of=cls.employee_type, salary=50000.0,
            duty_hrs=1, ot_hrs=1, overtime_option=2, ex_gratia=1000.0, vehicle_allowance=500.0,
            att_bonus_per1=5.0, att_bonus_per2=10.0, pf_employee_option=1, pf_company_option=1,
            increment=1
        )
        cls.hr_office_staff = HrOfficeStaff.objects.create(
            emp_id="EMP001", user_id="U001", company_id=cls.company_id, offer_id=cls.offer_master.offer_id,
            financial_year_id=cls.financial_year_id, title="Mr", employee_name="John Doe",
            department_id=1, bg_group=cls.bg_group_id, designation_id=1, grade_id=1,
            bank_account_no="**********", pf_no="PF001", pan_no="PAN001"
        )
        cls.hr_salary_master = HrSalaryMaster.objects.create(
            id=1, emp_id="EMP001", financial_month=cls.month_id, company_id=cls.company_id,
            financial_year_id=cls.financial_year_id, increment=1
        )
        cls.hr_salary_detail = HrSalaryDetail.objects.create(
            id=1, master=cls.hr_salary_master, present=20.0, absent=2.0, late_in=0.5, half_day=1.0,
            sunday_present=4.0, coff=0.0, pl=3.0, overtime_hrs=10.0, installment=500.0,
            mobile_exe_amt=100.0, addition=200.0, deduction=50.0
        )
        HrOfferAccessory.objects.create(id=1, offer=cls.offer_master, qty=1, amount=100, includes_in='2') # TH
        HrOfferAccessory.objects.create(id=2, offer=cls.offer_master, qty=1, amount=200, includes_in='1') # CTC

    @patch('hr_salary.models.FinancialMaster.objects')
    @patch('hr_salary.models.HrDepartment.objects')
    @patch('hr_salary.models.HrDesignation.objects')
    @patch('hr_salary.models.HrGrade.objects')
    @patch('hr_salary.models.HrEmpType.objects')
    @patch('hr_salary.models.HrOTHour.objects')
    @patch('hr_salary.models.HrDutyHour.objects')
    @patch('hr_salary.models.HrOfficeStaff.objects')
    @patch('hr_salary.models.HrSalaryMaster.objects')
    @patch('hr_salary.models.HrSalaryDetail.objects')
    @patch('hr_salary.models.HrOfferAccessory.objects')
    @patch('hr_salary.models.HrIncrementMaster.objects')
    @patch('hr_salary.models.HrIncrementAccessory.objects')
    def test_generate_summary_calculations(self, mock_increment_acc_objects, mock_increment_master_objects,
                                         mock_offer_acc_objects, mock_salary_detail_objects,
                                         mock_salary_master_objects, mock_office_staff_objects,
                                         mock_duty_hour_objects, mock_ot_hour_objects, mock_emp_type_objects,
                                         mock_grade_objects, mock_designation_objects, mock_department_objects,
                                         mock_financial_master_objects):
        # Configure mocks to return predefined data
        mock_financial_master_objects.get.return_value = self.setUpTestData.financial_master.financial_year_data
        mock_department_objects.all.return_value = [self.setUpTestData.hr_department]
        mock_designation_objects.all.return_value = [self.setUpTestData.hr_designation]
        mock_grade_objects.all.return_value = [self.setUpTestData.hr_grade]
        mock_emp_type_objects.all.return_value = [self.setUpTestData.hr_emp_type]
        mock_ot_hour_objects.all.return_value = [self.setUpTestData.hr_ot_hour]
        mock_duty_hour_objects.all.return_value = [self.setUpTestData.hr_duty_hour]
        
        mock_salary_master_objects.filter.return_value = MagicMock(
            values_list=MagicMock(return_value=MagicMock(flat=True, return_value=["EMP001"])),
            get=MagicMock(return_value=self.setUpTestData.hr_salary_master)
        )
        mock_office_staff_objects.filter.return_value = MagicMock(
            filter=MagicMock(return_value=MagicMock(
                select_related=MagicMock(return_value=[self.setUpTestData.hr_office_staff])
            ))
        )
        mock_salary_detail_objects.get.return_value = self.setUpTestData.hr_salary_detail
        mock_offer_acc_objects.filter.return_value = [
            HrOfferAccessory(qty=1, amount=100, includes_in='2'), # TH
            HrOfferAccessory(qty=1, amount=200, includes_in='1') # CTC
        ]
        mock_increment_master_objects.get.side_effect = HrIncrementMaster.DoesNotExist
        
        generator = SalarySummaryGenerator(
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            month_id=self.month_id,
            employee_type=self.employee_type,
            bg_group_id=self.bg_group_id,
            key=self.key
        )
        
        summaries = generator.generate_summary()
        self.assertEqual(len(summaries), 1)
        
        summary = summaries[0]
        self.assertEqual(summary.emp_id, "EMP001")
        self.assertEqual(summary.employee_name, "Mr.John Doe")
        self.assertEqual(summary.month_name, "July")
        self.assertEqual(summary.year, "2023") # Assuming 2023-2024 -> 2023 for July
        self.assertEqual(summary.department_name, "HR")
        self.assertEqual(summary.designation_name, "Manager [ MGR ]")
        self.assertEqual(summary.grade_name, "A")
        self.assertEqual(summary.status, "SAPL - Permanent")
        
        # Test basic salary calculations (values are approximations based on dummy _offer_cal)
        expected_basic = round(self.offer_master.salary * 0.40) # Offered Basic
        expected_da = round(self.offer_master.salary * 0.20)
        expected_hra = round(self.offer_master.salary * 0.15)
        
        self.assertEqual(summary.basic_salary, expected_basic)
        self.assertEqual(summary.da_allowance, expected_da)
        self.assertEqual(summary.hra_allowance, expected_hra)
        self.assertEqual(summary.gross_total_offered, 50000.0)

        # Days calculations
        day_of_month = monthrange(2023, 7)[1] # July 2023 has 31 days
        present_days = 20.0
        absent_days = 2.0
        pl_days = 3.0
        coff_days = 0.0
        total_days_eligible_for_pay = day_of_month - (absent_days - (pl_days + coff_days)) # 31 - (2 - 3) = 31 - (-1) = 32.0 (This calculation logic from ASP.NET seems odd or needs more context, but replicating)
        self.assertEqual(summary.present_days, present_days)
        self.assertEqual(summary.absent_days, absent_days)
        self.assertEqual(summary.pl_days, pl_days)
        self.assertEqual(summary.lwp_days, day_of_month - total_days_eligible_for_pay) # 31 - 32 = -1.0, again due to ASP.NET logic.

        # Pro-rata calculated components
        self.assertAlmostEqual(summary.basic_calculated, round((expected_basic * total_days_eligible_for_pay) / day_of_month), places=2)
        # Verify gross calculated
        self.assertGreater(summary.gross_total_calculated, 0)
        
        # PF, PTax, Deductions
        self.assertGreater(summary.pf_employee, 0)
        self.assertGreater(summary.p_tax, 0)
        self.assertGreater(summary.total_deductions, 0)
        
        # Net Pay
        self.assertGreater(summary.final_net_pay, 0)
        
        # Accesssories and OT
        # Based on HrOfferAccessory(qty=1, amount=100, includes_in='2') => accessories_th = 100
        # Overtime option is 2 (Yes), ot_hrs=1 (8.0 hours), duty_hrs=1 (8.0 hours)
        # OTRate = (50000 / (8 * 31)) * 1.5 = 302.21
        # OTAmt = 302.21 * 10 (overtime_hrs) = 3022.1
        # Misc Add = VehicleAllowance (500) + accessories_th (100) + accessories_both (0) + OTAmt (3022.1) + Addition (200) = 3822.1
        self.assertAlmostEqual(summary.misc_addition, 3822.0, places=0) # Rounded in ASP.NET
        
        # Att Bonus logic (AttBonusDays = 20 + 4 + 1 = 25)
        # DayOfMonth - (Holiday + SundayInMonth + 2) = 31 - (2 + 4 + 2) = 31 - 8 = 23
        # ((DayOfMonth + 2) - (Holiday + SundayInMonth)) = (31+2) - (2+4) = 33 - 6 = 27
        # Since 25 >= 23 AND 25 < 27, AttBonusType should be 1
        self.assertEqual(summary.attendance_bonus_type, 1)
        self.assertAlmostEqual(summary.attendance_bonus_amount, round((self.offer_master.salary * self.offer_master.att_bonus_per1) / 100), places=2) # 2500

    def test_financial_year_parsing(self):
        generator = SalarySummaryGenerator(1, 1, 7, 1, 1, "")
        self.assertEqual(generator._get_year_from_fin_year("2023-2024"), "2023")
        generator.month_id = 1 # Jan
        self.assertEqual(generator._get_year_from_fin_year("2023-2024"), "2024")
        generator.month_id = 0 # Invalid
        self.assertEqual(generator._get_year_from_fin_year("2023-2024"), "2023") # ASP.NET defaults to fy if invalid
        self.assertEqual(generator._get_year_from_fin_year(None), "")

    def test_month_name_retrieval(self):
        generator = SalarySummaryGenerator(1, 1, 1, 1, 1, "")
        self.assertEqual(generator._get_month_name(), "January")
        generator.month_id = 12
        self.assertEqual(generator._get_month_name(), "December")
        generator.month_id = 13
        self.assertEqual(generator._get_month_name(), "") # Should be empty for invalid month

# --- Integration Tests for Views ---
class SalarySummaryReportViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set up essential session variables
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1

        # Create minimal required database entries for the report to run without errors
        FinancialMaster.objects.create(id=1, company_id=1, financial_year="2023-2024")
        HrDepartment.objects.create(id=1, symbol="HR")
        HrDesignation.objects.create(id=1, type_name="Manager", symbol="MGR")
        HrGrade.objects.create(id=1, symbol="A")
        HrEmpType.objects.create(id=1, description="Permanent")
        HrOTHour.objects.create(id=1, hours=8.0)
        HrDutyHour.objects.create(id=1, hours=8.0)
        
        # Create a sample employee with salary data
        self.offer_master = HrOfferMaster.objects.create(
            offer_id=101, staff_type=1, type_of=1, salary=50000.0,
            duty_hrs=1, ot_hrs=1, overtime_option=2, ex_gratia=1000.0, vehicle_allowance=500.0,
            att_bonus_per1=5.0, att_bonus_per2=10.0, pf_employee_option=1, pf_company_option=1,
            increment=1
        )
        self.hr_office_staff = HrOfficeStaff.objects.create(
            emp_id="EMP001", user_id="U001", company_id=1, offer_id=self.offer_master.offer_id,
            financial_year_id=1, title="Mr", employee_name="Test Employee",
            department_id=1, bg_group=1, designation_id=1, grade_id=1,
            bank_account_no="**********", pf_no="PF001", pan_no="PAN001"
        )
        self.hr_salary_master = HrSalaryMaster.objects.create(
            id=1, emp_id="EMP001", financial_month=7, company_id=1,
            financial_year_id=1, increment=1
        )
        self.hr_salary_detail = HrSalaryDetail.objects.create(
            id=1, master=self.hr_salary_master, present=20.0, absent=2.0, late_in=0.5, half_day=1.0,
            sunday_present=4.0, coff=0.0, pl=3.0, overtime_hrs=10.0, installment=500.0,
            mobile_exe_amt=100.0, addition=200.0, deduction=50.0
        )
        HrOfferAccessory.objects.create(id=1, offer=self.offer_master, qty=1, amount=100, includes_in='2') # TH
        HrOfferAccessory.objects.create(id=2, offer=self.offer_master, qty=1, amount=200, includes_in='1') # CTC


    def test_report_view_success(self):
        # Test with valid parameters
        response = self.client.get(reverse('salary_summary_report'), {
            'BGGroupId': 1,
            'MonthId': 7, # July
            'EType': 1, # SAPL
            'Key': 'testkey'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/salarysummaryrow/list.html')
        self.assertContains(response, 'Salary Summary Report')
        self.assertContains(response, 'id="salarySummaryRowTable-container"')
        # Check if the table partial is loaded via HTMX
        self.assertContains(response, 'hx-get="/hr-salary/salary-report/table/?BGGroupId=1&MonthId=7&EType=1&Key=testkey"')

    def test_report_table_partial_success(self):
        # Test the HTMX partial view directly
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('salary_summary_table'), {
            'BGGroupId': 1,
            'MonthId': 7,
            'EType': 1,
            'Key': 'testkey'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/salarysummaryrow/_salarysummaryrow_table.html')
        self.assertContains(response, '<table id="salarySummaryRowTable"')
        self.assertContains(response, 'Test Employee')
        self.assertContains(response, 'July 2023') # Based on default mock values

    def test_report_view_missing_parameters(self):
        # Test with missing parameters
        response = self.client.get(reverse('salary_summary_report'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Missing essential report parameters.")
        self.assertNotContains(response, 'id="salarySummaryRowTable-container"') # Should not load table

    def test_report_table_partial_missing_parameters(self):
        # Test HTMX partial with missing parameters
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('salary_summary_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Missing parameters for report generation.")
        self.assertNotContains(response, '<table id="salarySummaryRowTable"')

    def test_cancel_button_redirect(self):
        response = self.client.get(reverse('hr_salary_report_selection'))
        self.assertEqual(response.status_code, 200) # Assuming it renders a selection page
        # More robust test would check for content of that selection page.

# --- Integration Tests for HrSalaryMaster CRUD Views (Pattern Demonstration) ---
class HrSalaryMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a sample record for update/delete tests
        cls.hr_salary_master = HrSalaryMaster.objects.create(
            id=10, emp_id="EMP999", financial_month=1, company_id=1, fin_year_id=1, increment=1
        )
    
    def setUp(self):
        self.client = Client()
        
    def test_list_view(self):
        response = self.client.get(reverse('hr_salary_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/hrsalarymaster/list.html')
        self.assertTrue('hr_salary_masters' in response.context)
        self.assertContains(response, "HR Salary Master Records")

    def test_list_view_htmx_partial(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_salary_master_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/hrsalarymaster/_hrsalarymaster_table.html')
        self.assertContains(response, '<table id="hrSalaryMasterTable"')
        self.assertContains(response, self.hr_salary_master.emp_id)

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_salary_master_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/hrsalarymaster/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, "Add HR Salary Master Record")

    def test_create_view_post_success(self):
        data = {
            'emp_id': 'EMP002',
            'financial_month': 8,
            'company_id': 1,
            'financial_year_id': 1,
            'increment': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_salary_master_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(HrSalaryMaster.objects.filter(emp_id='EMP002').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshHrSalaryMasterList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        data = {
            'emp_id': 'EMP003',
            'financial_month': 13, # Invalid month
            'company_id': 1,
            'financial_year_id': 1,
            'increment': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_salary_master_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertContains(response, "Financial month must be between 1 and 12.")
        self.assertFalse(HrSalaryMaster.objects.filter(emp_id='EMP003').exists())

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_salary_master_edit', args=[self.hr_salary_master.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/hrsalarymaster/form.html')
        self.assertContains(response, "Edit HR Salary Master Record")
        self.assertContains(response, self.hr_salary_master.emp_id)

    def test_update_view_post_success(self):
        updated_data = {
            'emp_id': self.hr_salary_master.emp_id,
            'financial_month': 9, # Change month
            'company_id': self.hr_salary_master.company_id,
            'financial_year_id': self.hr_salary_master.fin_year_id,
            'increment': self.hr_salary_master.increment
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_salary_master_edit', args=[self.hr_salary_master.pk]), updated_data, **headers)
        self.assertEqual(response.status_code, 204)
        self.hr_salary_master.refresh_from_db()
        self.assertEqual(self.hr_salary_master.financial_month, 9)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshHrSalaryMasterList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('hr_salary_master_delete', args=[self.hr_salary_master.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/hrsalarymaster/confirm_delete.html')
        self.assertContains(response, "Confirm Deletion")
        self.assertContains(response, self.hr_salary_master.emp_id)

    def test_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('hr_salary_master_delete', args=[self.hr_salary_master.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(HrSalaryMaster.objects.filter(pk=self.hr_salary_master.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshHrSalaryMasterList', response.headers['HX-Trigger'])
```

### Step 5: HTMX and Alpine.js Integration

The integration is demonstrated in the templates and views:

*   **HTMX for dynamic loading**:
    *   The `SalarySummaryReportView`'s `list.html` uses `hx-get` on a `div` (`salarySummaryRowTable-container`) to load `_salarysummaryrow_table.html` content via `{% url 'salary_summary_table' %}`. This ensures the table content is loaded after the initial page render.
    *   `hx-trigger="load, refreshSalarySummaryRowList from:body"` ensures the table reloads initially and whenever a custom HTMX event `refreshSalarySummaryRowList` is triggered (e.g., if a filter form were added).
    *   For the generic CRUD (HrSalaryMaster), similar `hx-trigger` and `hx-get` are used for modal forms and list refreshes. `HX-Trigger` headers are sent from `form_valid` and `delete` methods to signal the client to refresh the list.
*   **Alpine.js for UI state**:
    *   The modal visibility (`hidden` class toggle) is managed by Alpine.js's `_` attribute, allowing for simple client-side toggling without extra JavaScript.
*   **DataTables for list views**:
    *   The `_salarysummaryrow_table.html` and `_hrsalarymaster_table.html` partials include the `$(document).ready(function() { $('#tableName').DataTable({}); });` JavaScript call to initialize DataTables on the loaded table element. This provides out-of-the-box searching, sorting, and pagination.
*   **No custom JavaScript**: The strategy focuses on using HTMX and Alpine.js to handle all dynamic interactions, minimizing custom JavaScript.

---

### Final Notes

*   **Placeholders**: Remember to replace all placeholder values like `[FIELD_TYPE]`, `[TABLE_NAME]`, `[COLUMN_NAME]`, `[FRIENDLY_NAME]`, `[APP_NAME]` etc., with your actual database and project details.
*   **`base.html`**: The provided templates assume the existence of a `core/base.html` with necessary CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables.
*   **Business Logic Complexity**: The `SalarySummaryGenerator` class is where the majority of the original C# business logic has been translated. Ensure thorough testing and validation of all calculations, especially those involving date arithmetic, percentages, and conditional rules. The placeholder functions (`_offer_cal`, `_pf_cal`, `_p_tax_cal`, etc.) in `SalarySummaryGenerator` need precise implementation based on the exact formulas from the ASP.NET `clsFunctions`.
*   **Data Consistency**: Given `managed = False`, Django will not manage table creation or schema changes. Ensure your Django models perfectly match the existing database schema.
*   **Session Management**: The ASP.NET code relies on `Session["compid"]` and `Session["finyear"]`. In Django, `request.session` is used for this. Ensure these values are correctly populated in the Django session, possibly via an authentication flow or an initial setup page.
*   **Error Handling**: The Django views include basic `try-except` blocks. Comprehensive error logging and user-friendly error pages should be configured in a production environment.
*   **Security**: Always consider security best practices like input validation, authentication, and authorization (Django's `LoginRequiredMixin`, `PermissionRequiredMixin`) for a production system.