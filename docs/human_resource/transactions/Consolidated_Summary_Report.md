This document outlines a comprehensive plan for modernizing your existing ASP.NET application, "Consolidated_Summary_Report," by migrating it to a robust and scalable Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a smooth transition with minimal manual effort and maximum adherence to modern software engineering best practices.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The ASP.NET "Consolidated_Summary_Report" page primarily serves as a reporting tool, aggregating complex HR and salary data from various database tables, performing extensive calculations, and presenting them as a summary. The Crystal Reports integration will be replaced by a modern, interactive web-based data table, enhancing user experience and flexibility.

The core business value of this modernization is transforming a static, legacy report into a dynamic, browser-native dashboard component. This provides:
- **Enhanced Accessibility:** Users can view and interact with reports directly in their web browser without specialized software or plugins.
- **Improved Performance:** By leveraging Django's ORM and optimized database queries, data retrieval and processing can be significantly faster.
- **Modern User Experience:** Interactive DataTables allow for real-time searching, sorting, and pagination, making large datasets manageable and insightful.
- **Simplified Maintenance:** A unified Python/Django codebase is easier to develop, debug, and maintain compared to mixed ASP.NET/C#/Crystal Reports environments.
- **Future Scalability:** Django provides a strong foundation for adding new features, integrating with other systems, and handling increased data volumes.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code to define Django models.

**Instructions:**
The ASP.NET code interacts with numerous tables to compile the report data. We will infer the core entities and create Django models for them, specifying `managed = False` as these are existing tables. The final report itself is a computed aggregation, so we'll also define a Django model to represent its structure.

**Primary Tables Identified:**
- `tblHR_Salary_Master`
- `tblHR_OfficeStaff`
- `tblHR_Offer_Master`
- `tblHR_Salary_Details`
- `tblHR_Increment_Master`
- `tblHR_Departments`
- `tblHR_Designation`
- `tblHR_Grade`
- `tblFinancial_master`
- `tblHR_EmpType`
- `tblHR_Offer_Accessories`
- `tblHR_Increment_Accessories`
- `tblHR_OTHour`
- `tblHR_DutyHour`

For the purpose of this migration plan, we will focus on the most central tables for model definition and then a hypothetical `SalaryReportEntry` model that represents the output structure of the report, as it's the primary entity being "CRUD" operated on by the generic templates provided in the prompt.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic.

**Instructions:**
The ASP.NET code primarily performs a complex **Read** operation, culminating in a consolidated report. It involves:
- **Data Retrieval:** Fetching data from multiple HR-related tables based on query parameters (Company ID, Financial Year, Month ID, Employee Type, BG Group ID).
- **Extensive Calculations:** A significant portion of the C# code is dedicated to deriving various salary components (Basic, DA, HRA, PF, PTax, allowances, deductions), attendance details, and net pay. This logic is contained within a loop and relies heavily on helper functions (`clsFunctions`).
- **Data Aggregation:** The LINQ query at the end groups the calculated data, summing certain numeric fields.
- **Report Generation:** Using Crystal Reports to visualize the aggregated data.

There are no direct "Create," "Update," or "Delete" operations on this specific report page. However, to adhere to the prompt's template, we will define placeholder CRUD views for the `SalaryReportEntry` model, which would be relevant if this were a data entry screen for individual report entries. The core migration will focus on accurately re-implementing the complex read and calculation logic.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django UI mapping.

**Instructions:**
- **`CrystalReportViewer1`**: This will be replaced by a standard HTML table powered by DataTables for interactive display directly in the browser.
- **`GridView1`**: Although empty in the ASPX, the code-behind prepares a `DataTable` that could bind to it. This confirms the need for a DataTables implementation in Django.
- **`Panel1`**: A simple container, replaced by standard `div` elements.
- **`Cancel` Button**: A navigational element that redirects. In Django, this will be a link or button that triggers navigation.

### Step 4: Generate Django Code

We'll place this code within a new Django application, e.g., `hr_reports`.

#### 4.1 Models (`hr_reports/models.py`)

**Task:** Create Django models based on the identified database schema and a model representing the report's output structure.

**Instructions:**
- Define `managed = False` models for existing tables to map them directly to your legacy database.
- Define `SalaryReportEntry` to represent the structure of a single row in the final consolidated report. This model will NOT be `managed=False` as it is a calculated, transient entity for presentation rather than a direct database table.
- All complex business logic from `clsFunctions` will be encapsulated in a dedicated `SalaryCalculationService` class within `hr_reports/services.py`, and referenced by our views and models. This ensures the "fat model" principle is applied to the business logic layer, keeping our views "thin."

```python
from django.db import models
from django.utils.translation import gettext_lazy as _
import datetime

# --- Managed=False Models (Mapping to Existing Database Tables) ---
# These models are read-only mappings to your existing ASP.NET database tables.
# Ensure your Django settings.py has a database connection configured for this legacy database.

class HrOfficeStaff(models.Model):
    empid = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    userid = models.CharField(db_column='UserID', max_length=50, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    offerid = models.IntegerField(db_column='OfferId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    department = models.IntegerField(db_column='Department', blank=True, null=True)
    bggroup = models.IntegerField(db_column='BGGroup', blank=True, null=True)
    designation = models.IntegerField(db_column='Designation', blank=True, null=True)
    grade = models.IntegerField(db_column='Grade', blank=True, null=True)
    bankaccountno = models.CharField(db_column='BankAccountNo', max_length=50, blank=True, null=True)
    pfno = models.CharField(db_column='PFNo', max_length=50, blank=True, null=True)
    panno = models.CharField(db_column='PANNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = _('HR Office Staff')
        verbose_name_plural = _('HR Office Staff')

    def __str__(self):
        return f"{self.employeename} ({self.empid})"

class HrSalaryMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    empid = models.CharField(db_column='EmpId', max_length=50, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    fmonth = models.IntegerField(db_column='FMonth', blank=True, null=True)
    increment = models.IntegerField(db_column='Increment', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Master'
        verbose_name = _('HR Salary Master')
        verbose_name_plural = _('HR Salary Master')

    def __str__(self):
        return f"Salary for {self.empid} - Month {self.fmonth}"

class HrOfferMaster(models.Model):
    offerid = models.AutoField(db_column='OfferId', primary_key=True)
    stafftype = models.IntegerField(db_column='StaffType', blank=True, null=True)
    typeof = models.IntegerField(db_column='TypeOf', blank=True, null=True)
    salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2, blank=True, null=True)
    dutyhrs = models.IntegerField(db_column='DutyHrs', blank=True, null=True)
    othrs = models.IntegerField(db_column='OTHrs', blank=True, null=True)
    overtime = models.IntegerField(db_column='OverTime', blank=True, null=True)
    exgratia = models.DecimalField(db_column='ExGratia', max_digits=18, decimal_places=2, blank=True, null=True)
    vehicleallowance = models.DecimalField(db_column='VehicleAllowance', max_digits=18, decimal_places=2, blank=True, null=True)
    attbonusper1 = models.DecimalField(db_column='AttBonusPer1', max_digits=18, decimal_places=2, blank=True, null=True)
    attbonusper2 = models.DecimalField(db_column='AttBonusPer2', max_digits=18, decimal_places=2, blank=True, null=True)
    pfemployee = models.DecimalField(db_column='PFEmployee', max_digits=18, decimal_places=2, blank=True, null=True)
    pfcompany = models.DecimalField(db_column='PFCompany', max_digits=18, decimal_places=2, blank=True, null=True)
    increment = models.IntegerField(db_column='Increment', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = _('HR Offer Master')
        verbose_name_plural = _('HR Offer Master')

    def __str__(self):
        return f"Offer ID {self.offerid}"

class HrSalaryDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId', blank=True, null=True) # Corresponds to HrSalaryMaster.id
    present = models.DecimalField(db_column='Present', max_digits=18, decimal_places=2, blank=True, null=True)
    absent = models.DecimalField(db_column='Absent', max_digits=18, decimal_places=2, blank=True, null=True)
    latein = models.DecimalField(db_column='LateIn', max_digits=18, decimal_places=2, blank=True, null=True)
    halfday = models.DecimalField(db_column='HalfDay', max_digits=18, decimal_places=2, blank=True, null=True)
    sunday = models.DecimalField(db_column='Sunday', max_digits=18, decimal_places=2, blank=True, null=True)
    coff = models.DecimalField(db_column='Coff', max_digits=18, decimal_places=2, blank=True, null=True)
    pl = models.DecimalField(db_column='PL', max_digits=18, decimal_places=2, blank=True, null=True)
    overtimehrs = models.DecimalField(db_column='OverTimeHrs', max_digits=18, decimal_places=2, blank=True, null=True)
    installment = models.DecimalField(db_column='Installment', max_digits=18, decimal_places=2, blank=True, null=True)
    mobileexeamt = models.DecimalField(db_column='MobileExeAmt', max_digits=18, decimal_places=2, blank=True, null=True)
    addition = models.DecimalField(db_column='Addition', max_digits=18, decimal_places=2, blank=True, null=True)
    deduction = models.DecimalField(db_column='Deduction', max_digits=18, decimal_places=2, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Details'
        verbose_name = _('HR Salary Detail')
        verbose_name_plural = _('HR Salary Details')

    def __str__(self):
        return f"Detail for MId {self.mid}"


# Other lookup models (e.g., tblHR_Departments, tblHR_Designation, tblHR_Grade, tblFinancial_master, etc.)
# would also be defined here with managed=False if direct ORM access is needed.
# For simplicity, we'll assume direct queries or simpler lookups in the service layer.

# --- Report Output Model ---
# This model represents the structure of the *calculated* consolidated salary report row.
# It is not directly mapped to a database table but is generated by the SalaryCalculationService.

class SalaryReportEntry:
    def __init__(self, **kwargs):
        self.empid = kwargs.get('empid')
        self.compid = kwargs.get('compid')
        self.employee_name = kwargs.get('employee_name')
        self.month = kwargs.get('month')
        self.year = kwargs.get('year')
        self.department = kwargs.get('department')
        self.designation = kwargs.get('designation')
        self.status = kwargs.get('status')
        self.grade = kwargs.get('grade')
        self.basic = kwargs.get('basic', 0.0)
        self.da = kwargs.get('da', 0.0)
        self.hra = kwargs.get('hra', 0.0)
        self.conveyance = kwargs.get('conveyance', 0.0)
        self.education = kwargs.get('education', 0.0)
        self.medical = kwargs.get('medical', 0.0)
        self.sunday_p = kwargs.get('sunday_p', 0.0)
        self.gross_total = kwargs.get('gross_total', 0.0)
        self.attendance_bonus = kwargs.get('attendance_bonus', 0.0)
        self.special_allowance = kwargs.get('special_allowance', 0.0)
        self.exgratia = kwargs.get('exgratia', 0.0)
        self.travelling_allowance = kwargs.get('travelling_allowance', 0.0)
        self.miscellaneous = kwargs.get('miscellaneous', 0.0)
        self.total = kwargs.get('total', 0.0)
        self.net_pay = kwargs.get('net_pay', 0.0)
        self.working_days = kwargs.get('working_days', 0.0)
        self.present_days = kwargs.get('present_days', 0.0)
        self.absent_days = kwargs.get('absent_days', 0.0)
        self.sunday = kwargs.get('sunday', 0.0)
        self.holiday = kwargs.get('holiday', 0.0)
        self.late_in = kwargs.get('late_in', 0.0)
        self.coff = kwargs.get('coff', 0.0)
        self.half_days = kwargs.get('half_days', 0.0)
        self.pl = kwargs.get('pl', 0.0)
        self.lwp = kwargs.get('lwp', 0.0)
        self.pf_of_employee = kwargs.get('pf_of_employee', 0.0)
        self.p_tax = kwargs.get('p_tax', 0.0)
        self.personal_loan_install = kwargs.get('personal_loan_install', 0.0)
        self.mobile_bill = kwargs.get('mobile_bill', 0.0)
        self.miscellaneous2 = kwargs.get('miscellaneous2', 0.0)
        self.total2 = kwargs.get('total2', 0.0)
        self.emp_ac_no = kwargs.get('emp_ac_no')
        self.date = kwargs.get('date')
        self.basic_cal = kwargs.get('basic_cal', 0.0)
        self.da_cal = kwargs.get('da_cal', 0.0)
        self.hra_cal = kwargs.get('hra_cal', 0.0)
        self.conveyance_cal = kwargs.get('conveyance_cal', 0.0)
        self.education_cal = kwargs.get('education_cal', 0.0)
        self.medical_cal = kwargs.get('medical_cal', 0.0)
        self.gross_total_cal = kwargs.get('gross_total_cal', 0.0)
        self.att_bonus_type = kwargs.get('att_bonus_type', 0.0)
        self.att_bonus_amt = kwargs.get('att_bonus_amt', 0.0)
        self.pf_no = kwargs.get('pf_no')
        self.pan_no = kwargs.get('pan_no')
        self.path = kwargs.get('path') # For hyperlinked offer letter
        self.sn = kwargs.get('sn') # Serial Number, for display

    # For the generic CRUD template, we need a 'pk' equivalent.
    # Since this is a calculated model, a simple ID can be assigned for view operations.
    @property
    def pk(self):
        return self.empid # Use EmpId as a unique identifier for URL routing

    def __str__(self):
        return f"Report for {self.employee_name} ({self.month} {self.year})"

    # Business logic methods (if any specific to a single report entry) go here.
    # However, the primary calculation logic is in the SalaryCalculationService.

```

#### 4.1.1 Services (`hr_reports/services.py`)

**Task:** Encapsulate the complex calculation and data retrieval logic from the ASP.NET code-behind's `clsFunctions` and the main `Page_Init` method.

**Instructions:**
- This class will house the "fat model" logic that orchestrates data retrieval from various `managed=False` Django models and performs the extensive calculations.
- It will return a list of `SalaryReportEntry` objects.
- **Note:** Exact calculation logic details will be simplified or stubbed out where the specific database rules or full `clsFunctions` implementations are not available. This serves as a structural blueprint.

```python
import datetime
import calendar
import secrets
from decimal import Decimal, ROUND_HALF_UP
from django.db import connection # For raw SQL if needed, but prefer ORM
from django.utils import timezone
from .models import HrOfficeStaff, HrSalaryMaster, HrOfferMaster, HrSalaryDetail, SalaryReportEntry

class SalaryCalculationService:
    @staticmethod
    def _get_lookup_value(table_name, id_value, column_name, filter_column='Id'):
        """Helper to get a lookup value from a simple table."""
        # This function would query specific tables (e.g., tblHR_Departments)
        # For demonstration, we'll return a placeholder or a simple query
        try:
            if table_name == 'tblHR_Departments':
                # Example: HrDepartment.objects.using('legacy_db').get(id=id_value).symbol
                # For now, a placeholder
                return f"Dept_{id_value}"
            elif table_name == 'tblHR_Designation':
                return f"Desig_{id_value}"
            elif table_name == 'tblHR_Grade':
                return f"Grade_{id_value}"
            elif table_name == 'tblHR_EmpType':
                return f"EmpType_{id_value}"
            elif table_name == 'tblFinancial_master':
                # Example: FinancialMaster.objects.using('legacy_db').get(compid=id_value).finyear
                # For now, a placeholder
                return "2023-2024" # Example financial year
            elif table_name == 'tblHR_OTHour':
                return 8 # Example hours
            elif table_name == 'tblHR_DutyHour':
                return 8 # Example hours
        except Exception:
            return None # Or handle more robustly
        return None


    @staticmethod
    def _offer_cal(gross_salary, calc_type, factor, staff_type):
        """Simulates fun.Offer_Cal for salary components."""
        # This function would implement the logic from the C# fun.Offer_Cal
        # For simplicity, returning a fixed percentage of gross_salary
        # In a real scenario, this would involve detailed rules based on type, factor, staff_type
        gross_salary = Decimal(gross_salary)
        if calc_type == 1: # Basic
            return (gross_salary * Decimal('0.40')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        elif calc_type == 2: # DA
            return (gross_salary * Decimal('0.10')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        elif calc_type == 3: # HRA
            return (gross_salary * Decimal('0.20')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        elif calc_type == 4: # Conveyance
            return (gross_salary * Decimal('0.05')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        elif calc_type == 5: # Education
            return (gross_salary * Decimal('0.05')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        elif calc_type == 6: # Medical
            return (gross_salary * Decimal('0.05')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        return Decimal('0.00')

    @staticmethod
    def _pf_cal(calculated_gross, pf_type, pf_percentage_setting):
        """Simulates fun.Pf_Cal."""
        # In real scenario, this would use the offer's PFEmployee/PFCompany percentage
        calculated_gross = Decimal(calculated_gross)
        pf_percentage_setting = Decimal(pf_percentage_setting)
        # Assuming PF is a percentage of calculated gross, capped at some limit
        # This is a simplification; actual PF rules can be complex.
        pf_amount = (calculated_gross * (pf_percentage_setting / Decimal('100'))).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        return min(pf_amount, Decimal('1800.00')) # Example cap

    @staticmethod
    def _p_tax_cal(gross_for_ptax, month_id_str):
        """Simulates fun.PTax_Cal based on income slabs."""
        # Simplified example for Professional Tax calculation
        gross_for_ptax = Decimal(gross_for_ptax)
        if gross_for_ptax <= 10000:
            return Decimal('0.00')
        elif gross_for_ptax <= 20000:
            return Decimal('150.00')
        else:
            return Decimal('200.00')
        
    @staticmethod
    def _get_working_days(fin_year_id, month_id):
        """Simulates fun.WorkingDays - could be from a config table."""
        # For simplicity, assumes standard days in month
        return Decimal(calendar.monthrange(fin_year_id, month_id)[1])

    @staticmethod
    def _count_sundays(year, month_id):
        """Simulates fun.CountSundays."""
        num_sundays = 0
        for day in range(1, calendar.monthrange(year, month_id)[1] + 1):
            if datetime.date(year, month_id, day).weekday() == calendar.SUNDAY:
                num_sundays += 1
        return Decimal(num_sundays)

    @staticmethod
    def _get_holiday(month_id, comp_id, fin_year_id):
        """Simulates fun.GetHoliday - from a Holiday model/table."""
        # Placeholder: Assume a fixed number of holidays for the month
        return Decimal('2.0')

    @staticmethod
    def _ot_rate(gross_salary, ot_hours_setting, duty_hours_setting, days_in_month):
        """Simulates fun.OTRate."""
        # Example formula: (Gross / Days in Month / Duty Hours) * OT Factor
        if days_in_month == 0 or duty_hours_setting == 0: return Decimal('0.00')
        hourly_rate = Decimal(gross_salary) / Decimal(days_in_month) / Decimal(duty_hours_setting)
        ot_factor = Decimal('2.0') # Assuming 2x for overtime
        return (hourly_rate * ot_factor).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def _ot_amt(ot_rate, ot_hours_worked):
        """Simulates fun.OTAmt."""
        return (Decimal(ot_rate) * Decimal(ot_hours_worked)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def _get_company_address(comp_id):
        """Simulates fun.CompAdd."""
        # Lookup from a Company model
        return "123 Business Park, City, State - 123456" # Placeholder


    def get_consolidated_report_data(self, bg_group_id, month_id, comp_id, fin_year_id, etype):
        report_entries = []

        # Step 1: Initial Employee/Salary Master Data Retrieval
        # Replicates StrEmpSal query
        salary_masters = HrSalaryMaster.objects.using('legacy_db').filter(
            compid=comp_id,
            finyearid=fin_year_id,
            # fmonth=month_id, # Original code had this commented out for some queries
        ).select_related('hroffice_staff_empid', 'hroffice_staff_offerid') # Prefetch related if relations exist

        if bg_group_id != 1:
            salary_masters = salary_masters.filter(hroffice_staff__bggroup=bg_group_id)

        # Order by EmpId as in original
        salary_masters = salary_masters.order_by('empid')

        sn = 1
        for salary_master in salary_masters:
            staff_data = HrOfficeStaff.objects.using('legacy_db').filter(empid=salary_master.empid).first()
            if not staff_data:
                continue # Skip if no staff data found

            # Extract current month and year from financial year logic
            fin_year_obj = self._get_lookup_value('tblFinancial_master', fin_year_id, 'FinYear') # Placeholder
            fin_year_parts = fin_year_obj.split('-')
            fy = int(fin_year_parts[0])
            ty = int(fin_year_parts[1])
            
            report_year = str(ty) if salary_master.fmonth in [1, 2, 3] else str(fy)
            report_month_name = datetime.date(1900, salary_master.fmonth, 1).strftime('%b') # Abbreviated month name

            # Determine which offer/increment master to use
            # This logic is complex and relies on specific Increment values
            # Replicating StrSalMck, StrOfferMck, StrOfferM
            offer_master_inc = HrOfferMaster.objects.using('legacy_db').filter(offerid=staff_data.offerid).values_list('increment', flat=True).first()
            salary_master_inc = HrSalaryMaster.objects.using('legacy_db').filter(
                empid=salary_master.empid,
                fmonth=salary_master.fmonth,
                finyearid=fin_year_id
            ).values_list('increment', flat=True).first()

            offer_details = None
            if offer_master_inc == salary_master_inc:
                offer_details = HrOfferMaster.objects.using('legacy_db').filter(offerid=staff_data.offerid).first()
            else:
                # This would query tblHR_Increment_Master
                # For simplicity, assume HrOfferMaster has the necessary fields if Increment_Master is not mapped
                offer_details = HrOfferMaster.objects.using('legacy_db').filter(offerid=staff_data.offerid, increment=salary_master_inc).first() # Simplified
            
            if not offer_details:
                continue # Skip if no offer details found

            gross_salary = Decimal(offer_details.salary if offer_details.salary else 0.0)

            # Salary Component Calculations (using _offer_cal)
            basic = self._offer_cal(gross_salary, 1, 1, offer_details.stafftype)
            da = self._offer_cal(gross_salary, 2, 1, offer_details.typeof)
            hra = self._offer_cal(gross_salary, 3, 1, offer_details.typeof)
            conveyance = self._offer_cal(gross_salary, 4, 1, offer_details.typeof)
            education = self._offer_cal(gross_salary, 5, 1, offer_details.typeof)
            medical = self._offer_cal(gross_salary, 6, 1, offer_details.typeof)

            # Employee Status
            emp_type_desc = self._get_lookup_value('tblHR_EmpType', offer_details.stafftype, 'Description')
            status = ""
            if offer_details.typeof == 1:
                status = f"SAPL - {emp_type_desc}"
            elif offer_details.typeof == 2:
                status = f"NEHA - {emp_type_desc}"

            # Salary Details (from tblHR_Salary_Details)
            salary_detail = HrSalaryDetail.objects.using('legacy_db').filter(
                mid=salary_master.id # Assuming MId links to HrSalaryMaster.id
            ).first()

            # --- Attendance and Leave Calculations ---
            # This part requires careful replication of C# fun functions
            day_of_month = Decimal(calendar.monthrange(int(report_year), salary_master.fmonth)[1])
            working_days = self._get_working_days(fin_year_id, salary_master.fmonth)
            present_days = Decimal(salary_detail.present if salary_detail and salary_detail.present is not None else 0.0)
            absent_days = Decimal(salary_detail.absent if salary_detail and salary_detail.absent is not None else 0.0)
            pl_days = Decimal(salary_detail.pl if salary_detail and salary_detail.pl is not None else 0.0)
            coff_days = Decimal(salary_detail.coff if salary_detail and salary_detail.coff is not None else 0.0)
            half_days = Decimal(salary_detail.halfday if salary_detail and salary_detail.halfday is not None else 0.0)
            sunday_paid_days = Decimal(salary_detail.sunday if salary_detail and salary_detail.sunday is not None else 0.0)
            late_in_hours = Decimal(salary_detail.latein if salary_detail and salary_detail.latein is not None else 0.0)
            overtime_hrs = Decimal(salary_detail.overtimehrs if salary_detail and salary_detail.overtimehrs is not None else 0.0)
            installment_amt = Decimal(salary_detail.installment if salary_detail and salary_detail.installment is not None else 0.0)
            mobile_bill_amt = Decimal(salary_detail.mobileexeamt if salary_detail and salary_detail.mobileexeamt is not None else 0.0)
            addition_amt = Decimal(salary_detail.addition if salary_detail and salary_detail.addition is not None else 0.0)
            deduction_amt = Decimal(salary_detail.deduction if salary_detail and salary_detail.deduction is not None else 0.0)

            sunday_in_month = self._count_sundays(int(report_year), salary_master.fmonth)
            holiday_days = self._get_holiday(salary_master.fmonth, comp_id, fin_year_id)

            total_days_worked = day_of_month - (absent_days - (pl_days + coff_days)) # Derived from original code logic
            lwp = day_of_month - total_days_worked

            # Calculated Salary Components
            cal_basic = (basic * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            cal_da = (da * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            cal_hra = (hra * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            cal_conveyance = (conveyance * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            cal_education = (education * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            cal_medical = (medical * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            cal_gross_total = (cal_basic + cal_da + cal_hra + cal_conveyance + cal_education + cal_medical).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

            pf_employee = self._pf_cal(cal_gross_total, 1, offer_details.pfemployee if offer_details.pfemployee else 0)
            cal_exgratia = (offer_details.exgratia * total_days_worked / day_of_month).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP) if offer_details.exgratia else Decimal('0.00')

            # Accessories (from tblHR_Offer_Accessories or tblHR_Increment_Accessories)
            accessories_ctc = Decimal('0.00')
            accessories_th = Decimal('0.00')
            accessories_both = Decimal('0.00')
            # This would involve querying HrOfferAccessory or HrIncrementAccessory model
            # For simplicity, assume no accessories for now or hardcode values
            # e.g., HrOfferAccessory.objects.using('legacy_db').filter(mid=offer_details.offerid)
            # for acc in accessories_data: ... calculate based on IncludesIn
            
            # Overtime Amount
            ot_amount = Decimal('0.00')
            if offer_details.overtime == 2: # Assuming '2' means overtime is applicable
                ot_rate = self._ot_rate(gross_salary, 
                                        self._get_lookup_value('tblHR_OTHour', offer_details.othrs, 'Hours'),
                                        self._get_lookup_value('tblHR_DutyHour', offer_details.dutyhrs, 'Hours'),
                                        day_of_month)
                ot_amount = self._ot_amt(ot_rate, overtime_hrs)

            # Attendance Bonus
            att_bonus_type = 0
            att_bonus_amt = Decimal('0.00')
            att_bonus_days = present_days + sunday_paid_days + half_days
            threshold1 = (day_of_month - (holiday_days + sunday_in_month + 2))
            threshold2 = ((day_of_month + 2) - (holiday_days + sunday_in_month))
            
            if att_bonus_days >= threshold1 and att_bonus_days < threshold2:
                att_bonus_type = 1
                att_bonus_amt = (gross_salary * (offer_details.attbonusper1 if offer_details.attbonusper1 else 0) / Decimal('100')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            elif att_bonus_days >= threshold2:
                att_bonus_type = 2
                att_bonus_amt = (gross_salary * (offer_details.attbonusper2 if offer_details.attbonusper2 else 0) / Decimal('100')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

            # Miscellaneous Additions/Deductions
            misc_add = (Decimal(offer_details.vehicleallowance if offer_details.vehicleallowance else 0) + accessories_th + accessories_both + ot_amount + addition_amt).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            misc_deduct = deduction_amt.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

            # Professional Tax
            ptax_amt = self._p_tax_cal(cal_gross_total + att_bonus_amt + accessories_th + accessories_both + cal_exgratia + (offer_details.vehicleallowance if offer_details.vehicleallowance else 0) + addition_amt + ot_amount, str(salary_master.fmonth).zfill(2))
            
            total_deduct = (pf_employee + ptax_amt + installment_amt + mobile_bill_amt + misc_deduct).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            net_pay_before_deduct = (cal_gross_total + att_bonus_amt + cal_exgratia + misc_add).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            net_pay_final = (net_pay_before_deduct - total_deduct).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)


            report_entry = SalaryReportEntry(
                empid=staff_data.empid,
                compid=comp_id,
                employee_name=f"{staff_data.title}. {staff_data.employeename} [{staff_data.empid}]",
                month=report_month_name,
                year=report_year,
                department=self._get_lookup_value('tblHR_Departments', staff_data.department, 'Symbol'),
                designation=self._get_lookup_value('tblHR_Designation', staff_data.designation, 'Type'),
                status=status,
                grade=self._get_lookup_value('tblHR_Grade', staff_data.grade, 'Symbol'),
                basic=basic,
                da=da,
                hra=hra,
                conveyance=conveyance,
                education=education,
                medical=medical,
                sunday_p=sunday_paid_days,
                gross_total=gross_salary, # This was from offer master
                attendance_bonus=att_bonus_amt, # Set to 0 in C# then overwritten? Need to confirm C# logic
                special_allowance=Decimal('0.00'), # Not calculated in provided C#
                exgratia=cal_exgratia,
                travelling_allowance=Decimal('0.00'), # Not calculated in provided C#
                miscellaneous=misc_add,
                total=net_pay_before_deduct, # Corresponds to Total in C# dt
                net_pay=net_pay_final,
                working_days=working_days,
                present_days=present_days,
                absent_days=absent_days,
                sunday=sunday_in_month,
                holiday=holiday_days,
                late_in=late_in_hours,
                coff=coff_days,
                half_days=half_days,
                pl=pl_days,
                lwp=lwp,
                pf_of_employee=pf_employee,
                p_tax=ptax_amt,
                personal_loan_install=installment_amt,
                mobile_bill=mobile_bill_amt,
                miscellaneous2=misc_deduct,
                total2=total_deduct, # Corresponds to Total2 in C# dt
                emp_ac_no=staff_data.bankaccountno,
                date=timezone.now().strftime('%d/%m/%Y'), # fun.FromDateDMY(fun.getCurrDate())
                basic_cal=cal_basic,
                da_cal=cal_da,
                hra_cal=cal_hra,
                conveyance_cal=cal_conveyance,
                education_cal=cal_education,
                medical_cal=cal_medical,
                gross_total_cal=cal_gross_total,
                att_bonus_type=att_bonus_type,
                att_bonus_amt=att_bonus_amt,
                pf_no=staff_data.pfno,
                pan_no=staff_data.panno,
                path=f"/hr/offer_letter/{staff_data.offerid}/?T=4&Key={secrets.token_urlsafe(16)}&EType={etype}&MonthId={month_id}&BGGroupId={bg_group_id}&Increment={offer_details.increment}&ModId=12&SubModId=25",
                sn=sn
            )
            report_entries.append(report_entry)
            sn += 1
        
        # Implement LINQ grouping and summing here if necessary (the C# LINQ query)
        # The C# code performed a final GROUP BY EmpId and SUM.
        # This can be done in Python on the `report_entries` list
        grouped_data = {}
        for entry in report_entries:
            if entry.empid not in grouped_data:
                grouped_data[entry.empid] = {
                    'entry': entry, # Store the first entry for non-summed fields
                    'exgratia_sum': Decimal('0.00'),
                    'miscellaneous_sum': Decimal('0.00'),
                    'total_sum': Decimal('0.00'),
                    'net_pay_sum': Decimal('0.00'),
                    'pf_of_employee_sum': Decimal('0.00'),
                    'p_tax_sum': Decimal('0.00'),
                    'personal_loan_install_sum': Decimal('0.00'),
                    'mobile_bill_sum': Decimal('0.00'),
                    'miscellaneous2_sum': Decimal('0.00'),
                    'total2_sum': Decimal('0.00'),
                    'basic_cal_sum': Decimal('0.00'),
                    'da_cal_sum': Decimal('0.00'),
                    'hra_cal_sum': Decimal('0.00'),
                    'conveyance_cal_sum': Decimal('0.00'),
                    'education_cal_sum': Decimal('0.00'),
                    'medical_cal_sum': Decimal('0.00'),
                    'gross_total_cal_sum': Decimal('0.00'),
                    'att_bonus_amt_sum': Decimal('0.00'),
                }
            
            # Summing specific fields as per C# LINQ query
            grouped_data[entry.empid]['exgratia_sum'] += entry.exgratia
            grouped_data[entry.empid]['miscellaneous_sum'] += entry.miscellaneous
            grouped_data[entry.empid]['total_sum'] += entry.total
            grouped_data[entry.empid]['net_pay_sum'] += entry.net_pay
            grouped_data[entry.empid]['pf_of_employee_sum'] += entry.pf_of_employee
            grouped_data[entry.empid]['p_tax_sum'] += entry.p_tax
            grouped_data[entry.empid]['personal_loan_install_sum'] += entry.personal_loan_install
            grouped_data[entry.empid]['mobile_bill_sum'] += entry.mobile_bill
            grouped_data[entry.empid]['miscellaneous2_sum'] += entry.miscellaneous2
            grouped_data[entry.empid]['total2_sum'] += entry.total2
            grouped_data[entry.empid]['basic_cal_sum'] += entry.basic_cal
            grouped_data[entry.empid]['da_cal_sum'] += entry.da_cal
            grouped_data[entry.empid]['hra_cal_sum'] += entry.hra_cal
            grouped_data[entry.empid]['conveyance_cal_sum'] += entry.conveyance_cal
            grouped_data[entry.empid]['education_cal_sum'] += entry.education_cal
            grouped_data[entry.empid]['medical_cal_sum'] += entry.medical_cal
            grouped_data[entry.empid]['gross_total_cal_sum'] += entry.gross_total_cal
            grouped_data[entry.empid]['att_bonus_amt_sum'] += entry.att_bonus_amt
        
        final_report_list = []
        for empid, data in grouped_data.items():
            original_entry = data['entry']
            final_entry = SalaryReportEntry(
                empid=original_entry.empid,
                compid=original_entry.compid,
                employee_name=original_entry.employee_name,
                month=original_entry.month,
                year=original_entry.year,
                department=original_entry.department,
                designation=original_entry.designation,
                status=original_entry.status,
                grade=original_entry.grade,
                basic=original_entry.basic,
                da=original_entry.da,
                hra=original_entry.hra,
                conveyance=original_entry.conveyance,
                education=original_entry.education,
                medical=original_entry.medical,
                sunday_p=original_entry.sunday_p,
                gross_total=original_entry.gross_total,
                attendance_bonus=data['att_bonus_amt_sum'],
                special_allowance=original_entry.special_allowance,
                exgratia=data['exgratia_sum'],
                travelling_allowance=original_entry.travelling_allowance,
                miscellaneous=data['miscellaneous_sum'],
                total=data['total_sum'],
                net_pay=data['net_pay_sum'],
                working_days=original_entry.working_days,
                present_days=original_entry.present_days,
                absent_days=original_entry.absent_days,
                sunday=original_entry.sunday,
                holiday=original_entry.holiday,
                late_in=original_entry.late_in,
                coff=original_entry.coff,
                half_days=original_entry.half_days,
                pl=original_entry.pl,
                lwp=original_entry.lwp,
                pf_of_employee=data['pf_of_employee_sum'],
                p_tax=data['p_tax_sum'],
                personal_loan_install=data['personal_loan_install_sum'],
                mobile_bill=data['mobile_bill_sum'],
                miscellaneous2=data['miscellaneous2_sum'],
                total2=data['total2_sum'],
                emp_ac_no=original_entry.emp_ac_no,
                date=original_entry.date,
                basic_cal=data['basic_cal_sum'],
                da_cal=data['da_cal_sum'],
                hra_cal=data['hra_cal_sum'],
                conveyance_cal=data['conveyance_cal_sum'],
                education_cal=data['education_cal_sum'],
                medical_cal=data['medical_cal_sum'],
                gross_total_cal=data['gross_total_cal_sum'],
                att_bonus_type=original_entry.att_bonus_type,
                att_bonus_amt=data['att_bonus_amt_sum'],
                pf_no=original_entry.pf_no,
                pan_no=original_entry.pan_no,
                path=original_entry.path,
                sn=original_entry.sn # SN for grouped data might need recalculation if order changes
            )
            final_report_list.append(final_entry)

        return final_report_list
```

#### 4.2 Forms (`hr_reports/forms.py`)

**Task:** Define a Django form for the `SalaryReportEntry` model.

**Instructions:**
- Since `SalaryReportEntry` is a calculated output and not a direct database model, a `ModelForm` is not directly applicable in the typical sense. We will create a custom `Form` to represent its fields. For the purpose of adhering to the prompt's `ModelForm` template, we will simulate it, acknowledging that real "add/edit" operations on a *report entry* are unusual.

```python
from django import forms
from .models import SalaryReportEntry # We'll treat this like a model for form definition purposes

class SalaryReportEntryForm(forms.Form):
    # This form is for demonstration as per the prompt's template.
    # In a real reporting scenario, you wouldn't typically have a form to 'add' or 'edit' report entries,
    # as they are derived data. This would be used if you were modeling the *input* data instead.
    empid = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    employee_name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    # Add other fields here that you might want to display or filter by
    # Example:
    month = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    net_pay = forms.DecimalField(
        max_digits=18, decimal_places=2,
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.pop('instance', None)
        super().__init__(*args, **kwargs)
        if instance:
            # Populate form fields from instance for 'edit' scenario
            self.fields['empid'].initial = instance.empid
            self.fields['employee_name'].initial = instance.employee_name
            self.fields['month'].initial = instance.month
            self.fields['net_pay'].initial = instance.net_pay
            # ... set initial values for all fields from the instance
    
    def save(self, commit=True):
        # This method is not truly 'saving' to a database, as SalaryReportEntry is computed.
        # It would simulate updating/creating a report entry for the sake of the template.
        # In a real scenario, this would trigger re-calculation or update underlying source data.
        empid = self.cleaned_data.get('empid')
        # Here you would typically find the original data source and update it
        # For this report, it's a no-op or a complex re-calculation.
        return SalaryReportEntry(**self.cleaned_data) # Returns a mock instance

```

#### 4.3 Views (`hr_reports/views.py`)

**Task:** Implement the report display using a `ListView` and placeholder CRUD operations as requested by the template.

**Instructions:**
- The `SalaryReportListView` will be the primary view for displaying the report. It will call `SalaryCalculationService` to get the data.
- The `SalaryReportTablePartialView` is specifically for HTMX to fetch and render the table body.
- `SalaryReportCreateView`, `SalaryReportUpdateView`, `SalaryReportDeleteView` are included to strictly adhere to the prompt's template. They will be dummy implementations since `SalaryReportEntry` is a calculated model, not a direct database entity for CRUD.

```python
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import redirect, render
from .models import SalaryReportEntry
from .forms import SalaryReportEntryForm
from .services import SalaryCalculationService
import secrets # For generating unique keys if needed for URL parameters

# Instantiate the service
salary_service = SalaryCalculationService()

class SalaryReportListView(ListView):
    # model = SalaryReportEntry # Not a real Django ORM model
    template_name = 'hr_reports/salarysummaryreport/list.html'
    context_object_name = 'salarysummaryreports' # Renamed for clarity in template
    
    # We will override get_queryset to use our service
    def get_queryset(self):
        # Extract query parameters from URL, mimicking ASP.NET Request.QueryString
        bg_group_id = self.request.GET.get('bg_group_id', 0)
        month_id = self.request.GET.get('month_id', 0)
        comp_id = self.request.session.get('compid', 0) # From session
        fin_year_id = self.request.session.get('finyear', 0) # From session
        etype = self.request.GET.get('etype', 0)

        try:
            bg_group_id = int(bg_group_id)
            month_id = int(month_id)
            comp_id = int(comp_id)
            fin_year_id = int(fin_year_id)
            etype = int(etype)
        except ValueError:
            messages.error(self.request, "Invalid report parameters.")
            return [] # Return empty list on invalid input

        if not all([month_id, comp_id, fin_year_id]):
            messages.warning(self.request, "Please provide all required parameters (Month, Company, Financial Year) to generate the report.")
            return []

        # Call the service to get the calculated data
        try:
            return salary_service.get_consolidated_report_data(bg_group_id, month_id, comp_id, fin_year_id, etype)
        except Exception as e:
            messages.error(self.request, f"Error generating report: {e}")
            return [] # Return empty list on error

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add any additional context if needed
        # Example: Pass parameters back to the template for display
        context['bg_group_id'] = self.request.GET.get('bg_group_id')
        context['month_id'] = self.request.GET.get('month_id')
        # Add address from service, mimicking ASP.NET's Report.SetParameterValue("Address")
        context['company_address'] = SalaryCalculationService._get_company_address(self.request.session.get('compid', 0))
        return context

class SalaryReportTablePartialView(SalaryReportListView):
    # This view is for HTMX to load only the table content
    template_name = 'hr_reports/salarysummaryreport/_salarysummaryreport_table.html'

    # The get_queryset method is inherited from SalaryReportListView

class SalaryReportCreateView(View): # Using View for simple direct response to fulfill template
    # This view is a placeholder to fulfill the prompt's requirement for a CreateView.
    # In a real scenario, you would NOT typically create a "report entry" directly.
    # Instead, you would add/edit the *underlying data* that generates the report.
    def get(self, request, *args, **kwargs):
        form = SalaryReportEntryForm()
        return render(request, 'hr_reports/salarysummaryreport/_salarysummaryreport_form.html', {'form': form})

    def post(self, request, *args, **kwargs):
        form = SalaryReportEntryForm(request.POST)
        if form.is_valid():
            # In a real scenario, this would update underlying data sources
            # For a report entry, this is a simulated 'creation'
            form.save() # This just returns the mock instance
            messages.success(request, 'Salary Report Entry (simulated) added successfully.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSalaryReportEntryList'
                    }
                )
        # If form is not valid, re-render the form with errors
        return render(request, 'hr_reports/salarysummaryreport/_salarysummaryreport_form.html', {'form': form})


class SalaryReportUpdateView(View): # Using View for simple direct response to fulfill template
    # This view is a placeholder to fulfill the prompt's requirement for an UpdateView.
    # Retrieving an instance for a calculated report entry.
    def get_object(self, pk):
        # In a real scenario, you'd fetch a database object.
        # Here, we'll simulate finding the 'entry' by its primary key (empid).
        # This requires re-generating the entire report to find the specific entry.
        # This is highly inefficient and just for demonstration.
        bg_group_id = self.request.GET.get('bg_group_id', 0)
        month_id = self.request.GET.get('month_id', 0)
        comp_id = self.request.session.get('compid', 0)
        fin_year_id = self.request.session.get('finyear', 0)
        etype = self.request.GET.get('etype', 0)

        try:
            bg_group_id = int(bg_group_id)
            month_id = int(month_id)
            comp_id = int(comp_id)
            fin_year_id = int(fin_year_id)
            etype = int(etype)
        except ValueError:
            raise Http404("Invalid report parameters for lookup.")

        all_entries = salary_service.get_consolidated_report_data(bg_group_id, month_id, comp_id, fin_year_id, etype)
        for entry in all_entries:
            if str(entry.pk) == str(pk):
                return entry
        raise Http404("Salary Report Entry not found.")


    def get(self, request, pk, *args, **kwargs):
        instance = self.get_object(pk)
        form = SalaryReportEntryForm(instance=instance)
        return render(request, 'hr_reports/salarysummaryreport/_salarysummaryreport_form.html', {'form': form, 'instance': instance})

    def post(self, request, pk, *args, **kwargs):
        instance = self.get_object(pk)
        form = SalaryReportEntryForm(request.POST, instance=instance)
        if form.is_valid():
            # In a real scenario, this would update underlying data sources
            form.save() # This just returns the mock instance
            messages.success(request, 'Salary Report Entry (simulated) updated successfully.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSalaryReportEntryList'
                    }
                )
        return render(request, 'hr_reports/salarysummaryreport/_salarysummaryreport_form.html', {'form': form, 'instance': instance})


class SalaryReportDeleteView(View): # Using View for simple direct response to fulfill template
    # This view is a placeholder to fulfill the prompt's requirement for a DeleteView.
    # Deleting a "report entry" is illogical as it's computed.
    # In a real scenario, deletion would happen on the *underlying* data records.
    def get_object(self, pk):
        # Same simulation as update view
        bg_group_id = self.request.GET.get('bg_group_id', 0)
        month_id = self.request.GET.get('month_id', 0)
        comp_id = self.request.session.get('compid', 0)
        fin_year_id = self.request.session.get('finyear', 0)
        etype = self.request.GET.get('etype', 0)
        try:
            bg_group_id = int(bg_group_id)
            month_id = int(month_id)
            comp_id = int(comp_id)
            fin_year_id = int(fin_year_id)
            etype = int(etype)
        except ValueError:
            raise Http404("Invalid report parameters for lookup.")
        
        all_entries = salary_service.get_consolidated_report_data(bg_group_id, month_id, comp_id, fin_year_id, etype)
        for entry in all_entries:
            if str(entry.pk) == str(pk):
                return entry
        raise Http404("Salary Report Entry not found.")

    def get(self, request, pk, *args, **kwargs):
        instance = self.get_object(pk)
        return render(request, 'hr_reports/salarysummaryreport/confirm_delete.html', {'object': instance})

    def post(self, request, pk, *args, **kwargs):
        # In a real scenario, this would delete underlying data records
        # For a report entry, this is a simulated 'deletion'
        messages.success(request, 'Salary Report Entry (simulated) deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalaryReportEntryList'
                }
            )
        # Redirect to the list view, passing back the original parameters
        return redirect(reverse_lazy('salarysummaryreport_list') + f'?bg_group_id={request.GET.get("bg_group_id")}&month_id={request.GET.get("month_id")}&etype={request.GET.get("etype")}')

# View for Cancel button redirection (mimics ASP.NET's Response.Redirect)
class SalaryPrintRedirectView(View):
    def get(self, request, *args, **kwargs):
        month_id = request.GET.get('month_id', '')
        # Construct the target URL. This would be a specific URL in your Django app,
        # e.g., for a 'Salary Print' module.
        # For demonstration, redirect to a hypothetical salary_print_list view.
        return redirect(reverse_lazy('salary_print_list') + f'?month_id={month_id}')

```

#### 4.4 Templates (`hr_reports/templates/hr_reports/salarysummaryreport/`)

**Task:** Create templates for each view, incorporating HTMX, Alpine.js, and DataTables.

**Instructions:**
- `list.html`: The main report page, extends `core/base.html`.
- `_salarysummaryreport_table.html`: A partial template loaded via HTMX for the DataTables content.
- `_salarysummaryreport_form.html`: A partial template for add/edit modals.
- `confirm_delete.html`: A partial template for delete confirmation modals.

**`list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Consolidated Salary Summary Report</h2>
        <!-- No direct 'Add New' for a report. This button is hypothetical for prompt. -->
        <!-- In a real scenario, this might link to a data entry page for underlying data. -->
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded hidden"
            hx-get="{% url 'salarysummaryreport_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Report Entry (Simulated)
        </button>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Report Parameters:</h3>
        <p><strong>Month:</strong> {{ month_id|default:"N/A" }}</p>
        <p><strong>Company Address:</strong> {{ company_address|default:"N/A" }}</p>
        <!-- Add more parameters if needed -->
    </div>

    <div id="salarysummaryreportTable-container"
         hx-trigger="load, refreshSalaryReportEntryList from:body"
         hx-get="{% url 'salarysummaryreport_table' %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
         hx-swap="innerHTML"
         class="bg-white p-4 rounded-lg shadow-md">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Cancel button, mimicking ASP.NET's -->
    <div class="text-center mt-6">
        <a href="{% url 'salary_print_redirect' %}?month_id={{ month_id|default:'' }}" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
           Cancel
        </a>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
        Alpine.data('reportLogic', () => ({
            init() {
                // Any specific Alpine.js initialization for the report
            }
        }));
    });
</script>
{% endblock %}
```

**`_salarysummaryreport_table.html`**
```html
<div class="overflow-x-auto">
    <table id="salarysummaryreportTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp ID</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Gross Total</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF Emp</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">P. Tax</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Net Pay</th>
                <!-- Add headers for all relevant fields from SalaryReportEntry -->
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in salarysummaryreports %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.empid }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <a href="{{ obj.path }}" target="_blank" class="text-blue-600 hover:underline">
                        {{ obj.employee_name }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.month }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.department }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.designation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.status }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap">{{ obj.gross_total_cal|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap">{{ obj.pf_of_employee|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap">{{ obj.p_tax|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right whitespace-nowrap font-semibold">{{ obj.net_pay|floatformat:2 }}</td>
                <!-- Add cells for all other fields -->
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <!-- These buttons are hypothetical for a report entry -->
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'salarysummaryreport_edit' obj.pk %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit (Simulated)
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'salarysummaryreport_delete' obj.pk %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete (Simulated)
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-gray-500">No report data available. Please check parameters.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#salarysummaryreportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [], // Disable initial sorting
            "pagingType": "full_numbers", // For pagination controls
            "scrollX": true // Enable horizontal scrolling for many columns
        });
    });
</script>
```

**`_salarysummaryreport_form.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance|yesno:'Edit,Add' }} Salary Report Entry (Simulated)</h3>
    <form hx-post="{{ request.path }}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save (Simulated)
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete (Simulated)</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the simulated report entry for <strong>"{{ object.employee_name }}" ({{ object.empid }})</strong>?</p>
    <p class="text-sm text-gray-500 mb-6">Note: This is a simulation. Deleting a report entry typically means removing underlying source data, which is beyond this single report page's scope.</p>

    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'salarysummaryreport_delete' object.pk %}?bg_group_id={{ request.GET.bg_group_id|default:0 }}&month_id={{ request.GET.month_id|default:0 }}&etype={{ request.GET.etype|default:0 }}"
            hx-swap="none">
            Delete (Simulated)
        </button>
    </div>
</div>
```

#### 4.5 URLs (`hr_reports/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
- Create paths for list, create, update, delete, and any partial views used with HTMX.
- Include query parameters for report filtering.

```python
from django.urls import path
from .views import (
    SalaryReportListView, 
    SalaryReportCreateView, 
    SalaryReportUpdateView, 
    SalaryReportDeleteView,
    SalaryReportTablePartialView,
    SalaryPrintRedirectView,
)

urlpatterns = [
    # Main Report View (GET parameters for filters)
    path('consolidated-summary-report/', SalaryReportListView.as_view(), name='salarysummaryreport_list'),
    
    # HTMX Partial for the DataTables content
    path('consolidated-summary-report/table/', SalaryReportTablePartialView.as_view(), name='salarysummaryreport_table'),

    # CRUD operations for SalaryReportEntry (Simulated, as explained)
    path('consolidated-summary-report/add/', SalaryReportCreateView.as_view(), name='salarysummaryreport_add'),
    path('consolidated-summary-report/edit/<str:pk>/', SalaryReportUpdateView.as_view(), name='salarysummaryreport_edit'),
    path('consolidated-summary-report/delete/<str:pk>/', SalaryReportDeleteView.as_view(), name='salarysummaryreport_delete'),

    # Redirect for Cancel button (mimics ASP.NET flow)
    path('salary-print-redirect/', SalaryPrintRedirectView.as_view(), name='salary_print_redirect'),
]
```
**Project `urls.py` inclusion:**
Ensure your project's `urls.py` includes this app's URLs:
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('hr/', include('hr_reports.urls')), # Assuming 'hr_reports' is your app name
    # You might also have a URL for the hypothetical 'salary_print_list'
    path('salary-print-list/', lambda request: HttpResponse("This is the Salary Print page"), name='salary_print_list'),
]
```

#### 4.6 Tests (`hr_reports/tests.py`)

**Task:** Write tests for the models and views, ensuring comprehensive coverage.

**Instructions:**
- Include unit tests for the `managed=False` models to confirm their mapping and basic attributes.
- Write unit tests for the `SalaryCalculationService` methods to ensure calculations are correct.
- Add integration tests for all views (list, simulated create, update, delete) and HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import Http404
from django.contrib.messages import get_messages
from decimal import Decimal
import datetime
import calendar

# Import actual models (managed=False)
from .models import HrOfficeStaff, HrSalaryMaster, HrOfferMaster, HrSalaryDetail
# Import the calculated model and service
from .models import SalaryReportEntry
from .services import SalaryCalculationService

class SalaryCalculationServiceTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for models used by the service (assuming 'legacy_db' is configured)
        # In a real test, you'd use a test database or mock the ORM calls.
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.month_id = 10 # October
        cls.etype = 1
        cls.bg_group_id = 1 # Office staff group

        # Ensure database is configured for 'legacy_db' in settings.py
        # For actual testing without a legacy db, you'd mock these objects.
        # Example for mocking:
        # from unittest.mock import patch, MagicMock
        # @patch('hr_reports.services.HrOfficeStaff.objects')
        # def test_something(self, mock_hr_office_staff_objects):
        #     mock_hr_office_staff_objects.using.return_value.filter.return_value.first.return_value = MagicMock(empid='E001')

        # Create dummy instances for tests (will write to default DB unless 'legacy_db' is used explicitly in tests)
        # To truly test against legacy_db without actual connection, you need to mock the ORM calls.
        # For simplicity here, we create some mock data that the service can process.
        # NOTE: This setup uses the default Django test DB, not a mocked legacy DB.
        # For full isolation, use 'unittest.mock' or actual test database setup for 'legacy_db'.

        # Staff
        HrOfficeStaff.objects.create(
            empid='EMP001', compid=cls.comp_id, offerid=101, finyearid=cls.fin_year_id,
            title='Mr', employeename='John Doe', department=1, bggroup=cls.bg_group_id,
            designation=1, grade=1, bankaccountno='**********', pfno='PF123', panno='PAN123'
        )
        HrOfficeStaff.objects.create(
            empid='EMP002', compid=cls.comp_id, offerid=102, finyearid=cls.fin_year_id,
            title='Ms', employeename='Jane Smith', department=2, bggroup=2,
            designation=2, grade=2, bankaccountno='**********', pfno='PF456', panno='PAN456'
        )

        # Salary Master
        HrSalaryMaster.objects.create(
            id=1, empid='EMP001', compid=cls.comp_id, finyearid=cls.fin_year_id, fmonth=cls.month_id, increment=1
        )
        HrSalaryMaster.objects.create(
            id=2, empid='EMP002', compid=cls.comp_id, finyearid=cls.fin_year_id, fmonth=cls.month_id, increment=1
        )

        # Offer Master
        HrOfferMaster.objects.create(
            offerid=101, stafftype=1, typeof=1, salary=Decimal('50000.00'), dutyhrs=8, othrs=1,
            overtime=2, exgratia=Decimal('1000.00'), vehicleallowance=Decimal('500.00'),
            attbonusper1=5, attbonusper2=10, pfemployee=12, pfcompany=13, increment=1
        )
        HrOfferMaster.objects.create(
            offerid=102, stafftype=2, typeof=2, salary=Decimal('40000.00'), dutyhrs=8, othrs=1,
            overtime=1, exgratia=Decimal('0.00'), vehicleallowance=Decimal('0.00'),
            attbonusper1=5, attbonusper2=10, pfemployee=12, pfcompany=13, increment=1
        )

        # Salary Details
        HrSalaryDetail.objects.create(
            mid=1, present=22, absent=0, latein=0, halfday=0, sunday=4, coff=0, pl=1, overtimehrs=5,
            installment=1000, mobileexeamt=200, addition=150, deduction=50
        )
        HrSalaryDetail.objects.create(
            mid=2, present=20, absent=2, latein=1, halfday=1, sunday=3, coff=0, pl=0, overtimehrs=0,
            installment=0, mobileexeamt=0, addition=0, deduction=0
        )
    
    def test_get_consolidated_report_data_basic(self):
        service = SalaryCalculationService()
        report_data = service.get_consolidated_report_data(
            self.bg_group_id, self.month_id, self.comp_id, self.fin_year_id, self.etype
        )
        self.assertIsInstance(report_data, list)
        self.assertGreater(len(report_data), 0)
        self.assertIsInstance(report_data[0], SalaryReportEntry)
        self.assertEqual(report_data[0].empid, 'EMP001')
        self.assertAlmostEqual(report_data[0].net_pay, Decimal('37599.50'), places=2) # Based on example calc

    def test_offer_cal(self):
        self.assertAlmostEqual(SalaryCalculationService._offer_cal(1000, 1, 1, 1), Decimal('400.00')) # Basic
        self.assertAlmostEqual(SalaryCalculationService._offer_cal(1000, 2, 1, 1), Decimal('100.00')) # DA

    def test_pf_cal(self):
        # Example: 12% of 1000, capped at 1800
        self.assertAlmostEqual(SalaryCalculationService._pf_cal(1000, 1, 12), Decimal('120.00'))
        # Test capping
        self.assertAlmostEqual(SalaryCalculationService._pf_cal(100000, 1, 12), Decimal('1800.00')) 

    def test_p_tax_cal(self):
        self.assertAlmostEqual(SalaryCalculationService._p_tax_cal(5000, '01'), Decimal('0.00'))
        self.assertAlmostEqual(SalaryCalculationService._p_tax_cal(15000, '01'), Decimal('150.00'))
        self.assertAlmostEqual(SalaryCalculationService._p_tax_cal(25000, '01'), Decimal('200.00'))

    def test_count_sundays(self):
        # October 2023 has 5 Sundays
        self.assertEqual(SalaryCalculationService._count_sundays(2023, 10), Decimal('5'))
        # February 2024 has 4 Sundays
        self.assertEqual(SalaryCalculationService._count_sundays(2024, 2), Decimal('4'))

class SalaryReportViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set session data needed by the views
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        # Create base test data needed for the service to run
        # This mirrors setUpTestData from service tests, ensuring data exists.
        HrOfficeStaff.objects.create(empid='TESTEMP', compid=1, offerid=100, finyearid=2023,
                                     title='Dr', employeename='Test Employee', department=1, bggroup=1,
                                     designation=1, grade=1, bankaccountno='AC123', pfno='PFTEST', panno='PANTEST')
        HrSalaryMaster.objects.create(id=100, empid='TESTEMP', compid=1, finyearid=2023, fmonth=10, increment=1)
        HrOfferMaster.objects.create(offerid=100, stafftype=1, typeof=1, salary=Decimal('30000.00'), dutyhrs=8, othrs=1,
                                    overtime=2, exgratia=Decimal('500.00'), vehicleallowance=Decimal('200.00'),
                                    attbonusper1=5, attbonusper2=10, pfemployee=12, pfcompany=13, increment=1)
        HrSalaryDetail.objects.create(mid=100, present=20, absent=0, latein=0, halfday=0, sunday=4, coff=0, pl=0, overtimehrs=2,
                                      installment=0, mobileexeamt=0, addition=0, deduction=0)


    def test_list_view_get(self):
        response = self.client.get(reverse('salarysummaryreport_list'), {
            'bg_group_id': 1, 'month_id': 10, 'etype': 1
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salarysummaryreport/list.html')
        self.assertIn('salarysummaryreports', response.context)
        self.assertGreater(len(response.context['salarysummaryreports']), 0)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 0)

    def test_list_view_missing_params(self):
        response = self.client.get(reverse('salarysummaryreport_list'), {'bg_group_id': 1}) # Missing month_id, etype
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salarysummaryreport/list.html')
        self.assertIn('salarysummaryreports', response.context)
        self.assertEqual(len(response.context['salarysummaryreports']), 0)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Please provide all required parameters (Month, Company, Financial Year) to generate the report.")

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('salarysummaryreport_table'), {
            'bg_group_id': 1, 'month_id': 10, 'etype': 1
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salarysummaryreport/_salarysummaryreport_table.html')
        self.assertContains(response, '<table id="salarysummaryreportTable"') # Check if table is rendered
        self.assertGreater(len(response.context['salarysummaryreports']), 0)


    # --- Simulated CRUD Views Tests ---
    # These tests verify the placeholder CRUD functionality as per the prompt's template.
    # They do NOT test actual database persistence as SalaryReportEntry is a computed model.

    def test_create_view_get(self):
        response = self.client.get(reverse('salarysummaryreport_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salarysummaryreport/_salarysummaryreport_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'empid': 'NEWEMP',
            'employee_name': 'New Test Employee',
            'month': 'Dec',
            'net_pay': '9999.99',
            # Add other required fields if form was more complex
        }
        response = self.client.post(reverse('salarysummaryreport_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalaryReportEntryList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Salary Report Entry (simulated) added successfully.')
    
    def test_update_view_get(self):
        # Use a dummy existing ID (empid) for lookup
        response = self.client.get(reverse('salarysummaryreport_edit', args=['TESTEMP']), {
            'bg_group_id': 1, 'month_id': 10, 'etype': 1
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salarysummaryreport/_salarysummaryreport_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].initial['empid'], 'TESTEMP')
        self.assertEqual(response.context['form'].initial['employee_name'], 'Test Employee')

    def test_update_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'empid': 'TESTEMP', # Must match PK
            'employee_name': 'Updated Test Employee',
            'month': 'Oct',
            'net_pay': '12345.67'
        }
        response = self.client.post(reverse('salarysummaryreport_edit', args=['TESTEMP']), data, {
            'bg_group_id': 1, 'month_id': 10, 'etype': 1
        }, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalaryReportEntryList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Salary Report Entry (simulated) updated successfully.')

    def test_delete_view_get(self):
        response = self.client.get(reverse('salarysummaryreport_delete', args=['TESTEMP']), {
            'bg_group_id': 1, 'month_id': 10, 'etype': 1
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_reports/salarysummaryreport/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].empid, 'TESTEMP')

    def test_delete_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('salarysummaryreport_delete', args=['TESTEMP']), {
            'bg_group_id': 1, 'month_id': 10, 'etype': 1
        }, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalaryReportEntryList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Salary Report Entry (simulated) deleted successfully.')

    def test_salary_print_redirect_view(self):
        response = self.client.get(reverse('salary_print_redirect'), {'month_id': 10})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('salary_print_list') + '?month_id=10')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The provided templates already incorporate HTMX for:
- **Dynamic Table Loading:** The main list view uses `hx-get` to fetch the table content (`_salarysummaryreport_table.html`) on load and on `refreshSalaryReportEntryList` trigger.
- **Modal CRUD Operations:** Buttons for "Add New," "Edit," and "Delete" use `hx-get` to load the respective forms (`_salarysummaryreport_form.html` or `confirm_delete.html`) into a modal container (`#modalContent`).
- **Form Submission:** `hx-post` is used on the modal forms. Upon successful submission, the views respond with `status=204` and `HX-Trigger` headers to close the modal and refresh the table (`refreshSalaryReportEntryList`).
- **DataTables:** jQuery DataTables is initialized within the `_salarysummaryreport_table.html` partial to provide client-side searching, sorting, and pagination.

Alpine.js is included in `base.html` (implicitly, as per instructions) and can be used for any minor UI state management within the templates if needed. The provided modal logic `_="on click add .is-active to #modal"` is an example of Alpine.js (via `_` syntax from `Hyperscript` which is a common companion to HTMX, or Alpine itself).

This setup ensures that all interactions are handled without full page reloads, providing a smooth and responsive user experience.

## Final Notes

- **Database Configuration:** Remember to configure your `settings.py` to connect to the existing ASP.NET SQL Server database. This typically involves defining a `legacy_db` database connection in your `DATABASES` setting and using `objects.using('legacy_db')` in your model queries.
- **`clsFunctions` Full Replication:** The `SalaryCalculationService` provides a structured approach. For a complete migration, each `clsFunctions` method's exact logic (including all if-else branches, specific constants, and lookup tables) must be meticulously translated into Python within this service. AI-assisted tools can be invaluable for analyzing these complex C# functions and suggesting Python equivalents.
- **Error Handling:** The Django code includes basic `try-except` blocks. Robust error handling and logging should be added, similar to or more comprehensive than the ASP.NET `try-catch`.
- **Authentication/Authorization:** The ASP.NET code relies on `Session["compid"]` and `Session["finyear"]`. Django's built-in authentication system and custom middleware or context processors should manage user sessions and access control securely.
- **Front-end Libraries:** Ensure jQuery and DataTables, HTMX, and Alpine.js CDN links are included in your `core/base.html` template. Tailwind CSS classes are extensively used for styling.
- **Automated Testing:** The provided test templates are a starting point. Achieving 80% test coverage for the `SalaryCalculationService` and all views is crucial for confidence in the migrated application.
- **Incremental Migration:** For large applications, consider an incremental migration strategy where specific modules or reports are moved to Django one by one, allowing both systems to coexist during the transition.