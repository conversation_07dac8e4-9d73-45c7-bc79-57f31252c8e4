## ASP.NET to Django Conversion Script: Salary Processing Module

This document outlines a comprehensive modernization plan to transition the `Salary_New_Details.aspx` ASP.NET page and its C# code-behind to a modern Django-based solution. Our approach prioritizes automation, fat models, thin views, and leverages HTMX + Alpine.js for a highly interactive, efficient user experience without traditional JavaScript.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

This ASP.NET page interacts with multiple tables to fetch employee details, calculate salary components, and save new salary entries. The core tables for this page are `tblHR_Salary_Master` and `tblHR_Salary_Details`. Other tables are used for lookup and related employee information.

**Inferred Table Names and Relevant Columns:**

*   **`tblHR_Salary_Master`**: Stores the main salary record for a month.
    *   `Id` (PK, int)
    *   `SysDate` (date)
    *   `SysTime` (time)
    *   `SessionId` (string) - `sId` in C#
    *   `CompId` (int) - `Session["compid"]`
    *   `FinYearId` (int) - `Session["finyear"]`
    *   `EmpId` (string) - Foreign Key to `tblHR_OfficeStaff` `EmpId`
    *   `FMonth` (int) - Month ID (1-12)
    *   `Increment` (int) - `HFInc.Value`

*   **`tblHR_Salary_Details`**: Stores detailed attendance and miscellaneous deductions/additions.
    *   `MId` (FK to `tblHR_Salary_Master` `Id`)
    *   `Present` (double)
    *   `Absent` (double)
    *   `LateIn` (double)
    *   `HalfDay` (double)
    *   `Sunday` (double)
    *   `Coff` (double)
    *   `PL` (double) - (presumably "Privilege Leave")
    *   `OverTimeHrs` (double)
    *   `OverTimeRate` (double)
    *   `Installment` (double) - for bank loan
    *   `MobileExeAmt` (double) - mobile excess amount
    *   `Addition` (double)
    *   `Remarks1` (string)
    *   `Deduction` (double)
    *   `Remarks2` (string)

*   **`tblHR_OfficeStaff`**: Employee basic information.
    *   `EmpId` (PK, string) - Employee ID (used in `Request.QueryString["EmpId"]`)
    *   `OfferId` (int) - FK to `tblHR_Offer_Master` `OfferId`
    *   `Title` (string)
    *   `EmployeeName` (string)
    *   `SwapCardNo` (int) - FK to `tblHR_SwapCard` `Id`
    *   `Department` (int) - FK to `tblHR_Departments` `Id`
    *   `Designation` (int) - FK to `tblHR_Designation` `Id`
    *   `Grade` (int) - FK to `tblHR_Grade` `Id`
    *   `MobileNo` (int) - FK to `tblHR_CoporateMobileNo` `Id`
    *   `CompanyEmail` (string)
    *   `BankAccountNo` (string)
    *   `PFNo` (string)
    *   `PANNo` (string)
    *   `PhotoData` (binary) - Image data
    *   `PhotoFileName` (string)

*   **Auxiliary/Lookup Tables (simplified for this plan, but full models would be created):**
    *   `tblHR_CoporateMobileNo`: `Id`, `MobileNo`
    *   `tblHR_SwapCard`: `Id`, `SwapCardNo`
    *   `tblHR_Departments`: `Id`, `Description`, `Symbol`
    *   `tblHR_Designation`: `Id`, `Type`, `Symbol`
    *   `tblHR_Grade`: `Id`, `Symbol`
    *   `tblHR_Offer_Master`: `OfferId`, `StaffType`, `TypeOf`, `DutyHrs`, `OTHrs`, `salary`, `OverTime`, `Increment`
    *   `tblHR_EmpType`: `Id`, `Description`
    *   `tblHR_DutyHour`: `Id`, `Hours`
    *   `tblHR_OTHour`: `Id`, `Hours`
    *   `tblHR_BankLoan`: `Amount`, `Installment` (for aggregated loan data)
    *   `tblHR_OverTime`: `Id`, `Description`
    *   `tblHR_Offer_Accessories`: `Id`, `MId` (FK to `tblHR_Offer_Master` `OfferId`), `Perticulars`, `Qty`, `Amount`, `IncludesIn` (FK to `tblHR_IncludesIn` `Id`)
    *   `tblHR_IncludesIn`: `Id`, `IncludesIn`

### Step 2: Identify Backend Functionality

The ASP.NET page combines read, create, and update functionality, dynamically updating the UI based on user selections.

**Core Operations:**

1.  **Read/Display Employee & Monthly Data:**
    *   On `Page_Load` (initial GET request) and `ddlMonth_SelectedIndexChanged` (HTMX POST/GET to update partials):
        *   Retrieve employee details (`tblHR_OfficeStaff` and related lookups like department, designation, grade, etc.).
        *   Calculate and display: Days of Month, Sundays, Holidays, Working Days for the selected month/year.
        *   Retrieve Mobile Bill details (Limit, Bill Amount, Excess Amount) for the employee.
        *   Retrieve Bank Loan details (Total Loan, Installment Paid, Remaining Balance, Current Installment).
        *   Calculate Overtime Rate based on salary, duty hours, OT hours, and days in month.
        *   Display employee photo.
        *   Load and display "Offer Accessories" in a grid (`GridView1`).

2.  **Create/Update Salary Entry:**
    *   On `btnProceed_Click` (HTMX POST request for form submission):
        *   Validate all numeric input fields (Present, Absent, Late In, etc.).
        *   Check if a salary record for the selected employee and month already exists.
        *   **Create:** If no existing record, insert a new entry into `tblHR_Salary_Master` and `tblHR_Salary_Details`.
        *   **Update:** (Implicit, though the ASP.NET code primarily creates a new record) If a record exists, the user is prevented from proceeding, but a full solution would allow updating. For this migration, we'll focus on the "create" path as per the original code's `alert('Salary for the selected month is already found.')` and `btnProceed.Visible = false`.

**Validation Logic:**
*   Numeric validation (`^\d{1,15}(\.\d{0,3})?$`) for attendance and miscellaneous fields.
*   Required field validation for attendance and miscellaneous fields.
*   Checking if working days are found for the month.
*   Checking if salary for the selected month already exists.

### Step 3: Infer UI Components

The ASP.NET page uses standard Web Forms controls for UI:

*   `ddlMonth`: Dropdown list for selecting the month.
*   `lblDaysM`, `lblSunM`, `lblHolidayM`, `lblWDayM`: Labels to display calculated monthly data.
*   `Image1`: Displays employee photo.
*   `lblNameOfEmployee`, `lblSCNo`, `lblDept`, `lblDesig`, `lblGrade`, `lblStatus`, `lblDutyHrs`, `lblEOTHrs`, `lblACNo`, `lblPFNo`, `lblPANNo`, `lblEmail`, `lblMobNo`: Labels to display static employee details.
*   `txtPresent`, `txtAbsent`, `txtLateIn`, `txtHalfDay`, `txtSunday`, `txtCoff`, `txtPL`, `txtOverTimeHrs`, `txtInstallment`, `txtMobExeAmt`, `txtAddition`, `txtDeduction`: Textboxes for attendance and miscellaneous inputs.
*   `lblBLoan`, `lblBalance`, `lblLimit`, `lblBill`, `lblOverTimeRate`: Labels for loan, mobile bill, and OT rate details.
*   `txtRemarks1`, `txtRemarks2`: Multiline textboxes for remarks.
*   `GridView1`: Displays the list of "Offer Accessories".
*   `btnProceed`, `btnCancel`: Buttons for form submission and cancellation.

In Django, these will be mapped to standard HTML form elements, populated by Django forms, and dynamically updated using HTMX and Alpine.js. `GridView1` will become a DataTables-powered HTML table.

---

### Step 4: Generate Django Code (App Name: `hr_payroll`)

We will structure the code within a Django application named `hr_payroll`.

#### 4.1 Models (`hr_payroll/models.py`)

To ensure the "fat model" principle, all salary calculation and data retrieval logic previously in `clsFunctions` will be incorporated as methods within the relevant models, particularly `Employee` and `SalaryMaster`, or as manager methods.

```python
from django.db import models
from django.utils import timezone
from datetime import date, timedelta
import calendar

# --- Core HR Models (simplified, full models would have more fields) ---

class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompanyName')

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    start_date = models.DateField(db_column='StartDate')
    end_date = models.DateField(db_column='EndDate')
    year_name = models.CharField(max_length=50, db_column='FinYearName')

    class Meta:
        managed = False
        db_table = 'tblFinancialYear'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(max_length=255, db_column='Description')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return f"{self.description} [{self.symbol}]"

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type_name = models.CharField(max_length=255, db_column='Type')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.type_name} [{self.symbol}]"

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.symbol

class CorporateMobileNo(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(max_length=20, db_column='MobileNo')

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no

class SwapCard(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    swap_card_no = models.CharField(max_length=50, db_column='SwapCardNo')

    class Meta:
        managed = False
        db_table = 'tblHR_SwapCard'
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'

    def __str__(self):
        return self.swap_card_no

class EmpType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(max_length=255, db_column='Description')

    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'

    def __str__(self):
        return self.description

class DutyHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.DecimalField(max_digits=5, decimal_places=2, db_column='Hours')

    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'

    def __str__(self):
        return str(self.hours)

class OTHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.DecimalField(max_digits=5, decimal_places=2, db_column='Hours')

    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'OT Hour'
        verbose_name_plural = 'OT Hours'

    def __str__(self):
        return str(self.hours)

class OvertimeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(max_length=255, db_column='Description')

    class Meta:
        managed = False
        db_table = 'tblHR_OverTime'
        verbose_name = 'Overtime Type'
        verbose_name_plural = 'Overtime Types'

    def __str__(self):
        return self.description

class OfferMaster(models.Model):
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    staff_type = models.ForeignKey(EmpType, models.DO_NOTHING, db_column='StaffType')
    type_of = models.IntegerField(db_column='TypeOf') # 1 for SAPL, 2 for NEHA
    duty_hrs = models.ForeignKey(DutyHour, models.DO_NOTHING, db_column='DutyHrs')
    ot_hrs = models.ForeignKey(OTHour, models.DO_NOTHING, db_column='OTHrs')
    salary = models.DecimalField(max_digits=18, decimal_places=2, db_column='salary')
    overtime = models.ForeignKey(OvertimeType, models.DO_NOTHING, db_column='OverTime')
    increment = models.IntegerField(db_column='Increment')

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'

    def __str__(self):
        return f"Offer {self.offer_id}"

class Employee(models.Model):
    user_id = models.CharField(max_length=50, db_column='UserID', blank=True, null=True)
    emp_id = models.CharField(max_length=50, db_column='EmpId', primary_key=True) # Assuming EmpId is PK
    offer = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='OfferId', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    title = models.CharField(max_length=10, db_column='Title', blank=True, null=True)
    employee_name = models.CharField(max_length=255, db_column='EmployeeName')
    swap_card = models.ForeignKey(SwapCard, models.DO_NOTHING, db_column='SwapCardNo', blank=True, null=True)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', blank=True, null=True)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    grade = models.ForeignKey(Grade, models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    mobile_no = models.ForeignKey(CorporateMobileNo, models.DO_NOTHING, db_column='MobileNo', blank=True, null=True)
    company_email = models.CharField(max_length=255, db_column='CompanyEmail', blank=True, null=True)
    bank_account_no = models.CharField(max_length=50, db_column='BankAccountNo', blank=True, null=True)
    pf_no = models.CharField(max_length=50, db_column='PFNo', blank=True, null=True)
    pan_no = models.CharField(max_length=50, db_column='PANNo', blank=True, null=True)
    photo_data = models.BinaryField(db_column='PhotoData', blank=True, null=True)
    photo_file_name = models.CharField(max_length=255, db_column='PhotoFileName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employee_name} [{self.emp_id}]"

    def get_photo_url(self):
        if self.photo_data and self.photo_file_name:
            # Assuming a Django view handles image serving based on EmpId and CompId
            return f"/hr_payroll/employee/image/{self.emp_id}/{self.company.id}/"
        return "/static/images/User.jpg" # Default image path

    def get_salary_year(self, month_id, fin_year_id, company_id):
        # This emulates fun.SalYrs - assuming financial year dictates the year
        # A more robust implementation would consider the actual date of the financial year.
        try:
            fin_year = FinancialYear.objects.get(pk=fin_year_id)
            if fin_year.start_date.month <= month_id <= fin_year.end_date.month:
                return fin_year.start_date.year
            # Fallback for months spanning across calendar year boundaries within a financial year
            elif month_id < fin_year.start_date.month:
                return fin_year.end_date.year # e.g. FY Apr-Mar, for Jan/Feb/Mar, it's the next year
            else:
                return fin_year.start_date.year
        except FinancialYear.DoesNotExist:
            return timezone.now().year # Fallback
    
    def get_month_info(self, month_id, fin_year_id, company_id):
        """
        Calculates and returns month-specific information.
        Emulates fun.CountSundays, fun.GetHoliday, fun.WorkingDays.
        """
        year = self.get_salary_year(month_id, fin_year_id, company_id)
        
        num_days = calendar.monthrange(year, month_id)[1] # Days in month
        
        sundays = 0
        holidays = 0 # Placeholder for actual holiday logic
        working_days = 0 # Placeholder for actual working days logic

        # Count Sundays
        for day in range(1, num_days + 1):
            if date(year, month_id, day).weekday() == calendar.SUNDAY:
                sundays += 1
        
        # Holiday logic from `fun.GetHoliday` would go here, querying a Holiday table
        # For now, it's a placeholder.
        # Example: holidays = Holiday.objects.filter(month=month_id, year=year, company_id=company_id, fin_year_id=fin_year_id).count()
        # For demonstration, let's assume some fixed holidays or query a dummy model
        try:
            holidays = Holiday.objects.filter(
                month=month_id,
                year=year,
                company_id=company_id,
                fin_year_id=fin_year_id
            ).count()
        except Exception: # Handle case where Holiday model might not be implemented fully
            holidays = 0 # Default to 0 if table not found or no data

        # Working days calculation from `fun.WorkingDays` would go here
        # It typically depends on total days, Sundays, holidays, and possibly configured weekly off days.
        # For simplicity, if WorkingDays is 0, it should be alerted.
        # This needs a more sophisticated query or calculation based on company/finyear rules.
        # Let's assume a basic calculation or a lookup table for working days
        try:
            working_days_obj = WorkingDaysConfig.objects.get(
                fin_year_id=fin_year_id,
                month=month_id,
                company_id=company_id
            )
            working_days = working_days_obj.days
        except WorkingDaysConfig.DoesNotExist:
            working_days = num_days - sundays - holidays # Simple approximation if config not found

        return {
            'year': year,
            'days_in_month': num_days,
            'sundays': sundays,
            'holidays': holidays,
            'working_days': working_days,
        }

    def get_mobile_bill_details(self, month_id, fin_year_id, company_id):
        """
        Emulates fun.MobileBillDetails.
        Assumes a tblHR_MobileBillDetails exists.
        """
        # Placeholder for actual mobile bill details query
        # For demonstration, returning dummy data or querying a basic model
        try:
            # This would typically be a filter on a MobileBill model
            bill_data = MobileBill.objects.filter(
                employee=self,
                month=month_id,
                fin_year=fin_year_id,
                company=company_id
            ).first()

            if bill_data:
                return {
                    'bill_amount': float(bill_data.bill_amount),
                    'limit': float(bill_data.limit),
                    'excess_amount': float(bill_data.excess_amount)
                }
        except Exception: # Handle case where MobileBill model might not be implemented fully
            pass
        
        return {'bill_amount': 0.0, 'limit': 0.0, 'excess_amount': 0.0}

    def get_bank_loan_details(self, fin_year_id, company_id):
        """
        Emulates bank loan calculation logic.
        Aggregates from tblHR_BankLoan and tblHR_Salary_Details.
        """
        loan_amt = 0.0
        inst_amt = 0.0
        inst_paid_amt = 0.0

        # Sum of LoanAmt and Installment from tblHR_BankLoan up to current financial year
        # This implies a BankLoan model.
        bank_loans = BankLoan.objects.filter(
            employee=self,
            company__id=company_id,
            fin_year__id__lte=fin_year_id
        ).aggregate(
            total_loan=models.Sum('amount'),
            total_installment_amt=models.Sum('installment')
        )
        loan_amt = float(bank_loans['total_loan'] or 0.0)
        inst_amt = float(bank_loans['total_installment_amt'] or 0.0)

        # Sum of paid installments from tblHR_Salary_Details up to current financial year
        # This implies a SalaryDetail model linked to SalaryMaster, which links to Employee.
        paid_installments = SalaryDetail.objects.filter(
            salary_master__employee=self,
            salary_master__company__id=company_id,
            salary_master__fin_year__id__lte=fin_year_id
        ).aggregate(
            total_paid=models.Sum('installment')
        )
        inst_paid_amt = float(paid_installments['total_paid'] or 0.0)

        current_installment = inst_amt if (loan_amt - inst_paid_amt) > 0 else 0.0

        return {
            'loan_amount': loan_amt,
            'installment_amount': inst_amt, # This is the original installment amount per loan
            'installment_paid_amount': inst_paid_amt,
            'current_installment_to_pay': current_installment, # This is the calculated installment for current month
        }
    
    def calculate_ot_rate(self, month_id, fin_year_id, company_id):
        """
        Emulates fun.OTRate.
        OTR = Math.Round((fun.OTRate(salary, OTHrs, DutyHrs, NoOfDays)),2);
        """
        if not self.offer:
            return 0.0

        salary = float(self.offer.salary)
        try:
            ot_hours_per_month = float(self.offer.ot_hrs.hours)
            duty_hours_per_day = float(self.offer.duty_hrs.hours)
        except (AttributeError, TypeError): # Handle cases where ot_hrs or duty_hrs might be None
            return 0.0

        month_info = self.get_month_info(month_id, fin_year_id, company_id)
        num_days = month_info['days_in_month']

        if salary > 0 and ot_hours_per_month > 0 and duty_hours_per_day > 0 and num_days > 0:
            # Assuming a common formula: (Monthly_Salary / (Working_Days * Duty_Hours_Per_Day)) * OT_Factor
            # The fun.OTRate seems to use OTHrs from tblHR_OTHour, which might be total monthly OT hours.
            # Let's derive a reasonable formula from the context:
            # (salary / ((NoOfDays - Sundays - Holidays) * DutyHrs)) * OTHrsFactor
            # The ASP.NET code passes OTHrs (monthly from OfferMaster) and DutyHrs (daily from OfferMaster)
            # and NoOfDays (total days in month). This implies a rate per hour.

            # Simple interpretation: OTR = (Salary / Monthly Duty Hours) * (OT_Factor)
            # Monthly Duty Hours = (NoOfDays - Holidays - Sundays) * DutyHrs_Per_Day
            
            # The original C# `OTRate` function is a black box, but given context, it's likely:
            # OTR = (salary / (Number_of_Actual_Working_Hours_in_Month)) * OT_Multiplier
            # Let's assume the standard calculation as: (Monthly Salary / Total Working Hours in Month) * OT Factor
            # Total Working Hours in Month = (Working Days - OT Hours From SalaryDetail) * DutyHrs_Per_Day
            
            # Reinterpreting based on parameters: `fun.OTRate(Convert.ToDouble(dsOfferM.Tables[0].Rows[0]["salary"]), Convert.ToDouble(dsOTHrs.Tables[0].Rows[0]["Hours"]), Convert.ToDouble(dsDutyHrs.Tables[0].Rows[0]["Hours"].ToString()), NoOfDays))`
            # This suggests: salary, total monthly OT hours from offer, daily duty hours from offer, total days in month.
            # If `OTRate` is a rate for _each_ OT hour, it might be:
            # Rate per hour = salary / ((NoOfDays - Sundays - Holidays) * DutyHrs_Per_Day)
            # This 'rate per hour' is then used with actual `txtOverTimeHrs` for total OT pay.
            
            # Let's assume a basic hourly rate for the employee's standard work, then adjust for OT
            # The '2' in `Convert.ToInt32(dsOfferM.Tables[0].Rows[0]["OverTime"]) == 2`
            # implies 'Overtime type 2' triggers this calculation.
            
            working_days_for_rate = month_info['working_days'] # Using the calculated working days
            if working_days_for_rate <= 0:
                return 0.0

            # Hourly rate based on normal working days and duty hours
            hourly_rate = salary / (working_days_for_rate * duty_hours_per_day)
            
            # If the OT type is 2 (e.g., "Hourly Rate"), then the rate is the hourly rate
            # If OT type is not 2, `DSOverTime.Tables[0].Rows[0]["Description"].ToString()` is used.
            # This implies `tblHR_OverTime.Description` for ID=1 is "Not Applicable" or similar.
            if self.offer.overtime.id == 2: # Assuming ID 2 means calculated rate
                return round(float(hourly_rate), 2)
            else: # Assuming other IDs mean a fixed string (e.g., "Not Applicable")
                return 0.0 # Or return the description string, but C# converts to double so 0.0 is safer.
        return 0.0


# --- Data for Lookup Tables (Dummy Models for calculation support) ---
# In a real system, these would be properly seeded or managed.
class Holiday(models.Model):
    # Dummy model to simulate fun.GetHoliday
    month = models.IntegerField()
    year = models.IntegerField()
    count = models.IntegerField()
    company = models.ForeignKey(Company, models.DO_NOTHING)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING)

    class Meta:
        managed = False # Assume this is from the legacy DB if it exists
        db_table = 'tblHR_Holidays' # Placeholder table name
        verbose_name = 'Holiday'
        verbose_name_plural = 'Holidays'

class WorkingDaysConfig(models.Model):
    # Dummy model to simulate fun.WorkingDays if it comes from a config
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING)
    month = models.IntegerField()
    days = models.IntegerField()
    company = models.ForeignKey(Company, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'tblHR_WorkingDaysConfig' # Placeholder table name
        verbose_name = 'Working Day Config'
        verbose_name_plural = 'Working Day Configs'

class MobileBill(models.Model):
    # Dummy model to simulate fun.MobileBillDetails
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    month = models.IntegerField()
    year = models.IntegerField()
    bill_amount = models.DecimalField(max_digits=10, decimal_places=2)
    limit = models.DecimalField(max_digits=10, decimal_places=2)
    excess_amount = models.DecimalField(max_digits=10, decimal_places=2)
    company = models.ForeignKey(Company, models.DO_NOTHING)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'tblHR_MobileBillDetails' # Placeholder table name
        verbose_name = 'Mobile Bill'
        verbose_name_plural = 'Mobile Bills'

class BankLoan(models.Model):
    # Dummy model to simulate tblHR_BankLoan
    employee = models.ForeignKey(Employee, models.DO_NOTHING)
    amount = models.DecimalField(max_digits=18, decimal_places=2)
    installment = models.DecimalField(max_digits=18, decimal_places=2)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING)
    company = models.ForeignKey(Company, models.DO_NOTHING)

    class Meta:
        managed = False
        db_table = 'tblHR_BankLoan' # Placeholder table name
        verbose_name = 'Bank Loan'
        verbose_name_plural = 'Bank Loans'

class IncludesIn(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(max_length=255, db_column='IncludesIn')

    class Meta:
        managed = False
        db_table = 'tblHR_IncludesIn'
        verbose_name = 'Includes In'
        verbose_name_plural = 'Includes In Options'

    def __str__(self):
        return self.name

class OfferAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    offer_master = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='MId') # Assuming MId is OfferId
    particulars = models.CharField(max_length=255, db_column='Perticulars')
    qty = models.DecimalField(max_digits=18, decimal_places=2, db_column='Qty')
    amount = models.DecimalField(max_digits=18, decimal_places=2, db_column='Amount')
    includes_in = models.ForeignKey(IncludesIn, models.DO_NOTHING, db_column='IncludesIn')

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'

    def __str__(self):
        return self.particulars

    @property
    def total(self):
        return round(self.qty * self.amount, 2)


# --- Primary Salary Models ---

class SalaryMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(max_length=255, db_column='SessionId')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId')
    fmonth = models.IntegerField(db_column='FMonth') # Month ID (1-12)
    increment = models.IntegerField(db_column='Increment', default=0)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Master'
        verbose_name = 'Salary Master'
        verbose_name_plural = 'Salary Masters'
        unique_together = (('employee', 'fmonth', 'fin_year', 'company'),) # Ensure unique salary per emp/month/year/comp

    def __str__(self):
        return f"Salary for {self.employee.employee_name} ({calendar.month_name[self.fmonth]} {self.fin_year.year_name})"

    # Method to check if salary for month exists, similar to ddlMonth_SelectedIndexChanged
    @classmethod
    def salary_exists_for_month(cls, employee_id, month_id, fin_year_id, company_id):
        return cls.objects.filter(
            employee__emp_id=employee_id,
            fmonth=month_id,
            fin_year__id=fin_year_id,
            company__id=company_id
        ).exists()

    def get_details(self):
        """Retrieves related SalaryDetail or None if not exists."""
        return self.salarydetail_set.first()


class SalaryDetail(models.Model):
    # Id might be auto-incrementing in legacy, Django will handle it if not specified
    salary_master = models.OneToOneField(SalaryMaster, models.DO_NOTHING, db_column='MId', primary_key=True) # MId as PK for OneToOne
    present = models.DecimalField(max_digits=18, decimal_places=3, db_column='Present', default=0)
    absent = models.DecimalField(max_digits=18, decimal_places=3, db_column='Absent', default=0)
    late_in = models.DecimalField(max_digits=18, decimal_places=3, db_column='LateIn', default=0)
    half_day = models.DecimalField(max_digits=18, decimal_places=3, db_column='HalfDay', default=0)
    sunday = models.DecimalField(max_digits=18, decimal_places=3, db_column='Sunday', default=0)
    coff = models.DecimalField(max_digits=18, decimal_places=3, db_column='Coff', default=0)
    pl = models.DecimalField(max_digits=18, decimal_places=3, db_column='PL', default=0)
    over_time_hrs = models.DecimalField(max_digits=18, decimal_places=3, db_column='OverTimeHrs', default=0)
    over_time_rate = models.DecimalField(max_digits=18, decimal_places=3, db_column='OverTimeRate', default=0)
    installment = models.DecimalField(max_digits=18, decimal_places=3, db_column='Installment', default=0)
    mobile_exe_amt = models.DecimalField(max_digits=18, decimal_places=3, db_column='MobileExeAmt', default=0)
    addition = models.DecimalField(max_digits=18, decimal_places=3, db_column='Addition', default=0)
    remarks1 = models.TextField(db_column='Remarks1', blank=True, null=True)
    deduction = models.DecimalField(max_digits=18, decimal_places=3, db_column='Deduction', default=0)
    remarks2 = models.TextField(db_column='Remarks2', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Details'
        verbose_name = 'Salary Detail'
        verbose_name_plural = 'Salary Details'

    def __str__(self):
        return f"Details for {self.salary_master}"

```

#### 4.2 Forms (`hr_payroll/forms.py`)

We'll create a form for the attendance and miscellaneous inputs, as they are the primary user-editable fields. The month selection will be handled directly in the view logic for dynamic loading.

```python
from django import forms
from .models import SalaryDetail, SalaryMaster, Employee, Company, FinancialYear
import calendar

# A helper for month choices
MONTH_CHOICES = [(i, calendar.month_name[i]) for i in range(1, 13)]

class SalaryDetailForm(forms.ModelForm):
    # These fields correspond to the various textboxes in the ASP.NET form
    present = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Present days is required.'}
    )
    absent = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Absent days is required.'}
    )
    late_in = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Late in is required.'}
    )
    half_day = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Half day is required.'}
    )
    sunday = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Sunday is required.'}
    )
    coff = forms.DecimalField(
        max_digits=18,
    decimal_places=3,
    initial=0,
    required=True,
    widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
    error_messages={'required': 'C-off is required.'}
    )
    pl = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'PL is required.'}
    )
    over_time_hrs = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[70px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Over time hours is required.'}
    )
    installment = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[100px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Installment is required.'}
    )
    mobile_exe_amt = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[50px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Mobile excess amount is required.'}
    )
    addition = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[100px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Addition is required.'}
    )
    deduction = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        initial=0,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-[100px]', 'value': '0', 'min': '0'}),
        error_messages={'required': 'Deduction is required.'}
    )
    remarks1 = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 w-[250px] h-[45px]', 'rows': 3}),
        required=False
    )
    remarks2 = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 w-[250px] h-[45px]', 'rows': 3}),
        required=False
    )

    class Meta:
        model = SalaryDetail
        fields = [
            'present', 'absent', 'late_in', 'half_day', 'sunday', 'coff', 'pl',
            'over_time_hrs', 'installment', 'mobile_exe_amt', 'addition',
            'remarks1', 'deduction', 'remarks2'
        ]

    # No need for specific RegexValidator as DecimalField handles numeric types
    # and required is handled by required=True and error_messages.

class MonthSelectionForm(forms.Form):
    fmonth = forms.ChoiceField(
        choices=MONTH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-post': 'this.dataset.hxUrl', # Will be set dynamically by template
            'hx-trigger': 'change',
            'hx-target': '#salary_data_container',
            'hx-swap': 'innerHTML',
            'data-hx-url': '' # Placeholder to be filled in template
        }),
        label="Salary for the Month of",
        initial=timezone.now().month
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If there's a dynamic list of months, populate it here
        # For now, it's 1-12 as per general behavior
        pass

```

#### 4.3 Views (`hr_payroll/views.py`)

We'll use a single `TemplateView` for the main page, handling initial display and form presentation. HTMX will manage the dynamic updates of sections. A separate `View` or `DetailView` for the partial data.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from django.http import HttpResponse, JsonResponse, Http404
from django.contrib import messages
from django.db import transaction
from django.db.models import Sum
import calendar
from datetime import date
import io # For image serving

from .models import (
    Employee, SalaryMaster, SalaryDetail, OfferAccessory,
    Company, FinancialYear, OfferMaster
)
from .forms import SalaryDetailForm, MonthSelectionForm


class SalaryNewDetailsView(TemplateView):
    template_name = 'hr_payroll/salary_new_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        emp_id = self.kwargs['emp_id']
        session = self.request.session # Simulate session data for CompId, FinYearId, SessionId

        try:
            employee = Employee.objects.get(emp_id=emp_id)
        except Employee.DoesNotExist:
            raise Http404("Employee not found.")

        # Simulate session variables (replace with actual session logic if available)
        comp_id = session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = session.get('finyear', 1) # Default to 1 if not in session
        session_user_id = session.get('username', 'SYSTEM') # Default to SYSTEM

        company = get_object_or_404(Company, id=comp_id)
        fin_year = get_object_or_404(FinancialYear, id=fin_year_id)

        current_month = int(self.request.GET.get('month', date.today().month))
        
        # Pass data to the initial template for the month dropdown and initial load
        context['employee'] = employee
        context['month_form'] = MonthSelectionForm(initial={'fmonth': current_month})
        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id
        context['session_user_id'] = session_user_id

        # This will be replaced by HTMX loaded partials, but for initial render, it's useful
        context.update(self._get_dynamic_salary_data(
            employee, current_month, fin_year_id, comp_id, session_user_id
        ))
        
        context['accessories'] = OfferAccessory.objects.filter(offer_master=employee.offer)

        return context
    
    def _get_dynamic_salary_data(self, employee, month_id, fin_year_id, comp_id, session_user_id):
        """Helper to get data that changes with month selection."""
        month_info = employee.get_month_info(month_id, fin_year_id, comp_id)
        mobile_bill_details = employee.get_mobile_bill_details(month_id, fin_year_id, comp_id)
        bank_loan_details = employee.get_bank_loan_details(fin_year_id, comp_id)
        ot_rate = employee.calculate_ot_rate(month_id, fin_year_id, comp_id)
        
        # Check if salary already exists for this month
        salary_exists = SalaryMaster.salary_exists_for_month(
            employee.emp_id, month_id, fin_year_id, comp_id
        )

        salary_master_instance = None
        salary_detail_instance = None
        if salary_exists:
            salary_master_instance = SalaryMaster.objects.get(
                employee=employee, fmonth=month_id, fin_year__id=fin_year_id, company__id=comp_id
            )
            salary_detail_instance = salary_master_instance.get_details()
        
        salary_form = SalaryDetailForm(instance=salary_detail_instance)

        return {
            'month_id': month_id,
            'month_info': month_info,
            'mobile_bill_details': mobile_bill_details,
            'bank_loan_details': bank_loan_details,
            'ot_rate': ot_rate,
            'salary_form': salary_form,
            'salary_exists': salary_exists,
            'salary_master_instance': salary_master_instance,
        }


class SalaryDynamicDataPartialView(View):
    """
    Handles HTMX requests to update the month-specific salary details.
    """
    def post(self, request, emp_id, *args, **kwargs):
        # The month ID comes from the hx-post of the dropdown, in request.POST
        month_form = MonthSelectionForm(request.POST)
        if not month_form.is_valid():
            return HttpResponse("Invalid month selection.", status=400)
        
        month_id = int(month_form.cleaned_data['fmonth'])

        session = self.request.session
        comp_id = session.get('compid', 1)
        fin_year_id = session.get('finyear', 1)
        session_user_id = session.get('username', 'SYSTEM')

        try:
            employee = Employee.objects.get(emp_id=emp_id)
        except Employee.DoesNotExist:
            raise Http404("Employee not found.")

        context = {
            'employee': employee,
            'comp_id': comp_id,
            'fin_year_id': fin_year_id,
            'session_user_id': session_user_id,
        }
        context.update(SalaryNewDetailsView()._get_dynamic_salary_data(
            employee, month_id, fin_year_id, comp_id, session_user_id
        ))

        return render(request, 'hr_payroll/_salary_data.html', context)


class OfferAccessoriesPartialView(View):
    """
    Handles HTMX requests to render the Offer Accessories DataTables.
    """
    def get(self, request, emp_id, *args, **kwargs):
        try:
            employee = Employee.objects.get(emp_id=emp_id)
            accessories = OfferAccessory.objects.filter(offer_master=employee.offer)
        except Employee.DoesNotExist:
            accessories = []
        
        context = {'accessories': accessories}
        return render(request, 'hr_payroll/_offer_accessories_table.html', context)


class EmployeeImageView(View):
    """
    Handles serving employee photos.
    This replaces the ASP.NET Handler1.ashx.
    """
    def get(self, request, emp_id, comp_id, *args, **kwargs):
        try:
            employee = Employee.objects.get(emp_id=emp_id, company__id=comp_id)
            if employee.photo_data:
                return HttpResponse(employee.photo_data, content_type="image/jpeg") # Assuming JPEG
            else:
                # Serve a default image if no photo data
                with open("static/images/User.jpg", "rb") as f:
                    return HttpResponse(f.read(), content_type="image/jpeg")
        except Employee.DoesNotExist:
            # Serve a default image if employee not found
            with open("static/images/User.jpg", "rb") as f:
                return HttpResponse(f.read(), content_type="image/jpeg")


class SalaryProceedView(View):
    """
    Handles the 'Proceed' button click for salary creation/update.
    """
    def post(self, request, emp_id, *args, **kwargs):
        session = request.session
        comp_id = session.get('compid', 1)
        fin_year_id = session.get('finyear', 1)
        session_user_id = session.get('username', 'SYSTEM')

        employee = get_object_or_404(Employee, emp_id=emp_id)
        company = get_object_or_404(Company, id=comp_id)
        fin_year = get_object_or_404(FinancialYear, id=fin_year_id)
        
        # Get month from the form data (it will be passed in the POST from the main form)
        month_id = int(request.POST.get('fmonth'))

        # Check if salary for this month already exists
        if SalaryMaster.salary_exists_for_month(emp_id, month_id, fin_year_id, comp_id):
            messages.error(request, 'Salary for the selected month is already found.')
            return HttpResponse(status=200, headers={'HX-Retarget': '#messages', 'HX-Swap': 'innerHTML'}) # Retarget messages
        
        month_info = employee.get_month_info(month_id, fin_year_id, comp_id)
        if month_info['working_days'] == 0:
            messages.error(request, 'Working days is not found for selected month.')
            return HttpResponse(status=200, headers={'HX-Retarget': '#messages', 'HX-Swap': 'innerHTML'})

        # Instantiate form with POST data
        form = SalaryDetailForm(request.POST)

        if form.is_valid():
            try:
                with transaction.atomic():
                    # Get increment from OfferMaster
                    increment_value = employee.offer.increment if employee.offer else 0

                    # Create SalaryMaster entry
                    salary_master = SalaryMaster.objects.create(
                        sys_date=date.today(),
                        sys_time=timezone.now().time(),
                        session_id=session_user_id,
                        company=company,
                        fin_year=fin_year,
                        employee=employee,
                        fmonth=month_id,
                        increment=increment_value
                    )

                    # Populate SalaryDetail fields from form and calculated values
                    salary_detail = form.save(commit=False)
                    salary_detail.salary_master = salary_master
                    
                    # Overtime Rate needs to be calculated dynamically
                    salary_detail.over_time_rate = employee.calculate_ot_rate(
                        month_id, fin_year_id, comp_id
                    )
                    
                    salary_detail.save()

                messages.success(request, 'Salary details processed successfully!')
                # Redirect to a success page or the salary list view
                # The original ASP.NET redirects to Salary_New.aspx?ModId=12&SubModId=133
                # For HTMX, we can trigger a client-side redirect or reload
                return HttpResponse(status=204, headers={'HX-Redirect': reverse('hr_payroll:salary_list')})

            except Exception as e:
                messages.error(request, f'An error occurred during salary processing: {e}')
                return HttpResponse(status=200, headers={'HX-Retarget': '#messages', 'HX-Swap': 'innerHTML'})
        else:
            # If form is not valid, re-render the _salary_data partial with errors
            context = {
                'employee': employee,
                'comp_id': comp_id,
                'fin_year_id': fin_year_id,
                'session_user_id': session_user_id,
                'salary_form': form, # Pass the form with errors
                'salary_exists': False, # Mark as not existing to allow proceed button
            }
            # Re-calculate other dynamic data to populate labels
            context.update(employee.get_month_info(month_id, fin_year_id, comp_id))
            context.update(employee.get_mobile_bill_details(month_id, fin_year_id, comp_id))
            context.update(employee.get_bank_loan_details(fin_year_id, comp_id))
            context['ot_rate'] = employee.calculate_ot_rate(month_id, fin_year_id, comp_id)

            messages.error(request, 'Invalid data entry. Please correct the errors.')
            # This is complex with HTMX to pass back errors to specific fields,
            # so we'll re-render the entire partial and let HTMX update.
            return render(request, 'hr_payroll/_salary_data.html', context, status=400)


# Dummy list view for redirect target
class SalaryListView(TemplateView):
    template_name = 'hr_payroll/salary_list.html' # A placeholder for the main salary list page
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Dummy data for demonstration
        context['salaries'] = SalaryMaster.objects.all()[:10] 
        return context

```

#### 4.4 Templates (`hr_payroll/templates/hr_payroll/`)

We'll have a main template `salary_new_details.html` and two partials: `_salary_data.html` for the dynamic main section, and `_offer_accessories_table.html` for the DataTables grid.

**`salary_new_details.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col gap-6">
        <h2 class="text-2xl font-bold text-gray-800">PayRoll - New</h2>

        <!-- Employee Info Header & Month Selection -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex items-center space-x-4 mb-4">
                <label for="{{ month_form.fmonth.id_for_label }}" class="font-semibold text-gray-700">Salary for the Month of:</label>
                <!-- HTMX will post this form's value to update the salary data section -->
                <form hx-post="{% url 'hr_payroll:salary_month_data' employee.emp_id %}" hx-target="#salary_data_container" hx-swap="innerHTML">
                    {% csrf_token %}
                    {{ month_form.fmonth }}
                </form>
            </div>

            <!-- This div will be updated by HTMX when the month dropdown changes -->
            <div id="salary_data_container"
                 hx-get="{% url 'hr_payroll:salary_month_data' employee.emp_id %}?month={{ month_form.fmonth.initial }}"
                 hx-trigger="load delay:10ms"
                 hx-swap="innerHTML">
                <!-- Initial content will be loaded here by hx-get on page load -->
                <div class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading monthly data...</p>
                </div>
            </div>
        </div>

        <!-- Offer Accessories Grid -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Offer Accessories</h3>
            <div id="accessories_grid_container"
                 hx-get="{% url 'hr_payroll:offer_accessories_table' employee.emp_id %}"
                 hx-trigger="load, refreshAccessoriesGrid from:body"
                 hx-swap="innerHTML">
                <!-- DataTables will be loaded here via HTMX -->
                <div class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading accessories...</p>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-center space-x-4 mt-6">
            <button 
                type="submit" 
                form="salary_details_form"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                Proceed
            </button>
            <a href="{% url 'hr_payroll:salary_list' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md inline-flex items-center justify-center">
                Cancel
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // Event listener for messages
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'messages' || evt.detail.elt.id === 'messages') {
            setTimeout(() => {
                const messages = document.getElementById('messages');
                if (messages) {
                    messages.innerHTML = ''; // Clear messages after a delay
                }
            }, 5000); // Clear after 5 seconds
        }
    });

    // Initialize DataTable on HTMX swap for the accessories table
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'accessories_grid_container') {
            $('#offerAccessoriesTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing table if it exists before re-initializing
            });
        }
    });
</script>
{% endblock %}
```

**`_salary_data.html` (Partial for dynamic salary data)**
```html
<form id="salary_details_form" hx-post="{% url 'hr_payroll:salary_proceed' employee.emp_id %}" hx-swap="outerHTML" hx-target="#salary_data_container">
    {% csrf_token %}
    <input type="hidden" name="fmonth" value="{{ month_id }}">

    <div class="grid grid-cols-12 gap-x-4 gap-y-2 text-sm text-gray-700">
        <!-- Monthly Info Row -->
        <div class="col-span-12 flex justify-between items-center text-sm mb-4">
            <p>Days of Month: <span class="font-bold text-gray-900">{{ month_info.days_in_month }}</span></p>
            <p>Sundays: <span class="font-bold text-gray-900">{{ month_info.sundays }}</span></p>
            <p>Holidays: <span class="font-bold text-gray-900">{{ month_info.holidays }}</span></p>
            <p>Working Days: <span class="font-bold text-gray-900">{{ month_info.working_days }}</span></p>
        </div>

        <!-- Section Headers -->
        <div class="col-span-4 text-center font-bold bg-gray-200 py-2 border-l border-r border-gray-400">Employee Details</div>
        <div class="col-span-4 text-center font-bold bg-gray-200 py-2 border-r border-gray-400">Attendance Details</div>
        <div class="col-span-4 text-center font-bold bg-gray-200 py-2 border-r border-gray-400">Miscellanies</div>

        <!-- Employee Image & Name -->
        <div class="col-span-4 flex items-start gap-4 border-l border-gray-300 row-span-4 p-2">
            <img src="{{ employee.get_photo_url }}" alt="Employee Photo" class="w-20 h-24 object-cover border border-gray-200" />
            <div class="flex-1">
                <p class="font-bold text-lg text-gray-900">{{ employee.title }}. {{ employee.employee_name }} [{{ employee.emp_id }}]</p>
            </div>
        </div>

        <!-- Attendance Inputs -->
        <div class="col-span-2 text-right p-2">Present:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.present }}</div>
        
        <!-- Misc. Details 1 -->
        <div class="col-span-2 text-right p-2">Bank Loan:</div>
        <div class="col-span-2 p-2 font-bold text-gray-900">{{ bank_loan_details.loan_amount }}</div>
        <div class="col-span-2 border-r border-gray-300 p-2">Inst. Paid: <span class="font-bold text-gray-900">{{ bank_loan_details.installment_paid_amount }}</span></div>

        <div class="col-span-2 text-right p-2">Swap Card No:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.swap_card.swap_card_no|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Absent:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.absent }}</div>

        <div class="col-span-2 text-right p-2">Installment:</div>
        <div class="col-span-4 border-r border-gray-300 p-2">{{ salary_form.installment }}</div>

        <div class="col-span-2 text-right p-2">Department:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.department|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Late In:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.late_in }}</div>

        <div class="col-span-2 text-right p-2">Mobile Bill:</div>
        <div class="col-span-2 p-2">Limit: <span class="font-bold text-gray-900">{{ mobile_bill_details.limit }}</span></div>
        <div class="col-span-2 p-2">Bill Amt: <span class="font-bold text-gray-900">{{ mobile_bill_details.bill_amount }}</span></div>
        <div class="col-span-2 border-r border-gray-300 p-2">Exe.Amt: {{ salary_form.mobile_exe_amt }}</div>

        <div class="col-span-4 border-r border-gray-300 p-2"></div> <!-- Empty cell under image -->
        <div class="col-span-2 text-right p-2">Half Day:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.half_day }}</div>

        <div class="col-span-2 text-right p-2">Addition:</div>
        <div class="col-span-4 border-r border-gray-300 p-2">{{ salary_form.addition }}</div>

        <!-- Designation & Sunday/C-off -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Designation:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.designation|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Sunday:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.sunday }}</div>

        <div class="col-span-2 text-right p-2">Remarks:</div>
        <div class="col-span-4 border-r border-gray-300 row-span-2 p-2 flex items-start">{{ salary_form.remarks1 }}</div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300">Grade:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.grade|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">C-off:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.coff }}</div>

        <div class="col-span-2 border-r border-gray-300"></div> <!-- Empty cell for Remarks -->

        <!-- Status & PL -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Status:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">
            {% if employee.offer %}
                {% if employee.offer.type_of == 1 %}SAPL{% elif employee.offer.type_of == 2 %}NEHA{% endif %} - {{ employee.offer.staff_type.description|default:"N/A" }}
            {% else %}N/A{% endif %}
        </div>

        <div class="col-span-2 text-right p-2">PL:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.pl }}</div>

        <div class="col-span-2 text-right p-2">Deduction:</div>
        <div class="col-span-4 border-r border-gray-300 p-2">{{ salary_form.deduction }}</div>

        <!-- Duty Hrs & OT Hrs -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Duty Hrs.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.offer.duty_hrs.hours|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Over Time Hrs.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ salary_form.over_time_hrs }}</div>

        <div class="col-span-2 text-right p-2">Remarks:</div>
        <div class="col-span-4 border-r border-gray-300 row-span-2 p-2 flex items-start">{{ salary_form.remarks2 }}</div>

        <!-- Employee Over Time Hrs & Over Time Rate -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Over Time Hrs.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.offer.ot_hrs.hours|default:"N/A" }}</div>

        <div class="col-span-2 text-right p-2">Over Time Rate:</div>
        <div class="col-span-2 border-r border-gray-300 p-2 font-bold text-gray-900">{{ ot_rate }}</div>

        <!-- A/C No. & PF No. & PAN No. & Email & Mobile No. -->
        <div class="col-span-2 text-right p-2 border-l border-gray-300">A/C No.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.bank_account_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300">PF No.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.pf_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300">PAN No.:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.pan_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>
        
        <div class="col-span-2 text-right p-2 border-l border-gray-300">Email:</div>
        <div class="col-span-2 border-r border-gray-300 p-2">{{ employee.company_email|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300"></div>

        <div class="col-span-2 text-right p-2 border-l border-gray-300 border-b border-gray-300">Mobile No.:</div>
        <div class="col-span-2 border-r border-gray-300 border-b border-gray-300 p-2">{{ employee.mobile_no.mobile_no|default:"N/A" }}</div>
        <div class="col-span-8 border-r border-gray-300 border-b border-gray-300"></div>
    </div>

    <!-- Display form errors -->
    {% if salary_form.errors %}
        <div class="text-red-600 mt-4 p-2 bg-red-100 rounded">
            <p class="font-bold">Please correct the following errors:</p>
            <ul class="list-disc list-inside">
                {% for field in salary_form %}
                    {% if field.errors %}
                        <li>{{ field.label }}: {{ field.errors|join:", " }}</li>
                    {% endif %}
                {% endfor %}
                {% if salary_form.non_field_errors %}
                    <li>{{ salary_form.non_field_errors|join:", " }}</li>
                {% endif %}
            </ul>
        </div>
    {% endif %}

    <!-- Conditionally disable Proceed button -->
    {% if salary_exists or month_info.working_days == 0 %}
        <script>
            // Alpine.js to disable button
            document.querySelector('#salary_details_form button[type="submit"]').setAttribute('disabled', 'true');
            document.querySelector('#salary_details_form button[type="submit"]').classList.add('opacity-50', 'cursor-not-allowed');
            {% if salary_exists %}
                // Alpine.js to show message
                console.log('Salary already exists for this month.');
            {% elif month_info.working_days == 0 %}
                // Alpine.js to show message
                console.log('Working days is not found for selected month.');
            {% endif %}
        </script>
    {% else %}
        <script>
            document.querySelector('#salary_details_form button[type="submit"]').removeAttribute('disabled');
            document.querySelector('#salary_details_form button[type="submit"]').classList.remove('opacity-50', 'cursor-not-allowed');
        </script>
    {% endif %}
</form>
```

**`_offer_accessories_table.html` (Partial for DataTables grid)**
```html
<div class="overflow-x-auto">
    <table id="offerAccessoriesTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Include In</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if accessories %}
                {% for acc in accessories %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ acc.includes_in.name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ acc.particulars }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ acc.qty }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ acc.amount }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ acc.total }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="6" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- DataTable initialization is handled by the main template's htmx:afterSwap listener -->
```

#### 4.5 URLs (`hr_payroll/urls.py`)

```python
from django.urls import path
from .views import (
    SalaryNewDetailsView, SalaryDynamicDataPartialView, 
    OfferAccessoriesPartialView, EmployeeImageView,
    SalaryProceedView, SalaryListView # Dummy list view
)

app_name = 'hr_payroll'

urlpatterns = [
    # Main salary new/details view for a specific employee
    path('salary/new/<str:emp_id>/', SalaryNewDetailsView.as_view(), name='salary_new_details'),

    # HTMX endpoint for dynamic month-selected data (attendance, misc, bank loan, etc.)
    path('salary/new/<str:emp_id>/month_data/', SalaryDynamicDataPartialView.as_view(), name='salary_month_data'),

    # HTMX endpoint for the Offer Accessories DataTables
    path('salary/new/<str:emp_id>/accessories_grid/', OfferAccessoriesPartialView.as_view(), name='offer_accessories_table'),

    # Endpoint for serving employee images (replaces Handler1.ashx)
    path('employee/image/<str:emp_id>/<int:comp_id>/', EmployeeImageView.as_view(), name='employee_image'),

    # Endpoint for processing salary creation/update
    path('salary/proceed/<str:emp_id>/', SalaryProceedView.as_view(), name='salary_proceed'),

    # Dummy URL for redirection after proceeding (replace with actual salary list URL)
    path('salary/list/', SalaryListView.as_view(), name='salary_list'),
]

```

#### 4.6 Tests (`hr_payroll/tests.py`)

Comprehensive tests cover model methods and view interactions, especially HTMX-driven ones.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
import calendar
from unittest.mock import patch, MagicMock

# Import all models to ensure they are covered
from hr_payroll.models import (
    Company, FinancialYear, Department, Designation, Grade, CorporateMobileNo,
    SwapCard, EmpType, DutyHour, OTHour, OvertimeType, OfferMaster, Employee,
    Holiday, WorkingDaysConfig, MobileBill, BankLoan, IncludesIn, OfferAccessory,
    SalaryMaster, SalaryDetail
)


class BaseTestSetup(TestCase):
    """Setup common data for all tests."""
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data first
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, start_date='2023-04-01', end_date='2024-03-31', year_name='2023-24')
        cls.department = Department.objects.create(id=1, description='HR', symbol='HR')
        cls.designation = Designation.objects.create(id=1, type_name='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A')
        cls.mobile_no_obj = CorporateMobileNo.objects.create(id=1, mobile_no='**********')
        cls.swap_card_obj = SwapCard.objects.create(id=1, swap_card_no='SC001')
        cls.emp_type = EmpType.objects.create(id=1, description='Permanent')
        cls.duty_hour = DutyHour.objects.create(id=1, hours=8.0)
        cls.ot_hour = OTHour.objects.create(id=1, hours=40.0) # Monthly OT hours from offer
        cls.overtime_type_calc = OvertimeType.objects.create(id=2, description='Calculated Rate')
        cls.overtime_type_na = OvertimeType.objects.create(id=1, description='Not Applicable')
        
        cls.offer = OfferMaster.objects.create(
            offer_id=1,
            staff_type=cls.emp_type,
            type_of=1, # SAPL
            duty_hrs=cls.duty_hour,
            ot_hrs=cls.ot_hour,
            salary=50000.00,
            overtime=cls.overtime_type_calc,
            increment=1000
        )
        
        cls.employee = Employee.objects.create(
            emp_id='EMP001',
            offer=cls.offer,
            fin_year=cls.fin_year,
            company=cls.company,
            title='Mr',
            employee_name='John Doe',
            swap_card=cls.swap_card_obj,
            department=cls.department,
            designation=cls.designation,
            grade=cls.grade,
            mobile_no=cls.mobile_no_obj,
            company_email='<EMAIL>',
            bank_account_no='**********',
            pf_no='PF12345',
            pan_no='PANABCDE',
            photo_file_name='john_doe.jpg'
        )

        # Create dummy data for helper models
        Holiday.objects.create(month=date.today().month, year=date.today().year, count=2, company=cls.company, fin_year=cls.fin_year)
        WorkingDaysConfig.objects.create(month=date.today().month, fin_year=cls.fin_year, days=22, company=cls.company)
        MobileBill.objects.create(employee=cls.employee, month=date.today().month, year=date.today().year, bill_amount=500.0, limit=400.0, excess_amount=100.0, company=cls.company, fin_year=cls.fin_year)
        BankLoan.objects.create(employee=cls.employee, amount=10000.0, installment=500.0, fin_year=cls.fin_year, company=cls.company)
        BankLoan.objects.create(employee=cls.employee, amount=5000.0, installment=200.0, fin_year=cls.fin_year, company=cls.company)

        # For accessories
        cls.includes_in_earnings = IncludesIn.objects.create(id=1, name='Earnings')
        cls.includes_in_deductions = IncludesIn.objects.create(id=2, name='Deductions')
        OfferAccessory.objects.create(id=1, offer_master=cls.offer, particulars='Bonus', qty=1, amount=1000, includes_in=cls.includes_in_earnings)
        OfferAccessory.objects.create(id=2, offer_master=cls.offer, particulars='Insurance', qty=1, amount=200, includes_in=cls.includes_in_deductions)

class EmployeeModelTest(BaseTestSetup):
    def test_employee_creation(self):
        self.assertEqual(self.employee.employee_name, 'John Doe')
        self.assertEqual(self.employee.emp_id, 'EMP001')
        self.assertEqual(self.employee.department.description, 'HR')

    def test_get_photo_url(self):
        # Test with photo data (mocked binary field)
        self.employee.photo_data = b'\x00\x01\x02' # Add dummy photo data
        self.employee.save()
        self.assertEqual(self.employee.get_photo_url(), f"/hr_payroll/employee/image/{self.employee.emp_id}/{self.employee.company.id}/")

        # Test without photo data
        self.employee.photo_data = None
        self.employee.save()
        self.assertEqual(self.employee.get_photo_url(), "/static/images/User.jpg")

    def test_get_month_info(self):
        current_month = date.today().month
        month_info = self.employee.get_month_info(current_month, self.fin_year.id, self.company.id)
        
        self.assertIn('days_in_month', month_info)
        self.assertIn('sundays', month_info)
        self.assertIn('holidays', month_info)
        self.assertIn('working_days', month_info)
        
        self.assertGreater(month_info['days_in_month'], 27)
        self.assertGreaterEqual(month_info['sundays'], 4)
        self.assertEqual(month_info['holidays'], 2) # From dummy Holiday model
        self.assertEqual(month_info['working_days'], 22) # From dummy WorkingDaysConfig

    def test_get_mobile_bill_details(self):
        bill_details = self.employee.get_mobile_bill_details(date.today().month, self.fin_year.id, self.company.id)
        self.assertEqual(bill_details['bill_amount'], 500.0)
        self.assertEqual(bill_details['limit'], 400.0)
        self.assertEqual(bill_details['excess_amount'], 100.0)

    def test_get_bank_loan_details(self):
        loan_details = self.employee.get_bank_loan_details(self.fin_year.id, self.company.id)
        self.assertEqual(loan_details['loan_amount'], 15000.0) # 10000 + 5000
        self.assertEqual(loan_details['installment_amount'], 700.0) # 500 + 200
        self.assertEqual(loan_details['installment_paid_amount'], 0.0) # No salary details yet
        self.assertEqual(loan_details['current_installment_to_pay'], 700.0)

        # Create a salary detail to test paid installments
        sm = SalaryMaster.objects.create(
            id=100, sys_date=date.today(), sys_time=timezone.now().time(),
            session_id='testuser', company=self.company, fin_year=self.fin_year,
            employee=self.employee, fmonth=date.today().month, increment=0
        )
        SalaryDetail.objects.create(salary_master=sm, installment=300.0)
        
        loan_details_after_payment = self.employee.get_bank_loan_details(self.fin_year.id, self.company.id)
        self.assertEqual(loan_details_after_payment['installment_paid_amount'], 300.0)
        self.assertEqual(loan_details_after_payment['current_installment_to_pay'], 700.0) # Still outstanding

        # Pay off remaining loan
        sm2 = SalaryMaster.objects.create(
            id=101, sys_date=date.today(), sys_time=timezone.now().time(),
            session_id='testuser', company=self.company, fin_year=self.fin_year,
            employee=self.employee, fmonth=(date.today().month % 12) + 1, increment=0
        )
        SalaryDetail.objects.create(salary_master=sm2, installment=14700.0) # Total 15000 paid

        loan_details_fully_paid = self.employee.get_bank_loan_details(self.fin_year.id, self.company.id)
        self.assertEqual(loan_details_fully_paid['installment_paid_amount'], 15000.0)
        self.assertEqual(loan_details_fully_paid['current_installment_to_pay'], 0.0)

    def test_calculate_ot_rate(self):
        # Test with 'Calculated Rate' OT type
        ot_rate = self.employee.calculate_ot_rate(date.today().month, self.fin_year.id, self.company.id)
        # Expected: (50000 / (22 * 8)) rounded to 2 decimal places = 50000 / 176 = 284.09
        self.assertAlmostEqual(ot_rate, 284.09, places=2)

        # Test with 'Not Applicable' OT type
        self.employee.offer.overtime = self.overtime_type_na
        self.employee.offer.save()
        ot_rate_na = self.employee.calculate_ot_rate(date.today().month, self.fin_year.id, self.company.id)
        self.assertEqual(ot_rate_na, 0.0)

    def test_salary_exists_for_month(self):
        # Initially, no salary master exists
        self.assertFalse(SalaryMaster.salary_exists_for_month(
            self.employee.emp_id, date.today().month, self.fin_year.id, self.company.id
        ))

        # Create one
        SalaryMaster.objects.create(
            id=1, sys_date=date.today(), sys_time=timezone.now().time(),
            session_id='testuser', company=self.company, fin_year=self.fin_year,
            employee=self.employee, fmonth=date.today().month, increment=0
        )
        self.assertTrue(SalaryMaster.salary_exists_for_month(
            self.employee.emp_id, date.today().month, self.fin_year.id, self.company.id
        ))


class OfferAccessoryModelTest(BaseTestSetup):
    def test_total_property(self):
        accessory = OfferAccessory.objects.get(id=1)
        self.assertEqual(accessory.total, 1000.0) # 1 * 1000

        accessory2 = OfferAccessory.objects.get(id=2)
        self.assertEqual(accessory2.total, 200.0) # 1 * 200


class SalaryNewDetailsViewTest(BaseTestSetup):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Mock session variables
        self.session_data = {
            'compid': self.company.id,
            'finyear': self.fin_year.id,
            'username': 'testuser'
        }
        self.client.session = self.session_data

    def test_salary_new_details_view_get(self):
        response = self.client.get(reverse('hr_payroll:salary_new_details', args=[self.employee.emp_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/salary_new_details.html')
        self.assertIn('employee', response.context)
        self.assertEqual(response.context['employee'], self.employee)
        self.assertIn('month_form', response.context)
        self.assertIn('accessories', response.context)
        self.assertEqual(len(response.context['accessories']), 2) # Two accessories created

    def test_salary_new_details_view_employee_not_found(self):
        response = self.client.get(reverse('hr_payroll:salary_new_details', args=['NON_EXISTENT_EMP']))
        self.assertEqual(response.status_code, 404)

    def test_salary_dynamic_data_partial_view_post(self):
        current_month = date.today().month
        post_data = {'fmonth': current_month}
        response = self.client.post(
            reverse('hr_payroll:salary_month_data', args=[self.employee.emp_id]),
            post_data,
            HTTP_HX_REQUEST='true' # Indicate HTMX request
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/_salary_data.html')
        self.assertIn('month_info', response.context)
        self.assertIn('salary_form', response.context)
        self.assertIn('salary_exists', response.context)
        self.assertFalse(response.context['salary_exists']) # Should not exist initially

        # Verify month info
        self.assertGreater(response.context['month_info']['days_in_month'], 27)

        # Create salary master for the month and re-test
        SalaryMaster.objects.create(
            id=2, sys_date=date.today(), sys_time=timezone.now().time(),
            session_id='testuser', company=self.company, fin_year=self.fin_year,
            employee=self.employee, fmonth=current_month, increment=0
        )
        response_exists = self.client.post(
            reverse('hr_payroll:salary_month_data', args=[self.employee.emp_id]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_exists.status_code, 200)
        self.assertTrue(response_exists.context['salary_exists'])

    def test_offer_accessories_partial_view_get(self):
        response = self.client.get(
            reverse('hr_payroll:offer_accessories_table', args=[self.employee.emp_id]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_payroll/_offer_accessories_table.html')
        self.assertIn('accessories', response.context)
        self.assertEqual(len(response.context['accessories']), 2)

    @patch('hr_payroll.views.open', new_callable=MagicMock)
    def test_employee_image_view(self, mock_open):
        # Test with actual photo data
        self.employee.photo_data = b'imagedata'
        self.employee.save()
        response = self.client.get(reverse('hr_payroll:employee_image', args=[self.employee.emp_id, self.company.id]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg')
        self.assertEqual(response.content, b'imagedata')

        # Test without photo data (should serve default)
        self.employee.photo_data = None
        self.employee.save()
        
        mock_file = MagicMock()
        mock_file.read.return_value = b'defaultimagedata'
        mock_open.return_value.__enter__.return_value = mock_file

        response_default = self.client.get(reverse('hr_payroll:employee_image', args=[self.employee.emp_id, self.company.id]))
        self.assertEqual(response_default.status_code, 200)
        self.assertEqual(response_default['Content-Type'], 'image/jpeg')
        self.assertEqual(response_default.content, b'defaultimagedata')
        mock_open.assert_called_with("static/images/User.jpg", "rb")

    def test_salary_proceed_view_post_success(self):
        initial_salary_master_count = SalaryMaster.objects.count()
        initial_salary_detail_count = SalaryDetail.objects.count()

        current_month = date.today().month
        post_data = {
            'fmonth': current_month,
            'present': 20.0, 'absent': 5.0, 'late_in': 1.0, 'half_day': 0.5,
            'sunday': 4.0, 'coff': 0.0, 'pl': 2.0, 'over_time_hrs': 10.0,
            'installment': 500.0, 'mobile_exe_amt': 50.0, 'addition': 100.0,
            'remarks1': 'Test remarks 1', 'deduction': 20.0, 'remarks2': 'Test remarks 2'
        }
        
        response = self.client.post(
            reverse('hr_payroll:salary_proceed', args=[self.employee.emp_id]),
            post_data,
            HTTP_HX_REQUEST='true'
        )

        self.assertEqual(response.status_code, 204) # HTMX success code for redirect
        self.assertEqual(response.headers['HX-Redirect'], reverse('hr_payroll:salary_list'))
        
        # Verify creation
        self.assertEqual(SalaryMaster.objects.count(), initial_salary_master_count + 1)
        self.assertEqual(SalaryDetail.objects.count(), initial_salary_detail_count + 1)
        
        # Verify data
        new_salary_master = SalaryMaster.objects.get(employee=self.employee, fmonth=current_month)
        self.assertEqual(new_salary_master.increment, self.employee.offer.increment)
        
        new_salary_detail = new_salary_master.get_details()
        self.assertEqual(new_salary_detail.present, 20.0)
        self.assertEqual(new_salary_detail.installment, 500.0)
        self.assertAlmostEqual(new_salary_detail.over_time_rate, 284.09, places=2)

    def test_salary_proceed_view_post_validation_error(self):
        initial_salary_master_count = SalaryMaster.objects.count()
        
        current_month = date.today().month
        # Missing required field 'present'
        post_data = {
            'fmonth': current_month,
            # 'present': 20.0,
            'absent': 5.0, 'late_in': 1.0, 'half_day': 0.5,
            'sunday': 4.0, 'coff': 0.0, 'pl': 2.0, 'over_time_hrs': 10.0,
            'installment': 500.0, 'mobile_exe_amt': 50.0, 'addition': 100.0,
            'remarks1': 'Test remarks 1', 'deduction': 20.0, 'remarks2': 'Test remarks 2'
        }
        
        response = self.client.post(
            reverse('hr_payroll:salary_proceed', args=[self.employee.emp_id]),
            post_data,
            HTTP_HX_REQUEST='true'
        )

        self.assertEqual(response.status_code, 400) # Bad Request for form validation error
        self.assertTemplateUsed(response, 'hr_payroll/_salary_data.html') # Re-renders partial with errors
        self.assertEqual(SalaryMaster.objects.count(), initial_salary_master_count) # No new record
        self.assertContains(response, 'Present days is required.') # Check for error message in rendered content

    def test_salary_proceed_view_post_salary_exists(self):
        current_month = date.today().month
        # Create a salary master first to simulate existing salary
        SalaryMaster.objects.create(
            id=3, sys_date=date.today(), sys_time=timezone.now().time(),
            session_id='testuser', company=self.company, fin_year=self.fin_year,
            employee=self.employee, fmonth=current_month, increment=0
        )
        
        post_data = {
            'fmonth': current_month,
            'present': 20.0, 'absent': 5.0, 'late_in': 1.0, 'half_day': 0.5,
            'sunday': 4.0, 'coff': 0.0, 'pl': 2.0, 'over_time_hrs': 10.0,
            'installment': 500.0, 'mobile_exe_amt': 50.0, 'addition': 100.0,
            'remarks1': 'Test remarks 1', 'deduction': 20.0, 'remarks2': 'Test remarks 2'
        }

        response = self.client.post(
            reverse('hr_payroll:salary_proceed', args=[self.employee.emp_id]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200) # Not a 400 or 204, indicates message swap
        self.assertContains(response, 'Salary for the selected month is already found.')
        self.assertEqual(response.headers['HX-Retarget'], '#messages') # Should target messages div

    def test_salary_proceed_view_post_no_working_days(self):
        current_month = date.today().month
        # Mock WorkingDaysConfig to return 0 working days
        WorkingDaysConfig.objects.filter(
            month=current_month, fin_year=self.fin_year, company=self.company
        ).update(days=0)
        
        post_data = {
            'fmonth': current_month,
            'present': 20.0, 'absent': 5.0, 'late_in': 1.0, 'half_day': 0.5,
            'sunday': 4.0, 'coff': 0.0, 'pl': 2.0, 'over_time_hrs': 10.0,
            'installment': 500.0, 'mobile_exe_amt': 50.0, 'addition': 100.0,
            'remarks1': 'Test remarks 1', 'deduction': 20.0, 'remarks2': 'Test remarks 2'
        }

        response = self.client.post(
            reverse('hr_payroll:salary_proceed', args=[self.employee.emp_id]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200) # Not a 400 or 204
        self.assertContains(response, 'Working days is not found for selected month.')
        self.assertEqual(response.headers['HX-Retarget'], '#messages')
```

### Step 5: HTMX and Alpine.js Integration

The plan heavily relies on HTMX for dynamic content loading and form submissions, aligning with the "no additional JavaScript" directive beyond Alpine.js.

*   **Month Selection:** The `ddlMonth` is converted to a Django `forms.ChoiceField` wrapped in a `<form>` with `hx-post` and `hx-trigger="change"`. This triggers a POST request to `salary_month_data` URL, which returns the `_salary_data.html` partial, updating the `#salary_data_container` div. This replaces the `ddlMonth_SelectedIndexChanged` AutoPostBack.
*   **Form Submission:** The main form (`salary_details_form`) for attendance and miscellaneous inputs uses `hx-post` to `salary_proceed` URL. Upon success, `HX-Redirect` is used to navigate to the list page. Upon validation failure or business rule violation (salary exists, no working days), the view returns an HTMX response to `HX-Retarget` and `HX-Swap` into a messages container, and for form errors, the partial is re-rendered with validation messages visible.
*   **DataTables:** The `GridView1` is replaced by an HTML `<table>` that is loaded into `#accessories_grid_container` via `hx-get` to `offer_accessories_table` URL. Once loaded, DataTables JavaScript is initialized on that table. The `htmx:afterSwap` event listener ensures DataTables is re-initialized correctly after HTMX injects the new table content.
*   **Employee Image:** The `Image1` control's dynamic `ImageUrl` is replaced by a dedicated Django view (`EmployeeImageView`) that streams the image data, allowing the `<img>` tag `src` attribute to directly point to this URL.
*   **Alpine.js:** While not explicitly required for these core HTMX interactions, Alpine.js could be used for minor client-side UI states, such as showing/hiding loading spinners, managing modal visibility (if one were introduced for complex workflows), or small interactive elements on the page, like the modal for adding/editing items (though not explicitly needed for this specific page, the prompt includes it for generic CRUD). The current setup only relies on HTMX for the dynamic content loading. For the button disabling, a small Alpine.js directive could be added directly in the HTML if desired. I've opted for a simpler `script` tag for the button state as it's a direct HTML attribute manipulation.

### Final Notes

This comprehensive plan transforms the ASP.NET salary details page into a robust Django application. The focus on "fat models" ensures business logic is centralized, making the code more maintainable and testable. "Thin views" keep controllers lean and focused on orchestration. The exclusive use of HTMX and Alpine.js delivers a responsive, modern user experience without the complexity of traditional SPA frameworks, significantly reducing the front-end development burden. AI-assisted automation can greatly simplify the initial mapping of SQL queries to Django ORM and the translation of C# business logic into Python methods.