## ASP.NET to Django Conversion Script: Bank Loan Edit

This plan outlines the systematic modernization of your ASP.NET Bank Loan Edit module to a robust, scalable Django application. Our focus is on transforming legacy code into a maintainable, high-performance solution using modern architectural patterns and automation-friendly techniques.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify interactions with two primary tables: `tblHR_BankLoan` and `tblHR_OfficeStaff`.

**Inferred Schema:**

**Table: `tblHR_BankLoan`**
- **Id**: Integer (Primary Key)
- **EmpId**: String (Foreign Key to `tblHR_OfficeStaff`, represents Employee ID)
- **BankName**: String
- **Branch**: String
- **Amount**: Decimal/Double (Numeric with up to 3 decimal places)
- **Installment**: Decimal/Double (Numeric with up to 3 decimal places)
- **fromDate**: Date (Stored as string `dd-MM-yyyy`, but should be a Date type)
- **ToDate**: Date (Stored as string `dd-MM-yyyy`, but should be a Date type)
- **SysDate**: Date (System date of last modification)
- **SysTime**: Time (System time of last modification)
- **CompId**: Integer (Company ID)
- **FinYearId**: Integer (Financial Year ID)
- **SessionId**: String (User/Session ID)

**Table: `tblHR_OfficeStaff`**
- **EmpId**: String (Primary Key, Employee ID)
- **Title**: String (e.g., Mr., Ms.)
- **EmployeeName**: String (Full name of the employee)
- **CompId**: Integer (Company ID)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

-   **Read (R):** The `binddata()` function in the C# code-behind retrieves bank loan records from `tblHR_BankLoan`, optionally filtered by `EmpId`. It also fetches `EmployeeName` from `tblHR_OfficeStaff`. This data is then bound to `GridView2`.
-   **Update (U):** The `BtnSubmit_Click` event handles the update operation. It iterates through selected rows in `GridView2` (checked by `CheckBox1`), validates the input fields (Bank Name, Branch, Amount, Installment, From Date, To Date), and then updates the corresponding records in `tblHR_BankLoan`. This is a *batch update* functionality.
-   **Search/Filter:** The `Button1_Click` and `DrpField_SelectedIndexChanged` events trigger the `binddata()` function to filter the displayed loans based on `EmpId` (obtained from `TxtEmpName` using an internal `fun.getCode()` method, which parses "Employee Name [EmpId]" format).
-   **Dynamic UI (Client-side logic):** The `CheckBox1_CheckedChanged` event dynamically enables/disables input fields in a `GridView` row and toggles validation visibility.
-   **Autocomplete:** `GetCompletionList` provides employee name suggestions for the search box.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting interactions.

**Instructions:**

-   **Search/Filter Controls:**
    -   `DrpField` (DropDownList): Used to select search criteria (e.g., "Employee Name"). This will be a `<select>` element.
    -   `TxtEmpName` (TextBox) with `AutoCompleteExtender`: An input field for employee name search with autocomplete functionality. This will be an `<input type="text">` potentially combined with an HTMX-powered `datalist` or an HTMX request to dynamically populate options.
    -   `TxtMrs` (TextBox): Another search input, toggled visible/hidden. For this modernization, we'll simplify and only keep the `TxtEmpName` search based on `DrpField`'s current configuration.
    -   `Button1` (Button, "Search"): Triggers the data refresh. This will be an HTMX-enabled button.
-   **Data Display and Batch Edit Grid:**
    -   `GridView2` (GridView): Displays multiple bank loan records. Each row contains editable fields (TextBank, TextBranch, TextAmount, TextInstallment, TextFromDate, TextToDate) and a checkbox (`CheckBox1`) to enable editing for that specific row. This will be represented by an HTML `<table>` managed by DataTables, with each row containing form fields rendered by a Django formset.
    -   `CalendarExtender`: Used for date input. This will be replaced by standard HTML5 date inputs (`<input type="date">`) or a lightweight Alpine.js date picker if needed, but the current `dd-MM-yyyy` format implies text input with a JS date picker. We will implement basic date inputs, relying on browser's date picker capabilities or a simple Alpine.js integration if a specific format is enforced client-side.
    -   `RequiredFieldValidator`, `RegularExpressionValidator`: Client-side validation. This will be handled by Django Forms validation on the server-side, with Alpine.js or HTMX swap for immediate feedback on the client side.
-   **Action Button:**
    -   `BtnSubmit` (Button, "Update"): Submits the changes for all selected rows. This will be an HTMX-enabled button that submits the entire formset.

---

### Step 4: Generate Django Code

We will create a new Django application, e.g., `hr_app`, to house this module.

#### 4.1 Models (`hr_app/models.py`)

**Task:** Create Django models based on the database schema.

**Instructions:**
-   Map `tblHR_BankLoan` to `BankLoan` and `tblHR_OfficeStaff` to `OfficeStaff`.
-   Define fields with appropriate Django types, ensuring `db_column` is used for direct mapping.
-   Implement any business logic (like getting employee name) as model methods.

```python
from django.db import models
from django.db.models import F
from decimal import Decimal
from datetime import date, time

class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff table. Represents an employee.
    """
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=20, null=True, blank=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employeename} [{self.empid}]".strip()

class BankLoan(models.Model):
    """
    Maps to tblHR_BankLoan table. Represents a bank loan record for an employee.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    empid = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='empid') # Use FK to OfficeStaff
    bankname = models.CharField(db_column='BankName', max_length=100)
    branch = models.CharField(db_column='Branch', max_length=100)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3)
    installment = models.DecimalField(db_column='Installment', max_digits=18, decimal_places=3)
    fromdate = models.DateField(db_column='fromDate', null=True, blank=True)
    todate = models.DateField(db_column='ToDate', null=True, blank=True)
    sysdate = models.DateField(db_column='SysDate', null=True, blank=True)
    systime = models.TimeField(db_column='SysTime', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    sessionid = models.CharField(db_column='SessionId', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_BankLoan'
        verbose_name = 'Bank Loan'
        verbose_name_plural = 'Bank Loans'

    def __str__(self):
        return f"Loan {self.id} for {self.empid.employeename} - {self.bankname}"

    def get_employee_full_name(self):
        """
        Retrieves the full employee name including title.
        """
        return f"{self.empid.title or ''} {self.empid.employeename}".strip()

    @classmethod
    def get_filtered_loans(cls, company_id, financial_year_id, employee_id=None):
        """
        Business logic to retrieve bank loans based on company, financial year, and optional employee ID.
        Simulates the 'binddata' method from ASP.NET.
        """
        queryset = cls.objects.filter(
            compid=company_id,
            finyearid__lte=financial_year_id # ASP.NET used <= for FinYearId
        ).order_by(F('empid').desc())

        if employee_id:
            queryset = queryset.filter(empid=employee_id)
        
        # Prefetch related employee data to avoid N+1 queries
        queryset = queryset.select_related('empid') 
        return queryset

    def update_loan_details(self, user_session_id, company_id, financial_year_id,
                             bank_name, branch, amount, installment, from_date, to_date):
        """
        Business logic to update a bank loan record.
        This method encapsulates the update logic previously found in BtnSubmit_Click loop.
        """
        self.bankname = bank_name
        self.branch = branch
        self.amount = amount
        self.installment = installment
        self.fromdate = from_date
        self.todate = to_date
        self.sysdate = date.today()
        self.systime = time.now()
        self.sessionid = user_session_id
        self.compid = company_id
        self.finyearid = financial_year_id
        self.save()

```

#### 4.2 Forms (`hr_app/forms.py`)

**Task:** Define Django forms for user input and a formset for batch editing.

**Instructions:**
-   Create a `ModelForm` for `BankLoan`.
-   Create a `ModelForm` for the search functionality (employee filter).
-   Use `modelformset_factory` to create a formset for batch updates.
-   Add widgets with Tailwind CSS classes.
-   Implement validation methods as needed.

```python
from django import forms
from django.forms import modelformset_factory
from .models import BankLoan, OfficeStaff
import re

class EmployeeSearchForm(forms.Form):
    """
    Form for searching employees.
    """
    FIELD_CHOICES = [('0', 'Employee Name')] # Simplified as only '0' was truly active
    
    drp_field = forms.ChoiceField(
        choices=FIELD_CHOICES,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-48 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    txt_emp_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-96 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 
                                       'placeholder': 'Start typing employee name...',
                                       'hx-get': '/hr/autocomplete/employee/', # HTMX for autocomplete suggestions
                                       'hx-trigger': 'keyup changed delay:500ms, search',
                                       'hx-target': '#employee-suggestions',
                                       'hx-swap': 'innerHTML',
                                       'autocomplete': 'off'}) # Disable native autocomplete
    )
    # The actual employee ID will be parsed from the selected value (e.g., "Name [ID]")
    # Or, preferably, passed as a hidden field if a more complex autocomplete is used.
    # For now, we'll assume `txt_emp_name` will contain "EmployeeName [EmpId]"
    # and the view logic will extract the EmpId.

    def clean_txt_emp_name(self):
        emp_name_with_id = self.cleaned_data.get('txt_emp_name', '')
        if emp_name_with_id:
            match = re.search(r'\[(.*?)\]$', emp_name_with_id)
            if match:
                return match.group(1) # Return just the EmpId
            else:
                # If no ID found, try to match by name, or raise error
                # For this migration, we'll assume the format is always "Name [ID]" if selected
                # Or handle partial matches, which is more complex.
                # Let's assume the autocomplete ensures the format is correct for now.
                # If it's a partial name search, we might need a different approach.
                # For consistency with ASP.NET's fun.getCode, we assume the format.
                raise forms.ValidationError("Please select a valid employee from the suggestions.")
        return None # No employee selected

class BankLoanForm(forms.ModelForm):
    """
    Form for a single BankLoan record, used within the formset.
    """
    # Checkbox to enable/disable row editing (will be handled by Alpine.js)
    # This field is not directly bound to the model.
    select_for_edit = forms.BooleanField(required=False, initial=False, 
                                         widget=forms.CheckboxInput(attrs={'x-model': 'isSelected', 'x-on:change': 'toggleFields'}))
    
    class Meta:
        model = BankLoan
        fields = ['bankname', 'branch', 'amount', 'installment', 'fromdate', 'todate']
        widgets = {
            'bankname': forms.TextInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'branch': forms.TextInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'amount': forms.NumberInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'installment': forms.NumberInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected'}),
            'fromdate': forms.DateInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected', 'type': 'date'}),
            'todate': forms.DateInput(attrs={'class': 'box3 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-bind:disabled': '!isSelected', 'type': 'date'}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        is_selected_for_edit = cleaned_data.get('select_for_edit')
        
        # Only perform required field validation if the row is selected for edit
        if is_selected_for_edit:
            required_fields = ['bankname', 'branch', 'amount', 'installment', 'fromdate', 'todate']
            for field_name in required_fields:
                if not cleaned_data.get(field_name):
                    self.add_error(field_name, "This field is required.")
        return cleaned_data

# Formset for batch editing multiple BankLoan records
BankLoanFormSet = modelformset_factory(
    BankLoan,
    form=BankLoanForm,
    extra=0, # No extra blank forms by default
    can_delete=False # No delete functionality in original
)

```

#### 4.3 Views (`hr_app/views.py`)

**Task:** Implement a single CBV to manage search, display, and batch update of bank loans.

**Instructions:**
-   A `TemplateView` will handle the initial page load and render the base structure.
-   Helper methods or functions will fetch data and handle formset processing to keep view methods thin.
-   Separate `FormView` or dedicated HTMX endpoint for the formset table rendering and submission.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.forms import modelformset_factory
from .models import BankLoan, OfficeStaff
from .forms import EmployeeSearchForm, BankLoanForm, BankLoanFormSet # Import the formset

import re
from datetime import date, time # Import date and time

class BankLoanManagementView(TemplateView):
    """
    Handles the main Bank Loan Edit page, including search and batch update grid.
    """
    template_name = 'hr_app/bankloan/bankloan_management.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form
        context['search_form'] = EmployeeSearchForm()
        # Initialize empty formset for initial page load or no search results
        context['formset'] = BankLoanFormSet(queryset=BankLoan.objects.none())
        return context

    # The HTMX POST request for the search button will be handled by a dedicated view
    # The HTMX POST request for the update button will be handled by a dedicated view

class BankLoanTablePartialView(View):
    """
    Renders the BankLoan table partial, handling search and initial display.
    Triggered by HTMX GET for initial load and search button.
    """
    def get(self, request, *args, **kwargs):
        # Placeholder for CompId and FinYearId from session/user context
        # In a real app, these would come from request.user profile or similar
        comp_id = request.session.get('compid', 1)  # Default for testing
        fin_year_id = request.session.get('finyear', date.today().year) # Default for testing

        search_form = EmployeeSearchForm(request.GET)
        employee_id = None
        if search_form.is_valid():
            employee_id = search_form.cleaned_data.get('txt_emp_name') # This is already the extracted EmpId

        # Fetch loans based on search criteria using the model's business logic
        queryset = BankLoan.get_filtered_loans(comp_id, fin_year_id, employee_id)
        
        # Initialize formset with queryset
        formset = BankLoanFormSet(queryset=queryset)

        context = {
            'formset': formset,
            'search_form': search_form, # Pass form for consistency, though not used in table partial itself
            'employee_id': employee_id # Pass employee_id for potential display
        }
        return render(request, 'hr_app/bankloan/_bankloan_table_partial.html', context)

    def post(self, request, *args, **kwargs):
        """
        Handles the formset submission (batch update).
        Triggered by HTMX POST from the "Update" button within the partial.
        """
        # Placeholder for CompId and FinYearId from session/user context
        comp_id = request.session.get('compid', 1)  # Default for testing
        fin_year_id = request.session.get('finyear', date.today().year) # Default for testing
        session_id = request.user.username if request.user.is_authenticated else "anonymous" # For ASP.NET SessionId

        formset = BankLoanFormSet(request.POST)

        if formset.is_valid():
            # Process only the forms that were checked for edit (select_for_edit = True)
            updated_count = 0
            for form in formset:
                if form.cleaned_data.get('select_for_edit') and form.has_changed():
                    bank_loan_instance = form.save(commit=False)
                    bank_loan_instance.update_loan_details(
                        user_session_id=session_id,
                        company_id=comp_id,
                        financial_year_id=fin_year_id,
                        bank_name=bank_loan_instance.bankname, # Using values from the form
                        branch=bank_loan_instance.branch,
                        amount=bank_loan_instance.amount,
                        installment=bank_loan_instance.installment,
                        from_date=bank_loan_instance.fromdate,
                        to_date=bank_loan_instance.todate
                    )
                    updated_count += 1
            messages.success(request, f'{updated_count} Bank Loan records updated successfully.')
            
            # Re-fetch the queryset to ensure updated data is displayed
            # This is critical if updates affect data displayed in the grid
            # For simplicity, we just filter with existing criteria, but a real app might
            # store search criteria in session or hidden fields to re-apply.
            
            # Re-initialize the search form from request.POST to get current search parameters
            search_form = EmployeeSearchForm(request.POST)
            employee_id = None
            if search_form.is_valid():
                employee_id = search_form.cleaned_data.get('txt_emp_name') # This is already the extracted EmpId

            queryset = BankLoan.get_filtered_loans(comp_id, fin_year_id, employee_id)
            formset = BankLoanFormSet(queryset=queryset) # Create a new formset with updated data

            # HTMX triggers to refresh the table.
            # Using 'refreshBankLoanTable' to ensure the entire table reloads.
            response = render(request, 'hr_app/bankloan/_bankloan_table_partial.html', {
                'formset': formset,
                'search_form': search_form,
                'employee_id': employee_id
            })
            response['HX-Trigger'] = 'refreshBankLoanTable'
            return response
        else:
            # If formset is invalid, render the partial with errors
            messages.error(request, 'Error updating Bank Loan records. Please check the inputs.')
            
            # Re-initialize the search form from request.POST to get current search parameters
            search_form = EmployeeSearchForm(request.POST)
            employee_id = None
            if search_form.is_valid():
                employee_id = search_form.cleaned_data.get('txt_emp_name') # This is already the extracted EmpId
            
            # When formset is invalid, it contains the errors, so pass it directly
            return render(request, 'hr_app/bankloan/_bankloan_table_partial.html', {
                'formset': formset,
                'search_form': search_form,
                'employee_id': employee_id
            })

class EmployeeAutocompleteView(View):
    """
    Provides autocomplete suggestions for employee names.
    Simulates the GetCompletionList web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        # Placeholder for CompId from session/user context
        comp_id = request.session.get('compid', 1) # Default for testing

        if prefix_text:
            employees = OfficeStaff.objects.filter(
                compid=comp_id,
                employeename__icontains=prefix_text
            ).values('empid', 'employeename', 'title')[:10] # Limit to 10 suggestions

            suggestions = []
            for emp in employees:
                full_name = f"{emp['title'] or ''} {emp['employeename']}".strip()
                suggestions.append(f"{full_name} [{emp['empid']}]")
            
            # HTMX expects HTML for a datalist or options for a select.
            # For a simple input with datalist, it's a <datalist> tag.
            # For this example, we return a simple JSON response for `datalist` options.
            # For real autocomplete, you'd return <option> elements directly for hx-target of a <datalist> element.
            # Or, for more complex scenarios, a rich HTML snippet.
            return render(request, 'hr_app/bankloan/_employee_suggestions.html', {'suggestions': suggestions})
        return HttpResponse("") # Return empty response if no query

```

#### 4.4 Templates (`hr_app/templates/hr_app/bankloan/`)

**Task:** Create templates for the main view and partials for HTMX interactions.

**Instructions:**
-   `bankloan_management.html`: The main page, extends `core/base.html`, includes search form, and a container for the bank loan table.
-   `_bankloan_table_partial.html`: Renders the `formset` as a table, includes DataTables initialization, and Alpine.js for row-level field toggling.
-   `_employee_suggestions.html`: Renders the `datalist` options for autocomplete.

**`hr_app/templates/hr_app/bankloan/bankloan_management.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Bank Loan - Edit</h2>

        <div class="flex items-center space-x-4 mb-6">
            <label for="{{ search_form.drp_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ search_form.drp_field.label }}:
            </label>
            {{ search_form.drp_field }}

            <div class="relative flex-grow">
                <label for="{{ search_form.txt_emp_name.id_for_label }}" class="sr-only">
                    {{ search_form.txt_emp_name.label }}:
                </label>
                {{ search_form.txt_emp_name }}
                <datalist id="employee-suggestions"></datalist> {# Autocomplete suggestions will populate here #}
            </div>

            <button
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'bankloan_table' %}"
                hx-target="#bankloan-table-container"
                hx-swap="innerHTML"
                hx-include="#search-form" {# Include search form fields #}
                hx-indicator="#loading-indicator"
            >
                Search
            </button>
            <span id="loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>

        <div id="bankloan-table-container"
             hx-trigger="load, refreshBankLoanTable from:body"
             hx-get="{% url 'bankloan_table' %}"
             hx-swap="innerHTML"
             hx-indicator="#loading-indicator"
             class="min-h-[200px] flex items-center justify-center">
            <!-- Initial content or loading state -->
            <p class="text-gray-500">Use the search to find bank loan records.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is included in base.html. No additional global Alpine.js init needed here.
    // The component logic for rows is directly in the _bankloan_table_partial.html
</script>
{% endblock %}

```

**`hr_app/templates/hr_app/bankloan/_bankloan_table_partial.html`**
```html
<form id="bankloan-edit-form" hx-post="{% url 'bankloan_table' %}" hx-swap="outerHTML" hx-indicator="#loading-indicator">
    {% csrf_token %}
    {{ formset.management_form }}
    
    {# Hidden input to pass search form data back on update, if needed for re-querying #}
    {% for field in search_form %}
        <input type="hidden" name="{{ field.name }}" value="{{ field.value|default_if_none:'' }}">
    {% endfor %}

    <div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
        <table id="bankloanTable" class="min-w-full divide-y divide-gray-200 bg-white">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[4%]">SN</th>
                    <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[4%]">Edit</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[6%]">EmpId</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Emp Name</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[18%]">Bank Name</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Branch</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Amount</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">Installment</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">From Date</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">To Date</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% if formset %}
                    {% for form in formset %}
                        <tr x-data="{ isSelected: {% if form.instance.pk %}false{% else %}true{% endif %}, toggleFields() { /* Logic for enabling fields in Alpine.js */ } }">
                            <td class="py-2 px-4 text-right align-middle text-sm text-gray-900">{{ forloop.counter }}</td>
                            <td class="py-2 px-4 text-center align-middle">
                                {{ form.select_for_edit }}
                            </td>
                            <td class="py-2 px-4 text-center align-middle text-sm text-gray-900">
                                {{ form.instance.empid.empid }}
                                {{ form.id }} {# Hidden ID field for the model instance #}
                            </td>
                            <td class="py-2 px-4 text-left align-middle text-sm text-gray-900">
                                {{ form.instance.get_employee_full_name }}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.bankname }}
                                {% if form.bankname.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bankname.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.branch }}
                                {% if form.branch.errors %}<p class="text-red-500 text-xs mt-1">{{ form.branch.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.amount }}
                                {% if form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.installment }}
                                {% if form.installment.errors %}<p class="text-red-500 text-xs mt-1">{{ form.installment.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.fromdate }}
                                {% if form.fromdate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.fromdate.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 align-middle">
                                {{ form.todate }}
                                {% if form.todate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.todate.errors }}</p>{% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="10" class="py-4 px-4 text-center text-sm text-gray-500">
                            No data to display!
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    {% if formset %}
    <div class="mt-6 text-center">
        <button
            type="submit"
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-sm transition duration-150 ease-in-out"
            onclick="return confirm('Are you sure you want to update the selected bank loans?');"
        >
            Update
        </button>
    </div>
    {% endif %}
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables
        $('#bankloanTable').DataTable({
            "pageLength": 15, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "pagingType": "full_numbers", // For full pagination controls like ASP.NET
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] } // SN and Checkbox columns not sortable
            ]
        });

        // Alpine.js initialization for row-level checkbox logic
        // This targets each <tr> element within the formset
        document.querySelectorAll('#bankloanTable tbody tr').forEach(row => {
            Alpine.data('row', () => ({
                isSelected: false,
                init() {
                    // Initialize isSelected based on hidden value if needed
                    // For batch update, initial state is usually 'disabled'
                    // In this case, we rely on x-bind:disabled based on isSelected
                    // Fields are initially disabled, checkbox enables them.
                    this.$nextTick(() => {
                        const checkbox = row.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = this.isSelected; // Sync checkbox with Alpine state
                            this.toggleFields(); // Set initial disabled state
                        }
                    });
                },
                toggleFields() {
                    const inputs = row.querySelectorAll('input[type="text"], input[type="number"], input[type="date"]');
                    inputs.forEach(input => {
                        input.disabled = !this.isSelected;
                    });
                }
            }));
        });
    });
</script>
```

**`hr_app/templates/hr_app/bankloan/_employee_suggestions.html`**
```html
{% for suggestion in suggestions %}
    <option value="{{ suggestion }}"></option>
{% endfor %}
```

#### 4.5 URLs (`hr_app/urls.py`)

**Task:** Define URL patterns for the views and HTMX partials.

**Instructions:**
-   Map the main management view.
-   Map the partial view for the table content (for both GET and POST requests).
-   Map the autocomplete endpoint.

```python
from django.urls import path
from .views import BankLoanManagementView, BankLoanTablePartialView, EmployeeAutocompleteView

urlpatterns = [
    path('bankloan/edit/', BankLoanManagementView.as_view(), name='bankloan_management'),
    path('bankloan/table/', BankLoanTablePartialView.as_view(), name='bankloan_table'),
    path('autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]
```

#### 4.6 Tests (`hr_app/tests.py`)

**Task:** Write comprehensive tests for the models and views.

**Instructions:**
-   Include unit tests for `BankLoan` and `OfficeStaff` models (properties, methods).
-   Add integration tests for the `BankLoanManagementView` and its HTMX interactions (GET, POST for search and update).
-   Test autocomplete functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BankLoan, OfficeStaff
from datetime import date, time
from decimal import Decimal

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 1
        OfficeStaff.objects.create(
            empid='EMP001',
            title='Mr.',
            employeename='John Doe',
            compid=cls.company_id
        )
        OfficeStaff.objects.create(
            empid='EMP002',
            title='Ms.',
            employeename='Jane Smith',
            compid=cls.company_id
        )

    def test_office_staff_creation(self):
        staff = OfficeStaff.objects.get(empid='EMP001')
        self.assertEqual(staff.employeename, 'John Doe')
        self.assertEqual(staff.compid, self.company_id)

    def test_get_employee_full_name(self):
        staff = OfficeStaff.objects.get(empid='EMP001')
        self.assertEqual(staff.__str__(), 'Mr. John Doe [EMP001]')

class BankLoanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.fin_year_id = 2023
        cls.employee1 = OfficeStaff.objects.create(empid='EMP001', title='Mr.', employeename='John Doe', compid=cls.company_id)
        cls.employee2 = OfficeStaff.objects.create(empid='EMP002', title='Ms.', employeename='Jane Smith', compid=cls.company_id)

        BankLoan.objects.create(
            id=1, empid=cls.employee1, bankname='Bank A', branch='Branch X',
            amount=Decimal('10000.000'), installment=Decimal('1000.000'),
            fromdate=date(2023, 1, 1), todate=date(2023, 10, 1),
            sysdate=date.today(), systime=time(12, 0, 0),
            compid=cls.company_id, finyearid=cls.fin_year_id, sessionid='testuser'
        )
        BankLoan.objects.create(
            id=2, empid=cls.employee2, bankname='Bank B', branch='Branch Y',
            amount=Decimal('20000.000'), installment=Decimal('2000.000'),
            fromdate=date(2023, 2, 1), todate=date(2023, 11, 1),
            sysdate=date.today(), systime=time(13, 0, 0),
            compid=cls.company_id, finyearid=cls.fin_year_id, sessionid='testuser'
        )
        BankLoan.objects.create(
            id=3, empid=cls.employee1, bankname='Bank C', branch='Branch Z',
            amount=Decimal('5000.000'), installment=Decimal('500.000'),
            fromdate=date(2022, 5, 1), todate=date(2022, 12, 1), # Older financial year
            sysdate=date.today(), systime=time(14, 0, 0),
            compid=cls.company_id, finyearid=2022, sessionid='testuser'
        )

    def test_bank_loan_creation(self):
        loan = BankLoan.objects.get(id=1)
        self.assertEqual(loan.empid.employeename, 'John Doe')
        self.assertEqual(loan.bankname, 'Bank A')
        self.assertEqual(loan.amount, Decimal('10000.000'))

    def test_get_employee_full_name_method(self):
        loan = BankLoan.objects.get(id=1)
        self.assertEqual(loan.get_employee_full_name(), 'Mr. John Doe')

    def test_get_filtered_loans_no_employee_id(self):
        loans = BankLoan.get_filtered_loans(self.company_id, self.fin_year_id)
        # Should return loans from current or earlier financial years up to current
        self.assertEqual(loans.count(), 3) # Includes loan 1, 2, and 3 (as finyearid <= 2023)
        self.assertIn(BankLoan.objects.get(id=1), loans)
        self.assertIn(BankLoan.objects.get(id=2), loans)
        self.assertIn(BankLoan.objects.get(id=3), loans)

    def test_get_filtered_loans_with_employee_id(self):
        loans = BankLoan.get_filtered_loans(self.company_id, self.fin_year_id, 'EMP001')
        self.assertEqual(loans.count(), 2) # Should include loan 1 and 3 (from EMP001)
        self.assertIn(BankLoan.objects.get(id=1), loans)
        self.assertIn(BankLoan.objects.get(id=3), loans)
        self.assertNotIn(BankLoan.objects.get(id=2), loans)

    def test_update_loan_details(self):
        loan = BankLoan.objects.get(id=1)
        old_bank_name = loan.bankname
        new_bank_name = 'New Bank X'
        loan.update_loan_details('updater', self.company_id, self.fin_year_id,
                                 new_bank_name, 'New Branch', Decimal('11000.000'), Decimal('1100.000'),
                                 date(2023, 3, 1), date(2023, 12, 1))
        loan.refresh_from_db()
        self.assertEqual(loan.bankname, new_bank_name)
        self.assertEqual(loan.amount, Decimal('11000.000'))
        self.assertEqual(loan.sessionid, 'updater')
        self.assertEqual(loan.sysdate, date.today())


class BankLoanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.fin_year_id = 2023
        cls.employee1 = OfficeStaff.objects.create(empid='EMP001', title='Mr.', employeename='John Doe', compid=cls.company_id)
        cls.employee2 = OfficeStaff.objects.create(empid='EMP002', title='Ms.', employeename='Jane Smith', compid=cls.company_id)

        BankLoan.objects.create(
            id=1, empid=cls.employee1, bankname='Bank A', branch='Branch X',
            amount=Decimal('10000.000'), installment=Decimal('1000.000'),
            fromdate=date(2023, 1, 1), todate=date(2023, 10, 1),
            sysdate=date.today(), systime=time(12, 0, 0),
            compid=cls.company_id, finyearid=cls.fin_year_id, sessionid='testuser'
        )
        BankLoan.objects.create(
            id=2, empid=cls.employee2, bankname='Bank B', branch='Branch Y',
            amount=Decimal('20000.000'), installment=Decimal('2000.000'),
            fromdate=date(2023, 2, 1), todate=date(2023, 11, 1),
            sysdate=date.today(), systime=time(13, 0, 0),
            compid=cls.company_id, finyearid=cls.fin_year_id, sessionid='testuser'
        )

    def setUp(self):
        self.client = Client()
        # Simulate session variables if your views depend on them
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_bankloan_management_view_get(self):
        response = self.client.get(reverse('bankloan_management'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/bankloan_management.html')
        self.assertContains(response, 'Bank Loan - Edit')
        self.assertIsInstance(response.context['search_form'], dict) # Check form in context
        self.assertContains(response, 'No data to display!') # Initial state

    def test_bankloan_table_partial_view_get_no_search(self):
        # HTMX GET to load table without search
        response = self.client.get(reverse('bankloan_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/_bankloan_table_partial.html')
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')
        self.assertContains(response, 'Bank A')
        self.assertContains(response, 'Bank B')
        self.assertContains(response, 'data-dt-idx') # Check for DataTables elements

    def test_bankloan_table_partial_view_get_with_search(self):
        # HTMX GET with employee search
        search_data = {'drp_field': '0', 'txt_emp_name': 'John Doe [EMP001]'}
        response = self.client.get(reverse('bankloan_table'), search_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/_bankloan_table_partial.html')
        self.assertContains(response, 'John Doe')
        self.assertNotContains(response, 'Jane Smith') # Should be filtered out

    def test_bankloan_table_partial_view_post_update_success(self):
        loan1 = BankLoan.objects.get(id=1)
        loan2 = BankLoan.objects.get(id=2)

        # Build formset data to simulate user modifying loan1 and checking it
        formset_data = {
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000', # Large enough max_num
            # Form 0 (Loan 1)
            'form-0-id': str(loan1.id),
            'form-0-select_for_edit': 'on', # Checked
            'form-0-bankname': 'Updated Bank A',
            'form-0-branch': 'New Branch',
            'form-0-amount': '10500.00',
            'form-0-installment': '1050.00',
            'form-0-fromdate': '2023-01-01',
            'form-0-todate': '2023-10-01',
            # Form 1 (Loan 2) - not selected for edit
            'form-1-id': str(loan2.id),
            'form-1-select_for_edit': '', # Unchecked
            'form-1-bankname': loan2.bankname, # Original values
            'form-1-branch': loan2.branch,
            'form-1-amount': str(loan2.amount),
            'form-1-installment': str(loan2.installment),
            'form-1-fromdate': loan2.fromdate.isoformat(),
            'form-1-todate': loan2.todate.isoformat(),
            'txt_emp_name': '', # Simulate search field also being sent
            'drp_field': '0'
        }

        response = self.client.post(reverse('bankloan_table'), formset_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/_bankloan_table_partial.html')
        self.assertContains(response, 'Bank Loan records updated successfully.')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankLoanTable')

        loan1.refresh_from_db()
        self.assertEqual(loan1.bankname, 'Updated Bank A')
        self.assertEqual(loan1.amount, Decimal('10500.000'))
        loan2.refresh_from_db()
        self.assertEqual(loan2.bankname, 'Bank B') # Should remain unchanged

    def test_bankloan_table_partial_view_post_update_invalid(self):
        loan1 = BankLoan.objects.get(id=1)
        loan2 = BankLoan.objects.get(id=2)

        # Simulate invalid data (missing required field when selected)
        formset_data = {
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            # Form 0 (Loan 1) - invalid data
            'form-0-id': str(loan1.id),
            'form-0-select_for_edit': 'on', # Checked
            'form-0-bankname': '', # Missing
            'form-0-branch': 'New Branch',
            'form-0-amount': '10500.00',
            'form-0-installment': '1050.00',
            'form-0-fromdate': '2023-01-01',
            'form-0-todate': '2023-10-01',
            # Form 1 (Loan 2) - not selected
            'form-1-id': str(loan2.id),
            'form-1-select_for_edit': '',
            'form-1-bankname': loan2.bankname,
            'form-1-branch': loan2.branch,
            'form-1-amount': str(loan2.amount),
            'form-1-installment': str(loan2.installment),
            'form-1-fromdate': loan2.fromdate.isoformat(),
            'form-1-todate': loan2.todate.isoformat(),
            'txt_emp_name': '',
            'drp_field': '0'
        }

        response = self.client.post(reverse('bankloan_table'), formset_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/_bankloan_table_partial.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Error updating Bank Loan records.')
        self.assertNotIn('HX-Trigger', response.headers) # No refresh trigger on error

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'John'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/_employee_suggestions.html')
        self.assertContains(response, 'Mr. John Doe [EMP001]')
        self.assertNotContains(response, 'Jane Smith')
        
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'nonexistent'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/bankloan/_employee_suggestions.html')
        self.assertNotContains(response, '<option')
        
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **Main Page (`bankloan_management.html`):**
    -   The "Search" button uses `hx-get` to fetch the `_bankloan_table_partial.html` into `#bankloan-table-container`, including the search form data.
    -   `hx-trigger="load, refreshBankLoanTable from:body"` on `#bankloan-table-container` ensures the table loads on page init and refreshes when the `refreshBankLoanTable` event is triggered (after successful update).
    -   Autocomplete for employee search uses `hx-get` to `/hr/autocomplete/employee/` on keyup, targeting a `datalist` element for suggestions.
-   **Table Partial (`_bankloan_table_partial.html`):**
    -   The entire form for the `formset` is contained within this partial.
    -   The "Update" button uses `hx-post` to submit the form data back to the same URL (`bankloan_table`), swapping `outerHTML` (replaces the whole table with the updated one, including validation messages).
    -   Alpine.js (`x-data`, `x-bind:disabled`, `x-on:change`) is used for each row (`<tr>`). The `select_for_edit` checkbox toggles the `isSelected` state, which in turn enables/disables the input fields in that row. This replicates the `CheckBox1_CheckedChanged` logic.
    -   DataTables is initialized on `DOMContentLoaded` within this partial, ensuring it correctly applies to the dynamically loaded table.
-   **Backend:**
    -   Views send `HX-Trigger` headers (`refreshBankLoanTable`) on successful formset submission to inform the client to refresh the table.
    -   Validation errors on formset submission are handled by re-rendering the partial, allowing HTMX to swap the content and display errors inline.

### Final Notes

This comprehensive plan provides a blueprint for migrating the ASP.NET Bank Loan Edit module to a modern Django application. It emphasizes:
-   **Business Value:** A faster, more responsive user experience due to HTMX, reducing server load and improving perceived performance. Easier maintenance and development due to Django's clear structure, ORM, and "fat model, thin view" philosophy. Enhanced data integrity through Django's robust form and model validation.
-   **Automated Approach:** The detailed code and instructions are designed to be consumed by an AI-assisted automation tool, enabling efficient code generation and transformation.
-   **Scalability:** Leveraging Django's ORM and modern frontend techniques (HTMX, Alpine.js) ensures the application is well-positioned for future growth and easier integration with other systems.
-   **Test Coverage:** Mandating high test coverage ensures the reliability and correctness of the migrated functionality, reducing post-migration bugs.

This structured approach, focusing on modularity and modern best practices, will ensure a smooth and successful transition, delivering a more robust and maintainable application.