## ASP.NET to Django Conversion Script: Salary Edit Details

This plan outlines the modernization of the ASP.NET "Salary Edit" functionality into a modern Django application. Our focus is on leveraging Django's robust features, promoting a clean, maintainable architecture, and utilizing modern frontend tools for a highly responsive user experience.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, here's an inferred database schema focusing on the tables directly manipulated or extensively queried by this page. We'll map these to Django models:

-   **`tblHR_Salary_Master`**: Stores high-level salary information per employee per month.
    -   `Id` (Primary Key, INT)
    -   `CompId` (INT) - Company ID (Foreign Key to `tblHR_Company`)
    -   `FinYearId` (INT) - Financial Year ID (Foreign Key to `tblHR_FinancialYear`)
    -   `EmpId` (VARCHAR) - Employee ID (Foreign Key to `tblHR_OfficeStaff`)
    -   `FMonth` (INT) - Month (1-12)
    -   `SysDate` (VARCHAR/DATETIME) - Last update date
    -   `SysTime` (VARCHAR/TIME) - Last update time
    -   `SessionId` (VARCHAR) - User session ID

-   **`tblHR_Salary_Details`**: Stores detailed, editable salary components for a specific master record.
    -   `Id` (Primary Key, INT)
    -   `MId` (INT) - Master ID (Foreign Key to `tblHR_Salary_Master.Id`)
    -   `Present` (DECIMAL/FLOAT) - Present days
    -   `Absent` (DECIMAL/FLOAT) - Absent days
    -   `LateIn` (DECIMAL/FLOAT) - Late In hours/count
    -   `HalfDay` (DECIMAL/FLOAT) - Half days
    -   `Sunday` (DECIMAL/FLOAT) - Sundays worked/counted
    -   `Coff` (DECIMAL/FLOAT) - Compensatory Offs
    -   `PL` (DECIMAL/FLOAT) - Paid Leave
    -   `OverTimeHrs` (DECIMAL/FLOAT) - Over Time Hours
    -   `Installment` (DECIMAL/FLOAT) - Bank Loan Installment
    -   `MobileExeAmt` (DECIMAL/FLOAT) - Mobile Excess Amount
    -   `Addition` (DECIMAL/FLOAT) - Additional earnings
    -   `Remarks1` (VARCHAR) - Remarks 1
    -   `Deduction` (DECIMAL/FLOAT) - Deductions
    -   `Remarks2` (VARCHAR) - Remarks 2

-   **`tblHR_OfficeStaff`**: Employee basic information (partial, relevant fields).
    -   `EmpId` (Primary Key, VARCHAR)
    -   `OfferId` (INT) - Foreign Key to `tblHR_Offer_Master`
    -   `EmployeeName` (VARCHAR)
    -   `Title` (VARCHAR)
    -   `SwapCardNo` (VARCHAR) - Foreign Key to `tblHR_SwapCard`
    -   `Department` (INT) - Foreign Key to `tblHR_Departments`
    -   `Designation` (INT) - Foreign Key to `tblHR_Designation`
    -   `Grade` (INT) - Foreign Key to `tblHR_Grade`
    -   `MobileNo` (INT) - Foreign Key to `tblHR_CoporateMobileNo`
    -   `CompanyEmail` (VARCHAR)
    -   `BankAccountNo` (VARCHAR)
    -   `PFNo` (VARCHAR)
    -   `PANNo` (VARCHAR)
    -   `PhotoData` (VARBINARY/BLOB)
    -   `PhotoFileName` (VARCHAR)

-   **`tblHR_Offer_Accessories`**: Details of accessories associated with an employee's offer.
    -   `Id` (Primary Key, INT)
    -   `MId` (INT) - OfferMaster ID (Foreign Key to `tblHR_Offer_Master`)
    -   `Perticulars` (VARCHAR)
    -   `Qty` (DECIMAL/FLOAT)
    -   `Amount` (DECIMAL/FLOAT)
    -   `IncludesIn` (INT) - Foreign Key to `tblHR_IncludesIn`

-   **`tblHR_IncludesIn`**: Lookup for categories of inclusions in offer accessories.
    -   `Id` (Primary Key, INT)
    -   `IncludesIn` (VARCHAR) - Description

(Other lookup tables like `tblHR_Company`, `tblHR_FinancialYear`, `tblHR_CoporateMobileNo`, `tblHR_SwapCard`, `tblHR_Departments`, `tblHR_Designation`, `tblHR_Grade`, `tblHR_Offer_Master`, `tblHR_EmpType`, `tblHR_DutyHour`, `tblHR_OTHour`, `tblHR_OverTime`, `tblHR_BankLoan` will also be modeled to support relationships and data retrieval for the main form.)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements a **Read/Display** and **Update** operation for employee salary details:

-   **Read/Display (Initial Load):**
    -   Retrieves and displays comprehensive employee information (personal, departmental, contact, offer-related).
    -   Presents calculated month-specific attendance statistics (total days, Sundays, holidays, working days).
    -   Shows current mobile bill and bank loan details.
    -   Loads existing salary details (`Present`, `Absent`, `LateIn`, `HalfDay`, etc.) from `tblHR_Salary_Details` based on `EmpId`, `Mon`, `DId`, and `MId` query parameters. If no detail record exists for the given master, it defaults to zero values, preparing for a new entry.
    -   Calculates and displays the Over Time Rate based on employee's offer.
    -   Populates a read-only data grid (`GridView1`) with `OfferAccessory` details, joining with `tblHR_IncludesIn`.

-   **Update (`btnProceed_Click`):**
    -   Captures changes from `TextBox` controls for attendance, miscellaneous additions/deductions, and remarks.
    -   Updates the corresponding `tblHR_Salary_Details` record using `DId` and `MId`.
    -   Updates the `SysDate`, `SysTime`, and `SessionId` in the associated `tblHR_Salary_Master` record to track the last modification.
    -   Performs server-side validation to ensure all numerical input fields are valid.
    -   Upon successful update, redirects to a previous listing page.

### Step 3: Infer UI Components

The original ASP.NET page uses a mix of static HTML tables and ASP.NET Web Controls to build its user interface:

-   **Display Components (`asp:Label`):** Used to present read-only information such as month name, employee details, calculated attendance statistics, bank loan summary, mobile bill details, and overtime rate.
-   **Input Components (`asp:TextBox`):** Used for editable fields like `Present`, `Absent`, `Late In`, `Half Day`, `Sunday`, `C-off`, `PL`, `Over Time Hrs`, `Installment`, `Mobile Exe. Amt`, `Addition`, `Deduction`, and `Remarks`.
-   **Action Buttons (`asp:Button`):** "Update" to submit changes and "Cancel" to navigate back without saving.
-   **Data Presentation (`asp:GridView`):** Displays `tblHR_Offer_Accessories` in a tabular format, including calculations for "Total". This grid is read-only on this page.
-   **Validation Controls:** `RequiredFieldValidator` and `RegularExpressionValidator` provide client-side validation for numerical inputs.

### Step 4: Generate Django Code

The Django application will be named `hr_salary`.

#### 4.1 Models (`hr_salary/models.py`)

These models are designed to directly map to your existing database tables (using `managed = False` and `db_table`) and encapsulate business logic, adhering to the "fat model" principle.

```python
from django.db import models
from django.db.models import Sum, F
from datetime import date
from calendar import monthrange
import locale

# Dummy/Lookup Models (adapt based on your actual schema for Company, FinancialYear, etc.)
class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompanyName', blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Company'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    def __str__(self):
        return self.name or f"Company {self.id}"

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_start = models.DateField(db_column='YearStart', blank=True, null=True)
    year_end = models.DateField(db_column='YearEnd', blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_FinancialYear'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'
    def __str__(self):
        return f"{self.year_start.year}-{self.year_end.year}" if self.year_start and self.year_end else f"FinYear {self.id}"

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'
    def __str__(self):
        return f"{self.description} [ {self.symbol} ]" if self.description and self.symbol else f"Department {self.id}"

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type_name = models.CharField(db_column='Type', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'
    def __str__(self):
        return f"{self.type_name} [ {self.symbol} ]" if self.type_name and self.symbol else f"Designation {self.id}"

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'
    def __str__(self):
        return self.symbol or f"Grade {self.id}"

class MobileCorporate(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'
    def __str__(self):
        return self.mobile_no or f"Mobile {self.id}"

class SwapCard(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_SwapCard'
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'
    def __str__(self):
        return self.swap_card_no or f"Swap Card {self.id}"

class EmployeeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_EmpType'
        verbose_name = 'Employee Type'
        verbose_name_plural = 'Employee Types'
    def __str__(self):
        return self.description or f"Employee Type {self.id}"

class DutyHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.DecimalField(db_column='Hours', max_digits=10, decimal_places=2, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_DutyHour'
        verbose_name = 'Duty Hour'
        verbose_name_plural = 'Duty Hours'
    def __str__(self):
        return str(self.hours) if self.hours else f"Duty Hour {self.id}"

class OverTimeHour(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    hours = models.DecimalField(db_column='Hours', max_digits=10, decimal_places=2, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_OTHour'
        verbose_name = 'Overtime Hour'
        verbose_name_plural = 'Overtime Hours'
    def __str__(self):
        return str(self.hours) if self.hours else f"Overtime Hour {self.id}"

class OverTimeType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_OverTime'
        verbose_name = 'Overtime Type'
        verbose_name_plural = 'Overtime Types'
    def __str__(self):
        return self.description or f"Overtime Type {self.id}"

class OfferMaster(models.Model):
    id = models.IntegerField(db_column='OfferId', primary_key=True)
    staff_type = models.ForeignKey(EmployeeType, models.DO_NOTHING, db_column='StaffType', blank=True, null=True)
    type_of = models.IntegerField(db_column='TypeOf', blank=True, null=True) # 1: SAPL, 2: NEHA
    duty_hrs = models.ForeignKey(DutyHour, models.DO_NOTHING, db_column='DutyHrs', blank=True, null=True)
    ot_hrs_lookup = models.ForeignKey(OverTimeHour, models.DO_NOTHING, db_column='OTHrs', blank=True, null=True)
    salary = models.DecimalField(db_column='salary', max_digits=18, decimal_places=2, blank=True, null=True)
    overtime_type = models.ForeignKey(OverTimeType, models.DO_NOTHING, db_column='OverTime', blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'
    def __str__(self):
        return f"Offer {self.id}"

class Employee(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    user_id = models.CharField(db_column='UserID', max_length=50, blank=True, null=True)
    offer = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='OfferId', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    swap_card = models.ForeignKey(SwapCard, models.DO_NOTHING, db_column='SwapCardNo', blank=True, null=True)
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', blank=True, null=True)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    grade = models.ForeignKey(Grade, models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    mobile_no_lookup = models.ForeignKey(MobileCorporate, models.DO_NOTHING, db_column='MobileNo', blank=True, null=True)
    company_email = models.CharField(db_column='CompanyEmail', max_length=255, blank=True, null=True)
    bank_account_no = models.CharField(db_column='BankAccountNo', max_length=50, blank=True, null=True)
    pf_no = models.CharField(db_column='PFNo', max_length=50, blank=True, null=True)
    pan_no = models.CharField(db_column='PANNo', max_length=50, blank=True, null=True)
    photo_data = models.BinaryField(db_column='PhotoData', blank=True, null=True)
    photo_file_name = models.CharField(db_column='PhotoFileName', max_length=255, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'
    def __str__(self):
        return f"{self.title or ''}. {self.employee_name or ''} [{self.emp_id}]".strip()
    def get_photo_url(self):
        # In Django, images are typically served via media files.
        # This function provides a URL that a Django view (EmployeePhotoView) can handle
        # to serve binary image data from the database.
        if self.photo_data and self.photo_file_name:
            return f"/hr_salary/employee/photo/{self.emp_id}/"
        return "/static/images/User.jpg" # Default image
    def get_employee_status(self):
        if not self.offer:
            return "N/A"
        status_prefix = ""
        if self.offer.type_of == 1:
            status_prefix = "SAPL"
        elif self.offer.type_of == 2:
            status_prefix = "NEHA"
        emp_type_desc = self.offer.staff_type.description if self.offer.staff_type else ""
        return f"{status_prefix} - {emp_type_desc}".strip(' -')

class BankLoan(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='emp_id', blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, blank=True, null=True)
    installment_amount = models.DecimalField(db_column='Installment', max_digits=18, decimal_places=2, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_BankLoan'
        verbose_name = 'Bank Loan'
        verbose_name_plural = 'Bank Loans'
    def __str__(self):
        return f"Loan for {self.employee.employee_name if self.employee else 'N/A'}"

class SalaryMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='emp_id', blank=True, null=True)
    month = models.IntegerField(db_column='FMonth', blank=True, null=True) # 1-12
    system_date = models.CharField(db_column='SysDate', max_length=10, blank=True, null=True)
    system_time = models.CharField(db_column='SysTime', max_length=8, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Master'
        verbose_name = 'Salary Master'
        verbose_name_plural = 'Salary Masters'
    def __str__(self):
        return f"Salary for {self.employee.employee_name if self.employee else 'N/A'} - Month {self.month} ({self.financial_year.year_start.year if self.financial_year and self.financial_year.year_start else 'N/A'})"

    # Business logic methods for calculations (mimicking fun. methods)
    def get_month_name(self):
        try:
            return date(1900, self.month, 1).strftime('%B')
        except (ValueError, TypeError):
            return "Unknown"

    def get_total_days_in_month(self):
        if not self.month or not self.financial_year or not self.financial_year.year_start:
            return 0
        year_val = self.financial_year.year_start.year
        if self.month < self.financial_year.year_start.month: # Adjust for financial year spanning calendar years
            year_val += 1
        return monthrange(year_val, self.month)[1]

    def get_sundays_in_month(self):
        if not self.month or not self.financial_year or not self.financial_year.year_start:
            return 0
        year_val = self.financial_year.year_start.year
        if self.month < self.financial_year.year_start.month:
            year_val += 1
        num_days = self.get_total_days_in_month()
        sundays = 0
        for i in range(1, num_days + 1):
            if date(year_val, self.month, i).weekday() == 6:  # Monday is 0, Sunday is 6
                sundays += 1
        return sundays

    def get_holidays_in_month(self):
        # This requires querying a separate Holiday model (e.g., tblHR_Holidays)
        # For demonstration, returns a placeholder.
        return 0

    def get_working_days(self):
        return self.get_total_days_in_month() - self.get_sundays_in_month() - self.get_holidays_in_month()

    def get_mobile_bill_details(self, detail_type):
        # Requires a MobileBill model and associated logic.
        # For demonstration, returns a placeholder.
        return 0.0

    def get_bank_loan_details(self):
        total_loan = BankLoan.objects.filter(
            employee=self.employee,
            company=self.company,
            fin_year__lte=self.financial_year
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0

        # Sum of past installments paid through salary details
        total_installments_paid = SalaryDetail.objects.filter(
            salary_master__employee=self.employee,
            salary_master__company=self.company,
            salary_master__financial_year__lte=self.financial_year,
            # Critically, only previous months within the same FY, and all prior FYs
            salary_master__month__lt=self.month if self.financial_year.year_start.year == date.today().year else 13
        ).aggregate(Sum('installment'))['installment__sum'] or 0.0
        
        return {'loan_amount': total_loan, 'installments_paid': total_installments_paid}

    def get_ot_rate(self):
        if not self.employee or not self.employee.offer:
            return "N/A"
        offer = self.employee.offer
        if offer.overtime_type and offer.overtime_type.id == 2: # Assuming ID 2 means calculated rate
            salary = offer.salary or 0
            ot_hours_lookup = offer.ot_hrs_lookup.hours if offer.ot_hrs_lookup else 0
            duty_hours_lookup = offer.duty_hrs.hours if offer.duty_hrs else 0
            num_days = self.get_total_days_in_month()
            
            if salary and ot_hours_lookup and duty_hours_lookup and num_days and (duty_hours_lookup * num_days) > 0:
                return round((salary / (duty_hours_lookup * num_days)) * ot_hours_lookup, 2)
            return 0.0
        else:
            return offer.overtime_type.description if offer.overtime_type else "N/A"

class SalaryDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    salary_master = models.ForeignKey(SalaryMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    present = models.DecimalField(db_column='Present', max_digits=18, decimal_places=3, default=0.0)
    absent = models.DecimalField(db_column='Absent', max_digits=18, decimal_places=3, default=0.0)
    late_in = models.DecimalField(db_column='LateIn', max_digits=18, decimal_places=3, default=0.0)
    half_day = models.DecimalField(db_column='HalfDay', max_digits=18, decimal_places=3, default=0.0)
    sunday = models.DecimalField(db_column='Sunday', max_digits=18, decimal_places=3, default=0.0)
    coff = models.DecimalField(db_column='Coff', max_digits=18, decimal_places=3, default=0.0)
    pl = models.DecimalField(db_column='PL', max_digits=18, decimal_places=3, default=0.0)
    over_time_hrs = models.DecimalField(db_column='OverTimeHrs', max_digits=18, decimal_places=3, default=0.0)
    installment = models.DecimalField(db_column='Installment', max_digits=18, decimal_places=3, default=0.0)
    mobile_exe_amt = models.DecimalField(db_column='MobileExeAmt', max_digits=18, decimal_places=3, default=0.0)
    addition = models.DecimalField(db_column='Addition', max_digits=18, decimal_places=3, default=0.0)
    remarks1 = models.CharField(db_column='Remarks1', max_length=500, blank=True, null=True)
    deduction = models.DecimalField(db_column='Deduction', max_digits=18, decimal_places=3, default=0.0)
    remarks2 = models.CharField(db_column='Remarks2', max_length=500, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Salary_Details'
        verbose_name = 'Salary Detail'
        verbose_name_plural = 'Salary Details'
    def __str__(self):
        return f"Salary Details for {self.salary_master.employee.employee_name if self.salary_master and self.salary_master.employee else 'N/A'}"

    @classmethod
    def get_or_initialize(cls, m_id, d_id=None):
        """
        Mimics ASP.NET's behavior of either loading an existing detail record
        or returning a new one with default values if no record exists for the month/employee.
        """
        # First, try to find by specific DId and MId if provided
        if d_id:
            try:
                return cls.objects.get(id=d_id, salary_master__id=m_id)
            except cls.DoesNotExist:
                pass # If not found by DId, proceed to check if master exists

        # If DId not provided, or object not found by DId, check if a master exists
        salary_master = SalaryMaster.objects.filter(id=m_id).first()
        if salary_master:
            # Check if a detail record already exists for this master (it should be one-to-one)
            existing_detail = cls.objects.filter(salary_master=salary_master).first()
            if existing_detail:
                return existing_detail
            else:
                # Initialize new SalaryDetail with master association and defaults
                return cls(salary_master=salary_master)
        
        # Fallback: If no salary master found, return an unlinked empty object (won't save without master)
        return cls()

class IncludesIn(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    includes_in = models.CharField(db_column='IncludesIn', max_length=255, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_IncludesIn'
        verbose_name = 'Includes In'
        verbose_name_plural = 'Includes In Categories'
    def __str__(self):
        return self.includes_in or f"IncludesIn {self.id}"

class OfferAccessory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    offer_master = models.ForeignKey(OfferMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    perticulars = models.CharField(db_column='Perticulars', max_length=255, blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3, blank=True, null=True)
    includes_in = models.ForeignKey(IncludesIn, models.DO_NOTHING, db_column='IncludesIn', blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Accessories'
        verbose_name = 'Offer Accessory'
        verbose_name_plural = 'Offer Accessories'
    def __str__(self):
        return self.perticulars or f"Accessory {self.id}"
    @property
    def total(self):
        if self.qty is not None and self.amount is not None:
            return round(self.qty * self.amount, 2)
        return 0.0

```

#### 4.2 Forms (`hr_salary/forms.py`)

This form is a `ModelForm` for `SalaryDetail`, with specific widgets to apply Tailwind CSS classes and define validation rules based on the original ASP.NET validators.

```python
from django import forms
from .models import SalaryDetail

class SalaryDetailForm(forms.ModelForm):
    # Define fields explicitly to apply custom widgets and validation
    present = forms.DecimalField(
        label="Present", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    absent = forms.DecimalField(
        label="Absent", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    late_in = forms.DecimalField(
        label="Late In", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    half_day = forms.DecimalField(
        label="Half Day", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    sunday = forms.DecimalField(
        label="Sunday", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    coff = forms.DecimalField(
        label="C-off", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    pl = forms.DecimalField(
        label="PL", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    over_time_hrs = forms.DecimalField(
        label="Over Time Hrs.", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    installment = forms.DecimalField(
        label="Installment", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    mobile_exe_amt = forms.DecimalField(
        label="Exe.Amt", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    addition = forms.DecimalField(
        label="Addition", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    deduction = forms.DecimalField(
        label="Deduction", max_digits=18, decimal_places=3, required=True,
        widget=forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    remarks1 = forms.CharField(
        label="Remarks", required=False,
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'})
    )
    remarks2 = forms.CharField(
        label="Remarks", required=False,
        widget=forms.Textarea(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'})
    )

    class Meta:
        model = SalaryDetail
        fields = [
            'present', 'absent', 'late_in', 'half_day', 'sunday', 'coff', 'pl',
            'over_time_hrs', 'installment', 'mobile_exe_amt', 'addition',
            'remarks1', 'deduction', 'remarks2'
        ]

```

#### 4.3 Views (`hr_salary/views.py`)

The views encapsulate the logic for fetching data, handling form submissions, and rendering templates. They are kept thin by delegating complex data retrieval and calculations to the models.

```python
from django.views.generic import View, TemplateView
from django.views.generic.edit import UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from datetime import date

from .models import (
    SalaryDetail, SalaryMaster, Employee, OfferAccessory, IncludesIn,
    Company, FinancialYear, Department, Designation, Grade, MobileCorporate,
    SwapCard, OfferMaster, EmployeeType, DutyHour, OverTimeHour, OverTimeType,
    BankLoan
)
from .forms import SalaryDetailForm

# View for serving employee photos directly from binary data in DB
class EmployeePhotoView(View):
    def get(self, request, emp_id):
        employee = get_object_or_404(Employee, emp_id=emp_id)
        if employee.photo_data:
            return HttpResponse(employee.photo_data, content_type='image/jpeg') # Adjust content_type as needed
        return HttpResponse(status=404) # Not found if no photo data

class SalaryDetailEditView(UpdateView):
    model = SalaryDetail
    form_class = SalaryDetailForm
    template_name = 'hr_salary/salarydetail_edit.html'
    success_url = reverse_lazy('hr_salary:salary_edit_list') # Redirect target after successful update

    def get_object(self, queryset=None):
        # Parameters extracted from query string, mimicking ASP.NET
        emp_id = self.request.GET.get('EmpId')
        month_id = self.request.GET.get('Mon')
        d_id = self.request.GET.get('DId') # ID from tblHR_Salary_Details
        m_id = self.request.GET.get('MId') # ID from tblHR_Salary_Master

        if not all([emp_id, month_id, m_id]):
            raise Http404("Missing required parameters: EmpId, Mon, MId.")
        
        try:
            month_id = int(month_id)
            m_id = int(m_id)
            if d_id: d_id = int(d_id)
        except ValueError:
            raise Http404("Invalid month, DId, or MId format.")

        # Ensure SalaryMaster exists for the given parameters
        salary_master_obj = get_object_or_404(
            SalaryMaster.objects.select_related('employee__offer__staff_type', 'employee__offer__duty_hrs', 'employee__offer__ot_hrs_lookup', 'employee__offer__overtime_type'),
            id=m_id,
            employee__emp_id=emp_id,
            month=month_id,
            company__id=self.request.session.get('compid'),
            financial_year__id=self.request.session.get('finyear')
        )
        
        # Use the model's static method to get or initialize the SalaryDetail
        obj = SalaryDetail.get_or_initialize(m_id=salary_master_obj.id, d_id=d_id)
        
        # Store these for context and form processing
        self.employee_obj = salary_master_obj.employee
        self.salary_master_obj = salary_master_obj
        
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Access the objects retrieved by get_object
        salary_master = self.salary_master_obj
        employee = self.employee_obj

        context['employee'] = employee
        context['salary_master'] = salary_master
        
        # Populate all derived/lookup data by calling model methods
        context['month_name'] = salary_master.get_month_name()
        context['days_of_month'] = salary_master.get_total_days_in_month()
        context['sundays_in_month'] = salary_master.get_sundays_in_month()
        context['holidays_in_month'] = salary_master.get_holidays_in_month()
        context['working_days'] = salary_master.get_working_days()

        if context['working_days'] == 0:
            messages.warning(self.request, "Working days is not found for selected month.")

        bank_loan_info = salary_master.get_bank_loan_details()
        context['bank_loan_amount'] = bank_loan_info['loan_amount']
        context['bank_loan_installments_paid'] = bank_loan_info['installments_paid']
        
        context['mobile_bill_amount'] = salary_master.get_mobile_bill_details(1) # Placeholder
        context['mobile_bill_limit'] = salary_master.get_mobile_bill_details(2) # Placeholder
        
        context['ot_rate'] = salary_master.get_ot_rate()
        
        context['offer_master_id_for_accessories'] = employee.offer.id if employee.offer else None

        return context
    
    def form_valid(self, form):
        # Save SalaryDetail instance
        salary_detail = form.save(commit=False)
        # Link to SalaryMaster if it's a new instance being created
        if not salary_detail.pk: # If it's a new object (not yet saved)
            salary_detail.salary_master = self.salary_master_obj
        salary_detail.save()

        # Update associated SalaryMaster (SysDate, SysTime, SessionId)
        self.salary_master_obj.system_date = timezone.now().strftime('%Y-%m-%d')
        self.salary_master_obj.system_time = timezone.now().strftime('%H:%M:%S')
        self.salary_master_obj.session_id = self.request.session.get('username', 'system')
        self.salary_master_obj.save()

        messages.success(self.request, 'Salary details updated successfully.')
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, use HX-Location to trigger a client-side redirect
            # This is more robust than a 204 for full page navigation after form submission.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Location': self.get_success_url()
                }
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, 'Invalid data entry. Please check the fields.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors and swap it back
            return render(self.request, self.template_name, self.get_context_data(form=form))
        return super().form_invalid(form)


class OfferAccessoryTablePartialView(TemplateView):
    template_name = 'hr_salary/_offeraccessory_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        offer_master_id = self.request.GET.get('offer_master_id')

        if offer_master_id:
            try:
                offer_master_id = int(offer_master_id)
                # Fetch accessories and their related IncludesIn descriptions
                accessories = OfferAccessory.objects.filter(
                    offer_master__id=offer_master_id
                ).select_related('includes_in').order_by('-id')
                context['offer_accessories'] = accessories
            except ValueError:
                context['offer_accessories'] = []
        else:
            context['offer_accessories'] = []
        
        return context

```

#### 4.4 Templates (`hr_salary/templates/hr_salary/`)

Templates are designed with `{% extends 'core/base.html' %}` for DRY principles. All styling uses Tailwind CSS.

File: `hr_salary/templates/hr_salary/salarydetail_edit.html` (Main page)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Salary Edit</h2>
    </div>

    <!-- Main form for Salary Details -->
    <form hx-post="{% url 'hr_salary:salary_detail_edit' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" hx-swap="none">
        {% csrf_token %}
        <div class="bg-white shadow-md rounded-lg p-6 mb-8">
            <!-- Salary Month Summary -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-6 text-sm">
                <div class="col-span-full font-bold text-gray-700 pb-2 border-b border-gray-200">
                    &nbsp;Salary for the Month of <span class="font-bold">{{ month_name }}</span>
                </div>
                <div>Days of Month: <span class="font-bold">{{ days_of_month }}</span></div>
                <div>Sundays: <span class="font-bold">{{ sundays_in_month }}</span></div>
                <div>Holidays: <span class="font-bold">{{ holidays_in_month }}</span></div>
                <div class="col-span-full">Working Days: <span class="font-bold">{{ working_days }}</span></div>
            </div>

            <!-- Employee Details, Attendance, Miscellanies Sections -->
            <div class="mt-6 border-t border-gray-200 pt-6">
                <div class="grid grid-cols-1 md:grid-cols-12 gap-x-6 text-sm border border-gray-300">
                    <!-- Header Row -->
                    <div class="md:col-span-3 text-center font-bold bg-gray-200 p-2 border-r border-gray-300">Employee Details</div>
                    <div class="md:col-span-3 text-center font-bold bg-gray-200 p-2 border-r border-gray-300">Attendance Details</div>
                    <div class="md:col-span-6 text-center font-bold bg-gray-200 p-2">Miscellanies</div>

                    <!-- Row 1: Employee Image & Name, Present, Bank Loan -->
                    <div class="md:col-span-3 p-4 border-r border-gray-300 flex flex-col items-center justify-start gap-y-4 row-span-4">
                        <img src="{{ employee.get_photo_url }}" alt="Employee Photo" class="h-28 w-24 object-cover border border-gray-300 shadow-sm" onerror="this.onerror=null;this.src='{% static 'images/User.jpg' %}'">
                        <div class="w-full text-center">
                            <span class="font-bold">{{ employee.title }}. {{ employee.employee_name }} [{{ employee.emp_id }}]</span>
                        </div>
                    </div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">Swap Card No: <span class="font-bold">{{ employee.swap_card.swap_card_no }}</span></div>
                    <div class="md:col-span-1 p-2">Present</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.present }}</div>
                    <div class="md:col-span-2 p-2">Bank Loan</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300">: <span class="font-bold">{{ bank_loan_amount|floatformat:2 }}</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Inst. Paid : <span class="font-bold">{{ bank_loan_installments_paid|floatformat:2 }}</span></div>

                    <!-- Row 2: Swap Card No, Absent, Installment -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Department: <span class="font-bold">{{ employee.department }}</span></div>
                    <div class="md:col-span-1 p-2">Absent</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.absent }}</div>
                    <div class="md:col-span-2 p-2">Installment</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300">: {{ form.installment }}</div>

                    <!-- Row 3: Department, Late In, Mobile Bill -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Designation: <span class="font-bold">{{ employee.designation }}</span></div>
                    <div class="md:col-span-1 p-2">Late In</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.late_in }}</div>
                    <div class="md:col-span-2 p-2">Mobile Bill</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300">: Limit : <span class="font-bold">{{ mobile_bill_limit|floatformat:2 }}</span> &nbsp;Bill Amt : <span class="font-bold">{{ mobile_bill_amount|floatformat:2 }}</span> &nbsp; Exe.Amt : {{ form.mobile_exe_amt }}</div>

                    <!-- Row 4: Grade, Half Day, Addition -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Grade: <span class="font-bold">{{ employee.grade }}</span></div>
                    <div class="md:col-span-1 p-2">Half Day</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.half_day }}</div>
                    <div class="md:col-span-2 p-2">Addition</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300">: {{ form.addition }}</div>

                    <!-- Row 5: Status, Sunday, Remarks1 -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Status: <span class="font-bold">{{ employee.get_employee_status }}</span></div>
                    <div class="md:col-span-1 p-2">Sunday</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.sunday }}</div>
                    <div class="md:col-span-2 p-2">Remarks</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300 row-span-2" valign="top">{{ form.remarks1 }}</div>

                    <!-- Row 6: Duty Hrs, C-off -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Duty Hrs.: <span class="font-bold">{{ employee.offer.duty_hrs.hours }}</span></div>
                    <div class="md:col-span-1 p-2">C-off</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.coff }}</div>
                    <div class="md:col-span-2 p-2">&nbsp;</div>

                    <!-- Row 7: Employee OT Hrs, PL, Deduction -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Over Time Hrs.: <span class="font-bold">{{ employee.offer.ot_hrs_lookup.hours }}</span></div>
                    <div class="md:col-span-1 p-2">PL</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.pl }}</div>
                    <div class="md:col-span-2 p-2">Deduction</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300">{{ form.deduction }}</div>

                    <!-- Row 8: OT Rate, Editable OT Hrs, Remarks2 -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Over Time Rate: <span class="font-bold">{{ ot_rate }}</span></div>
                    <div class="md:col-span-1 p-2">Over Time Hrs.</div>
                    <div class="md:col-span-2 p-2 border-r border-gray-300">: {{ form.over_time_hrs }}</div>
                    <div class="md:col-span-2 p-2">Remarks</div>
                    <div class="md:col-span-4 p-2 border-r border-gray-300 row-span-2">{{ form.remarks2 }}</div>

                    <!-- Row 9: A/C No, Offer Accessory Grid -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">A/C No.: <span class="font-bold">{{ employee.bank_account_no }}</span></div>
                    <div class="md:col-span-3 p-2 border-r border-gray-300"></div>
                    <div class="md:col-span-6 p-2 border-r border-gray-300 row-span-4" valign="top">
                        <div class="overflow-auto border border-gray-300" style="max-height: 200px;">
                            <div id="offerAccessoryTable-container"
                                 hx-trigger="load"
                                 hx-get="{% url 'hr_salary:offer_accessory_table' %}?offer_master_id={{ offer_master_id_for_accessories }}"
                                 hx-swap="innerHTML">
                                <!-- DataTables will be loaded here via HTMX -->
                                <div class="text-center p-4">
                                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                    <p class="mt-2 text-gray-500">Loading Offer Accessories...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 10: PF No -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">PF No.: <span class="font-bold">{{ employee.pf_no }}</span></div>
                    <div class="md:col-span-3 p-2 border-r border-gray-300"></div>

                    <!-- Row 11: PAN No -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">PAN No.: <span class="font-bold">{{ employee.pan_no }}</span></div>
                    <div class="md:col-span-3 p-2 border-r border-gray-300"></div>

                    <!-- Row 12: Email -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300">Email: <span class="font-bold">{{ employee.company_email }}</span></div>
                    <div class="md:col-span-3 p-2 border-r border-gray-300"></div>

                    <!-- Row 13: Mobile No -->
                    <div class="md:col-span-3 p-2 border-r border-gray-300 border-b border-gray-300">Mobile No.: <span class="font-bold">{{ employee.mobile_no_lookup.mobile_no }}</span></div>
                    <div class="md:col-span-3 p-2 border-r border-gray-300 border-b border-gray-300"></div>
                    <div class="md:col-span-6 p-2 border-b border-gray-300"></div> {# Empty cell to match colspan #}

                    <!-- Action Buttons -->
                    <div class="col-span-full text-center p-4">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-4">Update</button>
                        <a href="{% url 'hr_salary:salary_edit_list' %}?EmpId={{ employee.emp_id }}&ModId=12&SubModId=133" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Cancel</a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is assumed to be available from base.html.
    // No specific Alpine.js component logic needed for this form's interactions,
    // as HTMX handles the form submission and content updates.
</script>
{% endblock %}
```

File: `hr_salary/templates/hr_salary/_offeraccessory_table.html` (Partial for DataTables)

```html
<table id="offerAccessoryTable" class="min-w-full bg-white yui-datatable-theme">
    <thead>
        <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <th class="py-2 px-4 border-b border-gray-200 w-[3%]">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 w-[20%]">Include In</th>
            <th class="py-2 px-4 border-b border-gray-200 w-[40%]">Perticulars</th>
            <th class="py-2 px-4 border-b border-gray-200 w-[13%] text-right">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 w-[12%] text-right">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 w-[12%] text-right">Total</th>
        </tr>
    </thead>
    <tbody>
        {% if offer_accessories %}
            {% for obj in offer_accessories %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.includes_in.includes_in }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.perticulars }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.amount|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.total|floatformat:2 }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Initialize DataTable only if data exists. This script block will be evaluated when HTMX injects the partial.
    $(document).ready(function() {
        // Destroy existing DataTable instance to prevent re-initialization errors if HTMX reloads
        if ($.fn.DataTable.isDataTable('#offerAccessoryTable')) {
            $('#offerAccessoryTable').DataTable().destroy();
        }
        
        $('#offerAccessoryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true,
            "responsive": true
        });
    });
</script>
```

#### 4.5 URLs (`hr_salary/urls.py`)

This file defines the URL patterns for the `hr_salary` application.

```python
from django.urls import path
from .views import SalaryDetailEditView, OfferAccessoryTablePartialView, EmployeePhotoView

app_name = 'hr_salary' # Namespace for the app

urlpatterns = [
    # Main salary edit page, accessed with query parameters
    path('salary/edit/', SalaryDetailEditView.as_view(), name='salary_detail_edit'),
    # Placeholder for the redirect success URL, adjust as per your actual list view
    path('salary/list/', SalaryDetailEditView.as_view(), name='salary_edit_list'), 
    # HTMX endpoint to load the Offer Accessory DataTable dynamically
    path('salary/offer-accessories-table/', OfferAccessoryTablePartialView.as_view(), name='offer_accessory_table'),
    # Endpoint to serve employee photos stored as binary data
    path('employee/photo/<str:emp_id>/', EmployeePhotoView.as_view(), name='employee_photo'),
]

```

#### 4.6 Tests (`hr_salary/tests.py`)

Comprehensive unit tests cover model methods and properties, and integration tests validate the view logic and HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, timedelta
from decimal import Decimal

from .models import (
    Company, FinancialYear, Employee, Department, Designation, Grade,
    MobileCorporate, SwapCard, OfferMaster, EmployeeType, DutyHour, OverTimeHour,
    OverTimeType, BankLoan, SalaryMaster, SalaryDetail, IncludesIn, OfferAccessory
)

class ModelCreationTest(TestCase):
    """
    Unit tests for creating and retrieving instances of all relevant models
    to ensure basic setup and relationships are correct, and to test model methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create dummy company and financial year for all related objects
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_start=date(2023, 4, 1), year_end=date(2024, 3, 31))

        # Create lookup/related data
        cls.department = Department.objects.create(id=1, description='HR', symbol='HRD')
        cls.designation = Designation.objects.create(id=1, type_name='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A')
        cls.mobile_corp = MobileCorporate.objects.create(id=1, mobile_no='**********')
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no='SC001')
        cls.emp_type = EmployeeType.objects.create(id=1, description='Full-time')
        cls.duty_hour = DutyHour.objects.create(id=1, hours=Decimal('8.00'))
        cls.ot_hour = OverTimeHour.objects.create(id=1, hours=Decimal('1.50'))
        cls.ot_type_fixed = OverTimeType.objects.create(id=1, description='Fixed OT')
        cls.ot_type_calculated = OverTimeType.objects.create(id=2, description='Calculated OT')

        cls.offer_master = OfferMaster.objects.create(
            id=101, staff_type=cls.emp_type, type_of=1, # SAPL
            duty_hrs=cls.duty_hour, ot_hrs_lookup=cls.ot_hour, salary=Decimal('50000.00'),
            overtime_type=cls.ot_type_calculated
        )

        cls.employee = Employee.objects.create(
            emp_id='EMP001', user_id='testuser', offer=cls.offer_master, fin_year=cls.fin_year, company=cls.company,
            title='Mr', employee_name='John Doe', swap_card=cls.swap_card, department=cls.department,
            designation=cls.designation, grade=cls.grade, mobile_no_lookup=cls.mobile_corp,
            company_email='<EMAIL>', bank_account_no='**********', pf_no='PF123',
            pan_no='PANXYZ', photo_data=b'dummy_photo_data', photo_file_name='john_doe.jpg'
        )

        cls.bank_loan = BankLoan.objects.create(
            id=201, company=cls.company, fin_year=cls.fin_year, employee=cls.employee,
            amount=Decimal('10000.00'), installment_amount=Decimal('500.00')
        )

        # Create a SalaryMaster for May 2023 (Month 5)
        cls.salary_master = SalaryMaster.objects.create(
            id=301, company=cls.company, financial_year=cls.fin_year, employee=cls.employee,
            month=5, # May
            system_date=date.today().strftime('%Y-%m-%d'), system_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testsession'
        )

        # Create a SalaryDetail associated with the SalaryMaster
        cls.salary_detail = SalaryDetail.objects.create(
            id=401, salary_master=cls.salary_master, present=Decimal('20.00'), absent=Decimal('2.00'),
            late_in=Decimal('0.5'), half_day=Decimal('1.0'), sunday=Decimal('4.0'), coff=Decimal('0.0'),
            pl=Decimal('0.0'), over_time_hrs=Decimal('10.0'), installment=Decimal('500.0'),
            mobile_exe_amt=Decimal('50.0'), addition=Decimal('100.0'), remarks1='Initial remarks',
            deduction=Decimal('20.0'), remarks2='More remarks'
        )

        cls.includes_in = IncludesIn.objects.create(id=1, includes_in='Allowance')
        cls.offer_accessory = OfferAccessory.objects.create(
            id=501, offer_master=cls.offer_master, perticulars='Mobile Allowance',
            qty=Decimal('1.0'), amount=Decimal('1000.0'), includes_in=cls.includes_in
        )

    def test_model_str_representations(self):
        self.assertEqual(str(self.employee), "Mr. John Doe [EMP001]")
        self.assertEqual(str(self.salary_master), f"Salary for John Doe - Month 5 ({self.fin_year.year_start.year})")
        self.assertEqual(str(self.salary_detail), f"Salary Details for John Doe")
        self.assertEqual(str(self.offer_accessory), "Mobile Allowance")

    def test_employee_derived_properties(self):
        self.assertEqual(self.employee.get_employee_status(), "SAPL - Full-time")
        self.assertIn('/hr_salary/employee/photo/EMP001/', self.employee.get_photo_url())

    def test_salary_master_derived_properties(self):
        self.assertEqual(self.salary_master.get_month_name(), "May")
        self.assertEqual(self.salary_master.get_total_days_in_month(), 31)
        self.assertEqual(self.salary_master.get_sundays_in_month(), 4) # Assuming May 2023 had 4 Sundays
        self.assertEqual(self.salary_master.get_holidays_in_month(), 0) # Placeholder for now
        self.assertEqual(self.salary_master.get_working_days(), 27) # 31 - 4 - 0

        # Create another salary master for a previous month to test bank loan calculation
        prev_salary_master = SalaryMaster.objects.create(
            id=302, company=self.company, financial_year=self.fin_year, employee=self.employee,
            month=4, # April
            system_date=date.today().strftime('%Y-%m-%d'), system_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testsession'
        )
        SalaryDetail.objects.create(
            id=402, salary_master=prev_salary_master, installment=Decimal('250.0') # Paid 250 in April
        )
        
        bank_loan_info = self.salary_master.get_bank_loan_details()
        self.assertEqual(bank_loan_info['loan_amount'], Decimal('10000.00'))
        self.assertEqual(bank_loan_info['installments_paid'], Decimal('250.0'))

        # Test OT Rate calculation
        # salary=50000, ot_hours=1.5, duty_hours=8, num_days=31
        # (50000 / (8 * 31)) * 1.5 = (50000 / 248) * 1.5 = 201.6129 * 1.5 = 302.41935...
        self.assertEqual(self.salary_master.get_ot_rate(), Decimal('302.42'))

    def test_offer_accessory_total_property(self):
        self.assertEqual(self.offer_accessory.total, Decimal('1000.00'))

    def test_salary_detail_get_or_initialize_existing(self):
        # Test getting an existing SalaryDetail
        retrieved_detail = SalaryDetail.get_or_initialize(m_id=self.salary_master.id, d_id=self.salary_detail.id)
        self.assertEqual(retrieved_detail.id, self.salary_detail.id)
        self.assertEqual(retrieved_detail.present, self.salary_detail.present)

    def test_salary_detail_get_or_initialize_new(self):
        # Test initializing a new SalaryDetail if DId not found or not provided
        # Ensure there's no existing SalaryDetail for this master
        SalaryDetail.objects.filter(salary_master=self.salary_master).delete()

        new_detail = SalaryDetail.get_or_initialize(m_id=self.salary_master.id)
        self.assertIsNone(new_detail.id) # Should not have an ID yet
        self.assertEqual(new_detail.salary_master, self.salary_master)
        self.assertEqual(new_detail.present, Decimal('0.0')) # Check default value

    def test_salary_detail_get_or_initialize_no_master(self):
        # Test case where master ID is invalid
        new_detail = SalaryDetail.get_or_initialize(m_id=9999) # Invalid MId
        self.assertIsNone(new_detail.id)
        self.assertIsNone(new_detail.salary_master)


class SalaryDetailEditViewTest(TestCase):
    """
    Integration tests for the SalaryDetailEditView.
    """
    @classmethod
    def setUpTestData(cls):
        # Create full set of dependencies for a realistic scenario
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_start=date(2023, 4, 1), year_end=date(2024, 3, 31))
        cls.department = Department.objects.create(id=1, description='HR', symbol='HRD')
        cls.designation = Designation.objects.create(id=1, type_name='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A')
        cls.mobile_corp = MobileCorporate.objects.create(id=1, mobile_no='**********')
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no='SC001')
        cls.emp_type = EmployeeType.objects.create(id=1, description='Full-time')
        cls.duty_hour = DutyHour.objects.create(id=1, hours=Decimal('8.00'))
        cls.ot_hour = OverTimeHour.objects.create(id=1, hours=Decimal('1.50'))
        cls.ot_type_calculated = OverTimeType.objects.create(id=2, description='Calculated OT')
        cls.offer_master = OfferMaster.objects.create(
            id=101, staff_type=cls.emp_type, type_of=1, duty_hrs=cls.duty_hour,
            ot_hrs_lookup=cls.ot_hour, salary=Decimal('50000.00'), overtime_type=cls.ot_type_calculated
        )
        cls.employee = Employee.objects.create(
            emp_id='EMP001', offer=cls.offer_master, fin_year=cls.fin_year, company=cls.company,
            title='Mr', employee_name='John Doe', swap_card=cls.swap_card, department=cls.department,
            designation=cls.designation, grade=cls.grade, mobile_no_lookup=cls.mobile_corp,
            company_email='<EMAIL>', bank_account_no='**********', pf_no='PF123',
            pan_no='PANXYZ', photo_data=b'photo_bytes', photo_file_name='photo.jpg'
        )
        cls.salary_master = SalaryMaster.objects.create(
            id=301, company=cls.company, financial_year=cls.fin_year, employee=cls.employee, month=5 # May
        )
        cls.salary_detail = SalaryDetail.objects.create(
            id=401, salary_master=cls.salary_master, present=Decimal('20.00'), absent=Decimal('2.00'),
            late_in=Decimal('0.5'), half_day=Decimal('1.0'), sunday=Decimal('4.0'), coff=Decimal('0.0'),
            pl=Decimal('0.0'), over_time_hrs=Decimal('10.0'), installment=Decimal('500.0'),
            mobile_exe_amt=Decimal('50.0'), addition=Decimal('100.0'), remarks1='Initial remarks',
            deduction=Decimal('20.0'), remarks2='More remarks'
        )
        cls.includes_in = IncludesIn.objects.create(id=1, includes_in='Allowance')
        cls.offer_accessory = OfferAccessory.objects.create(
            id=501, offer_master=cls.offer_master, perticulars='Mobile Allowance',
            qty=Decimal('1.0'), amount=Decimal('1000.0'), includes_in=cls.includes_in
        )
    
    def setUp(self):
        self.client = Client()
        # Simulate session variables (CompId and FinYearId for database lookups)
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year.id
        session['username'] = 'test_user'
        session.save()

        # URL parameters for the form
        self.url_params = {
            'EmpId': self.employee.emp_id,
            'Mon': self.salary_master.month,
            'DId': self.salary_detail.id,
            'MId': self.salary_master.id
        }
        self.url = f"{reverse('hr_salary:salary_detail_edit')}?{Client().encode_url(self.url_params)}"

    def test_get_salary_detail_edit_view(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/salarydetail_edit.html')
        self.assertContains(response, 'Salary Edit')
        self.assertContains(response, self.employee.employee_name)
        self.assertContains(response, str(self.salary_detail.present))
        self.assertContains(response, str(self.salary_detail.installment))
        self.assertContains(response, str(self.offer_accessory.perticulars)) # Check for accessory data
        
    def test_post_salary_detail_edit_view_success(self):
        updated_data = {
            'present': '22.0', 'absent': '1.0', 'late_in': '0.0', 'half_day': '0.0',
            'sunday': '4.0', 'coff': '0.0', 'pl': '0.0', 'over_time_hrs': '12.0',
            'installment': '600.0', 'mobile_exe_amt': '60.0', 'addition': '120.0',
            'remarks1': 'Updated remarks by test', 'deduction': '25.0', 'remarks2': 'Another test update'
        }
        
        response = self.client.post(self.url, updated_data, HTTP_HX_REQUEST='true')
        
        # Check for HTMX 204 No Content for successful update
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Location', response.headers)
        self.assertEqual(response.headers['HX-Location'], reverse_lazy('hr_salary:salary_edit_list'))

        # Verify data was updated in the database
        self.salary_detail.refresh_from_db()
        self.assertEqual(self.salary_detail.present, Decimal('22.0'))
        self.assertEqual(self.salary_detail.over_time_hrs, Decimal('12.0'))
        self.assertEqual(self.salary_detail.remarks1, 'Updated remarks by test')

        # Verify SalaryMaster was updated
        self.salary_master.refresh_from_db()
        self.assertEqual(self.salary_master.session_id, 'test_user')
        self.assertEqual(self.salary_master.system_date, timezone.now().strftime('%Y-%m-%d'))

    def test_post_salary_detail_edit_view_invalid_data(self):
        invalid_data = {
            'present': 'abc', 'absent': '1.0', 'late_in': '0.0', 'half_day': '0.0',
            'sunday': '4.0', 'coff': '0.0', 'pl': '0.0', 'over_time_hrs': '12.0',
            'installment': '600.0', 'mobile_exe_amt': '60.0', 'addition': '120.0',
            'remarks1': 'Updated remarks', 'deduction': '25.0', 'remarks2': 'Another update'
        }
        
        response = self.client.post(self.url, invalid_data, HTTP_HX_REQUEST='true')
        
        # Should return 200 OK with the form containing errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/salarydetail_edit.html')
        self.assertContains(response, 'Invalid data entry. Please check the fields.')
        self.assertContains(response, 'Enter a number.') # Specific error message from DecimalField

    def test_get_object_not_found(self):
        # Test case where SalaryMaster or Employee is not found
        invalid_url_params = {
            'EmpId': 'NONEXISTENT', # Invalid EmpId
            'Mon': 5,
            'DId': self.salary_detail.id,
            'MId': self.salary_master.id
        }
        invalid_url = f"{reverse('hr_salary:salary_detail_edit')}?{Client().encode_url(invalid_url_params)}"
        response = self.client.get(invalid_url)
        self.assertEqual(response.status_code, 404)

        # Test missing essential query parameters
        missing_params_url = f"{reverse('hr_salary:salary_detail_edit')}?EmpId={self.employee.emp_id}" # Missing Mon, MId
        response = self.client.get(missing_params_url)
        self.assertEqual(response.status_code, 404)

    def test_get_object_initializes_if_not_found_but_master_exists(self):
        # Test if SalaryDetail initializes if DId is not provided or not found, but SalaryMaster exists
        # Delete existing detail to simulate it not being found
        SalaryDetail.objects.filter(id=self.salary_detail.id).delete()

        url_params_no_did = {
            'EmpId': self.employee.emp_id,
            'Mon': self.salary_master.month,
            'MId': self.salary_master.id
        }
        url_no_did = f"{reverse('hr_salary:salary_detail_edit')}?{Client().encode_url(url_params_no_did)}"
        
        response = self.client.get(url_no_did)
        self.assertEqual(response.status_code, 200)
        # Check if form data is initialized (defaults should be 0.0)
        self.assertContains(response, 'value="0.0"')
        self.assertFalse(SalaryDetail.objects.filter(salary_master=self.salary_master).exists()) # Still not saved to DB

        # Submit the form with default values (which should be 0.0)
        default_data = {
            'present': '0.0', 'absent': '0.0', 'late_in': '0.0', 'half_day': '0.0',
            'sunday': '0.0', 'coff': '0.0', 'pl': '0.0', 'over_time_hrs': '0.0',
            'installment': '0.0', 'mobile_exe_amt': '0.0', 'addition': '0.0',
            'remarks1': '', 'deduction': '0.0', 'remarks2': ''
        }
        response = self.client.post(url_no_did, default_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(SalaryDetail.objects.filter(salary_master=self.salary_master, present=Decimal('0.0')).exists())


class OfferAccessoryTablePartialViewTest(TestCase):
    """
    Test suite for the OfferAccessoryTablePartialView.
    """
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_start=date(2023, 4, 1), year_end=date(2024, 3, 31))
        cls.emp_type = EmployeeType.objects.create(id=1, description='Full-time')
        cls.duty_hour = DutyHour.objects.create(id=1, hours=Decimal('8.00'))
        cls.ot_hour = OverTimeHour.objects.create(id=1, hours=Decimal('1.50'))
        cls.ot_type_calculated = OverTimeType.objects.create(id=2, description='Calculated OT')
        cls.offer_master = OfferMaster.objects.create(
            id=101, staff_type=cls.emp_type, type_of=1, duty_hrs=cls.duty_hour,
            ot_hrs_lookup=cls.ot_hour, salary=Decimal('50000.00'), overtime_type=cls.ot_type_calculated
        )
        cls.includes_in_allowance = IncludesIn.objects.create(id=1, includes_in='Allowance')
        cls.includes_in_deduction = IncludesIn.objects.create(id=2, includes_in='Deduction')
        
        OfferAccessory.objects.create(
            id=501, offer_master=cls.offer_master, perticulars='Mobile Allowance',
            qty=Decimal('1.0'), amount=Decimal('1000.0'), includes_in=cls.includes_in_allowance
        )
        OfferAccessory.objects.create(
            id=502, offer_master=cls.offer_master, perticulars='Travel Expense',
            qty=Decimal('0.5'), amount=Decimal('500.0'), includes_in=cls.includes_in_allowance
        )
        OfferAccessory.objects.create(
            id=503, offer_master=cls.offer_master, perticulars='Late Fine',
            qty=Decimal('1.0'), amount=Decimal('100.0'), includes_in=cls.includes_in_deduction
        )

    def setUp(self):
        self.client = Client()

    def test_get_offer_accessory_table(self):
        url = f"{reverse('hr_salary:offer_accessory_table')}?offer_master_id={self.offer_master.id}"
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/_offeraccessory_table.html')
        self.assertContains(response, 'Mobile Allowance')
        self.assertContains(response, 'Travel Expense')
        self.assertContains(response, 'Late Fine')
        self.assertContains(response, str(Decimal('1000.00')))
        self.assertContains(response, str(Decimal('250.00'))) # 0.5 * 500 = 250
        self.assertContains(response, str(Decimal('100.00')))

    def test_get_offer_accessory_table_no_data(self):
        # Create another offer master with no accessories
        offer_master_no_acc = OfferMaster.objects.create(
            id=102, staff_type=self.emp_type, type_of=1, duty_hrs=self.duty_hour,
            ot_hrs_lookup=self.ot_hour, salary=Decimal('30000.00'), overtime_type=self.ot_type_calculated
        )
        url = f"{reverse('hr_salary:offer_accessory_table')}?offer_master_id={offer_master_no_acc.id}"
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/_offeraccessory_table.html')
        self.assertContains(response, 'No data to display !')

    def test_get_offer_accessory_table_invalid_id(self):
        url = f"{reverse('hr_salary:offer_accessory_table')}?offer_master_id=invalid_id"
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/_offeraccessory_table.html')
        self.assertContains(response, 'No data to display !')

    def test_get_offer_accessory_table_missing_id(self):
        url = reverse('hr_salary:offer_accessory_table')
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_salary/_offeraccessory_table.html')
        self.assertContains(response, 'No data to display !')

```

### Step 5: HTMX and Alpine.js Integration

This modernization plan prioritizes a highly dynamic and interactive user experience using HTMX and Alpine.js, minimizing full page reloads.

-   **HTMX for Form Submission:** The main "Salary Edit" form uses `hx-post` to send data without a full page refresh. Upon successful submission, the Django view returns a `204 No Content` response with an `HX-Location` header, instructing HTMX to redirect the browser to the specified success URL (`salary_edit_list`), mimicking the original ASP.NET `Response.Redirect`. In case of validation errors, the view re-renders the form with error messages, which HTMX seamlessly swaps back into the DOM, providing immediate feedback to the user.

-   **HTMX for Dynamic DataTable Loading:** The "Offer Accessories" table is loaded dynamically using HTMX. The main `salarydetail_edit.html` template includes a container `div` with `hx-get` and `hx-trigger="load"` attributes. This tells HTMX to fetch the content of the `_offeraccessory_table.html` partial view as soon as the page loads, and insert it into the container. This makes the table's loading independent of the main page, improving perceived performance.

-   **DataTables for List Presentation:** The `_offeraccessory_table.html` partial template includes a JavaScript snippet that initializes DataTables on the loaded table. This provides out-of-the-box client-side features like searching, sorting, and pagination, delivering a professional user experience for data presentation, much like the original ASP.NET `GridView` with `yui-datatable-theme`. The DataTables initialization is carefully placed to run only after the HTMX content has been injected into the DOM.

-   **Alpine.js for UI Enhancements (Optional for this page):** While not strictly required for the current functionality of this specific page (HTMX handles direct form interactions), Alpine.js is assumed to be part of the base template. It would be used for any future client-side reactivity, such as dynamic calculations based on input changes, interactive modals, or UI state management, ensuring a minimal JavaScript footprint.

The combination of Django's server-side rendering, HTMX's asynchronous capabilities, DataTables for data grids, and Alpine.js for lightweight reactivity creates a modern, performant, and maintainable web application with a familiar feel for users.