This document outlines a modernization plan for transitioning your ASP.NET application, specifically the `OfficeStaff_Print_Details.aspx` page, to a modern Django-based solution. Our approach prioritizes automation, emphasizes clear separation of concerns, and leverages cutting-edge web technologies like HTMX and Alpine.js for a highly responsive user experience.

The original ASP.NET page is primarily a report viewer for detailed staff information, aggregating data from multiple database tables using Crystal Reports. In Django, we will replace Crystal Reports with direct rendering using Django templates, powered by a "fat model" approach that encapsulates all complex data retrieval and transformation logic. For comprehensive modernization, we will also demonstrate how such a detail view would fit within a broader `OfficeStaff` management module, incorporating list views, CRUD operations, DataTables, and HTMX-driven modals, even though explicit CRUD forms were not present on this *specific* ASP.NET page.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in retrieving staff details.

**Instructions:**
The ASP.NET code-behind performs numerous `SELECT` operations across multiple tables and constructs a `DataTable` with 41 specific columns. This `DataTable` structure defines the "report schema." We will infer the primary and lookup tables used and map the final 41 columns to our Django models, particularly a computed data structure for the `OfficeStaff` report.

**Identified Tables and Columns:**

*   **Primary Table:** `tblHR_OfficeStaff` (main staff details)
    *   `EmpId`, `CompId`, `PhotoData`, `Title`, `EmployeeName`, `SwapCardNo`, `PhotoFileName`, `Department`, `BGGroup`, `Designation`, `DeptHead`, `GroupLeader`, `DirectorsName`, `Grade`, `MobileNo`, `ContactNo`, `CompanyEmail`, `EmailId1`, `ExtensionNo`, `JoiningDate`, `ResignationDate`, `PermanentAddress`, `CorrespondenceAddress`, `EmailId2`, `DateOfBirth`, `Gender`, `MartialStatus`, `BloodGroup`, `PhysicallyHandycapped`, `Height`, `Weight`, `Religion`, `Cast`, `EducationalQualification`, `AdditionalQualification`, `LastCompanyName`, `WorkingDuration`, `TotalExperience`, `CurrentCTC`, `BankAccountNo`, `PFNo`, `PANNo`, `PassPortNo`, `ExpiryDate`, `AdditionalInformation`

*   **Lookup Tables (and their primary columns used):**
    *   `tblHR_SwapCard`: `Id`, `SwapCardNo`
    *   `tblCompany_master`: `CompId`, `LogoImage`, `CompAdd` (inferred from `fun.CompAdd` and `fun.getCompany`)
    *   `tblHR_Departments`: `Id`, `Description`, `Symbol`
    *   `BusinessGroup`: `Id`, `Name`, `Symbol`
    *   `tblHR_Designation`: `Id`, `Type`, `Symbol`
    *   `tblHR_Grade`: `Id`, `Symbol`
    *   `tblHR_CoporateMobileNo`: `Id`, `MobileNo`
    *   `tblHR_IntercomExt`: `Id`, `ExtNo`

*   **Report Data Columns (final 41 columns constructed in `dt`):**
    `CompanyName`, `PhotoData` (staff photo), `Address` (company address), `CardNo` (Swap Card No), `EmpName`, `Department`, `BussinessGroup`, `DeptDirector`, `DeptHead`, `GroupLeader`, `Designation`, `Grade`, `MobileNo` (corporate mobile), `extNo` (intercom extension), `joindate`, `birthdate`, `resigndate`, `martialstatus`, `physicalstatus`, `padd`, `cadd`, `email`, `gender`, `bgp` (blood group), `height`, `weight`, `religion`, `cast`, `edu` (educational qualification), `adq` (additional qualification), `lc` (last company name), `wd` (working duration), `te` (total experience), `ctc` (current CTC), `ba` (bank account no), `pf` (PF no), `pa` (PAN no), `ps` (passport no), `ex` (expiry date), `inf` (additional information), `LogoImage` (company logo).

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic of the ASP.NET page.

**Instructions:**
The ASP.NET page `OfficeStaff_Print_Details.aspx` is primarily a **Read/Display** page.

*   **Read:** The page fetches extensive details for a specific employee (`EmpId` from QueryString), performing numerous lookups and data transformations to compile a comprehensive "staff report." This involves:
    *   Retrieving employee's basic details.
    *   Looking up associated `SwapCardNo`, `Department`, `BusinessGroup`, `Designation`, `Grade`, `Corporate Mobile No`, `Intercom Extension No`.
    *   Identifying supervisor/director names by looking up other staff members based on `UserID` and `Designation`.
    *   Fetching company name and address, including the company logo.
    *   Handling binary image data (employee photo, company logo).
    *   Formatting date fields.
    *   Converting status codes (Martial Status, Physically Handicapped) into descriptive strings.
*   **No Create, Update, Delete:** This specific page does not perform any C, U, or D operations. Its sole purpose is to present a detailed report.
*   **Navigation:** A "Cancel" button facilitates redirection back to a previous page (either a staff list or another reports page).
*   **Session Management:** The Crystal Report object is stored in and retrieved from the session, which Django will handle differently (no server-side report object persistence like this).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The UI is minimal, focused on displaying the Crystal Report.

*   **`CR:CrystalReportViewer`:** This is the central component for displaying the report. In Django, this will be replaced by a well-structured HTML template that renders the retrieved data directly.
*   **`asp:Button ID="Cancel"`:** A standard navigation button. In Django, this will become a simple `<a href="...">` link for backward navigation.
*   **`asp:Panel`:** A container element for layout. This will be replaced by standard HTML `div` elements with Tailwind CSS for layout.

Since the original page is a detail view, there are no `GridView` or complex data entry forms here. However, to meet the full modernization requirements (DataTables, HTMX for CRUD), we will consider the broader `OfficeStaff` module context, where a list view and modal forms for adding/editing staff would exist.

### Step 4: Generate Django Code

We will create a Django application named `hr` (Human Resources) to house these components.

#### 4.1 Models (`hr/models.py`)

We'll define models for the primary `tblHR_OfficeStaff` and all lookup tables, setting `managed = False` to integrate with the existing database. The "fat model" approach for report generation will be implemented as a class method on the `OfficeStaff` model.

```python
from django.db import models
from django.urls import reverse
import datetime
from django.conf import settings
import os

# Assume these are utility functions or can be replaced by Django/Python equivalents
# For 'fun.Connection()', 'fun.getCurrDate()', 'fun.getCurrTime()', 'fun.CompAdd()', 'fun.getCompany()', 'fun.FromDateDMY()'
# We'll mock some of these or assume they are handled by Django's ORM or standard Python library.

# Placeholder for a function to get default user image if none is found
def get_default_user_image_binary():
    """
    Returns binary data of a default user image. In a real app, this would
    read from a static file or a default image path.
    """
    # This path needs to be adjusted based on your project structure
    # For simplicity, we'll return a placeholder byte string.
    # In a real scenario, you'd load a small default image like this:
    # with open(os.path.join(settings.STATIC_ROOT, 'images/User.jpg'), 'rb') as f:
    #     return f.read()
    return b'default_user_image_binary_data'

# Helper for Company Logo
def get_default_company_logo_binary():
    """
    Returns binary data of a default company logo.
    """
    return b'default_company_logo_binary_data'


class CompanyMaster(models.Model):
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    logo_image = models.BinaryField(db_column='LogoImage', blank=True, null=True)
    # Assuming there's an address field in tblCompany_master, or it's computed.
    # We'll infer an address field or method for fun.CompAdd
    company_address_text = models.TextField(db_column='CompanyAddress', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or 'No Name'

    def get_company_address(self):
        # This mocks fun.CompAdd(CompId)
        return self.company_address_text or 'N/A'


class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return f"{self.description} [{self.symbol}]" if self.symbol else self.description or 'N/A'


class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"{self.name} [{self.symbol}]" if self.symbol else self.name or 'N/A'


class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.type} [{self.symbol}]" if self.symbol else self.type or 'N/A'


class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    # Add other grade fields if necessary

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.symbol or 'N/A'


class CorporateMobileNo(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mobile_no = models.CharField(db_column='MobileNo', max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_CoporateMobileNo'
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        return self.mobile_no or 'N/A'


class IntercomExt(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ext_no = models.CharField(db_column='ExtNo', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_IntercomExt'
        verbose_name = 'Intercom Extension'
        verbose_name_plural = 'Intercom Extensions'

    def __str__(self):
        return self.ext_no or 'N/A'


class SwapCard(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    swap_card_no = models.CharField(db_column='SwapCardNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_SwapCard'
        verbose_name = 'Swap Card'
        verbose_name_plural = 'Swap Cards'

    def __str__(self):
        return self.swap_card_no or 'N/A'


class OfficeStaff(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True) # Assuming EmpId is unique and primary key
    compid = models.ForeignKey(CompanyMaster, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    photodata = models.BinaryField(db_column='PhotoData', blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    swap_card_no_fk = models.ForeignKey(SwapCard, models.DO_NOTHING, db_column='SwapCardNo', blank=True, null=True) # Renamed to avoid clash with report field
    photo_file_name = models.CharField(db_column='PhotoFileName', max_length=255, blank=True, null=True)
    department_fk = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', blank=True, null=True)
    bggroup_fk = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)
    designation_fk = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    dept_head_userid = models.CharField(db_column='DeptHead', max_length=50, blank=True, null=True) # UserID reference
    group_leader_userid = models.CharField(db_column='GroupLeader', max_length=50, blank=True, null=True) # UserID reference
    directors_name_userid = models.CharField(db_column='DirectorsName', max_length=50, blank=True, null=True) # UserID reference
    grade_fk = models.ForeignKey(Grade, models.DO_NOTHING, db_column='Grade', blank=True, null=True)
    mobile_no_fk = models.ForeignKey(CorporateMobileNo, models.DO_NOTHING, db_column='MobileNo', blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=20, blank=True, null=True)
    company_email = models.CharField(db_column='CompanyEmail', max_length=255, blank=True, null=True)
    email_id1 = models.CharField(db_column='EmailId1', max_length=255, blank=True, null=True)
    extension_no_fk = models.ForeignKey(IntercomExt, models.DO_NOTHING, db_column='ExtensionNo', blank=True, null=True)
    joining_date = models.DateField(db_column='JoiningDate', blank=True, null=True)
    resignation_date = models.DateField(db_column='ResignationDate', blank=True, null=True)
    permanent_address = models.TextField(db_column='PermanentAddress', blank=True, null=True)
    correspondence_address = models.TextField(db_column='CorrespondenceAddress', blank=True, null=True)
    email_id2 = models.CharField(db_column='EmailId2', max_length=255, blank=True, null=True)
    date_of_birth = models.DateField(db_column='DateOfBirth', blank=True, null=True)
    gender = models.CharField(db_column='Gender', max_length=10, blank=True, null=True)
    martial_status = models.IntegerField(db_column='MartialStatus', blank=True, null=True) # 1 for Married, 0 for Unmarried
    blood_group = models.CharField(db_column='BloodGroup', max_length=10, blank=True, null=True)
    physically_handycapped = models.IntegerField(db_column='PhysicallyHandycapped', blank=True, null=True) # 1 for Yes, 0 for No
    height = models.CharField(db_column='Height', max_length=10, blank=True, null=True)
    weight = models.CharField(db_column='Weight', max_length=10, blank=True, null=True)
    religion = models.CharField(db_column='Religion', max_length=50, blank=True, null=True)
    cast = models.CharField(db_column='Cast', max_length=50, blank=True, null=True)
    educational_qualification = models.TextField(db_column='EducationalQualification', blank=True, null=True)
    additional_qualification = models.TextField(db_column='AdditionalQualification', blank=True, null=True)
    last_company_name = models.CharField(db_column='LastCompanyName', max_length=255, blank=True, null=True)
    working_duration = models.CharField(db_column='WorkingDuration', max_length=50, blank=True, null=True)
    total_experience = models.CharField(db_column='TotalExperience', max_length=50, blank=True, null=True)
    current_ctc = models.CharField(db_column='CurrentCTC', max_length=50, blank=True, null=True)
    bank_account_no = models.CharField(db_column='BankAccountNo', max_length=50, blank=True, null=True)
    pf_no = models.CharField(db_column='PFNo', max_length=50, blank=True, null=True)
    pan_no = models.CharField(db_column='PANNo', max_length=50, blank=True, null=True)
    pass_port_no = models.CharField(db_column='PassPortNo', max_length=50, blank=True, null=True)
    expiry_date = models.DateField(db_column='ExpiryDate', blank=True, null=True)
    additional_information = models.TextField(db_column='AdditionalInformation', blank=True, null=True)
    
    # UserID field from ASP.NET code, used for lookup
    # We assume UserID is unique and distinct from EmpId for staff hierarchy lookups
    userid = models.CharField(db_column='UserID', max_length=50, blank=True, null=True, unique=True)


    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Member'
        verbose_name_plural = 'Office Staff Members'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name or 'N/A'}"

    def get_absolute_url(self):
        return reverse('staff_detail_report', args=[str(self.empid)])

    @classmethod
    def get_staff_report_data(cls, empid, company_id):
        """
        Gathers and transforms all necessary data for the staff detail report.
        This method encapsulates the complex logic from the ASP.NET Page_Init.
        """
        try:
            staff = cls.objects.select_related(
                'compid', 'swap_card_no_fk', 'department_fk', 'bggroup_fk',
                'designation_fk', 'grade_fk', 'mobile_no_fk', 'extension_no_fk'
            ).get(empid=empid, compid=company_id)
        except cls.DoesNotExist:
            return None # Staff not found

        report_data = {}

        # 1. Company Info
        report_data['CompanyName'] = staff.compid.company_name if staff.compid else 'N/A'
        report_data['Address'] = staff.compid.get_company_address() if staff.compid else 'N/A'
        report_data['LogoImage'] = staff.compid.logo_image if staff.compid and staff.compid.logo_image else get_default_company_logo_binary()

        # 2. Staff Photo
        report_data['PhotoData'] = staff.photodata if staff.photodata and staff.photo_file_name else get_default_user_image_binary()

        # 3. Swap Card No
        report_data['CardNo'] = staff.swap_card_no_fk.swap_card_no if staff.swap_card_no_fk else 'NA'

        # 4. Employee Name & Title
        report_data['EmpName'] = f"{staff.title or ''}. {staff.employee_name or 'N/A'}"

        # 5. Department, Business Group, Designation, Grade
        report_data['Department'] = str(staff.department_fk) if staff.department_fk else 'N/A'
        report_data['BussinessGroup'] = str(staff.bggroup_fk) if staff.bggroup_fk else 'N/A'
        report_data['Designation'] = str(staff.designation_fk) if staff.designation_fk else 'N/A'
        report_data['Grade'] = str(staff.grade_fk) if staff.grade_fk else 'N/A'

        # 6. Staff Hierarchy (Director, Dept Head, Group Leader)
        # These require lookups based on UserID, assuming UserID is distinct from EmpId but points to another OfficeStaff record.
        report_data['DeptDirector'] = 'NA'
        if staff.directors_name_userid:
            try:
                director = cls.objects.get(userid=staff.directors_name_userid)
                # Designation='2' OR Designation='3' is hardcoded in ASP.NET
                if director.designation_fk_id in [2, 3]: # Assuming IDs 2 and 3 are for Director roles
                    report_data['DeptDirector'] = f"{director.title or ''}.{director.employee_name or ''}"
            except cls.DoesNotExist:
                pass

        report_data['DeptHead'] = 'NA'
        if staff.dept_head_userid:
            try:
                head = cls.objects.get(userid=staff.dept_head_userid)
                report_data['DeptHead'] = f"{head.title or ''}.{head.employee_name or ''}"
            except cls.DoesNotExist:
                pass

        report_data['GroupLeader'] = 'NA'
        if staff.group_leader_userid:
            try:
                leader = cls.objects.get(userid=staff.group_leader_userid)
                # Designation='7' is hardcoded in ASP.NET
                if leader.designation_fk_id == 7: # Assuming ID 7 is for Group Leader role
                    report_data['GroupLeader'] = f"{leader.title or ''}.{leader.employee_name or ''}"
            except cls.DoesNotExist:
                pass

        # 7. Contact Info
        report_data['MobileNo'] = staff.mobile_no_fk.mobile_no if staff.mobile_no_fk else 'NA'
        report_data['extNo'] = staff.extension_no_fk.ext_no if staff.extension_no_fk else 'NA'
        report_data['email'] = staff.email_id2 or 'N/A' # ASP.NET used EmailId2

        # 8. Dates (formatted DMY)
        report_data['joindate'] = staff.joining_date.strftime('%d/%m/%Y') if staff.joining_date else ''
        report_data['birthdate'] = staff.date_of_birth.strftime('%d/%m/%Y') if staff.date_of_birth else ''
        report_data['resigndate'] = staff.resignation_date.strftime('%d/%m/%Y') if staff.resignation_date else ''
        report_data['ex'] = staff.expiry_date.strftime('%d/%m/%Y') if staff.expiry_date else ''

        # 9. Status and Personal Details
        report_data['martialstatus'] = "Married" if staff.martial_status == 1 else "Unmarried"
        report_data['physicalstatus'] = "Yes" if staff.physically_handycapped == 1 else "No"
        report_data['padd'] = staff.permanent_address or ''
        report_data['cadd'] = staff.correspondence_address or ''
        report_data['gender'] = staff.gender or ''
        report_data['bgp'] = staff.blood_group or '' # bgp is blood group in ASP.NET
        report_data['height'] = staff.height or ''
        report_data['weight'] = staff.weight or ''
        report_data['religion'] = staff.religion or ''
        report_data['cast'] = staff.cast or ''

        # 10. Education & Experience
        report_data['edu'] = staff.educational_qualification or ''
        report_data['adq'] = staff.additional_qualification or ''
        report_data['lc'] = staff.last_company_name or ''
        report_data['wd'] = staff.working_duration or ''
        report_data['te'] = staff.total_experience or ''

        # 11. Financial & Identification
        report_data['ctc'] = staff.current_ctc or ''
        report_data['ba'] = staff.bank_account_no or ''
        report_data['pf'] = staff.pf_no or ''
        report_data['pa'] = staff.pan_no or ''
        report_data['ps'] = staff.pass_port_no or ''

        # 12. Additional Information
        report_data['inf'] = staff.additional_information or ''

        return report_data
```

#### 4.2 Forms (`hr/forms.py`)

Even though the original ASP.NET page is only for viewing, a full Django modernization would include forms for managing `OfficeStaff` data. We'll create a basic form for staff details for potential future CRUD use in modals.

```python
from django import forms
from .models import OfficeStaff, Department, BusinessGroup, Designation, Grade, CorporateMobileNo, IntercomExt, SwapCard

class OfficeStaffForm(forms.ModelForm):
    # Dynamically populate choices for FKs if needed, or rely on default ModelChoiceField behavior
    department_fk = forms.ModelChoiceField(
        queryset=Department.objects.all(),
        required=False,
        label="Department",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    bggroup_fk = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        label="Business Group",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    designation_fk = forms.ModelChoiceField(
        queryset=Designation.objects.all(),
        required=False,
        label="Designation",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    grade_fk = forms.ModelChoiceField(
        queryset=Grade.objects.all(),
        required=False,
        label="Grade",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    mobile_no_fk = forms.ModelChoiceField(
        queryset=CorporateMobileNo.objects.all(),
        required=False,
        label="Corporate Mobile No.",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    extension_no_fk = forms.ModelChoiceField(
        queryset=IntercomExt.objects.all(),
        required=False,
        label="Intercom Extension No.",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    swap_card_no_fk = forms.ModelChoiceField(
        queryset=SwapCard.objects.all(),
        required=False,
        label="Swap Card No.",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = OfficeStaff
        # Include all fields that are typically editable for an OfficeStaff member
        fields = [
            'title', 'employee_name', 'userid', 'photo_file_name', 'department_fk',
            'bggroup_fk', 'designation_fk', 'grade_fk', 'mobile_no_fk',
            'extension_no_fk', 'contact_no', 'company_email', 'email_id1', 'email_id2',
            'joining_date', 'resignation_date', 'date_of_birth', 'permanent_address',
            'correspondence_address', 'gender', 'martial_status', 'blood_group',
            'physically_handycapped', 'height', 'weight', 'religion', 'cast',
            'educational_qualification', 'additional_qualification', 'last_company_name',
            'working_duration', 'total_experience', 'current_ctc', 'bank_account_no',
            'pf_no', 'pan_no', 'pass_port_no', 'expiry_date', 'additional_information',
            'dept_head_userid', 'group_leader_userid', 'directors_name_userid', 'swap_card_no_fk' # FKs
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'photo_file_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email_id1': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email_id2': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'joining_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'resignation_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'date_of_birth': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'permanent_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'correspondence_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'gender': forms.Select(choices=[('Male', 'Male'), ('Female', 'Female'), ('Other', 'Other')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'martial_status': forms.Select(choices=[(1, 'Married'), (0, 'Unmarried')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'blood_group': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'physically_handycapped': forms.Select(choices=[(1, 'Yes'), (0, 'No')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'height': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'weight': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'religion': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cast': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'educational_qualification': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
            'additional_qualification': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
            'last_company_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'working_duration': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_experience': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'current_ctc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_account_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pass_port_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'additional_information': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 2}),
            'dept_head_userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'group_leader_userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'directors_name_userid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    # Custom validation example (e.g., if employee_name must be unique or conform to a pattern)
    def clean_employee_name(self):
        name = self.cleaned_data['employee_name']
        # Add your validation logic here, e.g.,
        # if not name.isalpha():
        #     raise forms.ValidationError("Employee name must contain only alphabetic characters.")
        return name

```

#### 4.3 Views (`hr/views.py`)

We'll define views for listing staff, basic CRUD operations via HTMX modals, and a dedicated view for the detailed staff report.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import OfficeStaff, CompanyMaster # Import CompanyMaster for company_id

class OfficeStaffListView(ListView):
    model = OfficeStaff
    template_name = 'hr/officestaff/list.html'
    context_object_name = 'officestaff_members'

    def get_queryset(self):
        # Example: Filter by current user's company if session data was available
        # In ASP.NET, Session["compid"] was used. Assuming this context is passed or derived.
        # For this example, we'll fetch all or filter by a dummy company_id
        # current_comp_id = self.request.session.get('compid', 1) # Assuming a default company ID
        # return OfficeStaff.objects.filter(compid=current_comp_id).order_by('employee_name')
        return OfficeStaff.objects.all().order_by('employee_name')

class OfficeStaffTablePartialView(OfficeStaffListView):
    """
    Renders only the table rows, used for HTMX partial updates.
    """
    template_name = 'hr/officestaff/_officestaff_table.html'


class OfficeStaffCreateView(CreateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr/officestaff/_officestaff_form.html' # This will be loaded into a modal
    success_url = reverse_lazy('officestaff_list') # Redirection for non-HTMX requests

    def form_valid(self, form):
        # The ASP.NET code had Session["compid"] for new records.
        # Assuming current_comp_id is determined from request/session or user profile
        current_comp_id = self.request.session.get('compid', 1) # Example: Get from session
        company = get_object_or_404(CompanyMaster, compid=current_comp_id)
        form.instance.compid = company # Assign the company FK
        response = super().form_valid(form)
        messages.success(self.request, 'Office Staff member added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX specific response: trigger client-side event to refresh list and close modal
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': '{"refreshOfficeStaffList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If invalid, re-render the form within the modal
            return response
        return response


class OfficeStaffUpdateView(UpdateView):
    model = OfficeStaff
    form_class = OfficeStaffForm
    template_name = 'hr/officestaff/_officestaff_form.html' # This will be loaded into a modal
    success_url = reverse_lazy('officestaff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Office Staff member updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshOfficeStaffList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response


class OfficeStaffDeleteView(DeleteView):
    model = OfficeStaff
    template_name = 'hr/officestaff/_officestaff_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('officestaff_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Office Staff member deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshOfficeStaffList":true, "closeModal":true}'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add context to show the object being deleted in the confirmation modal
        context['staff_member'] = self.get_object()
        return context


class OfficeStaffDetailReportView(TemplateView):
    """
    Replaces OfficeStaff_Print_Details.aspx.cs for viewing detailed staff reports.
    It fetches all aggregated data using the fat model method.
    """
    template_name = 'hr/officestaff/staff_detail_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        empid = self.kwargs['pk'] # Get EmpId from URL
        
        # In ASP.NET, Session["compid"] was used. Simulate this:
        # You'd typically get this from the logged-in user's profile or session.
        # For demonstration, let's use a hardcoded default company ID or retrieve from user.
        # This part requires authentication/session context setup in Django.
        current_comp_id = self.request.session.get('compid', 1) # Default to 1 for testing

        report_data = OfficeStaff.get_staff_report_data(empid, current_comp_id)

        if not report_data:
            raise Http404("Office Staff Report not found for the given ID.")

        context['report_data'] = report_data
        
        # Determine the 'back' URL for the Cancel button
        # ASP.NET logic: Request.QueryString["PagePrev"]
        # For Django, we can pass it via query param or session, or determine it from referer
        page_prev_param = self.request.GET.get('page_prev')
        if page_prev_param == '1':
            context['back_url'] = reverse_lazy('officestaff_list') # Replace with your actual staff list URL
        else:
            context['back_url'] = reverse_lazy('some_other_reports_page') # Replace with your actual reports page URL
            # Example: context['back_url'] = reverse_lazy('reports_dashboard')

        return context

class ImageView(DetailView):
    """
    A view to serve binary image data stored in the database.
    """
    model = OfficeStaff # Can also be CompanyMaster
    pk_url_kwarg = 'pk' # The primary key for the staff or company

    def get(self, request, *args, **kwargs):
        obj = self.get_object()
        field_name = kwargs['field_name']

        if field_name == 'photodata':
            image_data = obj.photodata
        elif field_name == 'logo_image':
            # Assuming you might want to serve company logo via this view too
            # You'd need to adjust URL or have a separate view for CompanyMaster images
            company = obj.compid # Or get CompanyMaster directly if this view handles company images
            image_data = company.logo_image if company else None
        else:
            raise Http404("Invalid image field.")
        
        if not image_data:
            # Serve a default image if no data exists
            if field_name == 'photodata':
                image_data = OfficeStaff.get_default_user_image_binary()
            elif field_name == 'logo_image':
                image_data = OfficeStaff.get_default_company_logo_binary()
            else:
                raise Http404("Image data not found and no default available.")

        return HttpResponse(image_data, content_type="image/jpeg") # Adjust content_type based on actual image type
```

#### 4.4 Templates (`hr/templates/hr/officestaff/`)

We'll define templates for the list view, partials for HTMX-driven forms/modals, and the detailed staff report.

**`list.html`** (Main page for staff listing)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Office Staff Members</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'officestaff_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Staff
        </button>
    </div>

    {% if messages %}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div id="officestaffTable-container"
         hx-trigger="load, refreshOfficeStaffList from:body"
         hx-get="{% url 'officestaff_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Staff Data...</p>
        </div>
    </div>
    
    <!-- Modal for form & confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden z-50"
         _="on hx:afterOnLoad #modalContent remove .hidden from #modal
            on click if event.target.id == 'modal' remove .is-active from me
            on closeModal remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-3xl w-full mx-4 transform transition-all duration-300 scale-95 opacity-0 hidden"
             _="on hx:afterOnLoad set my.style.opacity='1' set my.style.transform='scale(1)'"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('staffModal', () => ({
            isOpen: false,
            openModal() {
                this.isOpen = true;
                document.getElementById('modal').classList.add('is-active');
            },
            closeModal() {
                this.isOpen = false;
                document.getElementById('modal').classList.remove('is-active');
                // Clear modal content if needed
                document.getElementById('modalContent').innerHTML = '';
            }
        }));
    });

    // Handle HTMX triggers for messages and modal closing
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.successful && evt.detail.xhr.status === 204) {
            // Check for custom triggers like 'closeModal'
            const hxTrigger = evt.detail.xhr.getResponseHeader('HX-Trigger');
            if (hxTrigger) {
                try {
                    const triggers = JSON.parse(hxTrigger);
                    if (triggers.closeModal) {
                        document.dispatchEvent(new CustomEvent('closeModal'));
                    }
                } catch (e) {
                    // console.error("Error parsing HX-Trigger:", e);
                }
            }
            // For messages, HTMX automatically handles them via HX-Swap headers,
            // or you can explicitly add them to #messages div on refreshOfficeStaffList
        }
    });

</script>
{% endblock %}
```

**`_officestaff_table.html`** (Partial for DataTable content)

```html
<table id="officestaffTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No.</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for member in officestaff_members %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.title }}. {{ member.employee_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.department_fk.description|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.designation_fk.type|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ member.mobile_no_fk.mobile_no|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <a href="{% url 'staff_detail_report' member.empid %}"
                   class="text-blue-600 hover:text-blue-900 mr-4">View Report</a>
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-xs shadow-sm transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'officestaff_edit' member.empid %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-xs shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'officestaff_delete' member.empid %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-sm text-gray-500">No staff members found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after HTMX loads the content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#officestaffTable')) {
            $('#officestaffTable').DataTable().destroy();
        }
        $('#officestaffTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [5] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

**`_officestaff_form.html`** (Partial for modal CRUD forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Office Staff Member</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Staff
            </button>
        </div>
    </form>
</div>
```

**`_officestaff_confirm_delete.html`** (Partial for delete confirmation modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the staff member **"{{ staff_member.employee_name }}"** (ID: {{ staff_member.empid }})?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`staff_detail_report.html`** (Replacement for ASP.NET `OfficeStaff_Print_Details.aspx`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-800">Staff Detail Report</h2>
        <a href="{{ back_url }}" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
            <i class="fas fa-arrow-left mr-2"></i>Back to Previous
        </a>
    </div>

    <div class="bg-white shadow-2xl rounded-lg p-8 mb-8 overflow-hidden print-area">
        <div class="text-center mb-10">
            <img src="{% url 'image_view' pk=report_data.CompanyID field_name='logo_image' %}" alt="Company Logo" class="h-24 mx-auto mb-4" onerror="this.onerror=null;this.src='/static/images/default_company_logo.png';">
            <h1 class="text-4xl font-bold text-blue-800 mb-2">{{ report_data.CompanyName }}</h1>
            <p class="text-gray-600 text-lg">{{ report_data.Address }}</p>
        </div>

        <div class="border-t border-gray-200 pt-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="md:col-span-1 flex justify-center items-start">
                    <img src="{% url 'image_view' pk=request.resolver_match.kwargs.pk field_name='photodata' %}" alt="Staff Photo" class="w-48 h-48 object-cover rounded-full shadow-lg border-4 border-blue-200" onerror="this.onerror=null;this.src='/static/images/default_user_image.png';">
                </div>
                <div class="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4 text-lg">
                    <p><strong>Employee Name:</strong> {{ report_data.EmpName }}</p>
                    <p><strong>Card No:</strong> {{ report_data.CardNo }}</p>
                    <p><strong>Department:</strong> {{ report_data.Department }}</p>
                    <p><strong>Business Group:</strong> {{ report_data.BussinessGroup }}</p>
                    <p><strong>Designation:</strong> {{ report_data.Designation }}</p>
                    <p><strong>Grade:</strong> {{ report_data.Grade }}</p>
                    <p><strong>Joining Date:</strong> {{ report_data.joindate }}</p>
                    <p><strong>Resignation Date:</strong> {{ report_data.resigndate }}</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-6 text-gray-700 text-base">
                <div class="space-y-4">
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Contact Information</h3>
                    <p><strong>Mobile No:</strong> {{ report_data.MobileNo }}</p>
                    <p><strong>Extension No:</strong> {{ report_data.extNo }}</p>
                    <p><strong>Email:</strong> {{ report_data.email }}</p>
                    <p><strong>Permanent Address:</strong> {{ report_data.padd }}</p>
                    <p><strong>Correspondence Address:</strong> {{ report_data.cadd }}</p>
                    
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4 mt-8">Hierarchy</h3>
                    <p><strong>Director:</strong> {{ report_data.DeptDirector }}</p>
                    <p><strong>Department Head:</strong> {{ report_data.DeptHead }}</p>
                    <p><strong>Group Leader:</strong> {{ report_data.GroupLeader }}</p>
                </div>

                <div class="space-y-4">
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Personal Details</h3>
                    <p><strong>Date of Birth:</strong> {{ report_data.birthdate }}</p>
                    <p><strong>Gender:</strong> {{ report_data.gender }}</p>
                    <p><strong>Martial Status:</strong> {{ report_data.martialstatus }}</p>
                    <p><strong>Blood Group:</strong> {{ report_data.bgp }}</p>
                    <p><strong>Height:</strong> {{ report_data.height }}</p>
                    <p><strong>Weight:</strong> {{ report_data.weight }}</p>
                    <p><strong>Religion:</strong> {{ report_data.religion }}</p>
                    <p><strong>Cast:</strong> {{ report_data.cast }}</p>
                    <p><strong>Physically Handicapped:</strong> {{ report_data.physicalstatus }}</p>

                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4 mt-8">Professional Details</h3>
                    <p><strong>Educational Qualification:</strong> {{ report_data.edu }}</p>
                    <p><strong>Additional Qualification:</strong> {{ report_data.adq }}</p>
                    <p><strong>Last Company:</strong> {{ report_data.lc }}</p>
                    <p><strong>Working Duration:</strong> {{ report_data.wd }}</p>
                    <p><strong>Total Experience:</strong> {{ report_data.te }}</p>
                    <p><strong>Current CTC:</strong> {{ report_data.ctc }}</p>
                </div>

                <div class="col-span-1 md:col-span-2 space-y-4">
                    <h3 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Identification & Financial</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <p><strong>Bank Account No:</strong> {{ report_data.ba }}</p>
                        <p><strong>PF No:</strong> {{ report_data.pf }}</p>
                        <p><strong>PAN No:</strong> {{ report_data.pa }}</p>
                        <p><strong>Passport No:</strong> {{ report_data.ps }}</p>
                        <p><strong>Passport Expiry Date:</strong> {{ report_data.ex }}</p>
                    </div>
                    <p><strong>Additional Information:</strong> {{ report_data.inf }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Add print-specific styles */
    @media print {
        body {
            font-size: 10pt; /* Adjust font size for print */
        }
        .container {
            max-width: none; /* Remove max-width for full page print */
            margin: 0;
            padding: 0;
        }
        .bg-white, .shadow-2xl, .rounded-lg, .mb-8 {
            box-shadow: none !important;
            border-radius: 0 !important;
            margin-bottom: 0 !important;
            padding: 0 !important;
        }
        .flex.justify-between.items-center.mb-6, .bg-red-600 {
            display: none; /* Hide header and back button */
        }
        .print-area {
            page-break-after: always; /* Ensure new page for each report if multiple */
        }
    }
</style>
{% endblock %}
```

#### 4.5 URLs (`hr/urls.py`)

```python
from django.urls import path
from .views import (
    OfficeStaffListView,
    OfficeStaffCreateView,
    OfficeStaffUpdateView,
    OfficeStaffDeleteView,
    OfficeStaffTablePartialView,
    OfficeStaffDetailReportView,
    ImageView
)

urlpatterns = [
    # Office Staff List and CRUD (HTMX driven)
    path('staff/', OfficeStaffListView.as_view(), name='officestaff_list'),
    path('staff/table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('staff/add/', OfficeStaffCreateView.as_view(), name='officestaff_add'),
    path('staff/edit/<int:pk>/', OfficeStaffUpdateView.as_view(), name='officestaff_edit'),
    path('staff/delete/<int:pk>/', OfficeStaffDeleteView.as_view(), name='officestaff_delete'),

    # Detailed Staff Report View (replaces ASP.NET OfficeStaff_Print_Details.aspx)
    path('staff/<int:pk>/report/', OfficeStaffDetailReportView.as_view(), name='staff_detail_report'),

    # Image serving view for photo data stored in DB
    path('image/<int:pk>/<str:field_name>/', ImageView.as_view(), name='image_view'),
]
```

#### 4.6 Tests (`hr/tests.py`)

Comprehensive tests for models, the report data generation logic, and all views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ObjectDoesNotExist
from django.http import Http404
from .models import (
    OfficeStaff, Department, BusinessGroup, Designation, Grade,
    CorporateMobileNo, IntercomExt, SwapCard, CompanyMaster
)
import datetime

class CompanyMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        CompanyMaster.objects.create(
            compid=1,
            company_name='Test Co.',
            company_address_text='123 Test St.',
            logo_image=b'logo_binary_data'
        )

    def test_company_creation(self):
        company = CompanyMaster.objects.get(compid=1)
        self.assertEqual(company.company_name, 'Test Co.')
        self.assertEqual(company.get_company_address(), '123 Test St.')

class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create related lookup data
        cls.company = CompanyMaster.objects.create(compid=1, company_name='Test Co.', company_address_text='123 Test St.')
        cls.department = Department.objects.create(id=1, description='HR', symbol='HRD')
        cls.bggroup = BusinessGroup.objects.create(id=1, name='Management', symbol='MGMT')
        cls.designation = Designation.objects.create(id=1, type='Manager', symbol='MGR')
        cls.grade = Grade.objects.create(id=1, symbol='A1')
        cls.mobile = CorporateMobileNo.objects.create(id=1, mobile_no='**********')
        cls.ext = IntercomExt.objects.create(id=1, ext_no='1234')
        cls.swap_card = SwapCard.objects.create(id=1, swap_card_no='SWAP001')

        # Create test staff data
        cls.staff1 = OfficeStaff.objects.create(
            empid=101,
            compid=cls.company,
            title='Mr',
            employee_name='John Doe',
            userid='jdoe',
            department_fk=cls.department,
            designation_fk=cls.designation,
            joining_date=datetime.date(2020, 1, 15),
            date_of_birth=datetime.date(1990, 5, 10),
            martial_status=1, # Married
            physically_handycapped=0, # No
            photodata=b'photo_data_1',
            photo_file_name='john_doe.jpg',
            email_id2='<EMAIL>',
            permanent_address='123 Main St',
            correspondence_address='456 Oak Ave',
            gender='Male',
            blood_group='O+',
            height='170cm',
            weight='70kg',
            religion='Christian',
            cast='General',
            educational_qualification='B.Sc. Computer Science',
            additional_qualification='PMP',
            last_company_name='Old Company',
            working_duration='5 Years',
            total_experience='10 Years',
            current_ctc='10 LPA',
            bank_account_no='**********',
            pf_no='PF12345',
            pan_no='**********',
            pass_port_no='P1234567',
            expiry_date=datetime.date(2025, 12, 31),
            additional_information='No issues',
            bggroup_fk=cls.bggroup,
            mobile_no_fk=cls.mobile,
            extension_no_fk=cls.ext,
            swap_card_no_fk=cls.swap_card,
        )

        cls.director = OfficeStaff.objects.create(
            empid=201, compid=cls.company, title='Dr', employee_name='Jane Smith', userid='jsmith',
            designation_fk=Designation.objects.create(id=2, type='Director', symbol='DIR'), # Director designation
        )
        cls.head = OfficeStaff.objects.create(
            empid=202, compid=cls.company, title='Mr', employee_name='Robert Johnson', userid='rjohnson',
            designation_fk=Designation.objects.create(id=3, type='Department Head', symbol='DPTHEAD'),
        )
        cls.leader = OfficeStaff.objects.create(
            empid=203, compid=cls.company, title='Ms', employee_name='Emily White', userid='ewhite',
            designation_fk=Designation.objects.create(id=7, type='Group Leader', symbol='GRPLDR'), # Group Leader designation
        )

        cls.staff1.directors_name_userid = cls.director.userid
        cls.staff1.dept_head_userid = cls.head.userid
        cls.staff1.group_leader_userid = cls.leader.userid
        cls.staff1.save()

    def test_officestaff_creation(self):
        staff = OfficeStaff.objects.get(empid=101)
        self.assertEqual(staff.employee_name, 'John Doe')
        self.assertEqual(staff.department_fk.description, 'HR')
        self.assertEqual(staff.compid.company_name, 'Test Co.')

    def test_get_staff_report_data(self):
        report_data = OfficeStaff.get_staff_report_data(self.staff1.empid, self.company.compid)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['EmpName'], 'Mr. John Doe')
        self.assertEqual(report_data['CompanyName'], 'Test Co.')
        self.assertEqual(report_data['Department'], 'HR [HRD]') # Ensure symbol is included as in ASP.NET
        self.assertEqual(report_data['joindate'], '15/01/2020')
        self.assertEqual(report_data['martialstatus'], 'Married')
        self.assertEqual(report_data['physicalstatus'], 'No')
        self.assertEqual(report_data['CardNo'], 'SWAP001')
        self.assertEqual(report_data['MobileNo'], '**********')
        self.assertEqual(report_data['extNo'], '1234')
        self.assertIn(report_data['DeptDirector'], 'Dr.Jane Smith')
        self.assertIn(report_data['DeptHead'], 'Mr.Robert Johnson')
        self.assertIn(report_data['GroupLeader'], 'Ms.Emily White')
        self.assertIsNotNone(report_data['PhotoData'])
        self.assertIsNotNone(report_data['LogoImage'])

    def test_get_staff_report_data_not_found(self):
        report_data = OfficeStaff.get_staff_report_data(999, self.company.compid)
        self.assertIsNone(report_data)

    def test_get_staff_report_data_no_related_data(self):
        staff_no_relations = OfficeStaff.objects.create(
            empid=102,
            compid=self.company,
            title='Ms',
            employee_name='Jane Smith',
            userid='jsmith_temp',
            # No related FKs assigned
        )
        report_data = OfficeStaff.get_staff_report_data(staff_no_relations.empid, self.company.compid)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['Department'], 'N/A')
        self.assertEqual(report_data['CardNo'], 'NA')
        self.assertEqual(report_data['DeptDirector'], 'NA')
        self.assertIsNotNone(report_data['PhotoData']) # Should return default image


class OfficeStaffViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.company = CompanyMaster.objects.create(compid=1, company_name='Test Co.', company_address_text='123 Test St.')
        self.department = Department.objects.create(id=1, description='HR', symbol='HRD')
        self.designation = Designation.objects.create(id=1, type='Manager', symbol='MGR')
        self.staff1 = OfficeStaff.objects.create(
            empid=101, compid=self.company, employee_name='Alice', title='Ms', userid='alice',
            department_fk=self.department, designation_fk=self.designation
        )
        self.staff2 = OfficeStaff.objects.create(
            empid=102, compid=self.company, employee_name='Bob', title='Mr', userid='bob',
            department_fk=self.department, designation_fk=self.designation
        )

        # Simulate session context for company ID
        session = self.client.session
        session['compid'] = 1
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('officestaff_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/list.html')
        self.assertIn('officestaff_members', response.context)
        self.assertEqual(len(response.context['officestaff_members']), 2)

    def test_table_partial_view(self):
        response = self.client.get(reverse('officestaff_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/_officestaff_table.html')
        self.assertIn('officestaff_members', response.context)
        self.assertEqual(len(response.context['officestaff_members']), 2)

    def test_create_view_get(self):
        response = self.client.get(reverse('officestaff_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/_officestaff_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post(self):
        data = {
            'title': 'Mr', 'employee_name': 'Charlie', 'userid': 'charlie', 'department_fk': self.department.id,
            'designation_fk': self.designation.id, 'joining_date': '2023-01-01', 'date_of_birth': '1995-03-01',
            'martial_status': 0, 'physically_handycapped': 0, 'permanent_address': 'New Address',
            'correspondence_address': 'New Address', 'email_id2': '<EMAIL>', 'gender': 'Male',
            'blood_group': 'A+', 'height': '180cm', 'weight': '75kg', 'religion': 'None', 'cast': 'None',
            'educational_qualification': 'PhD', 'additional_qualification': '', 'last_company_name': '',
            'working_duration': '', 'total_experience': '', 'current_ctc': '', 'bank_account_no': '',
            'pf_no': '', 'pan_no': '', 'pass_port_no': '', 'expiry_date': '', 'additional_information': '',
            'dept_head_userid': '', 'group_leader_userid': '', 'directors_name_userid': '', 'swap_card_no_fk': '',
            'mobile_no_fk': '', 'extension_no_fk': '',
        }
        response = self.client.post(reverse('officestaff_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertTrue(OfficeStaff.objects.filter(employee_name='Charlie').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('officestaff_edit', args=[self.staff1.empid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/_officestaff_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.employee_name, 'Alice')

    def test_update_view_post(self):
        data = {
            'title': 'Ms', 'employee_name': 'Alice Updated', 'userid': 'alice', 'department_fk': self.department.id,
            'designation_fk': self.designation.id, 'joining_date': '2020-01-15', 'date_of_birth': '1990-05-10',
            'martial_status': 1, 'physically_handycapped': 0, 'permanent_address': '123 Main St',
            'correspondence_address': '456 Oak Ave', 'email_id2': '<EMAIL>', 'gender': 'Female',
            'blood_group': 'O+', 'height': '170cm', 'weight': '70kg', 'religion': 'Christian', 'cast': 'General',
            'educational_qualification': 'B.Sc. Computer Science', 'additional_qualification': 'PMP', 'last_company_name': 'Old Company',
            'working_duration': '5 Years', 'total_experience': '10 Years', 'current_ctc': '10 LPA', 'bank_account_no': '**********',
            'pf_no': 'PF12345', 'pan_no': '**********', 'pass_port_no': 'P1234567', 'expiry_date': '2025-12-31', 'additional_information': 'No issues',
            'dept_head_userid': '', 'group_leader_userid': '', 'directors_name_userid': '', 'swap_card_no_fk': '',
            'mobile_no_fk': '', 'extension_no_fk': '',
        }
        response = self.client.post(reverse('officestaff_edit', args=[self.staff1.empid]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.staff1.refresh_from_db()
        self.assertEqual(self.staff1.employee_name, 'Alice Updated')

    def test_delete_view_get(self):
        response = self.client.get(reverse('officestaff_delete', args=[self.staff1.empid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/_officestaff_confirm_delete.html')
        self.assertIn('staff_member', response.context)
        self.assertEqual(response.context['staff_member'].employee_name, 'Alice')

    def test_delete_view_post(self):
        response = self.client.post(reverse('officestaff_delete', args=[self.staff1.empid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertFalse(OfficeStaff.objects.filter(empid=self.staff1.empid).exists())

    def test_staff_detail_report_view(self):
        # Create a full staff record for detailed report testing
        staff_for_report = OfficeStaff.objects.create(
            empid=103,
            compid=self.company,
            title='Dr',
            employee_name='Report User',
            userid='report_user',
            department_fk=self.department,
            designation_fk=self.designation,
            joining_date=datetime.date(2021, 1, 1),
            date_of_birth=datetime.date(1985, 2, 20),
            martial_status=1,
            physically_handycapped=0,
            photodata=b'report_user_photo',
            photo_file_name='report_user.jpg',
            email_id2='<EMAIL>',
            permanent_address='789 Report St',
            correspondence_address='789 Report St',
            gender='Male',
            blood_group='AB+',
            height='175cm',
            weight='80kg',
            religion='Hindu',
            cast='OBC',
            educational_qualification='M.Tech',
            additional_qualification='AWS Cert',
            last_company_name='Previous Corp',
            working_duration='3 Years',
            total_experience='8 Years',
            current_ctc='15 LPA',
            bank_account_no='**********',
            pf_no='PF54321',
            pan_no='**********',
            pass_port_no='P8765432',
            expiry_date=datetime.date(2028, 6, 30),
            additional_information='Some additional info.',
            bggroup_fk=self.department, # Can be different from department_fk
            mobile_no_fk=CorporateMobileNo.objects.create(id=2, mobile_no='**********'),
            extension_no_fk=IntercomExt.objects.create(id=2, ext_no='5678'),
            swap_card_no_fk=SwapCard.objects.create(id=2, swap_card_no='SWAP002'),
            # No hierarchy set for simplicity in this specific test
        )
        response = self.client.get(reverse('staff_detail_report', args=[staff_for_report.empid]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr/officestaff/staff_detail_report.html')
        self.assertIn('report_data', response.context)
        self.assertEqual(response.context['report_data']['EmpName'], 'Dr. Report User')
        self.assertEqual(response.context['report_data']['CompanyName'], 'Test Co.')
        self.assertIsNotNone(response.context['report_data']['PhotoData'])

    def test_staff_detail_report_view_not_found(self):
        with self.assertRaises(Http404):
            self.client.get(reverse('staff_detail_report', args=[999]))

    def test_image_view(self):
        # Test staff photo
        response = self.client.get(reverse('image_view', args=[self.staff1.empid, 'photodata']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg') # Adjust based on actual image type
        # For simplicity, we are not checking actual image data here, just content type

        # Test company logo (assuming CompanyMaster has an image field and can be accessed via Staff's company ID)
        # This requires a slight modification to ImageView or a separate URL for CompanyMaster
        # For now, let's test if it handles it conceptually for the staff's company_id
        response = self.client.get(reverse('image_view', args=[self.company.compid, 'logo_image']))
        self.assertEqual(response.status_code, 200) # Should pass if CompanyMaster is accessed correctly
        self.assertEqual(response['Content-Type'], 'image/jpeg')

    def test_image_view_not_found(self):
        with self.assertRaises(Http404):
            self.client.get(reverse('image_view', args=[999, 'photodata']))

        with self.assertRaises(Http404):
            self.client.get(reverse('image_view', args=[self.staff1.empid, 'invalid_field']))
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated code inherently uses HTMX for all CRUD operations within the `OfficeStaff` list view.

*   **List View (`list.html`):**
    *   `hx-get="{% url 'officestaff_table' %}" hx-swap="innerHTML" hx-trigger="load, refreshOfficeStaffList from:body"`: This loads the DataTables content dynamically on page load and refreshes it whenever a `refreshOfficeStaffList` event is triggered (e.g., after a successful CRUD operation).
    *   "Add New Staff" button uses `hx-get` to fetch the form and `hx-target` to load it into `#modalContent`, then `_="on click add .is-active to #modal"` to show the modal with Alpine.js.
*   **Table Partial (`_officestaff_table.html`):**
    *   "Edit" and "Delete" buttons use `hx-get` to fetch their respective forms/confirmations into `#modalContent`, also triggering Alpine.js to show the modal.
    *   DataTables initialization is done via `<script>` tags within the partial, ensuring it re-initializes correctly when HTMX reloads the table.
*   **Form Partials (`_officestaff_form.html`, `_officestaff_confirm_delete.html`):**
    *   `hx-post="{{ request.path }}" hx-swap="none"`: Forms submit back to their own URL, but HTMX prevents the full page reload. `hx-swap="none"` indicates that the form itself shouldn't be swapped out immediately.
    *   `HX-Trigger` header from the view (`HttpResponse` with status 204) is used to tell the client to close the modal (`closeModal`) and refresh the main list (`refreshOfficeStaffList`).
    *   "Cancel" buttons use `_="on click trigger closeModal from body"` to close the modal client-side without a server request.
*   **Alpine.js (`list.html` `extra_js` block):**
    *   An Alpine.js component (`staffModal`) is defined to manage the visibility of the modal (`isOpen`).
    *   Custom events (`closeModal`) are dispatched/listened for to control modal visibility based on HTMX success triggers.
*   **Detailed Report (`staff_detail_report.html`):** This template is primarily for display and printing. It doesn't use HTMX/Alpine.js for interactive elements *within* the report itself, as the original ASP.NET page was static in this regard. The "Back" button is a simple `<a>` tag.

---

### Final Notes

This modernization plan transforms the Crystal Report viewing functionality into a native Django solution while incorporating modern web development best practices. By using the "fat model" approach, complex data aggregation logic is kept out of the views, leading to cleaner, more maintainable code. The integration of HTMX and Alpine.js provides a responsive and dynamic user interface, replacing traditional full-page postbacks with efficient partial updates and client-side UI management. The comprehensive testing ensures reliability and stability of the migrated components. This systematic approach can be automated and scaled for other parts of your legacy ASP.NET application.