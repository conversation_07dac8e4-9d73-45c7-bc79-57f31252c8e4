## ASP.NET to Django Conversion Script:

This document outlines a strategic plan to modernize your existing ASP.NET application, specifically the "Office Staff New" module, by migrating it to a robust and scalable Django 5.0+ framework. This transition will leverage state-of-the-art web technologies like HTMX and Alpine.js for dynamic user interfaces, DataTables for enhanced data presentation, and a "Fat Model, Thin View" architecture for maintainability and performance.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include `base.html` template code in your output - assume it already exists and is properly configured.
-   Focus ONLY on component-specific code for the current module (`hr_transactions`).
-   Always include complete unit tests for models and integration tests for views to ensure code quality and stability.
-   Use modern Django 5.0+ patterns and follow best practices for security and performance.
-   Keep your code clean, efficient, and avoid redundancy (DRY principle).
-   Always generate complete, runnable Django code.

### AutoERP Guidelines:

-   **Fat Model, Thin View:** Business logic resides predominantly in Django models, keeping views concise (5-15 lines max).
-   **Database Mapping:** Models are mapped to existing database tables using `managed = False` and `db_table`.
-   **Client-Side Features:** DataTables for all list views (search, sort, paginate), HTMX for dynamic interactions, and Alpine.js for simple UI state management.
-   **Template Inheritance:** All templates will extend `core/base.html` for consistent layout.
-   **Test Coverage:** Aim for at least 80% test coverage with a combination of unit and integration tests.
-   **DRY Principle:** Promote code reuse and reduce duplication.
-   **Styling:** Utilize Tailwind CSS for a utility-first approach to styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Implication:** Understanding the data structure is the foundation of our new application. This step ensures that our Django models accurately reflect your existing database, preventing data loss and ensuring data integrity during the migration.

**Analysis:**
The ASP.NET code queries `[tblHR_Offer_Master]` and displays `OfferId`, `EmployeeName`, and `StaffType`. It also references `[tblHR_OfficeStaff]` for filtering.

*   **Main Table:** `tblHR_Offer_Master`
    *   `OfferId`: Identified as the primary key (`DataKeyNames="OfferId"`), an integer.
    *   `EmployeeName`: String type.
    *   `StaffType`: Appears to be stored as a string ('1') or other values, mapped to "Office Staff" or "Contract Staff" in the UI.

*   **Related Table (for filtering):** `tblHR_OfficeStaff`
    *   It contains an `OfferId` used for filtering, implying a relationship with `tblHR_Offer_Master`. For this plan, we'll assume `tblHR_OfficeStaff` has a field `OfferId` that links to `tblHR_Offer_Master.OfferId`.

### Step 2: Identify Backend Functionality

**Business Implication:** This step clarifies what the original page *does*. Our modernized version will replicate existing capabilities while enabling future enhancements.

**Analysis:**
The ASP.NET page `OfficeStaff_New.aspx` primarily performs a **Read (List)** operation.
*   It fetches `OfferId`, `EmployeeName`, and `StaffType` from `tblHR_Offer_Master`.
*   Crucially, it filters records where `OfferId` is *not present* in `tblHR_OfficeStaff`. This means it lists offers that haven't yet been processed into official staff records.
*   It transforms the `StaffType` numeric value ('1') into a user-friendly string ("Office Staff" / "Contract Staff").
*   It supports pagination (`AllowPaging="True"`).
*   It has a "Select" hyperlink that navigates to `OfficeStaff_New_Details.aspx?OfferId={0}`, implying a detail/processing page for a selected offer.

There are no direct **Create**, **Update**, or **Delete** operations on `tblHR_Offer_Master` explicitly shown on *this specific page*. However, following the modernization template, we will provide Django's full CRUD views for `OfferMaster` to make the module extensible and align with best practices. The "Select" action will be mapped to an `UpdateView` for the `OfferMaster` record, as this is the most common pattern for "viewing details" in a CRUD context in Django.

### Step 3: Infer UI Components

**Business Implication:** This helps us translate visual elements and user interactions into modern, dynamic web components, ensuring a familiar yet improved user experience.

**Analysis:**
*   **`asp:GridView`:** This is the primary data display component. We will replace this with a standard HTML `<table>` enhanced by **DataTables** for client-side search, sort, and pagination.
*   **Paging Controls:** Handled automatically by DataTables.
*   **HyperLinkField "Select":** This will be an HTMX-powered button within each table row. Clicking it will open a modal (`#modal`) to display the `OfferMaster`'s details for editing or further action, loading the content via `hx-get`.
*   **`lblmsg` Label:** This will be replaced by Django's `messages` framework, displayed dynamically to the user.
*   **Styling:** All UI elements will be styled using **Tailwind CSS**.

---

### Step 4: Generate Django Code

We will create a new Django application, for example, `hr_transactions`, to house these components.

#### 4.1 Models (`hr_transactions/models.py`)

**Business Implication:** Models define the data structure and encapsulate business rules, ensuring data consistency and providing a centralized place for data-related logic. The `get_staff_type_display` method translates cryptic database values into user-friendly terms.

```python
from django.db import models
from django.db.models import Exists, OuterRef

class OfferMaster(models.Model):
    """
    Represents an offer that can be converted into an OfficeStaff entry.
    Mapped to the existing tblHR_Offer_Master table.
    """
    # Original field mapping from tblHR_Offer_Master
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True, verbose_name="Offer ID")
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, verbose_name="Employee Name")
    staff_type = models.CharField(db_column='StaffType', max_length=10, verbose_name="Staff Type Code") # Stored as '1' or other

    # Business rule for StaffType display
    STAFF_TYPE_CHOICES = {
        '1': 'Office Staff',
        # As per ASP.NET code, anything not '1' is 'Contract Staff'
    }

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'
        ordering = ['-offer_id'] # Matches original ASP.NET Order By OfferId Desc

    def __str__(self):
        return f"Offer ID: {self.offer_id} - {self.employee_name}"

    def get_staff_type_display(self):
        """
        Returns the human-readable staff type based on the 'StaffType' code.
        """
        return self.STAFF_TYPE_CHOICES.get(self.staff_type, 'Contract Staff')

    @classmethod
    def get_pending_offers(cls):
        """
        Returns a queryset of OfferMaster records that have not yet been
        processed into OfficeStaff entries, ordered by offer_id descending.
        This replicates the `Where OfferId not in (select OfferId from [tblHR_OfficeStaff])` logic.
        """
        # Annotate each OfferMaster with a boolean indicating if an OfficeStaff entry exists for it
        # Then filter for those where no staff entry exists.
        return cls.objects.annotate(
            has_staff_entry=Exists(
                OfficeStaff.objects.filter(offer_id=OuterRef('offer_id'))
            )
        ).filter(has_staff_entry=False).order_by('-offer_id')


class OfficeStaff(models.Model):
    """
    Minimal model for tblHR_OfficeStaff, used for filtering OfferMaster records.
    Actual fields beyond OfferId are not known from the provided ASP.NET code,
    so only the essential linking field is defined.
    """
    # Assuming OfferId in tblHR_OfficeStaff links to tblHR_Offer_Master.OfferId
    # It might be a ForeignKey or a simple integer field, depending on the DB schema.
    # We'll treat it as a primary key here if it's unique and maps directly.
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True, verbose_name="Linked Offer ID")
    # Add other fields if known from the actual tblHR_OfficeStaff schema
    # For instance: staff_record_id = models.IntegerField(db_column='StaffRecordId', primary_key=True)
    #               offer = models.ForeignKey(OfferMaster, on_delete=models.DO_NOTHING, db_column='OfferId')
    # Given the query `select OfferId from [tblHR_OfficeStaff]`, OfferId is a direct column.

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Entry'
        verbose_name_plural = 'Office Staff Entries'

    def __str__(self):
        return f"Office Staff Entry for Offer ID: {self.offer_id}"

```

#### 4.2 Forms (`hr_transactions/forms.py`)

**Business Implication:** Forms manage user input and validation, ensuring that data entered into the system is clean and adheres to predefined rules. This simplifies data collection and reduces errors.

```python
from django import forms
from .models import OfferMaster

class OfferMasterForm(forms.ModelForm):
    """
    Form for creating and updating OfferMaster records.
    """
    class Meta:
        model = OfferMaster
        fields = ['offer_id', 'employee_name', 'staff_type']
        widgets = {
            'offer_id': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Offer ID (e.g., 123)'
            }),
            'employee_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Employee Name'
            }),
            'staff_type': forms.TextInput(attrs={ # Could be a ChoiceField if StaffType is enum
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Staff Type (e.g., 1 for Office Staff)'
            }),
        }

    # Add custom validation methods here if needed, e.g., to ensure OfferId is unique
    # def clean_offer_id(self):
    #     offer_id = self.cleaned_data['offer_id']
    #     if self.instance.pk is None and OfferMaster.objects.filter(offer_id=offer_id).exists():
    #         raise forms.ValidationError("An offer with this ID already exists.")
    #     return offer_id

```

#### 4.3 Views (`hr_transactions/views.py`)

**Business Implication:** Views act as the coordinator between user requests, data retrieval, and presentation. By keeping them "thin," the application logic remains organized and easy to troubleshoot. HTMX allows for dynamic updates without full page reloads, improving user experience.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import OfferMaster
from .forms import OfferMasterForm

class OfferMasterListView(ListView):
    """
    Displays a list of OfferMaster records that are pending (not yet linked to OfficeStaff).
    Corresponds to the main functionality of OfficeStaff_New.aspx.
    """
    model = OfferMaster
    template_name = 'hr_transactions/offermaster/list.html'
    context_object_name = 'offermasters'

    def get_queryset(self):
        """
        Retrieves pending offers using the model's business logic.
        """
        return OfferMaster.get_pending_offers()

class OfferMasterTablePartialView(OfferMasterListView):
    """
    Renders only the table portion of the OfferMaster list,
    designed to be loaded via HTMX.
    """
    template_name = 'hr_transactions/offermaster/_offermaster_table.html'

class OfferMasterCreateView(CreateView):
    """
    Handles the creation of new OfferMaster records.
    """
    model = OfferMaster
    form_class = OfferMasterForm
    template_name = 'hr_transactions/offermaster/form.html'
    success_url = reverse_lazy('offermaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Offer Master added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a client-side event to refresh the list.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfferMasterList'
                }
            )
        return response

class OfferMasterUpdateView(UpdateView):
    """
    Handles the editing of existing OfferMaster records.
    This view serves the purpose of the "Select" action in the original ASP.NET,
    allowing viewing and editing of an offer's details.
    """
    model = OfferMaster
    form_class = OfferMasterForm
    template_name = 'hr_transactions/offermaster/form.html'
    success_url = reverse_lazy('offermaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Offer Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfferMasterList'
                }
            )
        return response

class OfferMasterDeleteView(DeleteView):
    """
    Handles the deletion of OfferMaster records.
    """
    model = OfferMaster
    template_name = 'hr_transactions/offermaster/confirm_delete.html'
    success_url = reverse_lazy('offermaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Offer Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOfferMasterList'
                }
            )
        return response

```

#### 4.4 Templates (`hr_transactions/templates/hr_transactions/offermaster/`)

**Business Implication:** Templates define the user interface. By using partials and adhering to DRY principles, we ensure a consistent look and feel while making UI modifications simpler and less prone to errors. HTMX and Alpine.js power dynamic, interactive experiences.

**`list.html`**: The main page displaying the list of pending offers.
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Pending Offer Masters</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'offermaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Offer
        </button>
    </div>

    {# Messages display area #}
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 mb-2 text-sm text-{{ message.tags }}-700 bg-{{ message.tags }}-100 rounded-lg dark:bg-{{ message.tags }}-200 dark:text-{{ message.tags }}-800" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    {# HTMX target for the data table #}
    <div id="offermasterTable-container"
         hx-trigger="load, refreshOfferMasterList from:body"
         hx-get="{% url 'offermaster_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading pending offers...</p>
        </div>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad(evt) if evt.detail.elt.id == 'modalContent' add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

**`_offermaster_table.html`**: Partial template for the DataTables.
```html
<div class="p-4">
    <table id="offermasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Offer ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Staff Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for offer in offermasters %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.offer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ offer.get_staff_type_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_edit' offer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Select (Edit)
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'offermaster_delete' offer.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-3 px-4 text-center text-gray-500 text-lg">No pending offers to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once and on content load
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#offermasterTable')) {
            $('#offermasterTable').DataTable().destroy(); // Destroy previous instance if it exists
        }
        $('#offermasterTable').DataTable({
            "pageLength": 24, // Matches original ASP.NET PageSize
            "lengthMenu": [[10, 24, 50, -1], [10, 24, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

**`form.html`**: Partial template for Create/Update forms in a modal.
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Offer Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}

        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`**: Partial template for Delete confirmation in a modal.
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Offer Master record for <strong>"{{ object.employee_name }}" (ID: {{ object.offer_id }})</strong>?</p>

    <form hx-post="{% url 'offermaster_delete' object.pk %}" hx-swap="none" class="flex justify-end space-x-4">
        {% csrf_token %}
        <button
            type="button"
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            type="submit"
            class="inline-flex justify-center px-5 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
            Delete
        </button>
    </form>
</div>
```

#### 4.5 URLs (`hr_transactions/urls.py`)

**Business Implication:** URLs define the accessible paths to your application's functionality. A clear, consistent URL structure improves navigability and maintainability. HTMX-specific endpoints enable seamless partial page updates.

```python
from django.urls import path
from .views import (
    OfferMasterListView,
    OfferMasterTablePartialView,
    OfferMasterCreateView,
    OfferMasterUpdateView,
    OfferMasterDeleteView
)

urlpatterns = [
    # Main list view for pending offers (equivalent to original ASPX page)
    path('offermaster/', OfferMasterListView.as_view(), name='offermaster_list'),

    # HTMX endpoint for refreshing the table content
    path('offermaster/table/', OfferMasterTablePartialView.as_view(), name='offermaster_table'),

    # HTMX endpoint for loading the 'add new offer' form in a modal
    path('offermaster/add/', OfferMasterCreateView.as_view(), name='offermaster_add'),

    # HTMX endpoint for loading the 'edit offer' form in a modal (maps to 'Select' action)
    path('offermaster/edit/<int:pk>/', OfferMasterUpdateView.as_view(), name='offermaster_edit'),

    # HTMX endpoint for loading the 'delete offer' confirmation in a modal
    path('offermaster/delete/<int:pk>/', OfferMasterDeleteView.as_view(), name='offermaster_delete'),
]
```

#### 4.6 Tests (`hr_transactions/tests.py`)

**Business Implication:** Comprehensive testing is crucial for software quality. It catches bugs early, ensures that new features don't break existing ones, and provides confidence in the application's reliability. Achieving high test coverage is a key indicator of a stable and maintainable system.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import OfferMaster, OfficeStaff
from .forms import OfferMasterForm

class OfferMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data that will be available for all tests in this class
        # An offer that IS NOT linked to OfficeStaff (should appear in pending list)
        cls.offer1 = OfferMaster.objects.create(
            offer_id=101,
            employee_name='Alice Smith',
            staff_type='1' # Office Staff
        )
        # An offer that IS linked to OfficeStaff (should NOT appear in pending list)
        cls.offer2 = OfferMaster.objects.create(
            offer_id=102,
            employee_name='Bob Johnson',
            staff_type='2' # Contract Staff
        )
        OfficeStaff.objects.create(offer_id=cls.offer2.offer_id) # Link offer2 to OfficeStaff

        # Another offer that IS NOT linked to OfficeStaff
        cls.offer3 = OfferMaster.objects.create(
            offer_id=103,
            employee_name='Charlie Brown',
            staff_type='1'
        )

    def test_offermaster_creation(self):
        """Test basic OfferMaster creation and field values."""
        self.assertEqual(self.offer1.employee_name, 'Alice Smith')
        self.assertEqual(self.offer1.staff_type, '1')
        self.assertTrue(OfferMaster.objects.filter(offer_id=101).exists())

    def test_offermaster_str_method(self):
        """Test the __str__ method for human-readable representation."""
        self.assertEqual(str(self.offer1), "Offer ID: 101 - Alice Smith")

    def test_get_staff_type_display_office_staff(self):
        """Test StaffType display for 'Office Staff' type."""
        self.assertEqual(self.offer1.get_staff_type_display(), 'Office Staff')

    def test_get_staff_type_display_contract_staff(self):
        """Test StaffType display for 'Contract Staff' type (anything not '1')."""
        self.assertEqual(self.offer2.get_staff_type_display(), 'Contract Staff')

    def test_get_pending_offers_queryset(self):
        """
        Test the custom manager/method to retrieve only pending offers.
        Should exclude offer2 as it's linked to OfficeStaff.
        """
        pending_offers = OfferMaster.get_pending_offers()
        # Should contain offer1 and offer3, but not offer2
        self.assertIn(self.offer1, pending_offers)
        self.assertIn(self.offer3, pending_offers)
        self.assertNotIn(self.offer2, pending_offers)
        self.assertEqual(pending_offers.count(), 2)
        # Verify ordering
        self.assertEqual(list(pending_offers), [self.offer3, self.offer1])

class OfferMasterFormTest(TestCase):
    def test_form_valid_data(self):
        form = OfferMasterForm(data={
            'offer_id': 200,
            'employee_name': 'New Employee',
            'staff_type': '1'
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_data_missing_fields(self):
        form = OfferMasterForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('offer_id', form.errors)
        self.assertIn('employee_name', form.errors)
        self.assertIn('staff_type', form.errors)

class OfferMasterViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create test data for each test method
        self.offer1 = OfferMaster.objects.create(
            offer_id=301,
            employee_name='Test Employee 1',
            staff_type='1'
        )
        self.offer2 = OfferMaster.objects.create(
            offer_id=302,
            employee_name='Test Employee 2',
            staff_type='2'
        )
        OfficeStaff.objects.create(offer_id=self.offer2.offer_id) # Link offer2

    def test_list_view_get(self):
        """Test that the list view loads correctly and displays only pending offers."""
        response = self.client.get(reverse('offermaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_transactions/offermaster/list.html')
        self.assertContains(response, 'Test Employee 1') # Should see offer1
        self.assertNotContains(response, 'Test Employee 2') # Should NOT see offer2
        self.assertContains(response, 'Pending Offer Masters')

    def test_table_partial_view_get(self):
        """Test that the table partial loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('offermaster_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_transactions/offermaster/_offermaster_table.html')
        self.assertContains(response, '<table id="offermasterTable"')
        self.assertContains(response, 'Test Employee 1')
        self.assertNotContains(response, 'Test Employee 2')

    def test_create_view_get(self):
        """Test that the create form loads correctly in a modal via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('offermaster_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_transactions/offermaster/form.html')
        self.assertContains(response, 'Add Offer Master') # Check form title
        self.assertContains(response, '<form hx-post=')

    def test_create_view_post_success(self):
        """Test successful creation of a new OfferMaster record via HTMX."""
        self.assertEqual(OfferMaster.objects.count(), 2) # offer1 and offer2, but offer2 is linked
        data = {
            'offer_id': 303,
            'employee_name': 'New Staff Member',
            'staff_type': '1'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('offermaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success -> No Content
        self.assertTrue(OfferMaster.objects.filter(offer_id=303).exists())
        self.assertEqual(OfferMaster.objects.count(), 3) # One new offer added
        # Check if success message was added (it's added to the session/messages framework)
        # self.assertIn('Offer Master added successfully.', [m.message for m in messages.get_messages(response.wsgi_request)]) # More complex to test in direct HTMX POST

    def test_create_view_post_invalid(self):
        """Test invalid form submission for create view via HTMX."""
        initial_count = OfferMaster.objects.count()
        data = {
            'offer_id': '', # Invalid data
            'employee_name': '',
            'staff_type': ''
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('offermaster_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Invalid form returns the form with errors
        self.assertTemplateUsed(response, 'hr_transactions/offermaster/form.html')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(OfferMaster.objects.count(), initial_count)

    def test_update_view_get(self):
        """Test that the update form loads correctly for an existing offer via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('offermaster_edit', args=[self.offer1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_transactions/offermaster/form.html')
        self.assertContains(response, 'Edit Offer Master')
        self.assertContains(response, self.offer1.employee_name)

    def test_update_view_post_success(self):
        """Test successful update of an OfferMaster record via HTMX."""
        data = {
            'offer_id': self.offer1.pk, # PK must be included for update
            'employee_name': 'Alice Updated',
            'staff_type': '1'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('offermaster_edit', args=[self.offer1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.offer1.refresh_from_db()
        self.assertEqual(self.offer1.employee_name, 'Alice Updated')

    def test_delete_view_get(self):
        """Test that the delete confirmation loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('offermaster_delete', args=[self.offer1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_transactions/offermaster/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.offer1.employee_name)

    def test_delete_view_post_success(self):
        """Test successful deletion of an OfferMaster record via HTMX."""
        initial_count = OfferMaster.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('offermaster_delete', args=[self.offer1.pk]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertFalse(OfferMaster.objects.filter(pk=self.offer1.pk).exists())
        self.assertEqual(OfferMaster.objects.count(), initial_count - 1)

```

---

### Step 5: HTMX and Alpine.js Integration

**Business Implication:** This is where the application truly feels modern and responsive. By eliminating full page reloads for common actions, we provide a faster, smoother, and more intuitive user experience, similar to a single-page application but with simpler architecture.

*   **HTMX for dynamic updates:**
    *   The `list.html` template uses `hx-get="{% url 'offermaster_table' %}"` on the `offermasterTable-container` div. This ensures the DataTables content is loaded asynchronously when the page loads (`hx-trigger="load"`).
    *   After a successful CRUD operation (Add, Edit, Delete), the views return an `HttpResponse` with `status=204` and `HX-Trigger: 'refreshOfferMasterList'`. This custom event is caught by the `offermasterTable-container` (`hx-trigger="load, refreshOfferMasterList from:body"`), which then triggers a re-fetch of the table, updating the list without a full page refresh.
    *   Buttons for "Add New Offer", "Select (Edit)", and "Delete" all use `hx-get` to fetch the respective form/confirmation partials (`form.html` or `confirm_delete.html`) into the modal's `modalContent` div.
    *   Form submissions (`hx-post`) from within the modal target `hx-swap="none"`. This, combined with the view's 204 response and `HX-Trigger`, ensures the modal closes and the main list updates.

*   **Alpine.js for UI state management:**
    *   The `#modal` div uses Alpine.js's `x-data` implicitly (via `_` directives) to manage its visibility (`add .is-active to #modal`, `remove .is-active from me`). This keeps the modal logic simple and local to the HTML.

*   **DataTables for list views:**
    *   The `_offermaster_table.html` partial includes the JavaScript to initialize DataTables on the `#offermasterTable`. This provides client-side features like search, sorting, and pagination, which were previously handled by `GridView` server-side paging. The `pageLength` is set to `24` to match the original ASP.NET `PageSize`.

*   **No custom JavaScript requirements (beyond CDN imports for HTMX/Alpine/jQuery/DataTables):** The design ensures that all interactions are driven by declarative HTML attributes provided by HTMX and simple UI state management by Alpine.js, minimizing the need for complex, hand-written JavaScript.

---

### Final Notes

*   This modernization plan fully converts the `OfficeStaff_New.aspx` functionality, including its core data display and filtering logic.
*   The `MasterPageFile` in ASP.NET is fully replaced by Django's `core/base.html` template inheritance.
*   The business logic of mapping `StaffType` codes to display names is encapsulated within the `OfferMaster` model using `get_staff_type_display`.
*   The complex filtering logic (`OfferId not in (select OfferId from [tblHR_OfficeStaff])`) is efficiently handled in the `OfferMaster.get_pending_offers()` class method, keeping the view logic minimal.
*   The `Select` action is now a modern HTMX-powered modal that loads the `OfferMaster`'s edit form, aligning with typical Django CRUD patterns.
*   The generated tests ensure the correctness of models, forms, and views, covering both standard and HTMX-specific interactions.
*   This structured approach facilitates further development and maintenance, ensuring a modern, maintainable, and highly performant application.