## ASP.NET to Django Conversion Script: Working Days Management

This plan outlines the modernization of your ASP.NET "Working Days" module into a modern Django application. Our approach prioritizes automation-driven processes, ensuring a smooth and efficient transition. We will leverage Django's robust features, along with HTMX and Alpine.js, to create a highly interactive and maintainable system, moving away from legacy ASP.NET Web Forms.

### Business Value & Outcomes:

1.  **Reduced Technical Debt:** Eliminating outdated ASP.NET Web Forms and server-side view state, leading to a cleaner, more modern codebase that is easier to maintain and extend.
2.  **Enhanced User Experience:** Implementing HTMX and Alpine.js provides a dynamic, responsive interface without full page reloads, similar to a Single Page Application (SPA) but with simpler development.
3.  **Improved Performance:** Shifting frontend rendering and interactions to the client-side (via HTMX/Alpine.js) reduces server load and bandwidth usage, resulting in a faster application.
4.  **Simplified Development & Maintenance:** Adopting Django's "Fat Model, Thin View" pattern centralizes business logic, making it easier to understand, test, and debug. The use of DataTables provides out-of-the-box advanced table features.
5.  **Scalability:** Django's architecture is inherently more scalable than legacy ASP.NET Web Forms, preparing your application for future growth.
6.  **Testability:** Comprehensive unit and integration tests ensure the reliability and correctness of the new system, minimizing bugs and enabling rapid, confident feature development.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the `SqlDataSource` components and direct SQL queries, we identify three key tables:

-   **`tblHR_WorkingDays`**: This is the primary table for managing working days.
    -   `Id` (Primary Key, integer)
    -   `CompId` (Integer, inferred from `Session["compid"]`)
    -   `FinYearId` (Integer, Foreign Key to `tblFinancial_master`, inferred from `DropDownList` and SQL join)
    -   `MonthId` (Integer, Foreign Key to `tblHR_Months`, inferred from `DropDownList` and `RowDataBound` conversion)
    -   `Days` (Double, stores number of working days)

-   **`tblFinancial_master`**: Used to select financial years.
    -   `FinYearId` (Primary Key, integer)
    -   `FinYear` (String, e.g., "2023-2024")

-   **`tblHR_Months`**: Used to select months.
    -   `Id` (Primary Key, integer)
    -   `Month` (String, e.g., "January", "February")

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD (Create, Read, Update, Delete) operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET `GridView` and code-behind implement comprehensive data management:

-   **Read (Display List):**
    -   The `FillGrid()` method fetches `WorkingDays` records.
    -   It joins `tblHR_WorkingDays` with `tblFinancial_master` and conceptually `tblHR_Months` (though the join is commented out in the final SQL, the `MonthId` is converted to a name in `RowDataBound`).
    -   Records are filtered by `CompId` and `FinYearId` from the user's session.
    -   Data is displayed in `GridView1` with pagination.

-   **Create (Add New Record):**
    -   Triggered by `btnInsert` in the `GridView` footer (CommandName="Add") and `EmptyDataTemplate` (CommandName="Add1").
    -   Collects `FinYearId`, `MonthId`, and `Days` from dropdowns and textboxes.
    -   Inserts a new record into `tblHR_WorkingDays`.
    -   **Validation:** Checks if `Days` is numeric and not empty. Crucially, it prevents duplicate entries for the same `CompId`, `FinYearId`, and `MonthId` combination.

-   **Update (Edit Existing Record):**
    -   Triggered by the `Edit` `LinkButton` in `GridView1` (CommandName="Edit", then CommandName="Update").
    -   Allows editing the `Days` field. (The `FinYearId` and `MonthId` fields are displayed but commented out for editing in the ASPX, and the C# update query only updates `Days`).
    -   Updates the `Days` field for the corresponding `Id` in `tblHR_WorkingDays`.
    -   **Validation:** Checks if `Days` is numeric and not empty.

-   **Delete (Remove Record):**
    -   Triggered by the `Delete` `LinkButton` in `GridView1` (CommandName="Delete").
    -   Deletes a record from `tblHR_WorkingDays` based on its `Id`.

-   **Session Management:** `CompId` and `FinYearId` are critical session variables used for filtering and insertion.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactive roles to map them to modern Django and HTMX/Alpine.js components.

**Instructions:**
The ASP.NET controls are directly translated into Django templates using HTML, CSS (Tailwind), HTMX, and Alpine.js:

-   **`asp:GridView` (ID `GridView1`):** This central component will be replaced by an HTML `<table>` integrated with **DataTables.js**.
    -   `AllowPaging`, `ShowFooter`, `AutoGenerateColumns`, `DataKeyNames`: Handled by DataTables.js and explicit template rendering.
    -   `CommandField` (Edit/Delete): Replaced by `<a>` or `<button>` elements with HTMX attributes to trigger modals for edit/delete forms.
    -   `TemplateField` (SN, Id, Fin Year, Month, Days): Direct HTML rendering in `<tbody>`.
    -   Footer for `Insert`: Replaced by a button that triggers a HTMX modal for a new record form.
    -   `EmptyDataTemplate`: The "Insert" functionality will be part of the main page's "Add New" button.

-   **`asp:Label` (e.g., `lblFinYearId`, `lblMessage`):** Simple HTML `<span>` or `<p>` tags. `lblMessage` will be handled by Django's `messages` framework, displayed temporarily or within a modal.

-   **`asp:DropDownList` (e.g., `DptYear`, `ddlMonth`):** HTML `<select>` tags. These will be populated from Django model querysets in the form, ensuring correct data.

-   **`asp:TextBox` (e.g., `txtDays`):** HTML `<input type="text">`.

-   **`asp:Button`, `asp:LinkButton`:** HTML `<button>` or `<a>` tags. Crucially, these will have **HTMX attributes** (`hx-get`, `hx-post`, `hx-target`, `hx-swap`, `hx-trigger`) to enable dynamic interactions without full page reloads.

-   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** Replaced by **Django Form validation** (backend) and potentially Alpine.js for simpler client-side immediate feedback (though Django's validation is primary).

-   **Client-side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`, `confirmationAdd()`, etc.):** These will be replaced by **HTMX** for network requests and **Alpine.js** for modal management, basic UI state, and user confirmations.

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `hr_masters`, to house this module.

#### 4.1 Models (`hr_masters/models.py`)

**Task:** Create Django models that map to the existing database tables, including relationships and business logic.

**Instructions:**
We define three models: `FinancialYear`, `Month`, and `WorkingDay`. `FinancialYear` and `Month` are lookup tables. `WorkingDay` is the main entity, with foreign keys to `FinancialYear` and `Month`, and a unique constraint for `CompId`, `FinYear`, and `Month` to prevent duplicates.

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Assume 'Company' model exists or CompId is an integer field.
# If 'Company' model does not exist, consider creating a simple proxy model
# or keeping CompId as an IntegerField if it's purely an identifier.
# For this example, we'll keep it as IntegerField, as it's from session data.

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'
        ordering = ['-fin_year_id'] # Order by latest financial year first

    def __str__(self):
        return self.fin_year

class Month(models.Model):
    """
    Maps to tblHR_Months for month names.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    month_name = models.CharField(db_column='Month', max_length=20)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblHR_Months'
        verbose_name = 'Month'
        verbose_name_plural = 'Months'
        ordering = ['id'] # Order by month number

    def __str__(self):
        return self.month_name

class WorkingDay(models.Model):
    """
    Maps to tblHR_WorkingDays for storing working days per month/financial year.
    Includes business logic for validation.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK and auto-incrementing in DB
    comp_id = models.IntegerField(db_column='CompId')
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='working_days')
    month = models.ForeignKey(Month, on_delete=models.DO_NOTHING, db_column='MonthId', related_name='working_days')
    days = models.FloatField(db_column='Days')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblHR_WorkingDays'
        verbose_name = 'Working Day'
        verbose_name_plural = 'Working Days'
        # Unique constraint to prevent duplicate entries for the same company, financial year, and month
        unique_together = (('comp_id', 'fin_year', 'month'),)
        ordering = ['-id'] # Order by latest entry first, similar to ASP.NET GridView

    def __str__(self):
        return f"{self.month.month_name} - {self.fin_year.fin_year} ({self.days} days)"

    def clean(self):
        """
        Custom validation for WorkingDay model.
        Ensures that 'Days' is a positive number.
        The unique_together constraint handles the duplicate entry logic (comp_id, fin_year, month).
        """
        super().clean()
        if self.days is not None and self.days <= 0:
            raise ValidationError({'days': _('Days must be a positive number.')})
        
        # The unique_together constraint handles the 'Month is already exist' logic
        # at the database level. For form validation, we can catch IntegrityError.
        # However, for a more user-friendly message at the form level,
        # we can explicitly check if an object with the same unique fields already exists.
        # This will be better handled in the form's clean method, but putting it here
        # ensures model-level integrity.
        
        if self.pk is None: # Only on creation
            if WorkingDay.objects.filter(
                comp_id=self.comp_id,
                fin_year=self.fin_year,
                month=self.month
            ).exists():
                raise ValidationError(
                    _('This month already has working days defined for this financial year and company.'),
                    code='duplicate_entry'
                )

```

#### 4.2 Forms (`hr_masters/forms.py`)

**Task:** Define a Django form for user input, including field types, widgets for styling, and validation.

**Instructions:**
We create a `ModelForm` for `WorkingDay`. It will handle selecting `FinancialYear` and `Month` using `ModelChoiceField`s and validate the `Days` field.

```python
from django import forms
from .models import WorkingDay, FinancialYear, Month
from django.core.exceptions import ValidationError

class WorkingDayForm(forms.ModelForm):
    # ModelChoiceFields to represent ForeignKey relationships as dropdowns
    fin_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(),
        to_field_name='fin_year_id', # Use fin_year_id as the value for the option
        empty_label="Select Financial Year",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Financial Year"
    )
    month = forms.ModelChoiceField(
        queryset=Month.objects.all(),
        to_field_name='id', # Use id as the value for the option
        empty_label="Select Month",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Month"
    )

    class Meta:
        model = WorkingDay
        fields = ['fin_year', 'month', 'days'] # 'comp_id' will be set in the view
        widgets = {
            'days': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter number of days'}),
        }
        labels = {
            'days': 'Number of Days',
        }

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # Get request from kwargs to access session
        super().__init__(*args, **kwargs)
        
        # Prefill dropdowns on edit
        if self.instance.pk:
            self.fields['fin_year'].initial = self.instance.fin_year.fin_year_id
            self.fields['month'].initial = self.instance.month.id
            # Disable fin_year and month on edit, as per ASP.NET original behavior
            self.fields['fin_year'].widget.attrs['disabled'] = 'disabled'
            self.fields['month'].widget.attrs['disabled'] = 'disabled'
        else:
            # If creating a new record, set initial queryset for financial year
            # based on current financial year from session (if available)
            if self.request and 'finyear' in self.request.session:
                current_fin_year_id = self.request.session['finyear']
                # Filter FinancialYear queryset to only include current_fin_year_id or older
                # This matches the ASP.NET query logic 'FinYearId<='" + FinYearId + "'"
                self.fields['fin_year'].queryset = FinancialYear.objects.filter(fin_year_id__lte=current_fin_year_id)
            else:
                self.fields['fin_year'].queryset = FinancialYear.objects.all()

    def clean(self):
        """
        Custom form-level validation.
        Handles the unique constraint for (comp_id, fin_year, month) for user-friendly error messages.
        """
        cleaned_data = super().clean()
        fin_year = cleaned_data.get('fin_year')
        month = cleaned_data.get('month')
        days = cleaned_data.get('days')

        # Check for non-positive days as per ASP.NET validation
        if days is not None and days <= 0:
            self.add_error('days', "Days must be a positive number.")

        # Ensure unique combination for comp_id, fin_year, month on creation
        if not self.instance.pk and self.request: # Only for new records
            comp_id = self.request.session.get('compid')
            if comp_id and fin_year and month:
                if WorkingDay.objects.filter(comp_id=comp_id, fin_year=fin_year, month=month).exists():
                    raise ValidationError(
                        "This month already has working days defined for this financial year.",
                        code='duplicate_entry'
                    )
        
        return cleaned_data

```

#### 4.3 Views (`hr_masters/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and HTMX compatibility.

**Instructions:**
We define `ListView`, `CreateView`, `UpdateView`, and `DeleteView`. A dedicated `TablePartialView` will render the table for HTMX updates. Session variables `compid` and `finyear` are used to filter and assign data.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import IntegrityError # For handling unique constraint errors
from .models import WorkingDay, FinancialYear, Month
from .forms import WorkingDayForm
from django.shortcuts import get_object_or_404
from django.db.models import F

class WorkingDayListView(ListView):
    """
    Displays the main Working Days page with the DataTables container.
    This view renders the shell, and the table content is loaded via HTMX.
    """
    model = WorkingDay
    template_name = 'hr_masters/workingday/list.html'
    context_object_name = 'working_days' # Not directly used for table content, but good practice

    def get_queryset(self):
        # This queryset is not directly used by the list.html (as it loads partial via HTMX)
        # but provides a fallback or initial data for testing.
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        if comp_id and fin_year_id:
            # Matches ASP.NET query: filtered by CompId and FinYearId up to current
            return WorkingDay.objects.filter(
                comp_id=comp_id,
                fin_year__fin_year_id__lte=fin_year_id
            ).select_related('fin_year', 'month') # Eager load related objects
        return WorkingDay.objects.none()

class WorkingDayTablePartialView(ListView):
    """
    Renders only the working days table partial for HTMX requests.
    """
    model = WorkingDay
    template_name = 'hr_masters/workingday/_workingday_table.html'
    context_object_name = 'working_days'

    def get_queryset(self):
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        if comp_id and fin_year_id:
            # Matches ASP.NET query: filtered by CompId and FinYearId up to current
            # Also joins to get Month name (though not explicitly in ASP.NET's final query,
            # it was done in RowDataBound and implied by commented joins)
            return WorkingDay.objects.filter(
                comp_id=comp_id,
                fin_year__fin_year_id__lte=fin_year_id
            ).select_related('fin_year', 'month') # Eager load related objects
        return WorkingDay.objects.none()

class WorkingDayCreateView(CreateView):
    """
    Handles the creation of a new Working Day record.
    Renders a form in a modal via HTMX.
    """
    model = WorkingDay
    form_class = WorkingDayForm
    template_name = 'hr_masters/workingday/_workingday_form.html'
    success_url = reverse_lazy('workingday_list') # Fallback, HTMX usually handles redirect

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request # Pass request to form for session data
        return kwargs

    def form_valid(self, form):
        comp_id = self.request.session.get('compid')
        # Ensure comp_id and fin_year from session are set on the instance
        if not comp_id:
            form.add_error(None, "Company ID not found in session. Please log in again.")
            return self.form_invalid(form)

        form.instance.comp_id = comp_id
        # fin_year and month are already set via form fields
        
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Working Day added successfully.')
            if self.request.headers.get('HX-Request'):
                # On successful HTMX submission, return 204 No Content
                # and trigger a custom event to refresh the table.
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshWorkingDayList'}
                )
            return response
        except IntegrityError: # Catch unique constraint error
            form.add_error(None, "A working day entry for this month and financial year already exists.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # If form is invalid, re-render the form partial with errors for HTMX
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # HTMX will swap this back into the modal
        return response

class WorkingDayUpdateView(UpdateView):
    """
    Handles the update of an existing Working Day record.
    Renders a form in a modal via HTMX.
    """
    model = WorkingDay
    form_class = WorkingDayForm
    template_name = 'hr_masters/workingday/_workingday_form.html'
    success_url = reverse_lazy('workingday_list') # Fallback

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request # Pass request to form for session data
        return kwargs
        
    def get_queryset(self):
        # Ensure the user can only update records belonging to their company/financial year
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        if comp_id and fin_year_id:
            return super().get_queryset().filter(
                comp_id=comp_id,
                fin_year__fin_year_id__lte=fin_year_id
            ).select_related('fin_year', 'month')
        return super().get_queryset().none()


    def form_valid(self, form):
        # As per ASP.NET, fin_year and month are not editable on update, only days.
        # So we ensure they are not changed if the fields were disabled.
        # Django handles disabled fields by not including them in cleaned_data,
        # so we ensure the instance's fin_year and month remain unchanged.
        # This is implicitly handled by not allowing them to be changed in the form.

        response = super().form_valid(form)
        messages.success(self.request, 'Working Day updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshWorkingDayList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class WorkingDayDeleteView(DeleteView):
    """
    Handles the deletion of a Working Day record.
    Renders a confirmation prompt in a modal via HTMX.
    """
    model = WorkingDay
    template_name = 'hr_masters/workingday/_workingday_confirm_delete.html'
    success_url = reverse_lazy('workingday_list') # Fallback

    def get_queryset(self):
        # Ensure the user can only delete records belonging to their company/financial year
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        if comp_id and fin_year_id:
            return super().get_queryset().filter(
                comp_id=comp_id,
                fin_year__fin_year_id__lte=fin_year_id
            )
        return super().get_queryset().none()

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Working Day deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshWorkingDayList'}
            )
        return response

```

#### 4.4 Templates

**Task:** Create comprehensive HTML templates for all views, utilizing DRY principles, HTMX for dynamic content, Alpine.js for UI state, and DataTables for list presentation.

**Instructions:**
Templates will extend `core/base.html` (not included here). We'll have a main list template and partials for the table, form, and delete confirmation, all designed for HTMX interactions.

**`hr_masters/templates/hr_masters/workingday/list.html`**
This is the main page for managing working days. It sets up the modal and the HTMX container for the table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Working Days Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'workingday_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Working Day
        </button>
    </div>
    
    <!-- Messages Display (optional, can be integrated into base.html or a dedicated HTMX area) -->
    {% if messages %}
    <div id="messages-container" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 text-sm text-{{ message.tags }}-700 bg-{{ message.tags }}-100 rounded-lg" role="alert">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="workingDayTable-container"
         hx-trigger="load, refreshWorkingDayList from:body"
         hx-get="{% url 'workingday_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Working Days...</p>
        </div>
    </div>
    
    <!-- Reusable Modal Structure for HTMX content -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 transition-opacity duration-300 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Initialize DataTables if the table partial was swapped
        if (event.detail.target.id === 'workingDayTable-container') {
            $('#workingDayTable').DataTable({
                "pageLength": 17, // Matching ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance if recreating
                "language": {
                    "emptyTable": "No working days found for this company and financial year."
                }
            });
        }

        // Handle modal display logic after content is loaded
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (modal) {
                modal.classList.add('is-active');
                modal.classList.remove('hidden');
                setTimeout(() => { // Small delay for transition
                    modalContent.classList.remove('scale-95', 'opacity-0');
                    modalContent.classList.add('scale-100', 'opacity-100');
                }, 50);
            }
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        // If a 204 No Content response is received, it means the form was valid
        // and we should close the modal manually, as HTMX doesn't swap content.
        if (event.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (modal) {
                modalContent.classList.remove('scale-100', 'opacity-100');
                modalContent.classList.add('scale-95', 'opacity-0');
                setTimeout(() => { // Small delay for transition
                    modal.classList.remove('is-active');
                    modal.classList.add('hidden');
                    modalContent.innerHTML = ''; // Clear content
                }, 300);
            }
            event.detail.shouldSwap = false; // Prevent HTMX from attempting a swap
        }
    });
</script>
{% endblock %}
```

**`hr_masters/templates/hr_masters/workingday/_workingday_table.html`**
This partial template contains the actual DataTables structure. It's designed to be loaded dynamically via HTMX.

```html
<div class="overflow-x-auto">
    <table id="workingDayTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Days</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in working_days %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.fin_year.fin_year }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.month.month_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ obj.days|floatformat:"-2" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-center">
                    <button 
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-yellow-500 hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 mr-2"
                        hx-get="{% url 'workingday_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        hx-get="{% url 'workingday_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">No working days records found. Click "Add New Working Day" to create one.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script. This runs when the partial is loaded/swapped by HTMX. -->
<script>
    // DataTables should be initialized once the table element is available in the DOM.
    // The htmx:afterSwap event listener in list.html handles this to prevent re-initialization issues.
    // This script block is primarily for clarity on where the DataTables init happens.
</script>
```

**`hr_masters/templates/hr_masters/workingday/_workingday_form.html`**
This partial template renders the form for both creating and updating records.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Working Day</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="{% if field.field.widget.input_type == 'checkbox' %}flex items-center{% else %}relative{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs italic mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg" role="alert">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click 
                   remove .scale-100 .opacity-100 from #modalContent 
                   add .scale-95 .opacity-0 to #modalContent 
                   then wait 300ms 
                   remove .is-active from #modal 
                   add .hidden to #modal 
                   then set #modalContent.innerHTML to ''">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save
            </button>
            <div id="loadingIndicator" class="htmx-indicator ml-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`hr_masters/templates/hr_masters/workingday/_workingday_confirm_delete.html`**
This partial template provides a simple confirmation for deleting a record.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the working day entry for <strong>{{ object.month.month_name }} ({{ object.fin_year.fin_year }})</strong> with <strong>{{ object.days }}</strong> days?</p>
    
    <form hx-post="{% url 'workingday_delete' object.pk %}" hx-swap="none" hx-indicator="#deleteLoadingIndicator">
        {% csrf_token %}
        <div class="flex justify-end space-x-4 mt-6">
            <button 
                type="button" 
                class="px-5 py-2.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click 
                   remove .scale-100 .opacity-100 from #modalContent 
                   add .scale-95 .opacity-0 to #modalContent 
                   then wait 300ms 
                   remove .is-active from #modal 
                   add .hidden to #modal 
                   then set #modalContent.innerHTML to ''">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Delete
            </button>
            <div id="deleteLoadingIndicator" class="htmx-indicator ml-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </div>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`hr_masters/urls.py`)

**Task:** Define URL patterns for accessing the views.

**Instructions:**
URLs are set up for listing, creating, updating, and deleting `WorkingDay` records, including a specific URL for the HTMX-loaded table partial.

```python
from django.urls import path
from .views import (
    WorkingDayListView, 
    WorkingDayTablePartialView,
    WorkingDayCreateView, 
    WorkingDayUpdateView, 
    WorkingDayDeleteView
)

urlpatterns = [
    # Main page for working days list
    path('workingdays/', WorkingDayListView.as_view(), name='workingday_list'),
    
    # HTMX endpoint for the table partial
    path('workingdays/table/', WorkingDayTablePartialView.as_view(), name='workingday_table'),
    
    # HTMX endpoint for adding a new working day (modal form)
    path('workingdays/add/', WorkingDayCreateView.as_view(), name='workingday_add'),
    
    # HTMX endpoint for editing an existing working day (modal form)
    path('workingdays/edit/<int:pk>/', WorkingDayUpdateView.as_view(), name='workingday_edit'),
    
    # HTMX endpoint for deleting a working day (modal confirmation)
    path('workingdays/delete/<int:pk>/', WorkingDayDeleteView.as_view(), name='workingday_delete'),
]

```

#### 4.6 Tests (`hr_masters/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views, ensuring high code coverage.

**Instructions:**
Tests cover model creation, field attributes, and all CRUD operations through the views, including specific checks for HTMX responses.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from django.contrib.messages import get_messages
from unittest.mock import patch # For mocking session data

from .models import WorkingDay, FinancialYear, Month

class WorkingDayModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2022 = FinancialYear.objects.create(fin_year_id=2022, fin_year='2022-2023')
        cls.month_jan = Month.objects.create(id=1, month_name='January')
        cls.month_feb = Month.objects.create(id=2, month_name='February')

        cls.working_day_1 = WorkingDay.objects.create(
            id=101,
            comp_id=1,
            fin_year=cls.fin_year_2023,
            month=cls.month_jan,
            days=22.5
        )
        cls.working_day_2 = WorkingDay.objects.create(
            id=102,
            comp_id=1,
            fin_year=cls.fin_year_2022,
            month=cls.month_feb,
            days=20.0
        )
        cls.working_day_3 = WorkingDay.objects.create(
            id=103,
            comp_id=2, # Different company
            fin_year=cls.fin_year_2023,
            month=cls.month_jan,
            days=21.0
        )
  
    def test_working_day_creation(self):
        obj = WorkingDay.objects.get(id=101)
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.fin_year, self.fin_year_2023)
        self.assertEqual(obj.month, self.month_jan)
        self.assertEqual(obj.days, 22.5)
        
    def test_unique_together_constraint(self):
        # Attempt to create a duplicate working day for the same company, fin_year, month
        with self.assertRaises(IntegrityError):
            WorkingDay.objects.create(
                id=104, # Needs a unique ID for DB, but the constraint is on the other fields
                comp_id=1,
                fin_year=self.fin_year_2023,
                month=self.month_jan,
                days=25.0
            )

    def test_str_representation(self):
        self.assertEqual(str(self.working_day_1), "January - 2023-2024 (22.5 days)")

    def test_days_positive_validation(self):
        # Test clean method for positive days
        wd = WorkingDay(
            comp_id=1,
            fin_year=self.fin_year_2023,
            month=self.month_feb,
            days=-5.0
        )
        with self.assertRaisesMessage(ValidationError, 'Days must be a positive number.'):
            wd.clean()
        
        wd_valid = WorkingDay(
            comp_id=1,
            fin_year=self.fin_year_2023,
            month=self.month_feb,
            days=15.0
        )
        wd_valid.clean() # Should not raise error

    def test_model_verbose_names(self):
        self.assertEqual(self.working_day_1._meta.verbose_name, 'Working Day')
        self.assertEqual(self.working_day_1._meta.verbose_name_plural, 'Working Days')
        self.assertEqual(FinancialYear._meta.verbose_name, 'Financial Year')
        self.assertEqual(Month._meta.verbose_name_plural, 'Months')


class WorkingDayViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2022 = FinancialYear.objects.create(fin_year_id=2022, fin_year='2022-2023')
        cls.month_jan = Month.objects.create(id=1, month_name='January')
        cls.month_feb = Month.objects.create(id=2, month_name='February')

        cls.working_day_current_year_jan = WorkingDay.objects.create(
            id=1, comp_id=100, fin_year=cls.fin_year_2023, month=cls.month_jan, days=22.0
        )
        cls.working_day_previous_year_feb = WorkingDay.objects.create(
            id=2, comp_id=100, fin_year=cls.fin_year_2022, month=cls.month_feb, days=20.0
        )
        cls.working_day_other_comp = WorkingDay.objects.create(
            id=3, comp_id=200, fin_year=cls.fin_year_2023, month=cls.month_jan, days=21.0
        )
    
    def setUp(self):
        self.client = Client()
        # Set session variables as they are crucial for filtering
        session = self.client.session
        session['compid'] = 100
        session['finyear'] = 2023 # Current financial year ID
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('workingday_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/workingday/list.html')
        # Check that the main list view doesn't load data directly (it's done via HTMX)
        self.assertContains(response, 'id="workingDayTable-container"')

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('workingday_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_table.html')
        self.assertTrue('working_days' in response.context)
        # Check that only relevant data for current company/fin year is in queryset
        self.assertEqual(len(response.context['working_days']), 2) # jan (2023), feb (2022) for comp 100
        self.assertIn(self.working_day_current_year_jan, response.context['working_days'])
        self.assertIn(self.working_day_previous_year_feb, response.context['working_days'])
        self.assertNotIn(self.working_day_other_comp, response.context['working_days']) # Other company

    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('workingday_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_form.html')
        self.assertTrue('form' in response.context)
        # Check that financial year queryset is filtered based on session['finyear']
        self.assertIn(self.fin_year_2023, response.context['form'].fields['fin_year'].queryset)
        self.assertIn(self.fin_year_2022, response.context['form'].fields['fin_year'].queryset)


    def test_create_view_post_success_htmx(self):
        data = {
            'fin_year': self.fin_year_2023.fin_year_id,
            'month': self.month_feb.id,
            'days': 23.5,
        }
        response = self.client.post(reverse('workingday_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshWorkingDayList')
        # Verify object was created with session comp_id
        new_working_day = WorkingDay.objects.filter(
            comp_id=self.client.session['compid'],
            fin_year=self.fin_year_2023,
            month=self.month_feb
        ).first()
        self.assertIsNotNone(new_working_day)
        self.assertEqual(new_working_day.days, 23.5)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Working Day added successfully.')
        
    def test_create_view_post_invalid_data_htmx(self):
        # Test invalid 'days' value
        data = {
            'fin_year': self.fin_year_2023.fin_year_id,
            'month': self.month_feb.id,
            'days': -10.0, # Invalid: negative days
        }
        response = self.client.post(reverse('workingday_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_form.html')
        self.assertContains(response, 'Days must be a positive number.')
        self.assertFalse(WorkingDay.objects.filter(days=-10.0).exists())

    def test_create_view_post_duplicate_entry_htmx(self):
        # Attempt to create a duplicate of working_day_current_year_jan
        data = {
            'fin_year': self.fin_year_2023.fin_year_id,
            'month': self.month_jan.id,
            'days': 25.0,
        }
        response = self.client.post(reverse('workingday_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_form.html')
        self.assertContains(response, 'This month already has working days defined for this financial year.')
        # Ensure no new object was created
        self.assertEqual(WorkingDay.objects.filter(comp_id=100, fin_year=self.fin_year_2023, month=self.month_jan).count(), 1)


    def test_update_view_get_htmx(self):
        obj = self.working_day_current_year_jan
        response = self.client.get(reverse('workingday_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        # Check that fin_year and month fields are disabled on edit
        self.assertContains(response, 'name="fin_year" disabled')
        self.assertContains(response, 'name="month" disabled')

    def test_update_view_post_success_htmx(self):
        obj = self.working_day_current_year_jan
        data = {
            'fin_year': obj.fin_year.fin_year_id, # Value required by form, but should be ignored if disabled
            'month': obj.month.id, # Value required by form, but should be ignored if disabled
            'days': 24.0, # New value
        }
        response = self.client.post(reverse('workingday_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshWorkingDayList')
        obj.refresh_from_db()
        self.assertEqual(obj.days, 24.0)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Working Day updated successfully.')

    def test_update_view_post_invalid_data_htmx(self):
        obj = self.working_day_current_year_jan
        data = {
            'fin_year': obj.fin_year.fin_year_id,
            'month': obj.month.id,
            'days': 0, # Invalid: non-positive days
        }
        response = self.client.post(reverse('workingday_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_form.html')
        self.assertContains(response, 'Days must be a positive number.')
        obj.refresh_from_db()
        self.assertNotEqual(obj.days, 0) # Ensure it wasn't updated to invalid value

    def test_delete_view_get_htmx(self):
        obj = self.working_day_current_year_jan
        response = self.client.get(reverse('workingday_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/workingday/_workingday_confirm_delete.html')
        self.assertEqual(response.context['object'], obj)
        self.assertContains(response, f'Are you sure you want to delete the working day entry for <strong>{obj.month.month_name} ({obj.fin_year.fin_year})</strong>')

    def test_delete_view_post_success_htmx(self):
        obj_to_delete = WorkingDay.objects.create(
            id=999, comp_id=100, fin_year=self.fin_year_2023, month=self.month_feb, days=18.0
        ) # Create a new one to delete
        
        response = self.client.post(reverse('workingday_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshWorkingDayList')
        self.assertFalse(WorkingDay.objects.filter(id=obj_to_delete.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Working Day deleted successfully.')

    def test_delete_view_non_existent_obj(self):
        response = self.client.post(reverse('workingday_delete', args=[99999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Object not found

    def test_views_filter_by_session_company(self):
        # Create an object for a different company
        WorkingDay.objects.create(
            id=4, comp_id=300, fin_year=self.fin_year_2023, month=self.month_jan, days=25.0
        )
        # Attempt to access it with current session compid=100
        response = self.client.get(reverse('workingday_edit', args=[4]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Should not be found for this company
        
        response = self.client.get(reverse('workingday_delete', args=[4]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Should not be found for this company

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already incorporate HTMX for all dynamic operations:

-   **HTMX for CRUD:**
    -   `Add New Working Day` button: `hx-get` to `workingday_add` URL, targeting `#modalContent`.
    -   `Edit` and `Delete` buttons in table rows: `hx-get` to `workingday_edit` and `workingday_delete` URLs respectively, targeting `#modalContent`.
    -   All forms (`_workingday_form.html`, `_workingday_confirm_delete.html`): `hx-post` to `request.path` (the current modal URL) with `hx-swap="none"`. This tells HTMX not to replace the content of the form itself on success.
    -   Successful form submissions (Create/Update/Delete): Views return `HttpResponse(status=204)` and `HX-Trigger: refreshWorkingDayList`. This informs the frontend that the operation was successful and triggers a custom event.
    -   Table Container: `hx-trigger="load, refreshWorkingDayList from:body"` on `#workingDayTable-container`. This ensures the table is loaded on page load and refreshed whenever `refreshWorkingDayList` event is triggered (after any CRUD operation), automatically re-rendering the table with updated data.
    -   `htmx-indicator`: Added to forms to show a loading spinner during submission.

-   **Alpine.js for Modals:**
    -   The `list.html` includes an Alpine.js-like attribute `_` (`on click add .is-active to #modal`) on buttons that open the modal. This is a compact way to manage class toggles for modal visibility. Similarly, closing actions remove these classes.
    -   The `htmx:afterSwap` and `htmx:beforeSwap` JavaScript listeners handle the modal's `hidden` class and `is-active` state transitions, ensuring a smooth fade-in/fade-out effect.

-   **DataTables Integration:**
    -   The `_workingday_table.html` partial includes a `<script>` block that initializes DataTables on the `workingDayTable` ID.
    -   The `htmx:afterSwap` event in `list.html` ensures that DataTables is correctly initialized *after* the table content has been loaded into the DOM by HTMX. `destroy: true` is crucial to prevent re-initialization errors if the table is swapped multiple times.

This setup ensures that all interactions are dynamic, efficient, and provide a modern user experience without the complexity of a full JavaScript framework or full-page reloads.

---

### Final Notes

-   **Placeholder Replacement:** All placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[FIELD_TYPE]`, etc., have been replaced with the concrete values derived from the ASP.NET code analysis (e.g., `hr_masters`, `WorkingDay`, `ForeignKey`).
-   **DRY Templates:** The use of partial templates (`_workingday_table.html`, `_workingday_form.html`, `_workingday_confirm_delete.html`) promotes reusability and keeps the main template clean.
-   **Fat Model, Thin View:** Business logic, such as the unique constraint and custom validation for `days`, has been moved to the `WorkingDay` model's `clean()` method and the `WorkingDayForm`'s `clean()` method. Views remain concise, primarily handling HTTP requests and responses.
-   **Tests:** Comprehensive tests are provided for both models and views, ensuring robustness and maintainability.
-   **Session Data:** The `CompId` and `FinYearId` from the ASP.NET `Session` are accessed in Django views via `self.request.session`, ensuring data filtering and insertion respect the user's context.

This structured and automated approach facilitates a smooth transition from your legacy ASP.NET application to a modern, scalable, and maintainable Django solution.