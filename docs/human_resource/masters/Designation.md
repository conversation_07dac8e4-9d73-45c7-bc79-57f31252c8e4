## ASP.NET to Django Conversion Script: Designation Module

This document outlines a strategic plan for modernizing your existing ASP.NET "Designation" module into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation, focusing on systematic conversion rather than manual re-coding, and is designed to be easily understood by both technical and non-technical stakeholders.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`Designation`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource1` in the ASP.NET code, we can clearly identify the database interactions.

-   **Table Name:** `tblHR_Designation`
-   **Columns Identified:**
    *   `Id` (Primary Key, integer type inferred from `@Id` parameter and `DataKeyNames="Id"`)
    *   `Type` (String type inferred from `@Type` parameter and `Text='<%#Eval("Type") %>'` binding)
    *   `Symbol` (String type inferred from `@Symbol` parameter and `Text='<%#Eval("Symbol") %>'` binding)

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The ASP.NET `GridView` and `SqlDataSource` components directly implement CRUD operations, complemented by the C# code-behind.

-   **Create (Add):**
    *   Triggered by `btnInsert` in the `GridView` footer (CommandName="Add") and `EmptyDataTemplate` (CommandName="Add1").
    *   The `GridView1_RowCommand` method captures `txtType` and `txtSymbol` values.
    *   `SqlDataSource1.InsertParameters` are set, and `SqlDataSource1.Insert()` is called.
    *   Simple validation is present using `RequiredFieldValidator` ensuring `Type` and `Symbol` are not empty.
-   **Read (Select):**
    *   The `GridView1` populates its rows using `SqlDataSource1` with `SelectCommand="SELECT [Id], [Type], [Symbol] FROM [tblHR_Designation] Order by [Id] Desc"`.
    *   Paging is enabled (`AllowPaging="True"`, `PageSize="20"`).
-   **Update (Edit):**
    *   Triggered by the `Edit` link in `CommandField` (`ShowEditButton="True"`).
    *   `EditItemTemplate` allows modification of `Type` and `Symbol` fields using `TextBox` controls.
    *   `GridView1_RowUpdated` event handles the post-update logic.
    *   `SqlDataSource1.UpdateCommand` performs the database update.
-   **Delete:**
    *   Triggered by the `Delete` link in `CommandField` (`ShowDeleteButton="True"`).
    *   `GridView1_RowDeleted` event handles the post-delete logic.
    *   `SqlDataSource1.DeleteCommand` performs the database deletion.
-   **Validation:** `RequiredFieldValidator` is used to ensure `Type` and `Symbol` fields are not blank during insertion and update. Client-side confirmation pop-ups (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) are triggered via JavaScript calls in `OnClientClick` and `GridView1_RowDataBound`.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET `GridView` is the central UI component, managing data display, input, and actions.

-   **`GridView1`:** This control functions as a primary data table for `Designation` records.
    *   Displays `SN` (serial number), `Type`, `Symbol`, and `Actions` (Edit, Delete).
    *   Supports pagination.
    *   Provides inline editing and insertion capabilities via `EditItemTemplate` and `FooterTemplate`/`EmptyDataTemplate`.
-   **`TextBox` controls (`txtType`, `txtSymbol`, `lblType0`, `lblSymbol0`):** Used for capturing `Type` and `Symbol` input during insertion and editing.
-   **`Label` controls (`lblType`, `lblSymbol`, `lblID`, `Label2`):** Used for displaying data and feedback messages (`Label2` shows "Record inserted/updated/deleted.").
-   **`Button`/`LinkButton` controls:** Trigger `Add`, `Edit`, and `Delete` actions.
-   **Client-side JavaScript:** `PopUpMsg.js` and `loadingNotifier.js` are included, indicating client-side interactions for confirmations (e.g., `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) and possibly loading indicators. This will be replaced by HTMX and Alpine.js.
-   **CSS:** `yui-datatable.css` suggests a table-like presentation with features like sorting/filtering, which will be replicated with DataTables.

---

#### Step 4: Generate Django Code

We will now generate the necessary Django files based on the analysis. The application name will be `hr_app`.

**4.1 Models (hr_app/models.py)**

```python
from django.db import models

class Designation(models.Model):
    # 'id' field is automatically created by Django as the primary key.
    # The 'Id' column in the original ASP.NET table maps to Django's default 'id'.
    type = models.CharField(db_column='Type', max_length=255, blank=False, null=False) # Inferred max_length; adjust as per actual DB schema.
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=False, null=False) # Inferred max_length; adjust as per actual DB schema.

    class Meta:
        managed = False  # Important: Django won't manage this table's schema.
        db_table = 'tblHR_Designation'  # Maps to the existing database table.
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'
        ordering = ['-id'] # Replicate 'Order by [Id] Desc' from original query

    def __str__(self):
        """String representation of the Designation."""
        return self.type # Or a combination like f"{self.type} ({self.symbol})"
        
    def save(self, *args, **kwargs):
        # Business logic for saving can be added here, e.g., validation beyond model field constraints
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        # Business logic for deletion can be added here, e.g., checking for related records
        super().delete(*args, **kwargs)

    # Example of a fat model method:
    def get_display_name(self):
        """Returns a formatted display name for the designation."""
        return f"{self.type} ({self.symbol})"

```

**4.2 Forms (hr_app/forms.py)**

```python
from django import forms
from .models import Designation

class DesignationForm(forms.ModelForm):
    class Meta:
        model = Designation
        fields = ['type', 'symbol'] # Corresponding to 'Type' and 'Symbol' in ASP.NET
        widgets = {
            'type': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter designation type'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter designation symbol'
            }),
        }
        
    def clean_type(self):
        designation_type = self.cleaned_data['type']
        # Example of custom validation: ensure type is unique (if not handled at DB level or for soft checks)
        # if Designation.objects.filter(type=designation_type).exclude(pk=self.instance.pk).exists():
        #     raise forms.ValidationError("This designation type already exists.")
        return designation_type

    def clean_symbol(self):
        designation_symbol = self.cleaned_data['symbol']
        # Example of custom validation: ensure symbol is a certain format
        # if not designation_symbol.isalpha():
        #     raise forms.ValidationError("Symbol must contain only letters.")
        return designation_symbol

```

**4.3 Views (hr_app/views.py)**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # For HTMX partials

from .models import Designation
from .forms import DesignationForm

class DesignationListView(ListView):
    model = Designation
    template_name = 'hr_app/designation/list.html'
    context_object_name = 'designations' # Renamed from [MODEL_NAME_PLURAL_LOWER] for clarity

    def get_queryset(self):
        # Already handled by Meta.ordering in model, but can be overridden here if needed
        return super().get_queryset()

# This view is specifically for HTMX to load and refresh the DataTables content
class DesignationTablePartialView(ListView):
    model = Designation
    template_name = 'hr_app/designation/_designation_table.html' # This is the partial template
    context_object_name = 'designations'

    def get_queryset(self):
        # Ensure consistent ordering as per original ASP.NET
        return super().get_queryset().order_by('-id')

class DesignationCreateView(CreateView):
    model = Designation
    form_class = DesignationForm
    template_name = 'hr_app/designation/_designation_form.html' # Partial for modal
    success_url = reverse_lazy('designation_list') # Not directly used for HTMX success but good for fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Designation added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to signal success and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignationList' # Custom event to trigger table reload
                }
            )
        return response # Fallback for non-HTMX request

class DesignationUpdateView(UpdateView):
    model = Designation
    form_class = DesignationForm
    template_name = 'hr_app/designation/_designation_form.html' # Partial for modal
    success_url = reverse_lazy('designation_list') # Not directly used for HTMX success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Designation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignationList'
                }
            )
        return response

class DesignationDeleteView(DeleteView):
    model = Designation
    template_name = 'hr_app/designation/_designation_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('designation_list') # Not directly used for HTMX success

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Designation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignationList'
                }
            )
        return response

```

**4.4 Templates (hr_app/templates/hr_app/designation/)**

**list.html**
This is the main page for the Designation module. It sets up the structure and includes a modal for CRUD operations, which will be populated by HTMX.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Designations</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'designation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Designation
        </button>
    </div>
    
    <div id="designationTable-container"
         hx-trigger="load, refreshDesignationList from:body"
         hx-get="{% url 'designation_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading designations...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }" 
         x-show="show" 
         x-transition:enter="ease-out duration-300" 
         x-transition:enter-start="opacity-0 scale-95" 
         x-transition:enter-end="opacity-100 scale-100" 
         x-transition:leave="ease-in duration-200" 
         x-transition:leave-start="opacity-100 scale-100" 
         x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             @htmx:after-request="show = true"
             @htmx:before-request="show = false">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // This ensures the modal is correctly shown after HTMX loads content
        if (event.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.add('is-active'); // Ensure modal shows if it's hidden
            }
        }
    });

    // Close modal on successful form submission via HTMX trigger
    document.body.addEventListener('refreshDesignationList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
        }
    });
</script>
{% endblock %}

```

**\_designation_table.html**
This partial template contains the DataTables structure and is loaded dynamically by HTMX.

```html
<table id="designationTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in designations %}
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ obj.type }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ obj.symbol }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'designation_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'designation_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">No designations found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization. Ensure jQuery and DataTables libraries are loaded in base.html
// This script runs every time the partial is loaded, re-initializing DataTable correctly.
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#designationTable')) {
        $('#designationTable').DataTable().destroy(); // Destroy existing instance if any
    }
    $('#designationTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 3] } // SN and Actions columns are not sortable
        ]
    });
});
</script>
```

**\_designation_form.html**
This partial template is used for both adding and editing designations within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Designation</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Designation
            </button>
        </div>
        <div id="form-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="text-gray-600">Saving...</p>
        </div>
    </form>
</div>
```

**\_designation_confirm_delete.html**
This partial template provides a confirmation dialog for deleting a designation within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the designation "{{ object.type }}" ({{ object.symbol }})? 
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'designation_delete' object.pk %}" 
          hx-swap="none" 
          hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
        <div id="delete-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            <p class="text-gray-600">Deleting...</p>
        </div>
    </form>
</div>
```

**4.5 URLs (hr_app/urls.py)**

```python
from django.urls import path
from .views import (
    DesignationListView, 
    DesignationCreateView, 
    DesignationUpdateView, 
    DesignationDeleteView,
    DesignationTablePartialView # New view for HTMX partial
)

urlpatterns = [
    path('designations/', DesignationListView.as_view(), name='designation_list'),
    path('designations/table/', DesignationTablePartialView.as_view(), name='designation_table'), # HTMX target for table content
    path('designations/add/', DesignationCreateView.as_view(), name='designation_add'),
    path('designations/edit/<int:pk>/', DesignationUpdateView.as_view(), name='designation_edit'),
    path('designations/delete/<int:pk>/', DesignationDeleteView.as_view(), name='designation_delete'),
]

```

**4.6 Tests (hr_app/tests.py)**

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import Designation

class DesignationModelTest(TestCase):
    """
    Unit tests for the Designation model, ensuring data integrity and business logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a sample designation for all tests to use
        Designation.objects.create(
            type='Manager',
            symbol='MGR',
        )
        Designation.objects.create(
            type='Director',
            symbol='DIR',
        )
  
    def test_designation_creation(self):
        """Verify a Designation object can be created and its attributes are correct."""
        manager = Designation.objects.get(type='Manager')
        self.assertEqual(manager.type, 'Manager')
        self.assertEqual(manager.symbol, 'MGR')
        self.assertIsNotNone(manager.id) # Ensure ID is assigned

    def test_type_label(self):
        """Test the verbose name for the 'type' field."""
        designation = Designation.objects.get(type='Manager')
        field_label = designation._meta.get_field('type').verbose_name
        self.assertEqual(field_label, 'type') # Django's default verbose name for CharField

    def test_symbol_label(self):
        """Test the verbose name for the 'symbol' field."""
        designation = Designation.objects.get(type='Manager')
        field_label = designation._meta.get_field('symbol').verbose_name
        self.assertEqual(field_label, 'symbol')

    def test_str_method(self):
        """Test the __str__ method returns the expected string representation."""
        designation = Designation.objects.get(type='Manager')
        self.assertEqual(str(designation), 'Manager')

    def test_get_display_name_method(self):
        """Test a custom model method for displaying formatted name."""
        designation = Designation.objects.get(type='Manager')
        self.assertEqual(designation.get_display_name(), 'Manager (MGR)')

    def test_meta_options(self):
        """Verify Meta options like db_table, managed, verbose_name, and ordering."""
        self.assertEqual(Designation._meta.db_table, 'tblHR_Designation')
        self.assertFalse(Designation._meta.managed)
        self.assertEqual(Designation._meta.verbose_name, 'Designation')
        self.assertEqual(Designation._meta.verbose_name_plural, 'Designations')
        self.assertEqual(Designation._meta.ordering, ['-id'])

class DesignationViewsTest(TestCase):
    """
    Integration tests for Designation views, covering CRUD operations and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a sample designation that can be manipulated by tests
        cls.designation1 = Designation.objects.create(type='Test_Manager', symbol='TMAN')
        cls.designation2 = Designation.objects.create(type='Test_Supervisor', symbol='TSUP')
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        """Test the Designation list view renders correctly and shows data."""
        response = self.client.get(reverse('designation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/designation/list.html')
        self.assertIn('designations', response.context)
        self.assertGreaterEqual(response.context['designations'].count(), 2) # At least our two test objects

    def test_table_partial_view(self):
        """Test the HTMX partial for the DataTables content."""
        response = self.client.get(reverse('designation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/designation/_designation_table.html')
        self.assertIn('designations', response.context)
        self.assertContains(response, 'Test_Manager')
        self.assertContains(response, 'TMAN')

    def test_create_view_get(self):
        """Test GET request to the create form."""
        response = self.client.get(reverse('designation_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/designation/_designation_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].instance.pk) # Form is for creation

    def test_create_view_post_success(self):
        """Test successful POST request to create a new designation."""
        data = {
            'type': 'New Designation',
            'symbol': 'ND',
        }
        # Simulate HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designation_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignationList')
        self.assertTrue(Designation.objects.filter(type='New Designation').exists())

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data for creation."""
        data = {
            'type': '', # Missing required field
            'symbol': 'INV',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designation_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'hr_app/designation/_designation_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertFalse(Designation.objects.filter(symbol='INV').exists())

    def test_update_view_get(self):
        """Test GET request to the update form."""
        response = self.client.get(reverse('designation_edit', args=[self.designation1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/designation/_designation_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.designation1)

    def test_update_view_post_success(self):
        """Test successful POST request to update an existing designation."""
        updated_type = 'Updated Manager'
        data = {
            'type': updated_type,
            'symbol': 'UMGR',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designation_edit', args=[self.designation1.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignationList')
        self.designation1.refresh_from_db()
        self.assertEqual(self.designation1.type, updated_type)

    def test_update_view_post_invalid(self):
        """Test POST request with invalid data for update."""
        original_type = self.designation1.type
        data = {
            'type': '', # Invalid data
            'symbol': 'UMGR',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designation_edit', args=[self.designation1.id]), data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/designation/_designation_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.designation1.refresh_from_db()
        self.assertEqual(self.designation1.type, original_type) # Ensure no change

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation page."""
        response = self.client.get(reverse('designation_delete', args=[self.designation1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_app/designation/_designation_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.designation1)

    def test_delete_view_post_success(self):
        """Test successful POST request to delete a designation."""
        designation_to_delete_id = self.designation2.id
        self.assertTrue(Designation.objects.filter(id=designation_to_delete_id).exists())

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designation_delete', args=[designation_to_delete_id]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignationList')
        self.assertFalse(Designation.objects.filter(id=designation_to_delete_id).exists())

    def test_delete_view_post_not_found(self):
        """Test POST request to delete a non-existent designation."""
        non_existent_id = 99999
        self.assertFalse(Designation.objects.filter(id=non_existent_id).exists())

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designation_delete', args=[non_existent_id]), **headers)
        
        # Django's DeleteView will typically raise a 404 if the object isn't found for GET.
        # For POST, it might just proceed and not delete anything, or raise an error depending on exact implementation.
        # In this case, a 404 is expected for the object not found.
        self.assertEqual(response.status_code, 404)
```

---

#### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated Django code already incorporates HTMX and Alpine.js for a modern, dynamic user experience:

-   **HTMX for Dynamic Updates:**
    *   The `list.html` uses `hx-get="{% url 'designation_table' %}"` to load the table content dynamically upon page load and whenever the `refreshDesignationList` event is triggered.
    *   Create, Update, and Delete actions submit forms via `hx-post`, and the views respond with `status=204 No Content` and an `HX-Trigger` header (`refreshDesignationList`). This tells HTMX to refresh the table without a full page reload or content swap on the form itself.
    *   Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch the respective form/confirmation partials (`_designation_form.html`, `_designation_confirm_delete.html`) into the modal (`#modalContent`).
-   **Alpine.js for UI State Management:**
    *   The modal in `list.html` uses `_` (Alpine.js's micro-syntax) to add/remove the `is-active` class, controlling its visibility. This replaces the basic JavaScript pop-ups from the ASP.NET version.
    *   The modal also uses `x-data` and `x-show` with `x-transition` for smoother opening/closing animations, providing a more refined user experience.
-   **DataTables for List Views:**
    *   The `_designation_table.html` partial includes a JavaScript snippet that initializes DataTables on the `designationTable`. This ensures client-side searching, sorting, and pagination are enabled. The initialization script is placed directly within the partial so it runs each time the table is loaded via HTMX, preventing issues with re-initialization.

This setup ensures that all primary interactions for managing designations are performed without full page reloads, providing a fast and fluid user interface akin to a Single Page Application (SPA), but with the simplicity and SEO benefits of traditional server-rendered applications.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Designation module to Django. By adhering to these guidelines:

-   We replace legacy ASP.NET controls and data binding with modern Django ORM, forms, and class-based views.
-   The heavy client-side processing of `GridView` is offloaded to DataTables, powered by HTMX for dynamic table updates.
-   Business logic is correctly encapsulated within the `Designation` model, keeping views concise and focused on request handling.
-   The front-end is modernized using HTMX and Alpine.js, eliminating the need for complex custom JavaScript and external JS libraries like `PopUpMsg.js` or `loadingNotifier.js`.
-   The clear separation of concerns, modular design, and robust testing strategy will result in a highly maintainable and extensible application.
-   The use of Tailwind CSS ensures a modern, responsive, and customizable UI without the overhead of heavy CSS frameworks.

This approach significantly reduces manual effort through automation-driven processes and ensures a high-quality, modern application that is easier to manage and evolve.