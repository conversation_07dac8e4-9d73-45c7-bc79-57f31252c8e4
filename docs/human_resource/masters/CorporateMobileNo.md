The following Django modernization plan will guide the transition of your ASP.NET Corporate Mobile Number management module to a modern Django application. This plan prioritizes automation, clean architecture, and enhanced user experience using HTMX and Alpine.js.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- The `SqlDataSource1` component directly specifies the table and columns for CRUD operations.

**Extracted Information:**

*   **Table Name:** `tblHR_CoporateMobileNo`
*   **Columns:**
    *   `Id` (Primary Key, Integer)
    *   `MobileNo` (String)
    *   `LimitAmt` (Double)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET `SqlDataSource1` and `GridView1` define the following:

*   **Create (Insert):** Triggered by `btnInsert` in the `GridView` footer or `EmptyDataTemplate`. It uses `SqlDataSource1.InsertCommand` to add `MobileNo` and `LimitAmt` to `tblHR_CoporateMobileNo`.
*   **Read (Select):** The `GridView1` is populated using `SqlDataSource1.SelectCommand`, which retrieves `Id`, `MobileNo`, and `LimitAmt` from `tblHR_CoporateMobileNo`.
*   **Update:** Triggered by the `Edit` command field in the `GridView`. It uses `SqlDataSource1.UpdateCommand` to modify `MobileNo` and `LimitAmt` for a given `Id`.
*   **Delete:** Triggered by the `Delete` command field in the `GridView`. It uses `SqlDataSource1.DeleteCommand` to remove a record based on `Id`.
*   **Validation Logic:** `RequiredFieldValidator` is used for `MobileNo` and `LimitAmt` fields, ensuring they are not empty upon submission.
*   **Messaging:** A `Label2` displays simple success messages for deleted, updated, or inserted records.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **GridView1:** This is the primary data display component. It functions as a data table with built-in paging, editing, and deletion capabilities. This will be replaced by a modern HTML `<table>` combined with the DataTables.js library for enhanced client-side features like search, sorting, and pagination.
*   **TextBox Controls (`txtMobileNo`, `txtLimitAmt`, `lblMobileNo0`, `lblLimitAmt0`):** Used for user input for `MobileNo` and `LimitAmt` during insertion and editing. These will map directly to Django form fields rendered as text inputs with appropriate Tailwind CSS classes.
*   **Button/LinkButton Controls (`btnInsert`, `CommandField` buttons):** These trigger actions like adding, editing, and deleting records. In Django, these will be HTML `<button>` elements utilizing HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`) to perform dynamic operations without full page reloads.
*   **Label2:** Used for displaying user feedback messages. This will be replaced by Django's built-in messages framework, displayed dynamically via HTMX.
*   **Client-side JavaScript (`PopUpMsg.js`, `confirmationAdd()`, etc.):** Used for client-side confirmation dialogs. This functionality will be handled by HTMX for loading modal content and Alpine.js for managing modal visibility and simple UI state.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The model `CorporateMobileNo` will directly map to the `tblHR_CoporateMobileNo` table. `Id` will be explicitly defined as the primary key since it's an existing database table.

```python
# hr_masters/models.py
from django.db import models

class CorporateMobileNo(models.Model):
    # 'Id' from ASP.NET becomes 'id' in Django, explicitly set as primary key
    id = models.IntegerField(db_column='Id', primary_key=True)
    # 'MobileNo' from ASP.NET becomes 'mobile_no'
    mobile_no = models.CharField(db_column='MobileNo', max_length=255) # Assuming reasonable max_length
    # 'LimitAmt' from ASP.NET becomes 'limit_amount'
    limit_amount = models.FloatField(db_column='LimitAmt')

    class Meta:
        managed = False  # Tells Django not to create/manage this table's schema
        db_table = 'tblHR_CoporateMobileNo' # Explicitly links to the existing table
        verbose_name = 'Corporate Mobile Number'
        verbose_name_plural = 'Corporate Mobile Numbers'

    def __str__(self):
        """Returns the mobile number for easy identification."""
        return self.mobile_no

    # Example of a fat model method (if business logic were more complex):
    # def check_limit_exceeded(self, amount_to_add):
    #     """
    #     Example: Checks if adding an amount would exceed the limit.
    #     This kind of business logic belongs in the model.
    #     """
    #     return (self.current_balance + amount_to_add) > self.limit_amount
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for the `CorporateMobileNo` model. It will include `mobile_no` and `limit_amount` fields with Tailwind CSS classes applied via widgets. Django's form validation will automatically handle the "required field" validation.

```python
# hr_masters/forms.py
from django import forms
from .models import CorporateMobileNo

class CorporateMobileNoForm(forms.ModelForm):
    class Meta:
        model = CorporateMobileNo
        fields = ['mobile_no', 'limit_amount']
        widgets = {
            'mobile_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., +1234567890'}),
            'limit_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., 500.00'}),
        }
        
    def clean_limit_amount(self):
        """
        Custom validation for limit_amount (e.g., ensure it's positive).
        This mirrors the implicit validation often seen in ASP.NET numeric fields.
        """
        limit_amount = self.cleaned_data['limit_amount']
        if limit_amount < 0:
            raise forms.ValidationError("Limit amount cannot be negative.")
        return limit_amount
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

A set of Class-Based Views (CBVs) will handle the CRUD operations. A dedicated partial view (`CorporateMobileNoTablePartialView`) is added to serve just the DataTable content, optimized for HTMX `hx-swap` operations. Messages will be used for user feedback, and `HX-Trigger` headers will signal the frontend to refresh the table after successful operations.

```python
# hr_masters/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import CorporateMobileNo
from .forms import CorporateMobileNoForm

class CorporateMobileNoListView(ListView):
    model = CorporateMobileNo
    template_name = 'hr_masters/corporatemobileno/list.html'
    context_object_name = 'corporatemobilenos'
    # No direct data here, it's loaded via HTMX in _corporate_mobile_no_table.html

class CorporateMobileNoTablePartialView(ListView):
    """
    Renders only the table content, used by HTMX to refresh the list.
    """
    model = CorporateMobileNo
    template_name = 'hr_masters/corporatemobileno/_corporate_mobile_no_table.html'
    context_object_name = 'corporatemobilenos'
    # Order by Id Desc to match original ASP.NET behavior
    ordering = ['-id']

class CorporateMobileNoCreateView(CreateView):
    model = CorporateMobileNo
    form_class = CorporateMobileNoForm
    template_name = 'hr_masters/corporatemobileno/_corporate_mobile_no_form.html' # This is a partial template for modal
    success_url = reverse_lazy('corporatemobileno_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Corporate Mobile Number added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to close modal and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCorporateMobileNoList'
                }
            )
        return response

    def form_invalid(self, form):
        # Render the form again with errors for HTMX modal
        return self.render_to_response(self.get_context_data(form=form))

class CorporateMobileNoUpdateView(UpdateView):
    model = CorporateMobileNo
    form_class = CorporateMobileNoForm
    template_name = 'hr_masters/corporatemobileno/_corporate_mobile_no_form.html' # This is a partial template for modal
    success_url = reverse_lazy('corporatemobileno_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Corporate Mobile Number updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCorporateMobileNoList'
                }
            )
        return response
    
    def form_invalid(self, form):
        # Render the form again with errors for HTMX modal
        return self.render_to_response(self.get_context_data(form=form))

class CorporateMobileNoDeleteView(DeleteView):
    model = CorporateMobileNo
    template_name = 'hr_masters/corporatemobileno/_corporate_mobile_no_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('corporatemobileno_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Corporate Mobile Number deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCorporateMobileNoList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are designed for HTMX and Alpine.js, ensuring dynamic interactions. The main list view acts as a container, while partial templates are loaded into a modal or directly swap content.

**1. `hr_masters/corporatemobileno/list.html` (Main List Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Corporate Mobile Numbers</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'corporatemobileno_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Number
        </button>
    </div>
    
    <!-- Table container loaded dynamically via HTMX -->
    <div id="corporatemobilenoTable-container"
         hx-trigger="load, refreshCorporateMobileNoList from:body"
         hx-get="{% url 'corporatemobileno_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Corporate Mobile Numbers...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-auto animate-fade-in-up">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- No Alpine.js specific initialization needed here, as modal state is handled by _/HTMX. -->
{% endblock %}
```

**2. `hr_masters/corporatemobileno/_corporate_mobile_no_table.html` (Partial for DataTable)**

```html
<table id="corporateMobileNoTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amt. Limit</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in corporatemobilenos %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.mobile_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.limit_amount|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'corporatemobileno_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'corporatemobileno_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-6 text-center text-gray-500">No corporate mobile numbers found. Click "Add New Number" to get started.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#corporateMobileNoTable')) {
            $('#corporateMobileNoTable').DataTable().destroy();
        }
        $('#corporateMobileNoTable').DataTable({
            "pageLength": 10, // Default page size, matches ASP.NET if 20 was default
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 3] } // Disable ordering for SN and Actions
            ]
        });
    });
</script>
```

**3. `hr_masters/corporatemobileno/_corporate_mobile_no_form.html` (Partial for Create/Update Forms)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Corporate Mobile Number</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-2 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <ul class="mt-2 text-sm text-red-600">
                    {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save
            </button>
        </div>
    </form>
</div>
```

**4. `hr_masters/corporatemobileno/_corporate_mobile_no_confirm_delete.html` (Partial for Delete Confirmation)**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="mb-6 text-gray-700">Are you sure you want to delete the Corporate Mobile Number <strong>"{{ object.mobile_no }}"</strong>?</p>
    
    <form hx-post="{% url 'corporatemobileno_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are set up for each view, including the HTMX partial view for the table content. These will be included in the project's main `urls.py`.

```python
# hr_masters/urls.py
from django.urls import path
from .views import (
    CorporateMobileNoListView, 
    CorporateMobileNoCreateView, 
    CorporateMobileNoUpdateView, 
    CorporateMobileNoDeleteView,
    CorporateMobileNoTablePartialView, # New view for HTMX table refresh
)

urlpatterns = [
    # Main list page
    path('corporate-mobile-no/', CorporateMobileNoListView.as_view(), name='corporatemobileno_list'),
    
    # HTMX endpoint for table content only
    path('corporate-mobile-no/table/', CorporateMobileNoTablePartialView.as_view(), name='corporatemobileno_table'),
    
    # CRUD operations using HTMX modals
    path('corporate-mobile-no/add/', CorporateMobileNoCreateView.as_view(), name='corporatemobileno_add'),
    path('corporate-mobile-no/edit/<int:pk>/', CorporateMobileNoUpdateView.as_view(), name='corporatemobileno_edit'),
    path('corporate-mobile-no/delete/<int:pk>/', CorporateMobileNoDeleteView.as_view(), name='corporatemobileno_delete'),
]

```
**Note:** In your main `project/urls.py`, you would include these as:
`path('hr/', include('hr_masters.urls')),`
This would make the full URL `hr/corporate-mobile-no/`.

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the model ensure data integrity, while integration tests for views verify correct behavior of CRUD operations, including HTMX specific responses.

```python
# hr_masters/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import CorporateMobileNo

class CorporateMobileNoModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test record for all tests in this class
        CorporateMobileNo.objects.create(
            id=1, # Assuming id is an existing PK and not AutoField, so provide it
            mobile_no='1234567890',
            limit_amount=1000.50
        )
        CorporateMobileNo.objects.create(
            id=2,
            mobile_no='9876543210',
            limit_amount=500.00
        )
  
    def test_corporate_mobile_no_creation(self):
        obj = CorporateMobileNo.objects.get(id=1)
        self.assertEqual(obj.mobile_no, '1234567890')
        self.assertEqual(obj.limit_amount, 1000.50)
        self.assertEqual(obj.id, 1)

    def test_mobile_no_label(self):
        obj = CorporateMobileNo.objects.get(id=1)
        field_label = obj._meta.get_field('mobile_no').verbose_name
        self.assertEqual(field_label, 'mobile no') # Django's default verbose_name for field. If you customize, change this.
        
    def test_str_method(self):
        obj = CorporateMobileNo.objects.get(id=1)
        self.assertEqual(str(obj), '1234567890')

class CorporateMobileNoViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        CorporateMobileNo.objects.create(
            id=10, # Use a different ID range to avoid conflict if test DB is not fully reset
            mobile_no='1112223333',
            limit_amount=1500.75
        )
        CorporateMobileNo.objects.create(
            id=11,
            mobile_no='4445556666',
            limit_amount=200.00
        )
    
    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('corporatemobileno_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/corporatemobileno/list.html')
        # We don't check for 'corporatemobilenos' in context as it's loaded by HTMX
        # instead, we check the table partial view
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('corporatemobileno_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/corporatemobileno/_corporate_mobile_no_table.html')
        self.assertContains(response, '1112223333') # Check if existing data is present
        self.assertContains(response, '4445556666')
        self.assertTrue('corporatemobilenos' in response.context)
        self.assertEqual(len(response.context['corporatemobilenos']), 2)

    def test_create_view_get(self):
        response = self.client.get(reverse('corporatemobileno_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/corporatemobileno/_corporate_mobile_no_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Corporate Mobile Number')

    def test_create_view_post_success(self):
        data = {
            'mobile_no': '5558889999',
            'limit_amount': 750.00,
        }
        response = self.client.post(reverse('corporatemobileno_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on non-HTMX
        self.assertTrue(CorporateMobileNo.objects.filter(mobile_no='5558889999').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Corporate Mobile Number added successfully.')
        
    def test_create_view_post_success_htmx(self):
        data = {
            'mobile_no': '5557778888',
            'limit_amount': 900.00,
        }
        response = self.client.post(reverse('corporatemobileno_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX should return 204 No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCorporateMobileNoList')
        self.assertTrue(CorporateMobileNo.objects.filter(mobile_no='5557778888').exists())
        messages = list(get_messages(self.client.get(reverse('corporatemobileno_list')).wsgi_request)) # Need to fetch messages on next request
        self.assertEqual(str(messages[0]), 'Corporate Mobile Number added successfully.')

    def test_create_view_post_invalid(self):
        data = {
            'mobile_no': '', # Missing required field
            'limit_amount': -100.00, # Invalid amount
        }
        response = self.client.post(reverse('corporatemobileno_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'hr_masters/corporatemobileno/_corporate_mobile_no_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Limit amount cannot be negative.')
        self.assertFalse(CorporateMobileNo.objects.filter(mobile_no='').exists())

    def test_update_view_get(self):
        obj = CorporateMobileNo.objects.get(id=10)
        response = self.client.get(reverse('corporatemobileno_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/corporatemobileno/_corporate_mobile_no_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Corporate Mobile Number')
        self.assertContains(response, 'value="1112223333"') # Check if existing data is pre-filled

    def test_update_view_post_success_htmx(self):
        obj = CorporateMobileNo.objects.get(id=10)
        data = {
            'mobile_no': '111222333X',
            'limit_amount': 1600.00,
        }
        response = self.client.post(reverse('corporatemobileno_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCorporateMobileNoList')
        obj.refresh_from_db()
        self.assertEqual(obj.mobile_no, '111222333X')
        self.assertEqual(obj.limit_amount, 1600.00)
        messages = list(get_messages(self.client.get(reverse('corporatemobileno_list')).wsgi_request))
        self.assertEqual(str(messages[0]), 'Corporate Mobile Number updated successfully.')

    def test_delete_view_get(self):
        obj = CorporateMobileNo.objects.get(id=11)
        response = self.client.get(reverse('corporatemobileno_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'hr_masters/corporatemobileno/_corporate_mobile_no_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Are you sure you want to delete')

    def test_delete_view_post_success_htmx(self):
        obj_count_before = CorporateMobileNo.objects.count()
        obj = CorporateMobileNo.objects.get(id=11)
        response = self.client.post(reverse('corporatemobileno_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCorporateMobileNoList')
        self.assertEqual(CorporateMobileNo.objects.count(), obj_count_before - 1)
        self.assertFalse(CorporateMobileNo.objects.filter(id=11).exists())
        messages = list(get_messages(self.client.get(reverse('corporatemobileno_list')).wsgi_request))
        self.assertEqual(str(messages[0]), 'Corporate Mobile Number deleted successfully.')
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Dynamic Content:**
    *   The main list page (`list.html`) uses `hx-get="{% url 'corporatemobileno_table' %}"` to load the DataTables content dynamically into `#corporatemobilenoTable-container`.
    *   `hx-trigger="load, refreshCorporateMobileNoList from:body"` ensures the table loads on page load and refreshes whenever a `refreshCorporateMobileNoList` event is triggered (e.g., after a successful create, update, or delete).
    *   Add/Edit/Delete buttons use `hx-get` to fetch the respective form/confirmation partials into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) on the partials use `hx-swap="none"` and rely on the server's `HX-Trigger` to close the modal and refresh the list.
    *   Loading indicators (`htmx-indicator`) are used for visual feedback during HTMX requests.

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) itself uses an `_=` (hyperscript) attribute: `on click if event.target.id == 'modal' remove .is-active from me` to close the modal when clicking outside of `modalContent`.
    *   The `on click add .is-active to #modal` for opening the modal from buttons, also using `_=` (hyperscript). This replaces the need for custom JavaScript to manage the modal's `hidden` class.

*   **DataTables for List Views:**
    *   The `_corporate_mobile_no_table.html` partial contains the `<table id="corporateMobileNoTable">` and the `$(document).ready(function() { $('#corporateMobileNoTable').DataTable(); });` script. This ensures DataTables is initialized every time the table partial is loaded or reloaded by HTMX, providing client-side search, sort, and pagination.

*   **DRY Template Inheritance:**
    *   All templates explicitly extend `core/base.html`. This `base.html` (assumed to exist) should contain the necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and any CSS frameworks like Tailwind CSS, ensuring that these resources are loaded once and shared across the application.

## Final Notes

*   **Placeholders:** All placeholders like `[MODEL_NAME]`, `[FIELD1]`, `[APP_NAME]`, etc., have been replaced with concrete values derived from the ASP.NET code analysis.
*   **DRY Principle:** Templates are modularized using partials (e.g., `_corporate_mobile_no_table.html`, `_corporate_mobile_no_form.html`, `_corporate_mobile_no_confirm_delete.html`) to avoid repetition.
*   **Fat Model, Thin View:** While the current CRUD operations are relatively simple and don't require extensive model methods beyond `__str__` and basic validation in the form, the structure is in place for future business logic to reside within the `CorporateMobileNo` model. Views remain concise, focusing on handling HTTP requests and rendering responses.
*   **Comprehensive Tests:** The provided tests cover both model functionality and all CRUD view operations, including the specific responses expected from HTMX requests, targeting at least 80% code coverage.
*   **Business Value:** This modernized Django solution provides a significantly improved user experience with dynamic, responsive interactions (no full page reloads), a maintainable and scalable codebase following best practices, and a clear separation of concerns, making future development and maintenance more efficient. It also offers enhanced data presentation capabilities with DataTables and a robust backend powered by Django.