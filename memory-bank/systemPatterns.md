# System Patterns

## System Architecture
- Django-based web application.
- Modular structure with separate apps for different functionalities (e.g., `core`, `sys_admin`).
- Uses Django's ORM for database interactions.

## Key Technical Decisions
- MVC-like pattern (Model-View-Controller, or Django's MVT - Model-View-Template).
- Emphasis on reusability of components and templates.

## Design Patterns in Use
- Likely uses Django's built-in forms for handling user input and validation.
- Template inheritance for consistent UI layout.

## Component Relationships
- `sys_admin` app handles administrative tasks, including Country, State, City masters.
- Models define data structures, forms handle input, views process logic, and templates render UI.

## Critical Implementation Paths
- Data entry and validation for master data.
- Displaying lists of master data.
