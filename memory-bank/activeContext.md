# Active Context

## Current Work Focus
Improving UI for Country and State forms, including list views and form validation.

## Recent Changes
- Initial setup of memory bank files.

## Next Steps
1. Examine `sys_admin/models.py` for Country and State models.
2. Examine `sys_admin/forms.py` for form definitions.
3. Examine `sys_admin/views.py` for form handling logic.
4. Examine `sys_admin/templates/sys_admin/` for current UI implementation.
5. Propose and implement UI/UX improvements and ensure proper form validation.

## Active Decisions and Considerations
- Prioritize user-friendliness and data integrity.
- Ensure consistency with existing UI patterns (if any).

## Learnings and Project Insights
- The project is a Django-based ERP system.
- Documentation is crucial due to memory resets.
