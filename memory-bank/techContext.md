# Tech Context

## Technologies Used
- **Backend:** Python, Django
- **Database:** SQLite (for development, likely PostgreSQL/MySQL for production)
- **Frontend:** HTML, CSS, JavaScript (likely vanilla JS or a light framework/library)
- **Templating:** Django Templates

## Development Setup
- Standard Django development environment.
- `manage.py` for running server, migrations, etc.

## Technical Constraints
- Adherence to Django's framework conventions.
- Browser compatibility for UI.

## Dependencies
- Django and its built-in libraries.
- (Further dependencies to be identified from `requirements.txt` or similar if available).

## Tool Usage Patterns
- Use of `read_file` to inspect code.
- Use of `replace_in_file` or `write_to_file` for code modifications.
- Use of `execute_command` for running Django commands (e.g., `runserver`, `makemigrations`, `migrate`).
- Use of `browser_action` for UI verification.
