```python
# sysadmin/models.py (Updated to add State methods)
# Add these methods to the existing State model
from django.db import models

class State(models.Model):
    state_id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=255)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CId')

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

    @classmethod
    def create_new_state(cls, state_name, country_id):
        country_instance = Country.objects.get(id=country_id)
        state = cls.objects.create(state_name=state_name, country=country_instance)
        return state

    def update_existing_state(self, state_name, country_id):
        country_instance = Country.objects.get(id=country_id)
        self.state_name = state_name
        self.country = country_instance
        self.save()
        return self

    def delete_existing_state(self):
        self.delete()

# sysadmin/forms.py (Add StateForm)
from django import forms
from .models import Country, State

class StateForm(forms.ModelForm):
    country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('country_name'),
        empty_label="Select Country",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )

    class Meta:
        model = State
        fields = ['state_name', 'country']
        widgets = {
            'state_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'state_name': 'State Name',
            'country': 'Country',
        }

    def clean_state_name(self):
        state_name = self.cleaned_data['state_name']
        if not state_name:
            raise forms.ValidationError("State Name is required.")
        return state_name

# sysadmin/views.py (Add State views)
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from django.template.loader import render_to_string
from .models import State
from .forms import StateForm

class StateListView(ListView):
    model = State
    template_name = 'sysadmin/state/list.html'
    context_object_name = 'states'

class StateTablePartialView(ListView):
    model = State
    template_name = 'sysadmin/state/_state_table.html'
    context_object_name = 'states'

class StateCreateView(CreateView):
    model = State
    form_class = StateForm
    template_name = 'sysadmin/state/_state_form.html'
    success_url = reverse_lazy('sysadmin:state_list')

    def form_valid(self, form):
        state_name = form.cleaned_data['state_name']
        country_instance = form.cleaned_data['country']
        State.create_new_state(state_name=state_name, country_id=country_instance.id)
        messages.success(self.request, 'State added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshStateList'}
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request), status=400)
        return response

class StateUpdateView(UpdateView):
    model = State
    form_class = StateForm
    template_name = 'sysadmin/state/_state_form.html'
    success_url = reverse_lazy('sysadmin:state_list')

    def form_valid(self, form):
        state_instance = self.get_object()
        state_name = form.cleaned_data['state_name']
        country_instance = form.cleaned_data['country']
        state_instance.update_existing_state(state_name=state_name, country_id=country_instance.id)
        messages.success(self.request, 'State updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshStateList'}
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form, 'object': form.instance}, request=self.request), status=400)
        return response

class StateDeleteView(DeleteView):
    model = State
    template_name = 'sysadmin/state/_state_confirm_delete.html'
    success_url = reverse_lazy('sysadmin:state_list')

    def delete(self, request, *args, **kwargs):
        state_instance = self.get_object()
        state_instance.delete_existing_state()
        messages.success(self.request, 'State deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshStateList'}
            )
        return super().delete(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return self.render_to_response(context)

# sysadmin/urls.py (Update to include State URLs)
from django.urls import path
from .views import (
    CountryListView, CountryTablePartialView, CountryCreateView, CountryUpdateView, CountryDeleteView,
    CityListView, CityTablePartialView, StateDropdownPartialView, CityCreateView, CityUpdateView, CityDeleteView,
    StateListView, StateTablePartialView, StateCreateView, StateUpdateView, StateDeleteView
)

app_name = 'sysadmin'

urlpatterns = [
    path('country/', CountryListView.as_view(), name='country_list'),
    path('country/table/', CountryTablePartialView.as_view(), name='country_table'),
    path('country/add/', CountryCreateView.as_view(), name='country_add'),
    path('country/edit/<int:pk>/', CountryUpdateView.as_view(), name='country_edit'),
    path('country/delete/<int:pk>/', CountryDeleteView.as_view(), name='country_delete'),
    path('state/', StateListView.as_view(), name='state_list'),
    path('state/table/', StateTablePartialView.as_view(), name='state_table'),
    path('state/add/', StateCreateView.as_view(), name='state_add'),
    path('state/edit/<int:pk>/', StateUpdateView.as_view(), name='state_edit'),
    path('state/delete/<int:pk>/', StateDeleteView.as_view(), name='state_delete'),
    path('city/', CityListView.as_view(), name='city_list'),
    path('city/table/', CityTablePartialView.as_view(), name='city_table'),
    path('country/<int:country_id>/states/', StateDropdownPartialView.as_view(), name='states_by_country'),
    path('city/add/', CityCreateView.as_view(), name='city_add'),
    path('city/edit/<int:pk>/', CityUpdateView.as_view(), name='city_edit'),
    path('city/delete/<int:pk>/', CityDeleteView.as_view(), name='city_delete'),
]

# sysadmin/templates/sysadmin/state/list.html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">States</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'sysadmin:state_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New State
        </button>
    </div>
    
    <div id="stateTable-container"
         hx-trigger="load, refreshStateList from:body"
         hx-get="{% url 'sysadmin:state_table' %}"
         hx-swap="innerHTML">
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading states...</p>
        </div>
    </div>
    
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:w-full"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            Alpine.init();
            document.getElementById('modal').classList.add('is-active');
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        if (event.detail.xhr.status === 204 && event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}

# sysadmin/templates/sysadmin/state/_state_table.html
{% load static %}
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative"
     x-data="{}">
    <table id="stateTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">State Name</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Country</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for state in states %}
            <tr>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-gray-900">{{ state.state_name }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-gray-900">{{ state.country.country_name }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'sysadmin:state_edit' state.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'sysadmin:state_delete' state.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        hx-confirm="Are you sure you want to delete '{{ state.state_name }}'? This action cannot be undone.">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
            {% if not states %}
            <tr>
                <td colspan="4" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center text-gray-500">No states found. Click 'Add New State' to create one.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#stateTable')) {
            $('#stateTable').DataTable().destroy();
        }
        $('#stateTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 3] }
            ]
        });
    });
</script>

# sysadmin/templates/sysadmin/state/_state_form.html
{% load tailwind_filters %}
<div class="p-6" x-data="{}">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} State</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        <div class="space-y-4">
            {{ form|crispy }}
        </div>
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
            <span id="form-loading-indicator" class="htmx-indicator ml-4 text-blue-600">
                <i class="fas fa-spinner fa-spin"></i> Saving...
            </span>
        </div>
    </form>
</div>

# sysadmin/templates/sysadmin/state/_state_confirm_delete.html
<div class="p-6" x-data="{}">
    <h3 class="text-xl font-semibold text-red-700 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the state: <span class="font-bold">{{ object.state_name }}</span>?</p>
    <p class="text-red-600 mb-8">This action cannot be undone.</p>
    <form hx-post="{% url 'sysadmin:state_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>

# sysadmin/tests.py (Add State tests)
from django.test import TestCase, Client
from django.urls import reverse
from .models import Country, State
from .forms import StateForm

class StateModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(
            id=1,
            country_name='Testland',
            currency='TLD',
            symbol='T'
        )
        cls.state1 = State.objects.create(
            state_id=101,
            state_name='State Alpha',
            country=cls.country
        )
        cls.state2 = State.objects.create(
            state_id=102,
            state_name='State Beta',
            country=cls.country
        )

    def test_state_creation(self):
        self.assertEqual(self.state1.state_name, 'State Alpha')
        self.assertEqual(self.state1.country, self.country)
        self.assertIsInstance(self.state1, State)

    def test_state_str_method(self):
        self.assertEqual(str(self.state1), 'State Alpha')

    def test_state_meta_options(self):
        self.assertEqual(self.state1._meta.db_table, 'tblState')
        self.assertFalse(self.state1._meta.managed)
        self.assertEqual(self.state1._meta.verbose_name, 'State')
        self.assertEqual(self.state1._meta.verbose_name_plural, 'States')

    def test_create_new_state(self):
        new_state = State.create_new_state(state_name='State Gamma', country_id=self.country.id)
        self.assertIsInstance(new_state, State)
        self.assertEqual(new_state.state_name, 'State Gamma')
        self.assertEqual(new_state.country, self.country)

    def test_update_existing_state(self):
        updated_state = self.state1.update_existing_state(state_name='Updated Alpha', country_id=self.country.id)
        self.assertEqual(updated_state.state_name, 'Updated Alpha')
        self.state1.refresh_from_db()
        self.assertEqual(self.state1.state_name, 'Updated Alpha')

    def test_delete_existing_state(self):
        state_count = State.objects.count()
        self.state1.delete_existing_state()
        self.assertEqual(State.objects.count(), state_count - 1)
        self.assertFalse(State.objects.filter(state_id=self.state1.state_id).exists())

class StateViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(
            id=1,
            country_name='Testland',
            currency='TLD',
            symbol='T'
        )
        cls.state1 = State.objects.create(
            state_id=101,
            state_name='State Alpha',
            country=cls.country
        )
        cls.state2 = State.objects.create(
            state_id=102,
            state_name='State Beta',
            country=cls.country
        )

    def setUp(self):
        self.client = Client()

    def test_state_list_view(self):
        response = self.client.get(reverse('sysadmin:state_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/list.html')
        self.assertIn('states', response.context)
        self.assertEqual(list(response.context['states']), [self.state1, self.state2])

    def test_state_table_partial_view(self):
        response = self.client.get(reverse('sysadmin:state_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_table.html')
        self.assertIn('states', response.context)
        self.assertEqual(list(response.context['states']), [self.state1, self.state2])
        self.assertContains(response, 'State Alpha')

    def test_state_create_view_get(self):
        response = self.client.get(reverse('sysadmin:state_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIsInstance(response.context['form'], StateForm)

    def test_state_create_view_post_success(self):
        initial_state_count = State.objects.count()
        data = {
            'state_name': 'State Gamma',
            'country': self.country.id
        }
        response = self.client.post(reverse('sysadmin:state_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStateList')
        self.assertEqual(State.objects.count(), initial_state_count + 1)
        self.assertTrue(State.objects.filter(state_name='State Gamma').exists())

    def test_state_create_view_post_invalid(self):
        initial_state_count = State.objects.count()
        data = {
            'state_name': '',
            'country': self.country.id
        }
        response = self.client.post(reverse('sysadmin:state_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertEqual(State.objects.count(), initial_state_count)

    def test_state_update_view_get(self):
        response = self.client.get(reverse('sysadmin:state_edit', args=[self.state1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIsInstance(response.context['form'], StateForm)
        self.assertEqual(response.context['form'].instance, self.state1)

    def test_state_update_view_post_success(self):
        data = {
            'state_name': 'Updated Alpha',
            'country': self.country.id
        }
        response = self.client.post(reverse('sysadmin:state_edit', args=[self.state1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStateList')
        self.state1.refresh_from_db()
        self.assertEqual(self.state1.state_name, 'Updated Alpha')

    def test_state_update_view_post_invalid(self):
        original_name = self.state1.state_name
        data = {
            'state_name': '',
            'country': self.country.id
        }
        response = self.client.post(reverse('sysadmin:state_edit', args=[self.state1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.state1.refresh_from_db()
        self.assertEqual(self.state1.state_name, original_name)

    def test_state_delete_view_get(self):
        response = self.client.get(reverse('sysadmin:state_delete', args=[self.state1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.state1)

    def test_state_delete_view_post_success(self):
        state_to_delete_pk = self.state2.pk
        response = self.client.post(reverse('sysadmin:state_delete', args=[state_to_delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStateList')
        self.assertFalse(State.objects.filter(pk=state_to_delete_pk).exists())
        self.assertEqual(State.objects.count(), 1)
```