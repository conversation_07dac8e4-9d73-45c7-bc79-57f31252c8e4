from django.db import models
from datetime import date


class Country(models.Model):
    """
    Maps to the existing tblCountry table.
    Used for country lookup in State creation/editing.
    """

    cid = models.AutoField(db_column="CId", primary_key=True)
    countryname = models.CharField(
        db_column="CountryName", max_length=255, blank=False, null=False
    )
    currency = models.CharField(
        db_column="Currency", max_length=50, blank=False, null=False
    )
    symbol = models.CharField(
        db_column="Symbol", max_length=10, blank=False, null=False
    )
    keyshortcut = models.CharField(
        db_column="KeyShortcut", max_length=10, blank=True, null=True
    )

    class Meta:
        managed = False
        db_table = "tblCountry"
        verbose_name = "Country"
        verbose_name_plural = "Countries"
        ordering = ["countryname"]

    def __str__(self):
        return self.countryname or f"Country {self.cid}"

    def get_full_country_info(self):
        """Business logic method for displaying complete country information."""
        return f"{self.countryname} ({self.currency} {self.symbol})"


class State(models.Model):
    """
    Maps to the existing tblState table.
    Represents geographical states and their associated countries.
    """

    sid = models.AutoField(db_column="SId", primary_key=True)
    statename = models.CharField(
        db_column="StateName", max_length=255, blank=False, null=False
    )
    cid = models.ForeignKey(
        Country,
        models.DO_NOTHING,
        db_column="CId",
        blank=True,
        null=True,
        related_name="states",
    )

    class Meta:
        managed = False
        db_table = "tblState"
        verbose_name = "State"
        verbose_name_plural = "States"
        ordering = ["-sid"]  # From ASP.NET 'Order By SId Desc'

    def __str__(self):
        return self.statename or f"State {self.sid}"

    def can_be_deleted(self):
        """
        Business logic: A state cannot be deleted if it has associated cities.
        Replicates the logic from getCnt() in the ASP.NET code-behind.
        """
        return not self.cities.exists()  # Check if any related City objects exist


class City(models.Model):
    """
    Maps to the existing tblCity table.
    Represents cities within states.
    """

    cityid = models.AutoField(db_column="CityId", primary_key=True)
    cityname = models.CharField(
        db_column="CityName", max_length=255, blank=False, null=False
    )
    sid = models.ForeignKey(
        State,
        models.DO_NOTHING,
        db_column="SId",
        blank=True,
        null=True,
        related_name="cities",
    )

    class Meta:
        managed = False
        db_table = "tblCity"
        verbose_name = "City"
        verbose_name_plural = "Cities"
        ordering = ["-cityid"]  # Order by CityId Desc as seen in ASP.NET loadata

    def __str__(self):
        return self.cityname or f"City {self.cityid}"

    @classmethod
    def get_filtered_cities(cls, state_id=None):
        """
        Retrieves cities, optionally filtered by state.
        Corresponds to ASP.NET's loadata function.
        """
        if (
            state_id and state_id != "0"
        ):  # '0' represents 'Select' or all in ASP.NET logic
            try:
                return cls.objects.filter(sid__sid=int(state_id))
            except (ValueError, TypeError):
                # Handle invalid state_id gracefully
                return cls.objects.all()
        return cls.objects.all()

    @classmethod
    def create_new_city(cls, city_name, state_id):
        """
        Creates a new city record.
        Corresponds to ASP.NET's GridView1_RowCommand (Add/Add1).
        """
        state_instance = State.objects.get(sid=state_id)
        city = cls.objects.create(cityname=city_name, sid=state_instance)
        return city

    def update_existing_city(self, city_name, state_id):
        """
        Updates an existing city record.
        Corresponds to ASP.NET's GridView1_RowUpdating.
        """
        state_instance = State.objects.get(sid=state_id)
        self.cityname = city_name
        self.sid = state_instance
        self.save()
        return self

    def delete_existing_city(self):
        """
        Deletes a city record.
        Corresponds to ASP.NET's GridView1_RowCommand (Del).
        """
        self.delete()


class Company(models.Model):
    compid = models.AutoField(db_column="CompId", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50)
    systime = models.CharField(db_column="SysTime", max_length=50)
    companyname = models.TextField(db_column="CompanyName")
    regdaddress = models.TextField(db_column="RegdAddress", blank=True, null=True)
    regdcity = models.ForeignKey(City, models.DO_NOTHING, db_column="RegdCity")
    regdstate = models.IntegerField(db_column="RegdState")
    regdcountry = models.IntegerField(db_column="RegdCountry")
    regdpincode = models.TextField(db_column="RegdPinCode", blank=True, null=True)
    regdcontactno = models.TextField(db_column="RegdContactNo", blank=True, null=True)
    regdfaxno = models.TextField(db_column="RegdFaxNo", blank=True, null=True)
    regdemail = models.TextField(db_column="RegdEmail", blank=True, null=True)
    plantaddress = models.TextField(db_column="PlantAddress", blank=True, null=True)
    plantcity = models.ForeignKey(
        City,
        models.DO_NOTHING,
        db_column="PlantCity",
        related_name="tblcompanymaster_plantcity_set",
    )
    plantstate = models.IntegerField(db_column="PlantState")
    plantcountry = models.IntegerField(db_column="PlantCountry")
    plantpincode = models.TextField(db_column="PlantPinCode", blank=True, null=True)
    plantcontactno = models.TextField(db_column="PlantContactNo", blank=True, null=True)
    plantfaxno = models.TextField(db_column="PlantFaxNo", blank=True, null=True)
    plantemail = models.TextField(db_column="PlantEmail", blank=True, null=True)
    eccno = models.TextField(db_column="ECCNo", blank=True, null=True)
    commissionerate = models.TextField(
        db_column="Commissionerate", blank=True, null=True
    )
    range = models.TextField(db_column="Range", blank=True, null=True)
    division = models.TextField(db_column="Division", blank=True, null=True)
    locationno = models.TextField(db_column="LocationNo", blank=True, null=True)
    vat = models.TextField(db_column="VAT", blank=True, null=True)
    cstno = models.TextField(db_column="CSTNo", blank=True, null=True)
    panno = models.TextField(db_column="PANNo", blank=True, null=True)
    logoimage = models.BinaryField(db_column="LogoImage", blank=True, null=True)
    logofilename = models.TextField(db_column="LogoFileName", blank=True, null=True)
    prefix = models.TextField(db_column="Prefix", blank=True, null=True)
    licencenos = models.TextField(db_column="LicenceNos", blank=True, null=True)
    defaultcomp = models.IntegerField(db_column="DefaultComp", blank=True, null=True)
    mailserverip = models.TextField(db_column="MailServerIp", blank=True, null=True)
    serverip = models.TextField(db_column="ServerIp", blank=True, null=True)
    flag = models.IntegerField(db_column="Flag", blank=True, null=True)
    itemcodelimit = models.IntegerField(
        db_column="ItemCodeLimit", blank=True, null=True
    )
    erpsysmail = models.TextField(db_column="ErpSysmail", blank=True, null=True)
    mobileno = models.TextField(db_column="MobileNo", blank=True, null=True)
    password = models.TextField(db_column="Password", blank=True, null=True)

    class Meta:
        managed = False
        db_table = "tblCompany_master"

    def __str__(self):
        return self.companyname or f"Company {self.compid}"


class FinancialYear(models.Model):
    finyearid = models.AutoField(db_column="FinYearId", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50)
    systime = models.CharField(db_column="SysTime", max_length=50)
    sessionid = models.TextField(db_column="SessionId")
    compid = models.IntegerField(db_column="CompId")
    start_date = models.DateField(db_column="FinYearFrom", blank=True, null=True)
    end_date = models.DateField(db_column="FinYearTo", blank=True, null=True)
    finyear = models.CharField(
        db_column="FinYear", max_length=50, blank=True, null=True
    )
    is_current = models.BooleanField(db_column="Flag", default=False)

    class Meta:
        managed = False
        db_table = "tblFinancial_master"

    def __str__(self):
        return self.finyear or f"{self.start_date.year}-{self.end_date.year}"

    @property
    def year_name(self):
        if self.finyear:
            return self.finyear
        if self.start_date and self.end_date:
            return f"{self.start_date.year}-{self.end_date.year}"
        return "N/A"

    @property
    def duration_days(self):
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days + 1
        return 0


class Month(models.Model):
    monthid = models.AutoField(db_column="MonthId", primary_key=True)
    sysdate = models.CharField(db_column="SysDate", max_length=50)
    systime = models.CharField(db_column="SysTime", max_length=50)
    sessionid = models.TextField(db_column="SessionId")
    compid = models.IntegerField(db_column="CompId")
    finyearid = models.ForeignKey(
        FinancialYear, models.DO_NOTHING, db_column="FinYearId"
    )
    month_name = models.CharField(db_column="MonthName", max_length=50)
    month_number = models.IntegerField(db_column="MonthNo")

    class Meta:
        managed = False
        db_table = "tblMonth_master"
        unique_together = (("finyearid", "month_number"),)

    def __str__(self):
        return f"{self.month_name} ({self.finyearid.finyear})"
