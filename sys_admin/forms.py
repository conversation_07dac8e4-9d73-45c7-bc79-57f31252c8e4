from django import forms
from .models import Country, State, City, Company, FinancialYear


class CountryForm(forms.ModelForm):
    """
    Form for creating and updating Country objects.
    Uses Tailwind CSS classes for modern styling.
    """

    class Meta:
        model = Country
        fields = ["countryname", "currency", "symbol", "keyshortcut"]
        widgets = {
            "countryname": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                    "placeholder": "Enter Country Name",
                }
            ),
            "currency": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                    "placeholder": "Enter Currency",
                }
            ),
            "symbol": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                    "placeholder": "Enter Symbol",
                }
            ),
            "keyshortcut": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                    "placeholder": "Enter Key Shortcut",
                }
            ),
        }
        labels = {
            "countryname": "Country Name",
            "currency": "Currency",
            "symbol": "Symbol",
            "keyshortcut": "Key Shortcut",
        }


class StateForm(forms.ModelForm):
    """
    Form for creating and updating State objects.
    Automatically handles required fields based on model definition.
    """

    cid = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(
            attrs={
                "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            }
        ),
        label="Country Name",  # Matches ASP.NET column header
    )

    class Meta:
        model = State
        fields = ["statename", "cid"]
        widgets = {
            "statename": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Enter State Name",
                }
            ),
        }
        labels = {
            "statename": "State Name",  # Matches ASP.NET column header
            "cid": "Country",
        }


class CityForm(forms.ModelForm):
    """
    Form for creating and updating City objects.
    Uses modern Tailwind CSS styling.
    """

    sid = forms.ModelChoiceField(
        queryset=State.objects.all().order_by("statename"),
        empty_label="Select State",
        widget=forms.Select(
            attrs={
                "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            }
        ),
        required=True,
        label="State",
    )

    class Meta:
        model = City
        fields = ["cityname", "sid"]
        widgets = {
            "cityname": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Enter City Name",
                }
            ),
        }
        labels = {
            "cityname": "City Name",
            "sid": "State",
        }

    def clean_cityname(self):
        cityname = self.cleaned_data["cityname"]
        if not cityname:  # Corresponds to RequiredFieldValidator
            raise forms.ValidationError("City Name is required.")
        return cityname


class CityFilterForm(forms.Form):
    """
    Form for filtering cities by country and state.
    Used in the city list view for dynamic filtering.
    """

    country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by("countryname"),
        empty_label="Select Country",
        widget=forms.Select(
            attrs={
                "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
            }
        ),
        required=False,
    )
    state = forms.ModelChoiceField(
        queryset=State.objects.none(),  # Initially empty, populated via HTMX
        empty_label="Select State",
        widget=forms.Select(
            attrs={
                "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
            }
        ),
        required=False,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate the state queryset based on selected country if available
        if "country" in self.data and self.data["country"]:
            try:
                country_id = int(self.data["country"])
                self.fields["state"].queryset = State.objects.filter(
                    cid__cid=country_id
                ).order_by("statename")
            except (ValueError, TypeError):
                pass
        elif self.initial.get("country"):
            self.fields["state"].queryset = State.objects.filter(
                cid__cid=self.initial["country"].cid
            ).order_by("statename")


class CompanyForm(forms.ModelForm):
    class Meta:
        model = Company
        exclude = ["sysdate", "systime", "flag"]
        widgets = {
            "companyname": forms.TextInput(attrs={"class": "form-control"}),
            "regdaddress": forms.Textarea(attrs={"class": "form-control", "rows": 3}),
            "regdcity": forms.Select(attrs={"class": "form-select"}),
            "regdstate": forms.NumberInput(attrs={"class": "form-control"}),
            "regdcountry": forms.NumberInput(attrs={"class": "form-control"}),
            "regdpincode": forms.TextInput(attrs={"class": "form-control"}),
            "regdcontactno": forms.TextInput(attrs={"class": "form-control"}),
            "regdfaxno": forms.TextInput(attrs={"class": "form-control"}),
            "regdemail": forms.EmailInput(attrs={"class": "form-control"}),
            "plantaddress": forms.Textarea(attrs={"class": "form-control", "rows": 3}),
            "plantcity": forms.Select(attrs={"class": "form-select"}),
            "plantstate": forms.NumberInput(attrs={"class": "form-control"}),
            "plantcountry": forms.NumberInput(attrs={"class": "form-control"}),
            "plantpincode": forms.TextInput(attrs={"class": "form-control"}),
            "plantcontactno": forms.TextInput(attrs={"class": "form-control"}),
            "plantfaxno": forms.TextInput(attrs={"class": "form-control"}),
            "plantemail": forms.EmailInput(attrs={"class": "form-control"}),
            "eccno": forms.TextInput(attrs={"class": "form-control"}),
            "commissionerate": forms.TextInput(attrs={"class": "form-control"}),
            "range": forms.TextInput(attrs={"class": "form-control"}),
            "division": forms.TextInput(attrs={"class": "form-control"}),
            "locationno": forms.TextInput(attrs={"class": "form-control"}),
            "vat": forms.TextInput(attrs={"class": "form-control"}),
            "cstno": forms.TextInput(attrs={"class": "form-control"}),
            "panno": forms.TextInput(attrs={"class": "form-control"}),
            "logofilename": forms.TextInput(attrs={"class": "form-control"}),
            "prefix": forms.TextInput(attrs={"class": "form-control"}),
            "licencenos": forms.TextInput(attrs={"class": "form-control"}),
            "defaultcomp": forms.NumberInput(attrs={"class": "form-control"}),
            "mailserverip": forms.TextInput(attrs={"class": "form-control"}),
            "serverip": forms.TextInput(attrs={"class": "form-control"}),
            "itemcodelimit": forms.NumberInput(attrs={"class": "form-control"}),
            "erpsysmail": forms.EmailInput(attrs={"class": "form-control"}),
            "mobileno": forms.TextInput(attrs={"class": "form-control"}),
            "password": forms.PasswordInput(attrs={"class": "form-control"}),
        }


class FinancialYearForm(forms.ModelForm):
    class Meta:
        model = FinancialYear
        fields = ["finyear", "start_date", "end_date", "is_current", "compid"]
        widgets = {
            "finyear": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "e.g., 2023-2024"}
            ),
            "start_date": forms.DateInput(
                attrs={"class": "form-control", "type": "date"}
            ),
            "end_date": forms.DateInput(
                attrs={"class": "form-control", "type": "date"}
            ),
            "is_current": forms.CheckboxInput(attrs={"class": "form-check-input"}),
            "compid": forms.NumberInput(attrs={"class": "form-control"}),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")

        if start_date and end_date and end_date < start_date:
            self.add_error("end_date", "End date cannot be before start date")

        return cleaned_data
