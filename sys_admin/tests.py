from django.test import TestCase, Client
from django.urls import reverse
from .models import Country, State, City
from .forms import CountryForm, StateForm, CityForm


class CountryModelTest(TestCase):
    """
    Unit tests for the Country model.
    """

    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        cls.country1 = Country.objects.create(
            countryname="Testland", currency="TLD", symbol="T"
        )
        cls.country2 = Country.objects.create(
            countryname="Otherland", currency="OTL", symbol="O"
        )

    def test_country_creation(self):
        """
        Verify that a Country object can be created correctly.
        """
        self.assertEqual(self.country1.countryname, "Testland")
        self.assertEqual(self.country1.currency, "TLD")
        self.assertEqual(self.country1.symbol, "T")
        self.assertIsInstance(self.country1, Country)

    def test_country_str_method(self):
        """
        Verify the __str__ method returns the country name.
        """
        self.assertEqual(str(self.country1), "Testland")

    def test_country_meta_options(self):
        """
        Verify Meta options like db_table and managed.
        """
        self.assertEqual(self.country1._meta.db_table, "tblCountry")
        self.assertFalse(self.country1._meta.managed)
        self.assertEqual(self.country1._meta.verbose_name, "Country")
        self.assertEqual(self.country1._meta.verbose_name_plural, "Countries")

    def test_get_full_country_info(self):
        """
        Test the business logic method for displaying complete country information.
        """
        expected = "Testland (TLD T)"
        self.assertEqual(self.country1.get_full_country_info(), expected)


class StateModelTest(TestCase):
    """
    Unit tests for the State model.
    """

    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(
            countryname="TestCountry", currency="TC", symbol="T"
        )
        cls.state1 = State.objects.create(statename="TestState", cid=cls.country)
        cls.state2 = State.objects.create(statename="AnotherState", cid=cls.country)

    def test_state_creation(self):
        """
        Verify that a State object can be created correctly.
        """
        self.assertEqual(self.state1.statename, "TestState")
        self.assertEqual(self.state1.cid, self.country)
        self.assertIsInstance(self.state1, State)

    def test_state_str_method(self):
        """
        Verify the __str__ method returns the state name.
        """
        self.assertEqual(str(self.state1), "TestState")

    def test_state_meta_options(self):
        """
        Verify Meta options like db_table and managed.
        """
        self.assertEqual(self.state1._meta.db_table, "tblState")
        self.assertFalse(self.state1._meta.managed)
        self.assertEqual(self.state1._meta.verbose_name, "State")
        self.assertEqual(self.state1._meta.verbose_name_plural, "States")

    def test_can_be_deleted_with_no_cities(self):
        """
        Test that a state can be deleted when it has no associated cities.
        """
        self.assertTrue(self.state1.can_be_deleted())

    def test_can_be_deleted_with_cities(self):
        """
        Test that a state cannot be deleted when it has associated cities.
        """
        # Create a city associated with the state
        City.objects.create(cityname="TestCity", sid=self.state1)
        self.assertFalse(self.state1.can_be_deleted())


class CityModelTest(TestCase):
    """
    Unit tests for the City model.
    """

    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(
            countryname="TestCountry", currency="TC", symbol="T"
        )
        cls.state = State.objects.create(statename="TestState", cid=cls.country)
        cls.city1 = City.objects.create(cityname="TestCity", sid=cls.state)
        cls.city2 = City.objects.create(cityname="AnotherCity", sid=cls.state)

    def test_city_creation(self):
        """
        Verify that a City object can be created correctly.
        """
        self.assertEqual(self.city1.cityname, "TestCity")
        self.assertEqual(self.city1.sid, self.state)
        self.assertIsInstance(self.city1, City)

    def test_city_str_method(self):
        """
        Verify the __str__ method returns the city name.
        """
        self.assertEqual(str(self.city1), "TestCity")

    def test_city_meta_options(self):
        """
        Verify Meta options like db_table and managed.
        """
        self.assertEqual(self.city1._meta.db_table, "tblCity")
        self.assertFalse(self.city1._meta.managed)
        self.assertEqual(self.city1._meta.verbose_name, "City")
        self.assertEqual(self.city1._meta.verbose_name_plural, "Cities")

    def test_get_filtered_cities_all(self):
        """
        Test getting all cities when no state filter is applied.
        """
        cities = City.get_filtered_cities()
        self.assertEqual(cities.count(), 2)
        self.assertIn(self.city1, cities)
        self.assertIn(self.city2, cities)

    def test_get_filtered_cities_by_state(self):
        """
        Test getting cities filtered by state.
        """
        cities = City.get_filtered_cities(str(self.state.sid))
        self.assertEqual(cities.count(), 2)
        self.assertIn(self.city1, cities)
        self.assertIn(self.city2, cities)

    def test_get_filtered_cities_invalid_state(self):
        """
        Test getting cities with invalid state ID returns all cities.
        """
        cities = City.get_filtered_cities("invalid")
        self.assertEqual(cities.count(), 2)

    def test_create_new_city(self):
        """
        Test the create_new_city class method.
        """
        initial_count = City.objects.count()
        new_city = City.create_new_city("NewCity", self.state.sid)

        self.assertEqual(City.objects.count(), initial_count + 1)
        self.assertEqual(new_city.cityname, "NewCity")
        self.assertEqual(new_city.sid, self.state)

    def test_update_existing_city(self):
        """
        Test the update_existing_city method.
        """
        updated_city = self.city1.update_existing_city("UpdatedCity", self.state.sid)

        self.assertEqual(updated_city.cityname, "UpdatedCity")
        self.assertEqual(updated_city.sid, self.state)

        # Refresh from database to verify the change was saved
        self.city1.refresh_from_db()
        self.assertEqual(self.city1.cityname, "UpdatedCity")

    def test_delete_existing_city(self):
        """
        Test the delete_existing_city method.
        """
        initial_count = City.objects.count()
        city_id = self.city1.cityid

        self.city1.delete_existing_city()

        self.assertEqual(City.objects.count(), initial_count - 1)
        self.assertFalse(City.objects.filter(cityid=city_id).exists())


class CountryViewsTest(TestCase):
    """
    Integration tests for Country views.
    """

    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.country1 = Country.objects.create(
            countryname="ViewTestCountry", currency="VTC", symbol="V"
        )
        cls.country2 = Country.objects.create(
            countryname="AnotherViewTest", currency="AVT", symbol="A"
        )

    def setUp(self):
        # Set up a new client for each test method to ensure isolated state
        self.client = Client()

    def test_country_list_view(self):
        """
        Test the Country list view (GET request).
        """
        response = self.client.get(reverse("sys_admin:country_list"))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "sys_admin/country/list.html")
        self.assertIn("countries", response.context)

    def test_country_table_partial_view(self):
        """
        Test the HTMX partial for the country table.
        """
        response = self.client.get(reverse("sys_admin:country_table"))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "sys_admin/country/_country_table.html")
        self.assertIn("countries", response.context)
        self.assertContains(response, "ViewTestCountry")  # Check if content is present

    def test_country_create_view_get(self):
        """
        Test GET request for adding a new country.
        """
        response = self.client.get(reverse("sys_admin:country_add"))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "sys_admin/country/_country_form.html")
        self.assertIsInstance(response.context["form"], CountryForm)

    def test_country_create_view_post_success(self):
        """
        Test POST request for adding a new country successfully (HTMX).
        """
        initial_country_count = Country.objects.count()
        data = {"countryname": "NewCountry", "currency": "NCU", "symbol": "N"}
        response = self.client.post(
            reverse("sys_admin:country_add"), data, HTTP_HX_REQUEST="true"
        )
        self.assertEqual(response.status_code, 204)  # 204 No Content for HTMX success
        self.assertIn("HX-Trigger", response.headers)
        self.assertEqual(response.headers["HX-Trigger"], "refreshCountryList")
        self.assertEqual(Country.objects.count(), initial_country_count + 1)
        self.assertTrue(Country.objects.filter(countryname="NewCountry").exists())

    def test_country_create_view_post_invalid(self):
        """
        Test POST request for adding a new country with invalid data (HTMX).
        """
        initial_country_count = Country.objects.count()
        data = {
            "countryname": "",  # Invalid data (required field empty)
            "currency": "X",
            "symbol": "Y",
        }
        response = self.client.post(
            reverse("sys_admin:country_add"), data, HTTP_HX_REQUEST="true"
        )
        self.assertEqual(response.status_code, 400)  # Bad Request for invalid form
        self.assertTemplateUsed(response, "sys_admin/country/_country_form.html")
        self.assertEqual(
            Country.objects.count(), initial_country_count
        )  # No new object created

    def test_country_update_view_get(self):
        """
        Test GET request for editing an existing country.
        """
        response = self.client.get(
            reverse("sys_admin:country_edit", args=[self.country1.pk])
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "sys_admin/country/_country_form.html")
        self.assertIsInstance(response.context["form"], CountryForm)
        self.assertEqual(response.context["form"].instance, self.country1)

    def test_country_update_view_post_success(self):
        """
        Test POST request for updating an existing country successfully (HTMX).
        """
        data = {"countryname": "UpdatedCountry", "currency": "UCC", "symbol": "U"}
        response = self.client.post(
            reverse("sys_admin:country_edit", args=[self.country1.pk]),
            data,
            HTTP_HX_REQUEST="true",
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn("HX-Trigger", response.headers)
        self.assertEqual(response.headers["HX-Trigger"], "refreshCountryList")
        self.country1.refresh_from_db()
        self.assertEqual(self.country1.countryname, "UpdatedCountry")

    def test_country_update_view_post_invalid(self):
        """
        Test POST request for updating an existing country with invalid data (HTMX).
        """
        original_name = self.country1.countryname
        data = {"countryname": "", "currency": "X", "symbol": "Y"}  # Invalid data
        response = self.client.post(
            reverse("sys_admin:country_edit", args=[self.country1.pk]),
            data,
            HTTP_HX_REQUEST="true",
        )
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, "sys_admin/country/_country_form.html")
        self.country1.refresh_from_db()
        self.assertEqual(
            self.country1.countryname, original_name
        )  # Name should not have changed

    def test_country_delete_view_get(self):
        """
        Test GET request for delete confirmation.
        """
        response = self.client.get(
            reverse("sys_admin:country_delete", args=[self.country1.pk])
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(
            response, "sys_admin/country/_country_confirm_delete.html"
        )
        self.assertIn("object", response.context)
        self.assertEqual(response.context["object"], self.country1)

    def test_country_delete_view_post_success(self):
        """
        Test POST request for deleting a country successfully (HTMX).
        """
        country_to_delete_pk = self.country2.pk
        response = self.client.post(
            reverse("sys_admin:country_delete", args=[country_to_delete_pk]),
            HTTP_HX_REQUEST="true",
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn("HX-Trigger", response.headers)
        self.assertEqual(response.headers["HX-Trigger"], "refreshCountryList")
        self.assertFalse(Country.objects.filter(pk=country_to_delete_pk).exists())
        self.assertEqual(Country.objects.count(), 1)  # Only country1 should remain
