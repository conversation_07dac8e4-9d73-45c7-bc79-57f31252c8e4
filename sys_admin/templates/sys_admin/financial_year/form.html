{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">{% if object %}Edit{% else %}Add{% endif %} Financial Year</h2>
        <a href="{% url 'sysadmin:financial_year_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to List
        </a>
    </div>
    
    <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
        <form method="post" class="space-y-4">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <!-- Financial Year -->
                    <div>
                        <label for="{{ form.finyear.id_for_label }}" class="block text-sm font-medium text-gray-700">Financial Year</label>
                        <div class="mt-1">
                            {{ form.finyear }}
                            {% if form.finyear.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.finyear.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <p class="text-gray-500 text-xs mt-1">{{ form.finyear.help_text|default:"Format: YYYY-YYYY" }}</p>
                    </div>
                    
                    <!-- Start Date -->
                    <div>
                        <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <div class="mt-1">
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.start_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- End Date -->
                    <div>
                        <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700">End Date</label>
                        <div class="mt-1">
                            {{ form.end_date }}
                            {% if form.end_date.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.end_date.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <!-- Is Current -->
                    <div>
                        <div class="flex items-center">
                            {{ form.is_current }}
                            <label for="{{ form.is_current.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                                Is Current Financial Year
                            </label>
                        </div>
                        {% if form.is_current.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.is_current.errors.0 }}</p>
                        {% endif %}
                        <p class="text-gray-500 text-xs mt-1">Only one financial year can be current at a time.</p>
                    </div>
                    
                    <!-- Company -->
                    <div>
                        <label for="{{ form.compid.id_for_label }}" class="block text-sm font-medium text-gray-700">Company</label>
                        <div class="mt-1">
                            {{ form.compid }}
                            {% if form.compid.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.compid.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Read-only fields -->
                    {% if object %}
                    <div class="pt-4 border-t border-gray-200 mt-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">Year Name:</span>
                            <span class="text-sm text-gray-500">{{ object.year_name }}</span>
                        </div>
                        <div class="flex items-center justify-between mt-2">
                            <span class="text-sm font-medium text-gray-700">Duration:</span>
                            <span class="text-sm text-gray-500">{{ object.duration_days }} days</span>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="pt-5 border-t border-gray-200 mt-4">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'sysadmin:financial_year_list' %}" class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Cancel
                    </a>
                    <button type="submit" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        Save
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
{% endblock %}

{% block body_extra %}
<script>
    $(document).ready(function() {
        // Initialize date pickers
        flatpickr("#{{ form.start_date.id_for_label }}", {
            dateFormat: "Y-m-d",
            allowInput: true
        });
        
        flatpickr("#{{ form.end_date.id_for_label }}", {
            dateFormat: "Y-m-d",
            allowInput: true
        });
        
        // Add styling to form inputs
        $("input[type=text], input[type=date], select").addClass("mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50");
        
        // Style checkboxes
        $("input[type=checkbox]").addClass("h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded");
    });
</script>
{% endblock %} 