{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Financial Year Details</h2>
        <div class="space-x-2">
            <a href="{% url 'sysadmin:financial_year_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to List
            </a>
            <a href="{% url 'sysadmin:financial_year_edit' financial_year.pk %}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                Edit
            </a>
            <a href="{% url 'sysadmin:financial_year_delete' financial_year.pk %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </a>
        </div>
    </div>
    
    <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-medium mb-2">Basic Information</h3>
                <table class="w-full">
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">ID:</td>
                        <td class="py-2">{{ financial_year.finyearid }}</td>
                    </tr>
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">Financial Year:</td>
                        <td class="py-2">{{ financial_year.finyear }}</td>
                    </tr>
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">Start Date:</td>
                        <td class="py-2">{{ financial_year.start_date|date:"F d, Y" }}</td>
                    </tr>
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">End Date:</td>
                        <td class="py-2">{{ financial_year.end_date|date:"F d, Y" }}</td>
                    </tr>
                </table>
            </div>
            
            <div>
                <h3 class="text-lg font-medium mb-2">Additional Information</h3>
                <table class="w-full">
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">Current Year:</td>
                        <td class="py-2">
                            {% if financial_year.is_current %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                            {% else %}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">No</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">Duration:</td>
                        <td class="py-2">{{ financial_year.duration_days }} days</td>
                    </tr>
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">Year Name:</td>
                        <td class="py-2">{{ financial_year.year_name }}</td>
                    </tr>
                    <tr class="border-b">
                        <td class="py-2 font-semibold text-gray-600">Company:</td>
                        <td class="py-2">{{ financial_year.compid|default:"Not assigned" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 