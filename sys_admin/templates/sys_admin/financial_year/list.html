{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Financial Years</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'sysadmin:financial_year_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Financial Year
        </button>
    </div>
    
    {# Messages display #}
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full" id="financialYearTable">
            <thead>
                <tr class="bg-gray-100 border-b">
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for year in financial_years %}
                <tr class="border-b hover:bg-gray-50">
                    <td class="py-3 px-4">{{ year.finyearid }}</td>
                    <td class="py-3 px-4">{{ year.finyear }}</td>
                    <td class="py-3 px-4">{{ year.start_date|date:"M d, Y" }}</td>
                    <td class="py-3 px-4">{{ year.end_date|date:"M d, Y" }}</td>
                    <td class="py-3 px-4">
                        {% if year.is_current %}
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                        {% else %}
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">No</span>
                        {% endif %}
                    </td>
                    <td class="py-3 px-4">{{ year.duration_days }} days</td>
                    <td class="py-3 px-4 flex space-x-2">
                        <a href="{% url 'sys_admin:financial_year_detail' year.pk %}" class="bg-blue-500 hover:bg-blue-700 text-white py-1 px-2 rounded text-sm">View</a>
                        <a href="{% url 'sys_admin:financial_year_edit' year.pk %}" class="bg-yellow-500 hover:bg-yellow-700 text-white py-1 px-2 rounded text-sm">Edit</a>
                        <a href="{% url 'sys_admin:financial_year_delete' year.pk %}" class="bg-red-500 hover:bg-red-700 text-white py-1 px-2 rounded text-sm">Delete</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="py-4 px-4 text-center text-gray-500">No financial years found. Click 'Add New Financial Year' to create one.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Modal for forms -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4">
        <!-- Content loaded via HTMX -->
    </div>
</div>
{% endblock %}

{% block body_extra %}
<script>
    $(document).ready(function() {
        $('#financialYearTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [[2, "desc"]]  // Sort by start date, newest first
        });
    });
</script>
{% endblock %} 