{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Confirm Delete</h2>
        <a href="{% url 'sysadmin:financial_year_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to List
        </a>
    </div>
    
    <div class="bg-white shadow-md rounded-lg overflow-hidden p-6">
        <div class="text-center mb-6">
            <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 text-red-500 mb-4">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium mb-2">Are you sure you want to delete this financial year?</h3>
            <p class="text-gray-600 mb-4">
                This will permanently delete financial year <strong>{{ object.finyear }}</strong> ({{ object.start_date|date:"M d, Y" }} - {{ object.end_date|date:"M d, Y" }}).
                {% if object.is_current %}
                <span class="text-red-500 font-bold block mt-2">Warning: This is currently set as the current financial year!</span>
                {% endif %}
            </p>
        </div>
        
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <h4 class="font-medium text-gray-700 mb-2">Financial Year Details:</h4>
            <ul class="list-disc list-inside text-gray-600 space-y-1">
                <li>ID: {{ object.finyearid }}</li>
                <li>Financial Year: {{ object.finyear }}</li>
                <li>Start Date: {{ object.start_date|date:"F d, Y" }}</li>
                <li>End Date: {{ object.end_date|date:"F d, Y" }}</li>
                <li>Duration: {{ object.duration_days }} days</li>
            </ul>
        </div>
        
        <form method="post" class="flex justify-center space-x-4">
            {% csrf_token %}
            <a href="{% url 'sys_admin:financial_year_list' %}" class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                Cancel
            </a>
            <button type="submit" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                Delete
            </button>
        </form>
    </div>
</div>
{% endblock %} 