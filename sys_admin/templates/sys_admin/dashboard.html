{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">System Administration Dashboard</h1>
        <p class="text-gray-600">Manage countries, states, cities, companies, and financial years</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-globe text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">Countries</h3>
                    <p class="text-2xl font-bold text-blue-600">{{ total_countries }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-map text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">States</h3>
                    <p class="text-2xl font-bold text-green-600">{{ total_states }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-city text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">Cities</h3>
                    <p class="text-2xl font-bold text-yellow-600">{{ total_cities }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-building text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">Companies</h3>
                    <p class="text-2xl font-bold text-purple-600">{{ total_companies }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-calendar text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">Financial Years</h3>
                    <p class="text-2xl font-bold text-red-600">{{ total_financial_years }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 gap-4">
                <a href="{% url 'sys_admin:country_list' %}" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg text-center transition duration-200">
                    <i class="fas fa-globe mb-2 block"></i>
                    Manage Countries
                </a>
                <a href="{% url 'sys_admin:state_list' %}" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg text-center transition duration-200">
                    <i class="fas fa-map mb-2 block"></i>
                    Manage States
                </a>
                <a href="{% url 'sys_admin:city_list' %}" class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-4 rounded-lg text-center transition duration-200">
                    <i class="fas fa-city mb-2 block"></i>
                    Manage Cities
                </a>
                <a href="{% url 'sys_admin:financial_year_list' %}" class="bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-4 rounded-lg text-center transition duration-200">
                    <i class="fas fa-calendar mb-2 block"></i>
                    Financial Years
                </a>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Current Financial Year</h2>
            {% if current_financial_year %}
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-green-800">{{ current_financial_year.year_name }}</h3>
                    <p class="text-green-600">{{ current_financial_year.start_date }} to {{ current_financial_year.end_date }}</p>
                    <p class="text-sm text-green-500 mt-2">Duration: {{ current_financial_year.duration_days }} days</p>
                </div>
            {% else %}
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <p class="text-yellow-800">No current financial year set</p>
                    <a href="{% url 'sys_admin:financial_year_add' %}" class="text-yellow-600 hover:text-yellow-800 underline">Create one now</a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Activity</h2>
        {% if recent_activity_logs %}
            <div class="space-y-3">
                {% for log in recent_activity_logs %}
                <div class="flex items-center justify-between border-b border-gray-200 pb-3">
                    <div>
                        <p class="font-medium text-gray-800">{{ log.action }}</p>
                        <p class="text-sm text-gray-600">by {{ log.user }}</p>
                        {% if log.details %}
                            <p class="text-xs text-gray-500">{{ log.get_summary }}</p>
                        {% endif %}
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">{{ log.timestamp|date:"M d, Y" }}</p>
                        <p class="text-xs text-gray-400">{{ log.timestamp|time:"H:i" }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
            <div class="mt-4 text-center">
                <a href="{% url 'sys_admin:activity_log_list' %}" class="text-blue-600 hover:text-blue-800 underline">View all activity</a>
            </div>
        {% else %}
            <p class="text-gray-500">No recent activity</p>
        {% endif %}
    </div>
</div>
{% endblock %}
