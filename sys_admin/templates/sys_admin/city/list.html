{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">City Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-200"
            hx-get="{% url 'sysadmin:city_add' %}{% if request.GET.state %}{% if request.GET.state != '0' %}?state_id={{ request.GET.state }}{% endif %}{% endif %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal remove .hidden from #modal">
            Add New City
        </button>
    </div>
    
    {# Country and State filter dropdowns #}
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form class="flex flex-col md:flex-row gap-4 items-center">
            <div class="flex-grow w-full md:w-auto">
                <label for="id_country" class="block text-sm font-medium text-gray-700">Country:</label>
                <select name="country" id="id_country"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        hx-get="{% url 'sysadmin:states_by_country' 0 %}" {# Initial placeholder URL #}
                        hx-target="#state-dropdown-container"
                        hx-trigger="change, load from:body"
                        hx-swap="outerHTML">
                    <option value="0">Select Country</option>
                    {% for country in filter_form.country.field.queryset %}
                    <option value="{{ country.pk }}" {% if filter_form.country.value == country.pk|stringformat:"s" %}selected{% endif %}>{{ country.countryname }}</option>
                    {% endfor %}
                </select>
            </div>
            <div id="state-dropdown-container" class="flex-grow w-full md:w-auto">
                {# State dropdown will be loaded here via HTMX #}
                {% include 'sys_admin/city/_state_dropdown.html' with form=filter_form selected_state_id=request.GET.state %}
            </div>
            {# No explicit submit button for filter, changes trigger HTMX directly #}
        </form>
    </div>

    <div id="cityTable-container"
         hx-trigger="load, refreshCityList from:body"
         hx-get="{% url 'sysadmin:city_table' %}{% if request.GET.country %}?country={{ request.GET.country }}{% endif %}{% if request.GET.state %}{% if request.GET.state != '0' %}&state={{ request.GET.state }}{% endif %}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8 bg-white rounded-lg shadow-lg">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading cities...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0">
            <!-- Content will be loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Ensure stable table layout */
    #cityTable {
        table-layout: fixed !important;
        width: 100% !important;
    }

    /* Fixed column widths to prevent shifting */
    #cityTable th:nth-child(1), #cityTable td:nth-child(1) { width: 8% !important; }
    #cityTable th:nth-child(2), #cityTable td:nth-child(2) { width: 25% !important; }
    #cityTable th:nth-child(3), #cityTable td:nth-child(3) { width: 25% !important; }
    #cityTable th:nth-child(4), #cityTable td:nth-child(4) { width: 22% !important; }
    #cityTable th:nth-child(5), #cityTable td:nth-child(5) { width: 20% !important; }

    /* Prevent text overflow and maintain layout */
    #cityTable td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* DataTables wrapper styling */
    .dataTables_wrapper {
        overflow-x: auto;
    }

    /* Fixed columns styling */
    .dtfc-fixed-left {
        background-color: #f9fafb !important;
        border-right: 2px solid #e5e7eb !important;
    }

    /* Enhanced typography for better readability */
    #cityTable {
        font-family: 'Inter', system-ui, -apple-system, sans-serif !important;
    }

    /* DataTables search and pagination styling */
    .dataTables_wrapper .dataTables_filter input {
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 8px 12px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 6px !important;
    }

    .dataTables_wrapper .dataTables_length select {
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 6px 10px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 6px !important;
    }

    .dataTables_wrapper .dataTables_info {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #374151 !important;
    }

    /* Pagination buttons */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        font-size: 14px !important;
        font-weight: 600 !important;
        padding: 8px 12px !important;
        margin: 0 2px !important;
        border-radius: 6px !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTable function
    function initializeCityTable() {
        // Wait for the table to be in the DOM
        setTimeout(function() {
            if ($.fn.DataTable && $('#cityTable').length > 0) {
                // Destroy existing instance if it exists
                if ($.fn.DataTable.isDataTable('#cityTable')) {
                    $('#cityTable').DataTable().destroy();
                }

                // Initialize new DataTable with stable column configuration
                $('#cityTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": false, // Disable responsive to prevent column rearrangement
                    "scrollX": true, // Enable horizontal scrolling instead of responsive
                    "autoWidth": false, // Disable auto width calculation
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 4] }, // Disable sorting for SN and Actions columns
                        { "width": "8%", "targets": 0 }, // SN column
                        { "width": "25%", "targets": 1 }, // City Name column
                        { "width": "25%", "targets": 2 }, // State column
                        { "width": "22%", "targets": 3 }, // Country column
                        { "width": "20%", "targets": 4 } // Actions column
                    ],
                    "fixedColumns": {
                        "leftColumns": 1 // Keep SN column fixed
                    },
                    "language": {
                        "search": "Search cities:",
                        "lengthMenu": "Show _MENU_ cities per page",
                        "info": "Showing _START_ to _END_ of _TOTAL_ cities",
                        "infoFiltered": "(filtered from _MAX_ total cities)"
                    }
                });
            }
        }, 100);
    }

    // Handle modal visibility
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
        
        // Initialize DataTable after HTMX swap
        if (event.detail.target.id === 'cityTable-container') {
            initializeCityTable();
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        if (event.detail.xhr && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').classList.add('hidden');
        }
    });
    
    // Handle modal close on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').classList.add('hidden');
        }
    });
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeCityTable();
    });
</script>
{% endblock %}
