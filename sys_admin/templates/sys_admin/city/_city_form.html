<div class="p-6" x-data="{}">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} City</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.cityname.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.cityname.label }}
                </label>
                {{ form.cityname }}
                {% if form.cityname.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.cityname.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.sid.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.sid.label }}
                </label>
                {{ form.sid }}
                {% if form.sid.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.sid.errors.0 }}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                onclick="document.getElementById('modal').classList.remove('is-active'); document.getElementById('modal').classList.add('hidden');">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
            <span id="form-loading-indicator" class="htmx-indicator ml-4 text-blue-600">
                <i class="fas fa-spinner fa-spin"></i> Saving...
            </span>
        </div>
    </form>
</div>
