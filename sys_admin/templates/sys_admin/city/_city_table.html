<table id="cityTable" class="min-w-full bg-white border-collapse table-fixed">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">City Name</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">State</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">Country</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-center text-sm font-bold text-gray-800 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for city in cities %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
            <td class="py-3 px-4 border-b border-gray-200 text-base font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-base font-semibold text-gray-900">{{ city.cityname }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-base font-medium text-gray-700">{{ city.sid.statename|default:"-" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-base font-medium text-gray-700">{{ city.sid.cid.countryname|default:"-" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                    hx-get="{% url 'sysadmin:city_edit' city.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal remove .hidden from #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'sysadmin:city_delete' city.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
        {% if not cities %}
        <tr>
            <td colspan="5" class="py-6 px-4 border-b border-gray-200 text-center text-base font-medium text-gray-600">No cities found. Click 'Add New City' to create one.</td>
        </tr>
        {% endif %}
    </tbody>
</table>

