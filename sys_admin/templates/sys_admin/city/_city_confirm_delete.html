<div class="p-6" x-data="{}">
    <h3 class="text-xl font-semibold text-red-700 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the city: <span class="font-bold">{{ object.cityname }}</span>?</p>
    <p class="text-red-600 mb-8">This action cannot be undone.</p>
    
    <form hx-post="{% url 'sysadmin:city_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                onclick="document.getElementById('modal').classList.remove('is-active'); document.getElementById('modal').classList.add('hidden');">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
