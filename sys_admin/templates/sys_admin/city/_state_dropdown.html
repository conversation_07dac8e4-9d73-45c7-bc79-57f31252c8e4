<div id="state-dropdown-container" class="flex-grow w-full md:w-auto">
    <label for="id_state" class="block text-sm font-medium text-gray-700">State:</label>
    <select name="state" id="id_state"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            hx-get="{% url 'sysadmin:city_table' %}"
            hx-target="#cityTable-container"
            hx-trigger="change"
            hx-include="[name='country']"
            hx-swap="innerHTML">
        <option value="0">Select State</option>
        {% for state in form.state.field.queryset %}
        <option value="{{ state.pk }}" {% if selected_state_id == state.pk|stringformat:"s" %}selected{% endif %}>{{ state.statename }}</option>
        {% endfor %}
    </select>
</div>
