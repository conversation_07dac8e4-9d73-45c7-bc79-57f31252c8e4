{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Countries</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'sysadmin:country_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal remove .hidden from #modal">
            <i class="fas fa-plus mr-2"></i>Add New Country
        </button>
    </div>
    
    <!-- Container for HTMX-loaded DataTables -->
    <div id="countryTable-container"
         hx-trigger="load, refreshCountryList from:body"
         hx-get="{% url 'sysadmin:country_table' %}"
         hx-swap="innerHTML">
        <!-- Loading spinner while content loads -->
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading countries...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me add .hidden to me">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:w-full">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced typography for better readability */
    #countryTable {
        font-family: 'Inter', system-ui, -apple-system, sans-serif !important;
    }

    /* DataTables search and pagination styling */
    .dataTables_wrapper .dataTables_filter input {
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 8px 12px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 6px !important;
    }

    .dataTables_wrapper .dataTables_length select {
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 6px 10px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 6px !important;
    }

    .dataTables_wrapper .dataTables_info {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #374151 !important;
    }

    /* Pagination buttons */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        font-size: 14px !important;
        font-weight: 600 !important;
        padding: 8px 12px !important;
        margin: 0 2px !important;
        border-radius: 6px !important;
    }

    /* Fixed columns styling */
    .dtfc-fixed-left {
        background-color: #f9fafb !important;
        border-right: 2px solid #e5e7eb !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTable function
    function initializeCountryTable() {
        // Wait for the table to be in the DOM
        setTimeout(function() {
            if ($.fn.DataTable && $('#countryTable').length > 0) {
                // Destroy existing instance if it exists
                if ($.fn.DataTable.isDataTable('#countryTable')) {
                    $('#countryTable').DataTable().destroy();
                }

                // Initialize new DataTable with stable column configuration
                $('#countryTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": false, // Disable responsive to prevent column rearrangement
                    "scrollX": true, // Enable horizontal scrolling instead of responsive
                    "autoWidth": false, // Disable auto width calculation
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 5] }, // Disable sorting for SN and Actions columns
                        { "width": "8%", "targets": 0 }, // SN column
                        { "width": "20%", "targets": 1 }, // Country Name column
                        { "width": "15%", "targets": 2 }, // Country Code column
                        { "width": "15%", "targets": 3 }, // Currency column
                        { "width": "22%", "targets": 4 }, // Description column
                        { "width": "20%", "targets": 5 } // Actions column
                    ],
                    "fixedColumns": {
                        "leftColumns": 1 // Keep SN column fixed
                    },
                    "language": {
                        "search": "Search countries:",
                        "lengthMenu": "Show _MENU_ countries per page",
                        "info": "Showing _START_ to _END_ of _TOTAL_ countries",
                        "infoFiltered": "(filtered from _MAX_ total countries)"
                    }
                });
            }
        }, 100);
    }

    // Modal handling
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            // Show the modal
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
        
        // Initialize DataTable after HTMX swap
        if (event.detail.target.id === 'countryTable-container') {
            initializeCountryTable();
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        // If successful submission (204 response), hide modal
        if (event.detail.xhr && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').classList.add('hidden');
        }
    });
    
    // Handle modal close on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').classList.add('hidden');
        }
    });
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeCountryTable();
    });
</script>
{% endblock %}
