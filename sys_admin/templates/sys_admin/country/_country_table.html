{% load static %}
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative"
     x-data="{}"> {# Empty Alpine data scope for element, ensures Alpine processes it #}
    <table id="countryTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">Country Name</th>
                <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">Currency</th>
                <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-center text-sm font-bold text-gray-800 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-center text-sm font-bold text-gray-800 uppercase tracking-wider">Key Shortcut</th>
                <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-center text-sm font-bold text-gray-800 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for country in countries %}
            <tr class="hover:bg-gray-50 transition-colors duration-150">
                <td class="py-3 px-4 border-b border-gray-200 bg-white text-base font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 bg-white text-base font-semibold text-gray-900">{{ country.countryname }}</td>
                <td class="py-3 px-4 border-b border-gray-200 bg-white text-base font-medium text-gray-700">{{ country.currency }}</td>
                <td class="py-3 px-4 border-b border-gray-200 bg-white text-base font-medium text-gray-700 text-center">{{ country.symbol }}</td>
                <td class="py-3 px-4 border-b border-gray-200 bg-white text-base font-medium text-gray-700 text-center">{{ country.keyshortcut|default:"-" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 bg-white text-center">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'sysadmin:country_edit' country.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal remove .hidden from #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'sysadmin:country_delete' country.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal remove .hidden from #modal"
                        hx-confirm="Are you sure you want to delete '{{ country.countryname }}'? This action cannot be undone.">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
            {% if not countries %}
            <tr>
                <td colspan="6" class="py-6 px-4 border-b border-gray-200 bg-white text-base font-medium text-center text-gray-600">No countries found. Click 'Add New Country' to create one.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

