<table id="stateTable" class="min-w-full bg-white border-collapse table-fixed">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">Country Name</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">State Name</th>
            <th class="py-3 px-4 border-b-2 border-gray-300 bg-gray-100 text-center text-sm font-bold text-gray-800 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for state in states %}
        <tr class="hover:bg-gray-50 transition-colors duration-150">
            <td class="py-3 px-4 border-b border-gray-200 text-base font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-base font-medium text-gray-700">{{ state.cid.countryname|default:"-" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-base font-semibold text-gray-900">{{ state.statename }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                    hx-get="{% url 'sysadmin:state_edit' state.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal remove .hidden from #modal">
                    Edit
                </button>
                {% if state.can_be_deleted %}
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'sysadmin:state_delete' state.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal remove .hidden from #modal">
                    Delete
                </button>
                {% else %}
                <span class="text-gray-500 text-sm italic py-1 px-2 rounded">Delete blocked (has cities)</span>
                {% endif %}
            </td>
        </tr>
        {% endfor %}
        {% if not states %}
        <tr>
            <td colspan="4" class="py-2 px-4 border-b border-gray-200 text-center text-gray-500">No states found. Click 'Add New State' to create one.</td>
        </tr>
        {% endif %}
    </tbody>
</table>

