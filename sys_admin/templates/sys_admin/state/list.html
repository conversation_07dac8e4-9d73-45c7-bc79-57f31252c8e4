{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">States</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'sysadmin:state_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal remove .hidden from #modal">
            Add New State
        </button>
    </div>
    
    {# Messages display (similar to asp:Label lblMessage) #}
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="stateTable-container"
         hx-trigger="load, refreshStateList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'sysadmin:state_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading States...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4">
            <!-- Content loaded via HTMX (form or delete confirmation) -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Ensure stable table layout */
    #stateTable {
        table-layout: fixed !important;
        width: 100% !important;
    }

    /* Fixed column widths to prevent shifting */
    #stateTable th:nth-child(1), #stateTable td:nth-child(1) { width: 10% !important; }
    #stateTable th:nth-child(2), #stateTable td:nth-child(2) { width: 35% !important; }
    #stateTable th:nth-child(3), #stateTable td:nth-child(3) { width: 35% !important; }
    #stateTable th:nth-child(4), #stateTable td:nth-child(4) { width: 20% !important; }

    /* Prevent text overflow and maintain layout */
    #stateTable td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* DataTables wrapper styling */
    .dataTables_wrapper {
        overflow-x: auto;
    }

    /* Fixed columns styling */
    .dtfc-fixed-left {
        background-color: #f9fafb !important;
        border-right: 2px solid #e5e7eb !important;
    }

    /* Enhanced typography for better readability */
    #stateTable {
        font-family: 'Inter', system-ui, -apple-system, sans-serif !important;
    }

    /* DataTables search and pagination styling */
    .dataTables_wrapper .dataTables_filter input {
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 8px 12px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 6px !important;
    }

    .dataTables_wrapper .dataTables_length select {
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 6px 10px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 6px !important;
    }

    .dataTables_wrapper .dataTables_info {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #374151 !important;
    }

    /* Pagination buttons */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        font-size: 14px !important;
        font-weight: 600 !important;
        padding: 8px 12px !important;
        margin: 0 2px !important;
        border-radius: 6px !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTable function
    function initializeStateTable() {
        // Wait for the table to be in the DOM
        setTimeout(function() {
            if ($.fn.DataTable && $('#stateTable').length > 0) {
                // Destroy existing instance if it exists
                if ($.fn.DataTable.isDataTable('#stateTable')) {
                    $('#stateTable').DataTable().destroy();
                }

                // Initialize new DataTable with stable column configuration
                $('#stateTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": false, // Disable responsive to prevent column rearrangement
                    "scrollX": true, // Enable horizontal scrolling instead of responsive
                    "autoWidth": false, // Disable auto width calculation
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 3] }, // Disable sorting for SN and Actions columns
                        { "width": "10%", "targets": 0 }, // SN column
                        { "width": "35%", "targets": 1 }, // Country Name column
                        { "width": "35%", "targets": 2 }, // State Name column
                        { "width": "20%", "targets": 3 } // Actions column
                    ],
                    "fixedColumns": {
                        "leftColumns": 1 // Keep SN column fixed
                    },
                    "language": {
                        "search": "Search states:",
                        "lengthMenu": "Show _MENU_ states per page",
                        "info": "Showing _START_ to _END_ of _TOTAL_ states",
                        "infoFiltered": "(filtered from _MAX_ total states)"
                    }
                });
            }
        }, 100);
    }

    // Modal handling
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            // Show the modal
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
        
        // Initialize DataTable after HTMX swap
        if (event.detail.target.id === 'stateTable-container') {
            initializeStateTable();
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        // If successful submission (204 response), hide modal
        if (event.detail.xhr && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').classList.add('hidden');
        }
    });
    
    // Handle modal close on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').classList.add('hidden');
        }
    });
    
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeStateTable();
    });
</script>
{% endblock %}
