from django.urls import path
from . import views

app_name = "sysadmin"

urlpatterns = [
    # Dashboard
    path("", views.DashboardView.as_view(), name="dashboard"),
    # Country URLs
    path("countries/", views.CountryListView.as_view(), name="country_list"),
    path("countries/add/", views.CountryCreateView.as_view(), name="country_add"),
    path(
        "countries/<int:pk>/edit/",
        views.CountryUpdateView.as_view(),
        name="country_edit",
    ),
    path(
        "countries/<int:pk>/delete/",
        views.CountryDeleteView.as_view(),
        name="country_delete",
    ),
    # HTMX-specific endpoint for refreshing the country table content
    path(
        "countries/table/",
        views.CountryTablePartialView.as_view(),
        name="country_table",
    ),
    # State URLs
    path("states/", views.StateListView.as_view(), name="state_list"),
    path("states/add/", views.StateCreateView.as_view(), name="state_add"),
    path("states/<int:pk>/edit/", views.StateUpdateView.as_view(), name="state_edit"),
    path(
        "states/<int:pk>/delete/", views.StateDeleteView.as_view(), name="state_delete"
    ),
    # HTMX-specific endpoint for refreshing the state table content
    path("states/table/", views.StateTablePartialView.as_view(), name="state_table"),
    # City URLs
    path("cities/", views.CityListView.as_view(), name="city_list"),
    path("cities/add/", views.CityCreateView.as_view(), name="city_add"),
    path("cities/<int:pk>/edit/", views.CityUpdateView.as_view(), name="city_edit"),
    path("cities/<int:pk>/delete/", views.CityDeleteView.as_view(), name="city_delete"),
    # HTMX-specific endpoints for city filtering
    path("cities/table/", views.CityTablePartialView.as_view(), name="city_table"),
    path(
        "cities/states/<int:country_id>/",
        views.StateDropdownPartialView.as_view(),
        name="states_by_country",
    ),
    # Financial Year URLs
    path(
        "financial-years/",
        views.FinancialYearListView.as_view(),
        name="financial_year_list",
    ),
    path(
        "financial-years/<int:pk>/",
        views.FinancialYearDetailView.as_view(),
        name="financial_year_detail",
    ),
    path(
        "financial-years/add/",
        views.FinancialYearCreateView.as_view(),
        name="financial_year_add",
    ),
    path(
        "financial-years/<int:pk>/edit/",
        views.FinancialYearUpdateView.as_view(),
        name="financial_year_edit",
    ),
    path(
        "financial-years/<int:pk>/delete/",
        views.FinancialYearDeleteView.as_view(),
        name="financial_year_delete",
    ),
    # Activity Log URLs - Commented out as requested
    # path(
    #     "activity-logs/", views.ActivityLogListView.as_view(), name="activity_log_list"
    # ),
    # path(
    #     "activity-logs/table/",
    #     views.ActivityLogTableView.as_view(),
    #     name="activity_log_table",
    # ),
    # path(
    #     "activity-logs/<int:pk>/",
    #     views.ActivityLogDetailView.as_view(),
    #     name="activity_log_detail",
    # ),
    # path(
    #     "activity-logs/add/",
    #     views.ActivityLogCreateView.as_view(),
    #     name="activity_log_add",
    # ),
    # path(
    #     "activity-logs/<int:pk>/edit/",
    #     views.ActivityLogUpdateView.as_view(),
    #     name="activity_log_edit",
    # ),
    # path(
    #     "activity-logs/<int:pk>/delete/",
    #     views.ActivityLogDeleteView.as_view(),
    #     name="activity_log_delete",
    # ),
]
