from django.contrib import admin
from .models import Country, State, City, Company, FinancialYear


@admin.register(Country)
class CountryAdmin(admin.ModelAdmin):
    list_display = ("cid", "countryname", "currency", "symbol")
    search_fields = ("countryname", "currency")
    list_filter = ("currency",)
    ordering = ("countryname",)


@admin.register(State)
class StateAdmin(admin.ModelAdmin):
    list_display = ("sid", "statename", "cid")
    search_fields = ("statename",)
    list_filter = ("cid",)
    autocomplete_fields = ("cid",)


@admin.register(City)
class CityAdmin(admin.ModelAdmin):
    list_display = ("cityid", "cityname", "sid")
    search_fields = ("cityname",)
    list_filter = ("sid",)
    autocomplete_fields = ("sid",)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ("compid", "companyname", "regdcity", "regdemail", "regdcontactno")
    search_fields = ("companyname", "regdemail", "regdcontactno")
    fieldsets = (
        ("Basic Information", {"fields": ("companyname", "prefix", "defaultcomp")}),
        (
            "Registered Office",
            {
                "fields": (
                    "regdaddress",
                    "regdcity",
                    "regdstate",
                    "regdcountry",
                    "regdpincode",
                    "regdcontactno",
                    "regdfaxno",
                    "regdemail",
                )
            },
        ),
        (
            "Plant Address",
            {
                "fields": (
                    "plantaddress",
                    "plantcity",
                    "plantstate",
                    "plantcountry",
                    "plantpincode",
                    "plantcontactno",
                    "plantfaxno",
                    "plantemail",
                )
            },
        ),
        (
            "Tax & Registration Details",
            {
                "fields": (
                    "eccno",
                    "commissionerate",
                    "range",
                    "division",
                    "locationno",
                    "vat",
                    "cstno",
                    "panno",
                    "licencenos",
                )
            },
        ),
        (
            "System Configuration",
            {
                "fields": (
                    "logoimage",
                    "logofilename",
                    "mailserverip",
                    "serverip",
                    "erpsysmail",
                    "mobileno",
                    "itemcodelimit",
                )
            },
        ),
    )


@admin.register(FinancialYear)
class FinancialYearAdmin(admin.ModelAdmin):
    list_display = (
        "finyearid",
        "finyear",
        "start_date",
        "end_date",
        "is_current",
        "display_duration",
    )
    search_fields = ("finyear",)
    list_filter = ("is_current", "compid")
    ordering = ("-start_date",)
    readonly_fields = ("year_name", "duration_days")

    def display_duration(self, obj):
        return f"{obj.duration_days} days"

    display_duration.short_description = "Duration"
