from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import (
    ListView,
    DetailView,
    CreateView,
    UpdateView,
    DeleteView,
    View,
)
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.utils import timezone
from django.template.loader import render_to_string
from django.db.models import ProtectedError

from .models import Country, State, City, Company, FinancialYear
from .forms import (
    CountryForm,
    StateForm,
    CityForm,
    CityFilterForm,
    CompanyForm,
    FinancialYearForm,
)


# Country Views
class CountryListView(ListView):
    """
    Main view to display the list of Countries.
    This serves the initial page load.
    """

    model = Country
    template_name = "sys_admin/country/list.html"
    context_object_name = "countries"


class CountryTablePartialView(ListView):
    """
    Renders only the HTML table for Countries.
    This view is specifically designed to be loaded via HTMX
    to refresh the table content dynamically.
    """

    model = Country
    template_name = "sys_admin/country/_country_table.html"
    context_object_name = "countries"


class CountryCreateView(CreateView):
    """
    Handles creation of new Country objects.
    Uses a modal form loaded via HTMX.
    """

    model = Country
    form_class = CountryForm
    template_name = "sys_admin/country/_country_form.html"  # Partial template for modal
    success_url = reverse_lazy("sys_admin:country_list")  # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, "Country added successfully.")
        if self.request.headers.get("HX-Request"):
            # For HTMX requests, return a 204 No Content response
            # with an HX-Trigger header to signal the client to refresh the list.
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCountryList"}
            )
        return response

    def form_invalid(self, form):
        # For HTMX requests, re-render the form with errors
        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                render_to_string(
                    self.template_name, {"form": form}, request=self.request
                ),
                status=400,
            )
        return super().form_invalid(form)


class CountryUpdateView(UpdateView):
    """
    Handles updating existing Country objects.
    Uses a modal form loaded via HTMX.
    """

    model = Country
    form_class = CountryForm
    template_name = "sys_admin/country/_country_form.html"  # Partial template for modal
    success_url = reverse_lazy("sys_admin:country_list")  # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, "Country updated successfully.")
        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCountryList"}
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                render_to_string(
                    self.template_name,
                    {"form": form, "object": form.instance},
                    request=self.request,
                ),
                status=400,
            )
        return super().form_invalid(form)


class CountryDeleteView(DeleteView):
    """
    Handles deletion of Country objects.
    Uses a confirmation modal loaded via HTMX.
    """

    model = Country
    template_name = "sys_admin/country/_country_confirm_delete.html"  # Partial template
    success_url = reverse_lazy("sys_admin:country_list")  # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, "Country deleted successfully.")
        if request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCountryList"}
            )
        return response

    def get(self, request, *args, **kwargs):
        # This handles the GET request for showing the delete confirmation in the modal.
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return self.render_to_response(context)


# State Views
class StateListView(ListView):
    """
    Main view to display the list of States.
    This serves the initial page load.
    """

    model = State
    template_name = "sys_admin/state/list.html"
    context_object_name = "states"


class StateTablePartialView(ListView):
    """
    Renders only the HTML table for States.
    This view is specifically designed to be loaded via HTMX
    to refresh the table content dynamically.
    """

    model = State
    template_name = "sys_admin/state/_state_table.html"
    context_object_name = "states"


class StateCreateView(CreateView):
    """
    Handles creation of new State objects.
    Uses a modal form loaded via HTMX.
    """

    model = State
    form_class = StateForm
    template_name = "sys_admin/state/_state_form.html"  # Partial template for modal
    success_url = reverse_lazy("sys_admin:state_list")  # Fallback if not HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass a flag to the template to indicate this is an Add form
        context["is_add_form"] = True
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, "State added successfully.")
        if self.request.headers.get("HX-Request"):
            return HttpResponse(status=204, headers={"HX-Trigger": "refreshStateList"})
        return response

    def form_invalid(self, form):
        # For HTMX requests, re-render the form with errors
        if self.request.headers.get("HX-Request"):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class StateUpdateView(UpdateView):
    """
    Handles updating existing State objects.
    Uses a modal form loaded via HTMX.
    """

    model = State
    form_class = StateForm
    template_name = "sys_admin/state/_state_form.html"  # Partial template for modal
    context_object_name = "state"
    success_url = reverse_lazy("sys_admin:state_list")  # Fallback if not HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass a flag to the template to indicate this is an Edit form
        context["is_add_form"] = False
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, "State updated successfully.")
        if self.request.headers.get("HX-Request"):
            return HttpResponse(status=204, headers={"HX-Trigger": "refreshStateList"})
        return response

    def form_invalid(self, form):
        if self.request.headers.get("HX-Request"):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class StateDeleteView(DeleteView):
    """
    Handles deletion of State objects.
    Uses a confirmation modal loaded via HTMX.
    Includes business logic to prevent deletion if related City records exist.
    """

    model = State
    template_name = "sys_admin/state/_state_confirm_delete.html"  # Partial template
    context_object_name = "state"
    success_url = reverse_lazy("sys_admin:state_list")  # Fallback if not HTMX

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Business logic from ASP.NET: check if state can be deleted
        if not self.object.can_be_deleted():
            messages.error(
                self.request,
                f'Cannot delete State "{self.object.statename}" as it has associated cities.',
            )
            if request.headers.get("HX-Request"):
                # For HTMX, return a 200 OK and re-render the modal with error message
                return self.render_to_response(
                    self.get_context_data(object=self.object)
                )
            # For non-HTMX, redirect back to list
            return HttpResponseRedirect(self.get_success_url())

        try:
            # Proceed with deletion if allowed
            response = self.delete(request, *args, **kwargs)
            messages.success(self.request, "State deleted successfully.")
            if request.headers.get("HX-Request"):
                return HttpResponse(
                    status=204, headers={"HX-Trigger": "refreshStateList"}
                )
            return response
        except ProtectedError:
            # Django's ProtectedError for DO_NOTHING if other relations exist
            messages.error(
                self.request,
                f'Cannot delete State "{self.object.statename}" due to existing related records.',
            )
            if request.headers.get("HX-Request"):
                return self.render_to_response(
                    self.get_context_data(object=self.object)
                )
            return HttpResponseRedirect(self.get_success_url())

    def delete(self, request, *args, **kwargs):
        # Override delete to ensure messages are handled before actual delete
        # The main logic is in post()
        return super().delete(request, *args, **kwargs)


# City Views
class CityListView(ListView):
    """
    Main view to display the list of Cities with filtering.
    This serves the initial page load.
    """

    model = City
    template_name = "sys_admin/city/list.html"
    context_object_name = "cities"

    def get_queryset(self):
        # The actual city data for the table is fetched by CityTablePartialView.
        # This queryset is largely for initial setup or if not using HTMX for initial load.
        return City.objects.none()  # Return empty initially, HTMX will populate

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize filter form with current GET parameters for stickiness
        filter_form = CityFilterForm(self.request.GET)
        context["filter_form"] = filter_form
        return context


class CityTablePartialView(ListView):
    """
    Renders only the HTML table for Cities.
    This view is specifically designed to be loaded via HTMX
    to refresh the table content dynamically.
    """

    model = City
    template_name = "sys_admin/city/_city_table.html"
    context_object_name = "cities"

    def get_queryset(self):
        state_id = self.request.GET.get(
            "state", "0"
        )  # Default to '0' to load all as in ASP.NET
        return City.get_filtered_cities(state_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Re-initialize the filter form to pass selected values to the partial
        state_id = self.request.GET.get("state", "0")
        country_id = self.request.GET.get("country", "0")
        initial_data = {}
        try:
            if country_id != "0":
                initial_data["country"] = Country.objects.get(cid=int(country_id))
            if state_id != "0":
                initial_data["state"] = State.objects.get(sid=int(state_id))
        except (ValueError, Country.DoesNotExist, State.DoesNotExist):
            pass  # Invalid IDs
        context["filter_form"] = CityFilterForm(initial=initial_data)
        return context


class StateDropdownPartialView(View):
    """
    HTMX view to dynamically load state dropdown based on selected country.
    """

    def get(self, request, country_id):
        states = State.objects.none()
        if country_id != 0:  # 0 indicates 'Select Country'
            states = State.objects.filter(cid__cid=country_id).order_by("statename")

        # Create a form instance to render just the state dropdown part
        form = CityFilterForm(initial={"country": country_id})
        form.fields["state"].queryset = states

        selected_state_id = request.GET.get(
            "state", ""
        )  # Keep previously selected state if available

        return render(
            request,
            "sys_admin/city/_state_dropdown.html",
            {"form": form, "selected_state_id": selected_state_id},
        )


class CityCreateView(CreateView):
    """
    Handles creation of new City objects.
    Uses a modal form loaded via HTMX.
    """

    model = City
    form_class = CityForm
    template_name = "sys_admin/city/_city_form.html"
    success_url = reverse_lazy("sys_admin:city_list")  # Fallback for non-HTMX requests

    def get_initial(self):
        initial = super().get_initial()
        # Pre-populate the state field in the form if a state is selected in the main filter
        state_id_from_filter = self.request.GET.get("state_id")
        if state_id_from_filter and state_id_from_filter != "0":
            try:
                initial["sid"] = State.objects.get(sid=int(state_id_from_filter))
            except (ValueError, State.DoesNotExist):
                pass
        return initial

    def form_valid(self, form):
        # Delegate business logic to model method
        cityname = form.cleaned_data["cityname"]
        state_instance = form.cleaned_data["sid"]
        City.create_new_city(city_name=cityname, state_id=state_instance.sid)

        messages.success(self.request, "City added successfully.")
        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204,  # No Content response for HTMX success
                headers={
                    "HX-Trigger": "refreshCityList"
                },  # Custom event to refresh the list table
            )
        return super().form_valid(form)  # For non-HTMX (full page reload)

    def form_invalid(self, form):
        # For HTMX, render the form again with errors
        return render(self.request, self.template_name, {"form": form})


class CityUpdateView(UpdateView):
    """
    Handles updating existing City objects.
    Uses a modal form loaded via HTMX.
    """

    model = City
    form_class = CityForm
    template_name = "sys_admin/city/_city_form.html"
    context_object_name = "city"  # For displaying current object in template
    success_url = reverse_lazy("sys_admin:city_list")

    def form_valid(self, form):
        # Delegate business logic to model method
        city_instance = self.get_object()
        cityname = form.cleaned_data["cityname"]
        state_instance = form.cleaned_data["sid"]
        city_instance.update_existing_city(
            city_name=cityname, state_id=state_instance.sid
        )

        messages.success(self.request, "City updated successfully.")
        if self.request.headers.get("HX-Request"):
            return HttpResponse(status=204, headers={"HX-Trigger": "refreshCityList"})
        return super().form_valid(form)

    def form_invalid(self, form):
        # For HTMX, render the form again with errors
        return render(self.request, self.template_name, {"form": form})


class CityDeleteView(DeleteView):
    """
    Handles deletion of City objects.
    Uses a confirmation modal loaded via HTMX.
    """

    model = City
    template_name = "sys_admin/city/_city_confirm_delete.html"
    context_object_name = "city"
    success_url = reverse_lazy("sys_admin:city_list")

    def delete(self, request, *args, **kwargs):
        # Delegate business logic to model method
        city_instance = self.get_object()
        city_instance.delete_existing_city()

        messages.success(self.request, "City deleted successfully.")
        if request.headers.get("HX-Request"):
            return HttpResponse(
                status=204,  # No Content for HTMX success
                headers={"HX-Trigger": "refreshCityList"},
            )
        return super().delete(request, *args, **kwargs)


# Dashboard View
class DashboardView(View):
    """
    Dashboard view providing an overview of the sys_admin module.
    Shows statistics and quick access to main functionalities.
    """

    template_name = "sys_admin/dashboard.html"

    def get(self, request):
        # Gather statistics for the dashboard
        context = {
            "total_countries": Country.objects.count(),
            "total_states": State.objects.count(),
            "total_cities": City.objects.count(),
            "total_companies": Company.objects.count(),
            "total_financial_years": FinancialYear.objects.count(),
            "current_financial_year": FinancialYear.objects.filter(
                is_current=True
            ).first(),
        }
        return render(request, self.template_name, context)


# Financial Year Views
class FinancialYearListView(ListView):
    model = FinancialYear
    template_name = "sys_admin/financial_year/list.html"
    context_object_name = "financial_years"
    ordering = ["-start_date"]


class FinancialYearDetailView(DetailView):
    model = FinancialYear
    template_name = "sys_admin/financial_year/detail.html"
    context_object_name = "financial_year"
    pk_url_kwarg = "pk"


class FinancialYearCreateView(CreateView):
    model = FinancialYear
    form_class = FinancialYearForm
    template_name = "sys_admin/financial_year/form.html"
    success_url = reverse_lazy("sys_admin:financial_year_list")

    def form_valid(self, form):
        messages.success(self.request, "Financial Year created successfully.")
        return super().form_valid(form)


class FinancialYearUpdateView(UpdateView):
    model = FinancialYear
    form_class = FinancialYearForm
    template_name = "sys_admin/financial_year/form.html"
    success_url = reverse_lazy("sys_admin:financial_year_list")
    pk_url_kwarg = "pk"

    def form_valid(self, form):
        messages.success(self.request, "Financial Year updated successfully.")
        return super().form_valid(form)


class FinancialYearDeleteView(DeleteView):
    model = FinancialYear
    template_name = "sys_admin/financial_year/confirm_delete.html"
    success_url = reverse_lazy("sys_admin:financial_year_list")
    pk_url_kwarg = "pk"

    def delete(self, request, *args, **kwargs):
        messages.success(request, "Financial Year deleted successfully.")
        return super().delete(request, *args, **kwargs)
