# Generated by Django 5.2.1 on 2025-05-29 23:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('activityid', models.AutoField(db_column='ActivityId', primary_key=True, serialize=False)),
                ('timestamp', models.DateTimeField(db_column='Timestamp')),
                ('action', models.CharField(db_column='Action', max_length=255)),
                ('user', models.CharField(db_column='User', max_length=255)),
                ('details', models.TextField(blank=True, db_column='Details', null=True)),
            ],
            options={
                'verbose_name': 'Activity Log',
                'verbose_name_plural': 'Activity Logs',
                'db_table': 'tblActivityLog',
                'ordering': ['-timestamp'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='City',
            fields=[
                ('cityid', models.AutoField(db_column='CityId', primary_key=True, serialize=False)),
                ('cityname', models.CharField(db_column='CityName', max_length=255)),
            ],
            options={
                'verbose_name': 'City',
                'verbose_name_plural': 'Cities',
                'db_table': 'tblCity',
                'ordering': ['-cityid'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('compid', models.AutoField(db_column='CompId', primary_key=True, serialize=False)),
                ('sysdate', models.CharField(db_column='SysDate', max_length=50)),
                ('systime', models.CharField(db_column='SysTime', max_length=50)),
                ('companyname', models.TextField(db_column='CompanyName')),
                ('regdaddress', models.TextField(blank=True, db_column='RegdAddress', null=True)),
                ('regdstate', models.IntegerField(db_column='RegdState')),
                ('regdcountry', models.IntegerField(db_column='RegdCountry')),
                ('regdpincode', models.TextField(blank=True, db_column='RegdPinCode', null=True)),
                ('regdcontactno', models.TextField(blank=True, db_column='RegdContactNo', null=True)),
                ('regdfaxno', models.TextField(blank=True, db_column='RegdFaxNo', null=True)),
                ('regdemail', models.TextField(blank=True, db_column='RegdEmail', null=True)),
                ('plantaddress', models.TextField(blank=True, db_column='PlantAddress', null=True)),
                ('plantstate', models.IntegerField(db_column='PlantState')),
                ('plantcountry', models.IntegerField(db_column='PlantCountry')),
                ('plantpincode', models.TextField(blank=True, db_column='PlantPinCode', null=True)),
                ('plantcontactno', models.TextField(blank=True, db_column='PlantContactNo', null=True)),
                ('plantfaxno', models.TextField(blank=True, db_column='PlantFaxNo', null=True)),
                ('plantemail', models.TextField(blank=True, db_column='PlantEmail', null=True)),
                ('eccno', models.TextField(blank=True, db_column='ECCNo', null=True)),
                ('commissionerate', models.TextField(blank=True, db_column='Commissionerate', null=True)),
                ('range', models.TextField(blank=True, db_column='Range', null=True)),
                ('division', models.TextField(blank=True, db_column='Division', null=True)),
                ('locationno', models.TextField(blank=True, db_column='LocationNo', null=True)),
                ('vat', models.TextField(blank=True, db_column='VAT', null=True)),
                ('cstno', models.TextField(blank=True, db_column='CSTNo', null=True)),
                ('panno', models.TextField(blank=True, db_column='PANNo', null=True)),
                ('logoimage', models.BinaryField(blank=True, db_column='LogoImage', null=True)),
                ('logofilename', models.TextField(blank=True, db_column='LogoFileName', null=True)),
                ('prefix', models.TextField(blank=True, db_column='Prefix', null=True)),
                ('licencenos', models.TextField(blank=True, db_column='LicenceNos', null=True)),
                ('defaultcomp', models.IntegerField(blank=True, db_column='DefaultComp', null=True)),
                ('mailserverip', models.TextField(blank=True, db_column='MailServerIp', null=True)),
                ('serverip', models.TextField(blank=True, db_column='ServerIp', null=True)),
                ('flag', models.IntegerField(blank=True, db_column='Flag', null=True)),
                ('itemcodelimit', models.IntegerField(blank=True, db_column='ItemCodeLimit', null=True)),
                ('erpsysmail', models.TextField(blank=True, db_column='ErpSysmail', null=True)),
                ('mobileno', models.TextField(blank=True, db_column='MobileNo', null=True)),
                ('password', models.TextField(blank=True, db_column='Password', null=True)),
            ],
            options={
                'db_table': 'tblCompany_master',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('cid', models.AutoField(db_column='CId', primary_key=True, serialize=False)),
                ('countryname', models.CharField(db_column='CountryName', max_length=255)),
                ('currency', models.CharField(db_column='Currency', max_length=50)),
                ('symbol', models.CharField(db_column='Symbol', max_length=10)),
                ('keyshortcut', models.CharField(blank=True, db_column='KeyShortcut', max_length=10, null=True)),
            ],
            options={
                'verbose_name': 'Country',
                'verbose_name_plural': 'Countries',
                'db_table': 'tblCountry',
                'ordering': ['countryname'],
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='FinancialYear',
            fields=[
                ('finyearid', models.AutoField(db_column='FinYearId', primary_key=True, serialize=False)),
                ('sysdate', models.CharField(db_column='SysDate', max_length=50)),
                ('systime', models.CharField(db_column='SysTime', max_length=50)),
                ('sessionid', models.TextField(db_column='SessionId')),
                ('compid', models.IntegerField(db_column='CompId')),
                ('start_date', models.DateField(blank=True, db_column='FinYearFrom', null=True)),
                ('end_date', models.DateField(blank=True, db_column='FinYearTo', null=True)),
                ('finyear', models.CharField(blank=True, db_column='FinYear', max_length=50, null=True)),
                ('is_current', models.BooleanField(db_column='Flag', default=False)),
            ],
            options={
                'db_table': 'tblFinancial_master',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('sid', models.AutoField(db_column='SId', primary_key=True, serialize=False)),
                ('statename', models.CharField(db_column='StateName', max_length=255)),
            ],
            options={
                'verbose_name': 'State',
                'verbose_name_plural': 'States',
                'db_table': 'tblState',
                'ordering': ['-sid'],
                'managed': False,
            },
        ),
    ]
