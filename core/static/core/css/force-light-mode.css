/* 
 * Force Light Mode CSS
 * Prevents automatic dark mode detection and ensures consistent light theme
 * across all browsers and operating systems
 */

/* Root level dark mode prevention */
:root {
    color-scheme: light only !important;
    --color-scheme: light !important;
}

/* Force light mode even when system prefers dark */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: light only !important;
        --color-scheme: light !important;
    }
    
    /* Base elements */
    html, body {
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }
    
    /* All text elements */
    h1, h2, h3, h4, h5, h6, p, span, div, a, label {
        color: inherit !important;
    }
    
    /* Form elements */
    input, textarea, select, button {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #d1d5db !important;
    }
    
    input:focus, textarea:focus, select:focus {
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }
    
    /* Tables */
    table, th, td {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #e5e7eb !important;
    }
    
    /* DataTables specific overrides */
    .dataTables_wrapper {
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }
    
    .dataTables_wrapper table.dataTable {
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }
    
    .dataTables_wrapper table.dataTable thead th {
        background-color: #f9fafb !important;
        color: #1f2937 !important;
        border-bottom-color: #e5e7eb !important;
    }
    
    .dataTables_wrapper table.dataTable tbody td {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-top-color: #e5e7eb !important;
    }
    
    .dataTables_wrapper table.dataTable tbody tr:hover td {
        background-color: #f3f4f6 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #d1d5db !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background-color: #f3f4f6 !important;
        color: #1f2937 !important;
    }
    
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background-color: #3b82f6 !important;
        color: #ffffff !important;
    }
    
    .dataTables_wrapper .dataTables_filter input {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #d1d5db !important;
    }
    
    .dataTables_wrapper .dataTables_length select {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #d1d5db !important;
    }
    
    /* Bootstrap overrides */
    .btn {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #d1d5db !important;
    }
    
    .btn-primary {
        background-color: #3b82f6 !important;
        color: #ffffff !important;
        border-color: #3b82f6 !important;
    }
    
    .btn-secondary {
        background-color: #6b7280 !important;
        color: #ffffff !important;
        border-color: #6b7280 !important;
    }
    
    .card {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #e5e7eb !important;
    }
    
    .modal-content {
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }
    
    .dropdown-menu {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #e5e7eb !important;
    }
    
    .dropdown-item {
        color: #1f2937 !important;
    }
    
    .dropdown-item:hover {
        background-color: #f3f4f6 !important;
        color: #1f2937 !important;
    }
    
    /* Navigation overrides */
    .navbar {
        background-color: #ffffff !important;
        color: #1f2937 !important;
    }
    
    .navbar-brand {
        color: #1f2937 !important;
    }
    
    .nav-link {
        color: #1f2937 !important;
    }
    
    /* Alert overrides */
    .alert {
        background-color: #ffffff !important;
        color: #1f2937 !important;
        border-color: #e5e7eb !important;
    }
    
    .alert-success {
        background-color: #d1fae5 !important;
        color: #065f46 !important;
        border-color: #a7f3d0 !important;
    }
    
    .alert-danger {
        background-color: #fee2e2 !important;
        color: #991b1b !important;
        border-color: #fecaca !important;
    }
    
    .alert-warning {
        background-color: #fef3c7 !important;
        color: #92400e !important;
        border-color: #fde68a !important;
    }
    
    .alert-info {
        background-color: #dbeafe !important;
        color: #1e40af !important;
        border-color: #93c5fd !important;
    }
}

/* Additional universal overrides */
* {
    color-scheme: light !important;
}

/* Webkit specific overrides */
::-webkit-scrollbar {
    background-color: #f3f4f6 !important;
}

::-webkit-scrollbar-thumb {
    background-color: #d1d5db !important;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af !important;
}

/* Firefox specific overrides */
@-moz-document url-prefix() {
    html {
        color-scheme: light !important;
    }
}

/* Ensure all custom ERP colors are preserved */
.text-erp-primary { color: #1e40af !important; }
.text-erp-secondary { color: #3b82f6 !important; }
.text-erp-accent { color: #06b6d4 !important; }
.text-erp-success { color: #10b981 !important; }
.text-erp-warning { color: #f59e0b !important; }
.text-erp-danger { color: #ef4444 !important; }
.text-erp-dark { color: #1f2937 !important; }
.text-erp-light { color: #f8fafc !important; }
.text-erp-header { color: #111827 !important; }

.bg-erp-primary { background-color: #1e40af !important; }
.bg-erp-secondary { background-color: #3b82f6 !important; }
.bg-erp-accent { background-color: #06b6d4 !important; }
.bg-erp-success { background-color: #10b981 !important; }
.bg-erp-warning { background-color: #f59e0b !important; }
.bg-erp-danger { background-color: #ef4444 !important; }
.bg-erp-dark { background-color: #1f2937 !important; }
.bg-erp-light { background-color: #f8fafc !important; }
.bg-erp-header { background-color: #111827 !important; }

/* Force light mode for any remaining elements */
[data-bs-theme="dark"] {
    --bs-body-bg: #ffffff !important;
    --bs-body-color: #1f2937 !important;
    --bs-border-color: #e5e7eb !important;
}
