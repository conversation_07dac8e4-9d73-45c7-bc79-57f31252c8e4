from django.shortcuts import redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import TemplateView
from django.views import View


class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = "core/dashboard.html"
    login_url = reverse_lazy("core:login")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add dashboard-specific context data here
        return context


class LoginView(View):
    template_name = "core/login.html"
    success_url = reverse_lazy("core:dashboard")

    def get(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect(self.success_url)
        return self.render_login_form()

    def post(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect(self.success_url)

        username = request.POST.get("username")
        password = request.POST.get("password")

        if not username or not password:
            messages.error(request, "Please enter both username and password.")
            return self.render_login_form()

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            next_url = request.GET.get("next", self.success_url)
            return redirect(next_url)
        else:
            messages.error(request, "Invalid username or password.")
            return self.render_login_form()

    def render_login_form(self):
        from django.shortcuts import render

        return render(self.request, self.template_name)


class LogoutView(LoginRequiredMixin, View):
    login_url = reverse_lazy("core:login")

    def get(self, request, *args, **kwargs):
        return self.perform_logout()

    def post(self, request, *args, **kwargs):
        return self.perform_logout()

    def perform_logout(self):
        logout(self.request)
        messages.success(self.request, "You have been logged out successfully.")
        return redirect("core:login")


# View instances for URL configuration
dashboard = DashboardView.as_view()
login_view = LoginView.as_view()
logout_view = LogoutView.as_view()
