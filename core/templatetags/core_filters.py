from django import template

register = template.Library()

@register.filter(name='attr')
def add_attr(field, css):
    """
    Adds a CSS class or other attributes to a form field.
    Usage: {{ field|attr:"class:my-class,hx-post:/some-url/" }}
    """
    attrs = {}
    pairs = css.split(',')
    for pair in pairs:
        key_value = pair.split(':')
        if len(key_value) == 2:
            key = key_value[0].strip()
            value = key_value[1].strip()
            attrs[key] = value
        elif len(key_value) == 1: # For attributes like 'required' or 'checked'
            attrs[key_value[0].strip()] = True
    
    return field.as_widget(attrs=attrs)

@register.filter(name='mul')
def multiply(value, arg):
    """
    Multiplies the value by the argument.
    Usage: {{ value|mul:20 }}
    """
    try:
        return int(value) * int(arg)
    except (ValueError, TypeError):
        return 0
