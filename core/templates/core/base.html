{% load static %}
{% load core_filters %}
<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AsyERP - Enterprise Resource Planning{% endblock %}</title>

    <!-- Meta tags for better SEO and mobile experience -->
    <meta name="description" content="AsyERP - Enterprise Resource Planning System">
    <meta name="theme-color" content="#1e40af">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- Force Light Mode CSS -->
    <link rel="stylesheet" href="{% static 'core/css/force-light-mode.css' %}">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            darkMode: false, // Disable dark mode completely
            theme: {
                extend: {
                    colors: {
                        'erp': {
                            'primary': '#1e40af',
                            'secondary': '#3b82f6',
                            'accent': '#06b6d4',
                            'success': '#10b981',
                            'warning': '#f59e0b',
                            'danger': '#ef4444',
                            'dark': '#1f2937',
                            'light': '#f8fafc',
                            'header': '#111827',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem',
                    }
                }
            }
        }
    </script>

    <!-- HTMX for dynamic interactions -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>

    <!-- Hyperscript for declarative client-side scripting -->
    <script src="https://unpkg.com/hyperscript.org@0.9.12"></script>

    <!-- Alpine.js for lightweight client-side interactions -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- DataTables CSS with Tailwind styling -->
    <link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/5.0.0/css/fixedColumns.dataTables.min.css">

    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {% block extra_css %}{% endblock %}
    {% block head_extra %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans antialiased">
    <!-- Fixed Header -->
    <header class="fixed top-0 left-0 right-0 bg-gradient-to-r from-erp-primary to-erp-secondary shadow-lg z-50 h-20">
        <div class="flex items-center justify-between h-full px-6">
            <!-- Logo and Company Info -->
            <div class="flex items-center space-x-6">
                <div class="bg-white rounded-lg p-2 shadow-md">
                    <img src="{% static 'core/css/logo.PNG' %}" alt="Company Logo" class="h-16 w-auto object-contain">
                </div>

                <div class="text-white">
                    <h1 class="text-xl font-bold">
                        {% if company %}{{ company.companyname }}{% else %}AsyERP{% endif %}
                    </h1>
                    {% if financial_year %}
                    <div class="text-sm bg-white/20 px-3 py-1 rounded-full mt-1">
                        <span class="font-medium">FY:</span> {{ financial_year.year_name }}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- User Info -->
            {% if user.is_authenticated %}
            <div class="flex items-center space-x-4 text-white">
                <div class="text-right">
                    <p class="font-medium">{{ user.get_full_name|default:user.username }}</p>
                    <p class="text-sm text-blue-100">Administrator</p>
                </div>
                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white"></i>
                </div>
            </div>
            {% endif %}
        </div>
    </header>

    <!-- Main Layout Container -->
    <div class="flex pt-20 min-h-screen">
        <!-- Sidebar -->
        <aside class="fixed top-20 left-0 w-64 bg-gray-800 text-white shadow-lg z-40 h-[calc(100vh-5rem)] overflow-y-auto">
            {% include "core/navigation.html" %}
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 ml-64 p-6 bg-gray-50">
            <!-- Messages Framework -->
            {% if messages %}
            <div class="mb-6 space-y-3">
                {% for message in messages %}
                <div class="{% if message.tags == 'error' %}bg-red-50 border-red-200 text-red-800{% elif message.tags == 'success' %}bg-green-50 border-green-200 text-green-800{% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-800{% else %}bg-blue-50 border-blue-200 text-blue-800{% endif %} border px-4 py-3 rounded-lg shadow-sm" role="alert">
                    <div class="flex items-center">
                        <i class="{% if message.tags == 'error' %}fas fa-exclamation-circle text-red-500{% elif message.tags == 'success' %}fas fa-check-circle text-green-500{% elif message.tags == 'warning' %}fas fa-exclamation-triangle text-yellow-500{% else %}fas fa-info-circle text-blue-500{% endif %} mr-3"></i>
                        {{ message }}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Page Content -->
            {% block content %}
            <!-- This will be overridden by child templates -->
            {% endblock %}
        </main>
    </div>

    <!-- Core JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- DataTables JavaScript -->
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/5.0.0/js/dataTables.fixedColumns.min.js"></script>

    <!-- DataTables Extensions for Export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

    <!-- HTMX Configuration -->
    <script>
        // Global modal functions
        function showModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('is-active');
            }
        }

        function hideModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('is-active');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Configure HTMX to include CSRF token in all requests
            document.body.addEventListener('htmx:configRequest', function(evt) {
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                if (csrfToken) {
                    evt.detail.headers['X-CSRFToken'] = csrfToken.value;
                }
            });

            // Force light mode on page load
            document.documentElement.classList.remove('dark');
            document.documentElement.classList.add('light');
            document.documentElement.style.colorScheme = 'light only';

            // Global HTMX event listener for modal closing after successful form submission
            document.body.addEventListener('htmx:afterSwap', function(evt) {
                if (evt.detail.xhr.status === 204) {
                    hideModal();
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
    {% block extra_scripts %}{% endblock %}
</body>
</html>
