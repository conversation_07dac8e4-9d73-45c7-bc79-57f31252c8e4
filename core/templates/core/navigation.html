<!-- Navigation Menu -->
<div class="bg-gray-800 text-white w-full h-full flex flex-col overflow-y-auto">
    <nav x-data="{
        activeModule: null,
        activeSubMenu: {},
        toggleModule(name) {
            this.activeModule = this.activeModule === name ? null : name;
            // Reset sub-menus when parent module is closed
            if (this.activeModule !== name) {
                this.activeSubMenu = {};
            }
        },
        toggleSubMenu(module, name) {
            if (!this.activeSubMenu[module]) {
                this.activeSubMenu[module] = {};
            }
            this.activeSubMenu[module][name] = !this.activeSubMenu[module][name];
        },
        isSubMenuOpen(module, name) {
            return this.activeSubMenu[module] && this.activeSubMenu[module][name];
        }
    }">
        <!-- Quick Dashboard Link -->
        <div class="p-4 border-b border-gray-700">
            <a href="{% url 'core:dashboard' %}"
               class="flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200">
                <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"/>
                </svg>
                Home
            </a>
        </div>

        <!-- Navigation Menu -->
        <div class="flex-1 mt-2 px-2 space-y-1">
            {% if user.is_authenticated %}
            <!-- System Administrator -->
            <div>
                <div @click="toggleModule('sysadmin')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    Administrator
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'sysadmin'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'sysadmin'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Role Management</a>
                    <a href="{% url 'sysadmin:country_list' %}" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Country</a>
                    <a href="{% url 'sysadmin:state_list' %}" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">State</a>
                    <a href="{% url 'sysadmin:city_list' %}" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">City</a>
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Financial Year</a>
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Country-State-City Demo</a>
                </div>
            </div>
            {% endif %}
            
            <!-- Sales Distribution -->
            <div>
                <div id="sales-distribution-toggle" @click="toggleModule('sales')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                    </svg>
                    Sales Distribution
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'sales'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'sales'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Configuration section with toggle -->
                    <div @click="toggleSubMenu('sales', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Configuration</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('sales', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('sales', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="{% url 'customer_list' %}" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Customer</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Products</a>
                        <a href="{% url 'category_list' %}" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">WO Category</a>
                        <a href="{% url 'subcategory_list' %}" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">WO Sub-Category</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">WO Release & Dispatch Authority</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('sales', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('sales', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('sales', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Enquiry</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Quotation</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Quotation Check</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Quotation Approve</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Quotation Authorize</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Customer PO</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Work Order</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">WO Release</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">WO Dispatch</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Dispatch GunRail</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">WO Open/Close</a>
                    </div>

                    <!-- Report section -->
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200 mt-2">Report</a>
                </div>
            </div>

            <!-- Design Module -->
            <div>
                <div id="design-toggle" @click="toggleModule('design')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Design
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'design'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'design'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Configuration section with toggle -->
                    <div @click="toggleSubMenu('design', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Configuration</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('design', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('design', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Category</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Sub-Category</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Items</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Units</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">ECN Reasons</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Revision Types</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('design', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('design', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('design', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <!-- BOM Section -->
                        <div class="mt-1 mb-2">
                            <span class="block pl-3 pr-3 py-1 text-sm font-medium text-gray-400">Bill of Materials (BOM)</span>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Dashboard</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">BOM List</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Work Orders</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Create BOM</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Bulk Upload</a>
                        </div>
                        
                        <!-- TPL Section -->
                        <div class="mt-2 mb-2">
                            <span class="block pl-3 pr-3 py-1 text-sm font-medium text-gray-400">Technical Parts List (TPL)</span>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Dashboard</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">TPL List</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Create TPL</a>
                        </div>
                        
                        <!-- ECN Section -->
                        <div class="mt-2 mb-2">
                            <span class="block pl-3 pr-3 py-1 text-sm font-medium text-gray-400">Engineering Change Notice (ECN)</span>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Dashboard</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">ECN List</a>
                            <a href="#" class="block pl-6 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Create ECN</a>
                        </div>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('design', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('design', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('design', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Item History</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">BOM Reports</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">TPL Reports</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Category Statistics</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Item Statistics</a>
                    </div>
                </div>
            </div>

            <!-- Inventory Module -->
            <div>
                <div @click="toggleModule('inventory')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"/>
                    </svg>
                    Inventory
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'inventory'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'inventory'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Configuration section with toggle -->
                    <div @click="toggleSubMenu('inventory', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Configuration</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('inventory', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('inventory', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Item location</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">AutoWIS Timer</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('inventory', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('inventory', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('inventory', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Goods Inward Note [GIN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Goods Received Receipt [GRR]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Goods Service Note [GSN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Requisition Slip [MRS]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Issue Note [MIN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Return Note [MRN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Release WIS</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Dry / Actual WIS Run</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Supplier Challan</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Customer Challan</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Closing Stock</a>
                    </div>

                    <!-- Reports section with toggle -->
                    <div @click="toggleSubMenu('inventory', 'reports')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Reports</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('inventory', 'reports')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Reports submenu items -->
                    <div x-show="isSubMenuOpen('inventory', 'reports')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Stock Ledger</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Stock Statement</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Issue/Shortage list</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">ABC Analysis</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Moving-Non Moving Items</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Inward/Outward Register</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Search</a>
                    </div>
                </div>
            </div>

            <!-- Material Planning Module -->
            <div>
                <div @click="toggleModule('materialplanning')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    Planning
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'materialplanning'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'materialplanning'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Master section with toggle -->
                    <div @click="toggleSubMenu('materialplanning', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Master</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('materialplanning', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('materialplanning', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Process</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('materialplanning', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('materialplanning', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('materialplanning', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">BOM</a>
                    </div>

                    <!-- Report section -->
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200 mt-2">Report</a>
                </div>
            </div>

            <!-- Material Management Module -->
            <div>
                <div @click="toggleModule('materialmanagement')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                    </svg>
                    Material
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'materialmanagement'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'materialmanagement'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Master section with toggle -->
                    <div @click="toggleSubMenu('materialmanagement', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Master</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('materialmanagement', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('materialmanagement', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Business Nature</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Business Type</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Service Coverage</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Buyer</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Supplier</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Set Rate</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('materialmanagement', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('materialmanagement', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('materialmanagement', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Rate Lock/UnLock</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Purchase Requisition [PR]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Special Purpose Requisition [SPR]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Check SPR</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Approve SPR</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Authorize SPR</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Purchase Order [PO]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Check PO</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Approve PO</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Authorize PO</a>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('materialmanagement', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('materialmanagement', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('materialmanagement', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Rate Register</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Rate Lock/UnLock</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Supplier Rating</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Forecasting</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Inward/Outward Register</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Search</a>
                    </div>
                </div>
            </div>

            <!-- Project Management Module -->
            <div>
                <div @click="toggleModule('projectmanagement')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Project
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'projectmanagement'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'projectmanagement'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Master section -->
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Master</a>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('projectmanagement', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('projectmanagement', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('projectmanagement', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Onsite Attendance</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Man Power Planning</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Project Planning</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Credit Note [MCN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">For Customers</a>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('projectmanagement', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('projectmanagement', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('projectmanagement', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Project Summary</a>
                    </div>
                </div>
            </div>

            <!-- Quality Control Module -->
            <div>
                <div @click="toggleModule('qualitycontrol')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Quality Control
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'qualitycontrol'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'qualitycontrol'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Master section -->
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Master</a>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('qualitycontrol', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('qualitycontrol', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('qualitycontrol', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Goods Quality Note [GQN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Material Return Quality Note [MRQN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Authorize MCN</a>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('qualitycontrol', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('qualitycontrol', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('qualitycontrol', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Goods Rejection Note [GRN]</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Scrap Material</a>
                    </div>
                </div>
            </div>

            <!-- Accounts Module -->
            <div>
                <div @click="toggleModule('accounts')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                    Accounts
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'accounts'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'accounts'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Master section with toggle -->
                    <div @click="toggleSubMenu('accounts', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Master</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('accounts', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('accounts', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Account Heads</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Excise/Service Tax</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">VAT</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Octroi</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Packing & Forwarding</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Freight</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Excisable Commodity</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Warranty Terms</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Payment Terms</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Cash/Bank Entry</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">IOU Reasons</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Payment/Receipt Against</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Bank</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('accounts', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('accounts', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('accounts', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Proforma Invoice</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Sales Invoice</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Services Invoice</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Contra</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Debit Note</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Credit Note</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">IOU Payment/Receipt</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Bill Booking</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Authorize Bill Booking</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Cash Voucher</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Payment/Receipt Voucher</a>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('accounts', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('accounts', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('accounts', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Sales Register</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Purchase Register</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Pending For Invoice</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">PVEV Search</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Cash/Bank Register</a>
                    </div>
                </div>
            </div>

            <!-- HR/Admin Module -->
            <div>
                <div @click="toggleModule('hradmin')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                    HR/Admin
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'hradmin'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'hradmin'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Master section with toggle -->
                    <div @click="toggleSubMenu('hradmin', 'master')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Master</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('hradmin', 'master')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Master submenu items -->
                    <div x-show="isSubMenuOpen('hradmin', 'master')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Business Group</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Designation</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Department</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Grade</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">SwapCard No</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Corporate Mobile</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Intercom Ext</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Gate Pass Types</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Holiday</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">PF Slab</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Working Days</a>
                    </div>

                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('hradmin', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('hradmin', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('hradmin', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">News And Notices</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Offer Letter</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Staff</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Mobile Bill</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">SMS</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Authorize Gate Pass</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Tour Intimation</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Bank Loan</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">PayRoll</a>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('hradmin', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('hradmin', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('hradmin', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Staff</a>
                    </div>
                </div>
            </div>

            <!-- MR Office Module -->
            <div>
                <div @click="toggleModule('mroffice')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"/>
                    </svg>
                    MR Office
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'mroffice'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'mroffice'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Master</a>
                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('mroffice', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('mroffice', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('mroffice', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Format/Documents</a>
                    </div>
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200 mt-2">Report</a>
                </div>
            </div>

            <!-- MIS Module -->
            <div>
                <div @click="toggleModule('mis')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    MIS
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'mis'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'mis'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <!-- Transaction section with toggle -->
                    <div @click="toggleSubMenu('mis', 'transaction')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer">
                        <span class="font-semibold">Transaction</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('mis', 'transaction')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Transaction submenu items -->
                    <div x-show="isSubMenuOpen('mis', 'transaction')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Financial Budget</a>
                    </div>

                    <!-- Report section with toggle -->
                    <div @click="toggleSubMenu('mis', 'report')"
                         class="flex items-center px-3 py-1 text-base text-gray-300 hover:bg-gray-700 hover:text-white rounded-md transition-colors duration-200 cursor-pointer mt-2">
                        <span class="font-semibold">Report</span>
                        <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                             :class="{'rotate-90': isSubMenuOpen('mis', 'report')}"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </div>
                    <!-- Report submenu items -->
                    <div x-show="isSubMenuOpen('mis', 'report')"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform -translate-y-2"
                         x-transition:enter-end="opacity-100 transform translate-y-0"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform translate-y-0"
                         x-transition:leave-end="opacity-0 transform -translate-y-2"
                         class="pl-4">
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Sales Distribution</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Purchase</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Sales</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Service</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">BOM Costing</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Purchase/Sales Computation</a>
                        <a href="#" class="block pl-3 pr-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">QA Report</a>
                    </div>
                </div>
            </div>

            <!-- System Support -->
            <div>
                <div @click="toggleModule('syssupport')"
                     class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                    System Support
                    <svg class="ml-auto h-4 w-4 transition-transform duration-200"
                         :class="{'rotate-90': activeModule === 'syssupport'}"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </div>
                <div x-show="activeModule === 'syssupport'"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="ml-6 mt-1 space-y-1">
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">Change Password</a>
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">System Credentials</a>
                    <a href="#" class="block px-3 py-1 text-base text-cyan-200 hover:text-white hover:bg-gray-700 rounded-md transition-colors duration-200">ECN</a>
                </div>
            </div>

            <!-- Scheduler and other modules -->
            <div>
                <a href="#" class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"/>
                    </svg>
                    My Scheduler
                </a>
            </div>

            <div>
                <a href="#" class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                    </svg>
                    Chat Room
                </a>
            </div>

            <div>
                <a href="#" class="group w-full flex items-center px-3 py-2 text-base font-semibold rounded-md text-white hover:bg-gray-700 hover:text-yellow-300 transition-colors duration-200 cursor-pointer">
                    <svg class="mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                    </svg>
                    Gate Pass
                </a>
            </div>

            <!-- More modules will be added here as needed -->
        </div>
    </nav>
</div>
