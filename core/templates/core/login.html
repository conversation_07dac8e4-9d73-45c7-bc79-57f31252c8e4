{% extends 'core/base.html' %}

{% block title %}Login - AsyERP{% endblock %}

{% block content %}
<!-- Full page login with gradient background -->
<div class="min-h-screen flex flex-col justify-center" 
     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-black opacity-10" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="1"/></g></svg>');"></div>
    
    <!-- Messages -->
    {% if messages %}
        <div class="fixed top-0 left-0 right-0 z-50">
            {% for message in messages %}
                <div class="mx-auto max-w-md mt-4 px-4">
                    <div class="rounded-md p-4 shadow-lg {% if message.tags == 'error' %}bg-red-100 border border-red-200 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-200 text-yellow-800{% elif message.tags == 'success' %}bg-green-100 border border-green-200 text-green-800{% else %}bg-blue-100 border border-blue-200 text-blue-800{% endif %}">
                        <div class="flex">
                            <div class="ml-3">
                                <p class="text-sm font-medium">{{ message }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
    
    <div class="py-12 sm:px-6 lg:px-8">
        <div class="relative sm:mx-auto sm:w-full sm:max-w-md">
            <!-- Logo Section -->
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-white rounded-full flex items-center justify-center shadow-lg mb-6">
                    <svg class="h-12 w-12 text-erp-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                    </svg>
                </div>
                <h2 class="text-4xl font-bold tracking-tight text-white mb-2">AsyERP</h2>
                <p class="text-lg text-gray-200">Enterprise Resource Planning</p>
            </div>
        </div>
    
        <div class="relative mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div class="bg-white py-8 px-6 shadow-2xl rounded-lg sm:px-10 backdrop-blur-lg">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">Login to AsyERP</h3>
                    <p class="text-gray-600">Sign in to your account to continue</p>
                </div>
    
                <form method="post" action="{% url 'core:login' %}" class="space-y-6">
                    {% csrf_token %}
                    
                    <!-- Username Field -->
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            Username
                        </label>
                        <div class="relative">
                            <input type="text" 
                                   id="username" 
                                   name="username" 
                                   required
                                   class="block w-full appearance-none rounded-md border border-gray-300 px-4 py-3 placeholder-gray-400 shadow-sm focus:border-erp-primary focus:outline-none focus:ring-erp-primary transition-colors duration-200 text-gray-900"
                                   placeholder="Enter your username">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password
                        </label>
                        <div class="relative">
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   required
                                   class="block w-full appearance-none rounded-md border border-gray-300 px-4 py-3 placeholder-gray-400 shadow-sm focus:border-erp-primary focus:outline-none focus:ring-erp-primary transition-colors duration-200 text-gray-900"
                                   placeholder="Enter your password">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Login Button -->
                    <div>
                        <button type="submit" 
                                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-erp-primary hover:bg-erp-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-erp-primary transition-all duration-200 transform hover:scale-105 shadow-lg">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <svg class="h-5 w-5 text-erp-light group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a5 5 0 0110 0z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                            Sign In
                        </button>
                    </div>
                    
                    {% if request.GET.next %}
                        <input type="hidden" name="next" value="{{ request.GET.next }}">
                    {% endif %}
                </form>
                
                <!-- Development Info Card -->
                <div class="mt-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="text-center">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Development Environment</h4>
                        <div class="space-y-1 text-xs text-gray-600">
                            <div class="flex justify-between">
                                <span>Username:</span>
                                <span class="font-mono bg-gray-200 px-2 py-1 rounded">admin</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Password:</span>
                                <span class="font-mono bg-gray-200 px-2 py-1 rounded">admin</span>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 mt-2">
                            <em>Create superuser: <code class="bg-gray-200 px-1 rounded">python manage.py createsuperuser</code></em>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center mt-6">
                <p class="text-sm text-gray-200">
                    Secure access to your enterprise data
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}