{% extends 'core/base.html' %}

{% block title %}Dashboard - AsyERP{% endblock %}

{% block content %}
{% if user.is_authenticated %}
        
        <!-- Main Content -->
        <main class="flex-1">
            {% if messages %}
                <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-4">
                    {% for message in messages %}
                        <div class="rounded-md p-4 mb-4 {% if message.tags == 'error' %}bg-red-50 border border-red-200 text-red-800{% elif message.tags == 'warning' %}bg-yellow-50 border border-yellow-200 text-yellow-800{% elif message.tags == 'success' %}bg-green-50 border border-green-200 text-green-800{% else %}bg-blue-50 border border-blue-200 text-blue-800{% endif %}">
                            <div class="flex">
                                <div class="ml-3">
                                    <p class="text-sm font-medium">{{ message }}</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="px-4 sm:px-6 lg:px-8 py-6">
        
        <!-- Enhanced Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Sales Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-12 w-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">Total Sales</p>
                        <p class="text-2xl font-bold text-gray-900">₹12.4M</p>
                        <div class="flex items-center mt-1">
                            <svg class="h-4 w-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"/>
                            </svg>
                            <span class="text-sm text-green-600 font-medium">+12.5%</span>
                            <span class="text-sm text-gray-500 ml-1">vs last month</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Items Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-12 w-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">Inventory Items</p>
                        <p class="text-2xl font-bold text-gray-900">2,847</p>
                        <div class="flex items-center mt-1">
                            <svg class="h-4 w-4 text-yellow-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5C3.312 16.333 4.27 18 5.81 18z"/>
                            </svg>
                            <span class="text-sm text-yellow-600 font-medium">5 low stock</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Employees Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-12 w-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">Active Employees</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                        <div class="flex items-center mt-1">
                            <svg class="h-4 w-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                            </svg>
                            <span class="text-sm text-green-600 font-medium">3 new hires</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Orders Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 group">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="h-12 w-12 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <p class="text-sm font-medium text-gray-500">Pending Orders</p>
                        <p class="text-2xl font-bold text-gray-900">23</p>
                        <div class="flex items-center mt-1">
                            <svg class="h-4 w-4 text-red-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-sm text-red-600 font-medium">5 urgent</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Quick Access Modules -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-900">Quick Access Modules</h2>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            
                            <!-- System Administration Card -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <svg class="h-6 w-6 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-gray-900">System Administration</h3>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Financial Year Management</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Location Master</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">User Role Management</a>
                                </div>
                            </div>

                            <!-- Sales & Distribution Card -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <svg class="h-6 w-6 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-gray-900">Sales & Distribution</h3>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Customer Management</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Work Order Processing</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Quotation Management</a>
                                </div>
                            </div>

                            <!-- Material Management Card -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <svg class="h-6 w-6 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-gray-900">Material Management</h3>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Supplier Management</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Purchase Orders</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Purchase Requisitions</a>
                                </div>
                            </div>

                            <!-- Inventory Management Card -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <svg class="h-6 w-6 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-gray-900">Inventory Management</h3>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Stock Management</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Goods Inward Note</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Stock Reports</a>
                                </div>
                            </div>

                            <!-- Accounts & Finance Card -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <svg class="h-6 w-6 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-gray-900">Accounts & Finance</h3>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Sales Invoicing</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Purchase Register</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Cash/Bank Management</a>
                                </div>
                            </div>
                            
                            <!-- Human Resources Card -->
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-3">
                                    <svg class="h-6 w-6 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    <h3 class="text-lg font-semibold text-gray-900">Human Resources</h3>
                                </div>
                                <div class="space-y-2">
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Employee Management</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Payroll Processing</a>
                                    <a href="#" class="block text-sm text-gray-600 hover:text-erp-primary transition-colors duration-200">Attendance</a>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                
                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="h-2 w-2 bg-green-500 rounded-full mt-2"></div>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">New sales order <span class="font-medium">#SO-2024-001</span></p>
                                    <p class="text-xs text-gray-500">2 hours ago</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="h-2 w-2 bg-blue-500 rounded-full mt-2"></div>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">Inventory updated for Item <span class="font-medium">MS-001</span></p>
                                    <p class="text-xs text-gray-500">4 hours ago</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="h-2 w-2 bg-yellow-500 rounded-full mt-2"></div>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">Purchase order <span class="font-medium">#PO-2024-045</span> approved</p>
                                    <p class="text-xs text-gray-500">1 day ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">System Notifications</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <svg class="h-5 w-5 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5C3.312 16.333 4.27 18 5.81 18z"/>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-yellow-800">Low Stock Alert</p>
                                    <p class="text-xs text-yellow-600">5 items below minimum level</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <svg class="h-5 w-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-blue-800">System Update</p>
                                    <p class="text-xs text-blue-600">Maintenance scheduled for tonight</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        
            </div>
        </main>
        
        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-4">
                <p class="text-center text-sm text-gray-500">
                    &copy; 2024 AsyERP - Enterprise Resource Planning System
                </p>
            </div>
        </footer>
    </div>
{% else %}
    <!-- Not authenticated state -->
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center">
        {% if messages %}
            <div class="fixed top-0 left-0 right-0 z-50">
                {% for message in messages %}
                    <div class="mx-auto max-w-md mt-4 px-4">
                        <div class="rounded-md p-4 shadow-lg {% if message.tags == 'error' %}bg-red-100 border border-red-200 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-200 text-yellow-800{% elif message.tags == 'success' %}bg-green-100 border border-green-200 text-green-800{% else %}bg-blue-100 border border-blue-200 text-blue-800{% endif %}">
                            <div class="flex">
                                <div class="ml-3">
                                    <p class="text-sm font-medium">{{ message }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="text-center py-12">
            <div class="mx-auto h-20 w-20 bg-erp-primary rounded-full flex items-center justify-center shadow-lg mb-6">
                <svg class="h-12 w-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-4">Welcome to AsyERP</h1>
            <p class="text-gray-600 mb-6">Please login to access the Enterprise Resource Planning system.</p>
            <a href="{% url 'core:login' %}" 
               class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-erp-primary hover:bg-erp-secondary transition-colors duration-200">
                Login to Continue
            </a>
        </div>
    </div>
{% endif %}
{% endblock %}