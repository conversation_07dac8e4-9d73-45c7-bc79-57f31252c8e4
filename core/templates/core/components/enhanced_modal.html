<!-- Enhanced Modal Component with improved animations and accessibility -->
<div id="modal" 
     class="fixed inset-0 z-50 overflow-y-auto"
     x-data="{ showModal: false }"
     x-show="showModal"
     x-on:keydown.escape.window="showModal = false"
     style="display: none;">
    
    <!-- Backdrop with blur effect -->
    <div class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         x-on:click="showModal = false">
    </div>
    
    <!-- Modal container -->
    <div class="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
        <div id="modalContent" 
             class="relative transform overflow-hidden rounded-xl bg-white text-left shadow-2xl transition-all duration-300 sm:my-8 sm:w-full sm:max-w-lg"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-on:click.stop>
            <!-- Content will be loaded here by HTMX -->
            <div class="flex items-center justify-center p-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-erp-primary"></div>
                <span class="ml-3 text-gray-600">Loading...</span>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced modal management
document.addEventListener('alpine:init', () => {
    Alpine.data('modalManager', () => ({
        showModal: false,
        
        openModal() {
            this.showModal = true;
            document.body.style.overflow = 'hidden'; // Prevent background scroll
        },
        
        closeModal() {
            this.showModal = false;
            document.body.style.overflow = ''; // Restore scroll
        }
    }));
});

// Global modal functions
window.openModal = () => {
    const modal = document.getElementById('modal');
    if (modal && modal.__x) {
        modal.__x.showModal = true;
        document.body.style.overflow = 'hidden';
    }
};

window.closeModal = () => {
    const modal = document.getElementById('modal');
    if (modal && modal.__x) {
        modal.__x.showModal = false;
        document.body.style.overflow = '';
    }
};

// HTMX integration
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.detail.target.id === 'modalContent') {
        // Re-initialize Alpine for new content
        if (typeof Alpine !== 'undefined') {
            Alpine.initTree(event.detail.target);
        }
        window.openModal();
    }
});

document.addEventListener('htmx:beforeSwap', function(event) {
    // Handle successful form submissions (204 status)
    if (event.detail.xhr.status === 204 && event.detail.target.id === 'modalContent') {
        window.closeModal();
        event.detail.shouldSwap = false;
    }
});

// Handle manual close events
document.addEventListener('closeModal', function() {
    window.closeModal();
});

// Helper function for buttons
window.dispatchCloseModalEvent = () => {
    document.dispatchEvent(new CustomEvent('closeModal'));
};
</script>
