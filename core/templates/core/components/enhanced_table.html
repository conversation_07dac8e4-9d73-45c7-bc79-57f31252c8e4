<!-- Enhanced Data Table Component -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- Table Header -->
    <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    {% if table_icon %}
                        <svg class="h-5 w-5 text-erp-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {{ table_icon|safe }}
                        </svg>
                    {% endif %}
                    {{ table_title|default:"Data Table" }}
                </h3>
                {% if table_subtitle %}
                    <p class="text-sm text-gray-600 mt-1">{{ table_subtitle }}</p>
                {% endif %}
            </div>
            
            <!-- Table Actions -->
            <div class="flex items-center space-x-3">
                {% if show_export %}
                    <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        Export
                    </button>
                {% endif %}
                
                {% if add_button_url %}
                    <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-erp-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-erp-primary transition-all duration-200"
                            hx-get="{{ add_button_url }}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            onclick="window.openModal()">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {{ add_button_text|default:"Add New" }}
                    </button>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Table Content -->
    <div class="overflow-x-auto">
        <table id="{{ table_id|default:'dataTable' }}" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    {% for header in table_headers %}
                        <th scope="col" 
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider {{ header.class|default:'' }}">
                            {{ header.title }}
                        </th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for row in table_data %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        {% for cell in row %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm {{ cell.class|default:'text-gray-900' }}">
                                {{ cell.value|default:cell }}
                            </td>
                        {% endfor %}
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="{{ table_headers|length }}" class="px-6 py-12 text-center">
                            <div class="flex flex-col items-center">
                                <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                                </svg>
                                <h3 class="text-sm font-medium text-gray-900 mb-2">{{ empty_title|default:"No data found" }}</h3>
                                <p class="text-sm text-gray-500 mb-4">{{ empty_subtitle|default:"Get started by adding a new record." }}</p>
                                {% if add_button_url %}
                                    <button class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-erp-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-erp-primary transition-all duration-200"
                                            hx-get="{{ add_button_url }}"
                                            hx-target="#modalContent"
                                            hx-trigger="click"
                                            onclick="window.openModal()">
                                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                        {{ add_button_text|default:"Add New" }}
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- DataTables Initialization -->
{% if table_data %}
<script>
$(document).ready(function() {
    var table = $('#{{ table_id|default:'dataTable' }}');
    
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable(table)) {
        table.DataTable().destroy();
    }
    
    // Initialize DataTable with enhanced configuration
    table.DataTable({
        "pageLength": {{ page_length|default:10 }},
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        "responsive": true,
        "autoWidth": false,
        "processing": true,
        "language": {
            "emptyTable": "{{ empty_title|default:'No data available in table' }}",
            "zeroRecords": "{{ no_results_title|default:'No matching records found' }}",
            "processing": '<div class="flex items-center justify-center p-4"><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-erp-primary"></div><span class="ml-2 text-gray-600">Loading...</span></div>',
            "search": "Search:",
            "lengthMenu": "Show _MENU_ entries",
            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
            "infoEmpty": "Showing 0 to 0 of 0 entries",
            "infoFiltered": "(filtered from _MAX_ total entries)",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        },
        "columnDefs": [
            {% if non_orderable_columns %}
                { "orderable": false, "targets": {{ non_orderable_columns|safe }} },
            {% endif %}
            {% if column_widths %}
                {% for width in column_widths %}
                    { "width": "{{ width.width }}", "targets": [{{ width.target }}] },
                {% endfor %}
            {% endif %}
        ],
        "dom": '<"flex flex-col sm:flex-row justify-between items-center mb-4"<"flex items-center"l><"flex items-center"f>>rtip',
        "initComplete": function() {
            // Style the search input
            $('.dataTables_filter input').addClass('px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-erp-primary focus:border-erp-primary text-sm');
            $('.dataTables_filter label').addClass('text-sm font-medium text-gray-700');
            
            // Style the length select
            $('.dataTables_length select').addClass('px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-erp-primary focus:border-erp-primary text-sm');
            $('.dataTables_length label').addClass('text-sm font-medium text-gray-700');
            
            // Style pagination
            $('.dataTables_paginate .paginate_button').addClass('px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 hover:bg-gray-50 border border-gray-300 rounded-md transition-colors duration-200');
            $('.dataTables_paginate .paginate_button.current').addClass('bg-erp-primary text-white border-erp-primary');
        }
    });
});
</script>
{% endif %}
