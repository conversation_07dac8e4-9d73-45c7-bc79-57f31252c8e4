<!-- Enhanced Form Component Template -->
{% load core_filters %}

<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- Form Header -->
    <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900 flex items-center">
            {% if form_icon %}
                <svg class="h-6 w-6 text-erp-primary mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {{ form_icon|safe }}
                </svg>
            {% endif %}
            {{ form_title|default:"Form" }}
        </h3>
        {% if form_subtitle %}
            <p class="text-sm text-gray-600 mt-1">{{ form_subtitle }}</p>
        {% endif %}
    </div>
    
    <!-- Form Body -->
    <div class="px-6 py-6">
        <form hx-post="{{ form_action|default:request.path }}" 
              hx-swap="none" 
              hx-indicator="#form-loading-indicator"
              class="space-y-6">
            {% csrf_token %}
            
            <!-- Form Fields -->
            <div class="space-y-5">
                {% for field in form %}
                    <div class="form-group">
                        <!-- Field Label -->
                        <label for="{{ field.id_for_label }}" 
                               class="block text-sm font-semibold text-gray-700 mb-2">
                            {{ field.label }}
                            {% if field.field.required %}
                                <span class="text-red-500 ml-1">*</span>
                            {% endif %}
                        </label>
                        
                        <!-- Field Input -->
                        <div class="relative">
                            {% if field.field.widget.input_type == 'select' %}
                                {{ field|attr:"class:w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-erp-primary focus:border-erp-primary transition-all duration-200 bg-white text-gray-900" }}
                            {% elif field.field.widget.input_type == 'textarea' %}
                                {{ field|attr:"class:w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-erp-primary focus:border-erp-primary transition-all duration-200 bg-white text-gray-900 resize-vertical,rows:4" }}
                            {% else %}
                                {{ field|attr:"class:w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-erp-primary focus:border-erp-primary transition-all duration-200 bg-white text-gray-900 placeholder-gray-400" }}
                            {% endif %}
                            
                            <!-- Field Icon (if provided) -->
                            {% if field.field.widget.attrs.icon %}
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        {{ field.field.widget.attrs.icon|safe }}
                                    </svg>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Field Help Text -->
                        {% if field.help_text %}
                            <p class="mt-2 text-sm text-gray-500 flex items-center">
                                <svg class="h-4 w-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                {{ field.help_text }}
                            </p>
                        {% endif %}
                        
                        <!-- Field Errors -->
                        {% if field.errors %}
                            <div class="mt-2">
                                {% for error in field.errors %}
                                    <p class="text-sm text-red-600 flex items-center">
                                        <svg class="h-4 w-4 text-red-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        {{ error }}
                                    </p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
                
                <!-- Non-field Errors -->
                {% if form.non_field_errors %}
                    <div class="rounded-lg bg-red-50 border border-red-200 p-4">
                        <div class="flex">
                            <svg class="h-5 w-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <div>
                                {% for error in form.non_field_errors %}
                                    <p class="text-sm text-red-800">{{ error }}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
            
            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <!-- Cancel Button -->
                <button type="button" 
                        class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
                        onclick="dispatchCloseModalEvent()">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Cancel
                </button>
                
                <!-- Submit Button -->
                <button type="submit" 
                        class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-erp-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-erp-primary transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    {{ submit_text|default:"Save" }}
                </button>
                
                <!-- Loading Indicator -->
                <div id="form-loading-indicator" 
                     class="htmx-indicator flex items-center text-erp-primary">
                    <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                </div>
            </div>
        </form>
    </div>
</div>
