from sys_admin.models import Company, FinancialYear

def company_context(request):
    """
    Context processor to add company and financial year information to all templates.
    Returns the default company or the first company if no default is set,
    and the current financial year.
    """
    try:
        # Try to get the default company first
        company = Company.objects.filter(defaultcomp=1).first()
        
        # If no default company exists, get the first company
        if not company:
            company = Company.objects.first()
        
        # Get the current financial year
        financial_year = FinancialYear.objects.filter(is_current=True).first()
        
        # If no current financial year exists, get the most recent one
        if not financial_year:
            financial_year = FinancialYear.objects.order_by('-start_date').first()
            
        return {
            'company': company,
            'financial_year': financial_year
        }
    except Exception:
        # Return empty context if there's any error
        return {
            'company': None,
            'financial_year': None
        } 