import pytest
from django.test import TestCase
from django.urls import reverse, resolve


@pytest.mark.unit
class TestCoreUrls(TestCase):
    """Unit tests for core app URL patterns"""
    
    def test_dashboard_url_resolves(self):
        """Test dashboard URL resolves to correct view"""
        url = reverse('core:dashboard')
        self.assertEqual(url, '/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.view_name, 'core:dashboard')
    
    def test_login_url_resolves(self):
        """Test login URL resolves to correct view"""
        url = reverse('core:login')
        self.assertEqual(url, '/login/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.view_name, 'core:login')
    
    def test_logout_url_resolves(self):
        """Test logout URL resolves to correct view"""
        url = reverse('core:logout')
        self.assertEqual(url, '/logout/')
        
        resolver = resolve(url)
        self.assertEqual(resolver.view_name, 'core:logout')