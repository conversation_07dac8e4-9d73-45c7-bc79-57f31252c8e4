import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.messages import get_messages


@pytest.mark.unit
class TestDashboardView(TestCase):
    """Unit tests for DashboardView"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.dashboard_url = reverse('core:dashboard')
    
    def test_dashboard_requires_login(self):
        """Test that dashboard requires authentication"""
        response = self.client.get(self.dashboard_url)
        # Should redirect to login
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
    
    def test_dashboard_get_authenticated(self):
        """Test dashboard GET request for authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Welcome to AsyERP')
        self.assertContains(response, 'testuser')
        self.assertTemplateUsed(response, 'core/dashboard.html')
    
    def test_dashboard_context_data(self):
        """Test dashboard context data is properly set"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('user', response.context)
        self.assertEqual(response.context['user'], self.user)


@pytest.mark.unit
class TestLoginView(TestCase):
    """Unit tests for LoginView"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.login_url = reverse('core:login')
        self.dashboard_url = reverse('core:dashboard')
    
    def test_login_get_anonymous(self):
        """Test login GET request for anonymous user"""
        response = self.client.get(self.login_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Login to AsyERP')
        self.assertTemplateUsed(response, 'core/login.html')
    
    def test_login_get_authenticated_redirects(self):
        """Test login GET request redirects authenticated users"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(self.login_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)
    
    def test_login_post_valid_credentials(self):
        """Test login POST with valid credentials"""
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)
        
        # Verify user is logged in
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
    
    def test_login_post_invalid_credentials(self):
        """Test login POST with invalid credentials"""
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'core/login.html')
        
        # Check error message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Invalid username or password' in str(m) for m in messages))
    
    def test_login_post_missing_credentials(self):
        """Test login POST with missing credentials"""
        # Missing password
        response = self.client.post(self.login_url, {
            'username': 'testuser'
        })
        
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Please enter both username and password' in str(m) for m in messages))
        
        # Missing username
        response = self.client.post(self.login_url, {
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Please enter both username and password' in str(m) for m in messages))
    
    def test_login_post_with_next_parameter(self):
        """Test login POST with next parameter redirects correctly"""
        next_url = '/some-protected-page/'
        response = self.client.post(f'{self.login_url}?next={next_url}', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, next_url)
    
    def test_login_post_authenticated_user_redirects(self):
        """Test login POST with already authenticated user redirects"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)


@pytest.mark.unit
class TestLogoutView(TestCase):
    """Unit tests for LogoutView"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.logout_url = reverse('core:logout')
        self.login_url = reverse('core:login')
    
    def test_logout_get_authenticated(self):
        """Test logout GET request for authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(self.logout_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.login_url)
        
        # Verify user is logged out
        response = self.client.get(reverse('core:dashboard'))
        self.assertEqual(response.status_code, 302)  # Should redirect to login
    
    def test_logout_post_authenticated(self):
        """Test logout POST request for authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(self.logout_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.login_url)
    
    def test_logout_anonymous_user_redirects_to_login(self):
        """Test logout for anonymous user redirects to login"""
        response = self.client.get(self.logout_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
    
    def test_logout_success_message(self):
        """Test logout displays success message"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(self.logout_url)
        
        # Follow redirect to see messages
        response = self.client.get(self.login_url)
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('logged out successfully' in str(m) for m in messages))