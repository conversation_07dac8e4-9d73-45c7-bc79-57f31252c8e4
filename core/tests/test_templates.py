import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.template.loader import render_to_string


@pytest.mark.unit
class TestCoreTemplates(TestCase):
    """Unit tests for core app templates"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_base_template_structure(self):
        """Test base template has correct structure"""
        template_content = render_to_string('core/base.html', {
            'user': self.user
        })
        
        # Check for essential HTML structure
        self.assertIn('<!DOCTYPE html>', template_content)
        self.assertIn('<html lang="en">', template_content)
        self.assertIn('<head>', template_content)
        self.assertIn('<title>', template_content)
        self.assertIn('AsyERP', template_content)
        self.assertIn('https://unpkg.com/htmx.org@1.9.6', template_content)
        self.assertIn('<body>', template_content)
        self.assertIn('<header>', template_content)
        self.assertIn('<main>', template_content)
        self.assertIn('<footer>', template_content)
    
    def test_base_template_navigation_inclusion(self):
        """Test base template includes navigation for authenticated users"""
        # Test with authenticated user via actual request
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        
        # Check navigation elements are present for authenticated user
        self.assertContains(response, 'System Administrator')
        self.assertContains(response, 'Logout (testuser)')
        
        # Test with anonymous user
        self.client.logout()
        response = self.client.get(reverse('core:login'))
        
        # Navigation should not be present for anonymous user
        self.assertNotContains(response, 'System Administrator')
        self.assertNotContains(response, 'Logout')
    
    def test_login_template_structure(self):
        """Test login template has correct form structure"""
        response = self.client.get(reverse('core:login'))
        
        self.assertContains(response, 'Login to AsyERP')
        self.assertContains(response, '<form method="post"')
        self.assertContains(response, 'name="username"')
        self.assertContains(response, 'name="password"')
        self.assertContains(response, 'csrfmiddlewaretoken')
        self.assertContains(response, 'type="submit"')
    
    def test_dashboard_template_structure(self):
        """Test dashboard template structure for authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        
        self.assertContains(response, 'Welcome to AsyERP')
        self.assertContains(response, 'Enterprise Resource Planning System')
        self.assertContains(response, 'testuser')
        self.assertContains(response, 'Quick Access Modules')
        
        # Check for module sections
        self.assertContains(response, 'System Administration')
        self.assertContains(response, 'Sales & Distribution')
        self.assertContains(response, 'Material Management')
        self.assertContains(response, 'Inventory Management')
        self.assertContains(response, 'Accounts & Finance')
        self.assertContains(response, 'Human Resources')
    
    def test_dashboard_template_anonymous_user(self):
        """Test dashboard template for anonymous user shows login prompt"""
        # Create a mock request context
        template_content = render_to_string('core/dashboard.html', {
            'user': None
        })
        
        self.assertIn('Please', template_content)
        self.assertIn('login', template_content)
    
    def test_navigation_template_structure(self):
        """Test navigation template has correct menu structure"""
        template_content = render_to_string('core/navigation.html', {
            'user': self.user
        })
        
        # Check for main navigation elements
        self.assertIn('<nav>', template_content)
        self.assertIn('Home', template_content)
        self.assertIn('System Administrator', template_content)
        self.assertIn('Sales Distribution', template_content)
        self.assertIn('Design', template_content)
        self.assertIn('Material Management', template_content)
        self.assertIn('Inventory', template_content)
        self.assertIn('Accounts', template_content)
        self.assertIn('HR/Admin', template_content)
        self.assertIn('System Support', template_content)
        self.assertIn('Logout', template_content)
        
        # Check for dropdown structure using details/summary
        self.assertIn('<details>', template_content)
        self.assertIn('<summary>', template_content)
    
    def test_templates_extend_base(self):
        """Test that core templates properly extend base template"""
        # Test login template extends base
        response = self.client.get(reverse('core:login'))
        self.assertContains(response, 'AsyERP')  # Title from base template
        
        # Test dashboard template extends base
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        self.assertContains(response, 'AsyERP')  # Title from base template
        self.assertContains(response, 'Enterprise Resource Planning')  # From base
    
    def test_template_blocks_work_correctly(self):
        """Test that template blocks work correctly in inheritance"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        
        # Check title block works
        self.assertContains(response, 'Dashboard - AsyERP')
        
        # Check content block works
        self.assertContains(response, 'Welcome to AsyERP')
        
        # Test login page title block
        response = self.client.get(reverse('core:logout'))  # This will redirect but let's check login template
        response = self.client.get(reverse('core:login'))
        self.assertContains(response, 'Login - AsyERP')