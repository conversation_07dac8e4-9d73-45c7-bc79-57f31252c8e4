import pytest
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.sessions.models import Session


@pytest.mark.integration
class TestAuthenticationFlow(TestCase):
    """Integration tests for complete authentication workflow"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.login_url = reverse('core:login')
        self.logout_url = reverse('core:logout')
        self.dashboard_url = reverse('core:dashboard')
    
    def test_complete_login_logout_flow(self):
        """Test complete login → dashboard → logout flow"""
        # Step 1: Anonymous user tries to access dashboard
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
        
        # Step 2: User goes to login page
        response = self.client.get(self.login_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Login to AsyERP')
        
        # Step 3: User submits login form
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)
        
        # Step 4: User accesses dashboard successfully
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Welcome to AsyERP')
        self.assertContains(response, 'Test User')  # Full name displayed
        
        # Step 5: User logs out
        response = self.client.get(self.logout_url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.login_url)
        
        # Step 6: User can no longer access dashboard
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)
    
    def test_login_with_next_parameter_flow(self):
        """Test login flow with next parameter redirection"""
        protected_url = self.dashboard_url
        
        # Step 1: Anonymous user tries to access protected page
        response = self.client.get(protected_url)
        self.assertEqual(response.status_code, 302)
        login_redirect_url = response.url
        self.assertIn('login', login_redirect_url)
        self.assertIn('next=', login_redirect_url)
        
        # Step 2: User follows redirect to login with next parameter
        response = self.client.get(login_redirect_url)
        self.assertEqual(response.status_code, 200)
        
        # Step 3: User logs in and gets redirected to original page
        response = self.client.post(login_redirect_url, {
            'username': 'testuser',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, protected_url)
        
        # Step 4: User successfully accesses the originally requested page
        response = self.client.get(protected_url)
        self.assertEqual(response.status_code, 200)
    
    def test_failed_login_retry_flow(self):
        """Test failed login attempt followed by successful retry"""
        # Step 1: User attempts login with wrong password
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Invalid username or password')
        
        # Verify user is still not logged in
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 302)
        
        # Step 2: User tries again with correct credentials
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)
        
        # Step 3: User successfully accesses dashboard
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test User')
    
    def test_session_persistence_across_requests(self):
        """Test that user session persists across multiple requests"""
        # Login
        self.client.login(username='testuser', password='testpass123')
        
        # Make multiple requests to dashboard
        for i in range(3):
            response = self.client.get(self.dashboard_url)
            self.assertEqual(response.status_code, 200)
            self.assertContains(response, 'Test User')
            
        # Verify user stays authenticated across requests
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
    
    def test_already_authenticated_user_redirects(self):
        """Test that already authenticated users are redirected appropriately"""
        # Login first
        self.client.login(username='testuser', password='testpass123')
        
        # Try to access login page while authenticated
        response = self.client.get(self.login_url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)
        
        # Try to POST to login while authenticated
        response = self.client.post(self.login_url, {
            'username': 'testuser',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, self.dashboard_url)
    
    def test_navigation_visibility_based_on_auth(self):
        """Test navigation is shown/hidden based on authentication status"""
        # Test anonymous user - no navigation
        response = self.client.get(self.login_url)
        self.assertEqual(response.status_code, 200)
        # Check that navigation elements are not present
        self.assertNotContains(response, 'System Administrator')
        self.assertNotContains(response, 'Logout')
        
        # Login and test authenticated user - navigation present
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
        # Check that navigation elements are present
        self.assertContains(response, 'System Administrator')
        self.assertContains(response, 'Logout (testuser)')
    
    def test_multiple_user_sessions(self):
        """Test multiple users can have separate sessions"""
        # Create second user
        User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass456'
        )
        
        # Create two separate clients for two users
        client1 = Client()
        client2 = Client()
        
        # Both users login
        client1.login(username='testuser', password='testpass123')
        client2.login(username='testuser2', password='testpass456')
        
        # Verify both can access dashboard with their respective info
        response1 = client1.get(self.dashboard_url)
        self.assertEqual(response1.status_code, 200)
        self.assertContains(response1, 'testuser')
        
        response2 = client2.get(self.dashboard_url)
        self.assertEqual(response2.status_code, 200)
        self.assertContains(response2, 'testuser2')
        
        # Verify sessions are separate
        self.assertNotEqual(
            client1.session.session_key,
            client2.session.session_key
        )


@pytest.mark.integration
class TestUserInterfaceFlow(TestCase):
    """Integration tests for complete UI workflows"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='uitestuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_navigation_menu_accessibility(self):
        """Test that all navigation menu items are accessible"""
        self.client.login(username='uitestuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        
        # Check that main menu items are present
        menu_items = [
            'System Administrator',
            'Sales Distribution',
            'Design',
            'Material Management',
            'Inventory',
            'Accounts',
            'HR/Admin',
            'System Support'
        ]
        
        for item in menu_items:
            self.assertContains(response, item)
    
    def test_responsive_template_structure(self):
        """Test that templates have responsive structure"""
        self.client.login(username='uitestuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        
        # Check for viewport meta tag
        self.assertContains(response, 'name="viewport"')
        self.assertContains(response, 'width=device-width')
        
        # Check for semantic HTML structure
        self.assertContains(response, '<header>')
        self.assertContains(response, '<main>')
        self.assertContains(response, '<footer>')
        self.assertContains(response, '<nav>')
    
    def test_form_csrf_protection(self):
        """Test that forms have CSRF protection enabled"""
        response = self.client.get(reverse('core:login'))
        
        # Check for CSRF token in login form
        self.assertContains(response, 'csrfmiddlewaretoken')