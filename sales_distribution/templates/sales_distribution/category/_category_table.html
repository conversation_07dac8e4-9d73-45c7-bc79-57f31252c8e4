<table id="categoryTable" class="min-w-full bg-white table-auto border-collapse">
    <thead>
        <tr class="bg-gray-100 border-b border-gray-200">
            <th class="py-3 px-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider rounded-tl-lg">SN</th>
            <th class="py-3 px-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Category Name</th>
            <th class="py-3 px-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Has SubCategory</th>
            <th class="py-3 px-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Created Date</th>
            <th class="py-3 px-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider rounded-tr-lg">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in categories %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-4 text-sm font-semibold text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm font-semibold text-gray-800">{{ obj.CName }}</td>
            <td class="py-3 px-4 text-sm font-semibold text-gray-800">{{ obj.Symbol }}</td>
            <td class="py-3 px-4 text-sm font-semibold text-gray-800">
                <span class="{% if obj.HasSubCat == '1' %}text-green-600{% else %}text-red-600{% endif %}">
                    {% if obj.HasSubCat == '1' %}<i class="fas fa-check-circle"></i> Yes{% else %}<i class="fas fa-times-circle"></i> No{% endif %}
                </span>
            </td>
            <td class="py-3 px-4 text-sm font-semibold text-gray-800">{{ obj.SysDate|date:"M d, Y" }}</td>
            <td class="py-3 px-4 text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'category_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    onclick="showModal()">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'category_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    onclick="showModal()">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-8 text-center text-gray-500 text-lg">No work order categories found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is loaded and initialized only once per table load
    // Check if DataTables has already been initialized on this table
    if ($.fn.DataTable.isDataTable('#categoryTable')) {
        $('#categoryTable').DataTable().destroy();
    }
    
    $('#categoryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "responsive": true,
        "language": {
            "search": "Filter records:",
            "lengthMenu": "Show _MENU_ entries"
        }
    });
</script>
