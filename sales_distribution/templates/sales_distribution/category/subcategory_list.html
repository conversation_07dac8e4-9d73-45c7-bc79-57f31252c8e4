{% extends 'core/base.html' %}

{% block title %}Sub-Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-gray-900">Work Order Sub-Categories</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'subcategory_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            onclick="showModal()">
            <i class="fas fa-plus mr-2"></i> Add New Sub-Category
        </button>
    </div>

    <!-- Category Filter -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow">
        <label for="categoryFilter" class="block text-sm font-medium text-gray-700 mb-2">Filter by Category:</label>
        <select id="categoryFilter" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                hx-get="{% url 'subcategory_table' %}"
                hx-target="#subcategoryTable-container"
                hx-trigger="change"
                hx-include="this">
            <option value="">All Categories</option>
            {% for category in categories %}
            <option value="{{ category.CId }}" {% if category.CId|stringformat:"s" == selected_category_id %}selected{% endif %}>
                {{ category.CName }} ({{ category.Symbol }})
            </option>
            {% endfor %}
        </select>
    </div>
    
    <div id="subcategoryTable-container"
         hx-trigger="load, refreshSubCategoryList from:body"
         hx-get="{% url 'subcategory_table' %}{% if selected_category_id %}?category_id={{ selected_category_id }}{% endif %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading sub-categories...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50"
         style="display: none;"
         onclick="if(event.target.id === 'modal') hideModal()">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.body.addEventListener('showToastMessage', function(evt) {
        console.log("Show toast message event triggered!");
    });
</script>
{% endblock %}
