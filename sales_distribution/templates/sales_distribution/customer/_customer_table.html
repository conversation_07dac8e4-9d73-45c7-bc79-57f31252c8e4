{% load static %}

<div class="overflow-x-auto">
    <table id="customerTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Customer ID
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Customer Name
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Email
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Contact Person
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Contact No
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Registered Address
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for customer in customers %}
            <tr class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                    {{ customer.CustomerId }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                    {{ customer.CustomerName }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {{ customer.Email|default:"-" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {{ customer.ContactPerson|default:"-" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                    {{ customer.ContactNo|default:"-" }}
                </td>
                <td class="px-6 py-4 text-sm text-gray-700 max-w-xs truncate">
                    {{ customer.full_regd_address|default:"-" }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200"
                        hx-get="{% url 'customer_edit' customer.CustomerId %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        onclick="showModal()"
                        title="Edit Customer">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 transition-colors duration-200"
                        hx-get="{% url 'customer_delete' customer.CustomerId %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        onclick="showModal()"
                        title="Delete Customer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                    <div class="flex flex-col items-center justify-center py-8">
                        <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                        <p class="text-lg font-medium">No customers found</p>
                        <p class="text-sm text-gray-400">Click "Add New Customer" to create your first customer.</p>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize DataTables if not already initialized
        if (!$.fn.DataTable.isDataTable('#customerTable')) {
            $('#customerTable').DataTable({
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                "order": [[0, "desc"]], // Order by Customer ID descending
                "columnDefs": [
                    { "orderable": false, "targets": [6] }, // Disable sorting on Actions column
                    { "width": "15%", "targets": [0] }, // Customer ID
                    { "width": "20%", "targets": [1] }, // Customer Name
                    { "width": "15%", "targets": [2] }, // Email
                    { "width": "15%", "targets": [3] }, // Contact Person
                    { "width": "10%", "targets": [4] }, // Contact No
                    { "width": "15%", "targets": [5] }, // Address
                    { "width": "10%", "targets": [6] }  // Actions
                ],
                "language": {
                    "search": "Search customers:",
                    "lengthMenu": "Show _MENU_ customers per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ customers",
                    "infoEmpty": "No customers available",
                    "infoFiltered": "(filtered from _MAX_ total customers)",
                    "emptyTable": "No customer data available",
                    "zeroRecords": "No matching customers found"
                },
                "dom": '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"mb-2 sm:mb-0"l><"mb-2 sm:mb-0"f>>rtip',
                "responsive": true
            });
        }
    });
</script>
