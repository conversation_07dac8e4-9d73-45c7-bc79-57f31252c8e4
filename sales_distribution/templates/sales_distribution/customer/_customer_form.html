<div class="bg-white p-6 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-5">
        <h3 class="text-2xl font-semibold text-gray-900">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                onclick="hideModal()">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.CustomerName.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Customer Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.CustomerName }}
                    {% if form.CustomerName.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.CustomerName.errors.0 }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.Email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Email
                    </label>
                    {{ form.Email }}
                    {% if form.Email.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.Email.errors.0 }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.ContactPerson.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Contact Person
                    </label>
                    {{ form.ContactPerson }}
                    {% if form.ContactPerson.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.ContactPerson.errors.0 }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.ContactNo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Contact Number
                    </label>
                    {{ form.ContactNo }}
                    {% if form.ContactNo.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.ContactNo.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Registered Address -->
        <div class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2">Registered Address</h4>
            <div class="grid grid-cols-1 gap-4">
                <div>
                    <label for="{{ form.RegdAddress.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Address
                    </label>
                    {{ form.RegdAddress }}
                    {% if form.RegdAddress.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.RegdAddress.errors.0 }}</p>
                    {% endif %}
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ form.RegdCountry.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Country
                        </label>
                        {{ form.RegdCountry }}
                        {% if form.RegdCountry.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.RegdCountry.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.RegdState.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            State
                        </label>
                        {{ form.RegdState }}
                        {% if form.RegdState.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.RegdState.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.RegdCity.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            City
                        </label>
                        {{ form.RegdCity }}
                        {% if form.RegdCity.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.RegdCity.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ form.RegdPinNo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            PIN Code
                        </label>
                        {{ form.RegdPinNo }}
                        {% if form.RegdPinNo.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.RegdPinNo.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.RegdContactNo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Contact Number
                        </label>
                        {{ form.RegdContactNo }}
                        {% if form.RegdContactNo.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.RegdContactNo.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label for="{{ form.RegdFaxNo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Fax Number
                        </label>
                        {{ form.RegdFaxNo }}
                        {% if form.RegdFaxNo.errors %}
                            <p class="mt-1 text-sm text-red-600">{{ form.RegdFaxNo.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2">Additional Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.PanNo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        PAN Number
                    </label>
                    {{ form.PanNo }}
                    {% if form.PanNo.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.PanNo.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="mt-4">
                <label for="{{ form.Remark.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Remarks
                </label>
                {{ form.Remark }}
                {% if form.Remark.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.Remark.errors.0 }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button type="button" 
                    onclick="hideModal()"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                Cancel
            </button>
            <button type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                <span id="form-spinner" class="htmx-indicator">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                </span>
                {{ form.instance.pk|yesno:'Update Customer,Add Customer' }}
            </button>
        </div>
    </form>
</div>
