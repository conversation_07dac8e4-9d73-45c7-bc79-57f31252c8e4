<div class="bg-white p-6 rounded-lg shadow-lg">
    <div class="flex justify-between items-center mb-5">
        <h3 class="text-2xl font-semibold text-gray-900">Confirm Deletion</h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                onclick="hideModal()">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the customer: <strong class="text-red-600">"{{ object.CustomerName }}"</strong> ({{ object.CustomerId }})?</p>
    <p class="text-sm text-gray-500 mb-6">This action cannot be undone.</p>
    
    <form hx-delete="{% url 'customer_delete' object.CustomerId %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-end space-x-3">
            <button type="button" 
                    onclick="hideModal()"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                Cancel
            </button>
            <button type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <span id="delete-spinner" class="htmx-indicator">
                    <i class="fas fa-spinner fa-spin mr-2"></i>
                </span>
                Delete Customer
            </button>
        </div>
    </form>
</div>
