{% extends 'core/base.html' %}

{% block title %}Customers{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-gray-900">Customer Master</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'customer_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            onclick="showModal()">
            <i class="fas fa-plus mr-2"></i> Add New Customer
        </button>
    </div>
    
    <div id="customerTable-container"
         hx-trigger="load, refreshCustomerList from:body"
         hx-get="{% url 'customer_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading customers...</p>
        </div>
    </div>
</div>

<!-- Modal for Add/Edit/Delete operations -->
<div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div id="modalContent">
            <!-- Modal content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Customer-specific JavaScript can be added here if needed
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Customer list page loaded');
    });
</script>
{% endblock %}
