from django.urls import path
from .views.category_views import (
    CategoryListView,
    CategoryTablePartialView,
    CategoryCreateView,
    CategoryUpdateView,
    CategoryDeleteView,
    SubCategoryListView,
    SubCategoryTablePartialView,
    SubCategoryCreateView,
    SubCategoryUpdateView,
    SubCategoryDeleteView,
)
from .views.customer_views import (
    CustomerListView,
    CustomerTablePartialView,
    CustomerCreateView,
    CustomerUpdateView,
    CustomerDeleteView,
    GetStatesView,
    GetCitiesView,
)

urlpatterns = [
    # Category URLs
    path("category/", CategoryListView.as_view(), name="category_list"),
    path("category/table/", CategoryTablePartialView.as_view(), name="category_table"),
    path("category/add/", CategoryCreateView.as_view(), name="category_add"),
    path("category/edit/<int:pk>/", CategoryUpdateView.as_view(), name="category_edit"),
    path(
        "category/delete/<int:pk>/",
        CategoryDeleteView.as_view(),
        name="category_delete",
    ),
    # SubCategory URLs
    path("subcategory/", SubCategoryListView.as_view(), name="subcategory_list"),
    path(
        "subcategory/table/",
        SubCategoryTablePartialView.as_view(),
        name="subcategory_table",
    ),
    path("subcategory/add/", SubCategoryCreateView.as_view(), name="subcategory_add"),
    path(
        "subcategory/edit/<int:pk>/",
        SubCategoryUpdateView.as_view(),
        name="subcategory_edit",
    ),
    path(
        "subcategory/delete/<int:pk>/",
        SubCategoryDeleteView.as_view(),
        name="subcategory_delete",
    ),
    # Customer URLs
    path("customer/", CustomerListView.as_view(), name="customer_list"),
    path("customer/table/", CustomerTablePartialView.as_view(), name="customer_table"),
    path("customer/add/", CustomerCreateView.as_view(), name="customer_add"),
    path("customer/edit/<str:pk>/", CustomerUpdateView.as_view(), name="customer_edit"),
    path(
        "customer/delete/<str:pk>/",
        CustomerDeleteView.as_view(),
        name="customer_delete",
    ),
    # HTMX endpoints for cascading dropdowns
    path("get-states/", GetStatesView.as_view(), name="get_states"),
    path("get-cities/", GetCitiesView.as_view(), name="get_cities"),
]
