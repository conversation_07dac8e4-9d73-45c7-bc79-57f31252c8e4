from django import forms
from .models import Category, SubCategory, Customer, Country, State, City


class CategoryForm(forms.ModelForm):
    """
    Form for creating and updating Work Order Categories.
    """

    # This BooleanField is for form input, will be mapped to model's HasSubCat ('1'/'0')
    has_sub_cat_checkbox = forms.BooleanField(
        label="Has SubCategory",
        required=False,
        widget=forms.CheckboxInput(
            attrs={
                "class": "h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
            }
        ),
    )

    class Meta:
        model = Category
        # Only include fields that are directly editable by the user
        # Audit/context fields like CompId, FinYearId, SessionId are set in the view
        fields = ["CName", "Symbol"]
        widgets = {
            "CName": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Enter category name",
                }
            ),
            "Symbol": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "maxlength": "1",
                    "placeholder": "Enter 1-char symbol",
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize the checkbox based on the model instance's HasSubCat value
        if self.instance and self.instance.pk:
            self.fields["has_sub_cat_checkbox"].initial = self.instance.HasSubCat == "1"

        # Set required attributes for fields that had RequiredFieldValidator in ASP.NET
        self.fields["CName"].required = True
        self.fields["Symbol"].required = True

    def clean_Symbol(self):
        """Ensure Symbol is uppercase, consistent with ASP.NET storage."""
        symbol = self.cleaned_data["Symbol"]
        if symbol:
            return symbol.upper()
        return symbol

    def save(self, commit=True):
        """
        Overrides save to handle the mapping of the form's boolean checkbox
        back to the model's '1'/'0' string representation for HasSubCat.
        """
        instance = super().save(commit=False)
        # Map the checkbox value to '1' or '0' for the model's HasSubCat field
        instance.HasSubCat = (
            "1" if self.cleaned_data.get("has_sub_cat_checkbox") else "0"
        )

        # The audit/context fields (CompId, FinYearId, SessionId) are set in the view
        # because they depend on the request/session, which forms don't directly access.

        if commit:
            instance.save()
        return instance


class SubCategoryForm(forms.ModelForm):
    """
    Form for creating and updating Work Order Sub-Categories.
    """

    class Meta:
        model = SubCategory
        fields = ["CId", "SCName", "SCSymbol"]
        widgets = {
            "CId": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                }
            ),
            "SCName": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Enter sub-category name",
                }
            ),
            "SCSymbol": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "maxlength": "1",
                    "placeholder": "Enter 1-char symbol",
                }
            ),
        }
        labels = {
            "CId": "Category",
            "SCName": "Sub-Category Name",
            "SCSymbol": "Sub-Category Symbol",
        }

    def __init__(self, *args, **kwargs):
        categories = kwargs.pop("categories", None)
        super().__init__(*args, **kwargs)

        # Set required attributes
        self.fields["CId"].required = True
        self.fields["SCName"].required = True
        self.fields["SCSymbol"].required = True

        # Populate category choices
        if categories:
            self.fields["CId"].queryset = categories
            self.fields["CId"].widget.choices = [
                (cat.CId, f"{cat.CName} ({cat.Symbol})") for cat in categories
            ]

    def clean_SCSymbol(self):
        """Ensure SCSymbol is uppercase, consistent with ASP.NET storage."""
        symbol = self.cleaned_data["SCSymbol"]
        if symbol:
            return symbol.upper()
        return symbol


class CustomerForm(forms.ModelForm):
    """
    Form for creating and updating Customer Master records.
    """

    class Meta:
        model = Customer
        fields = [
            "CustomerName",
            "RegdAddress",
            "RegdCountry",
            "RegdState",
            "RegdCity",
            "RegdPinNo",
            "RegdContactNo",
            "RegdFaxNo",
            "WorkAddress",
            "WorkCountry",
            "WorkState",
            "WorkCity",
            "WorkPinNo",
            "WorkContactNo",
            "WorkFaxNo",
            "MaterialDelAddress",
            "MaterialDelCountry",
            "MaterialDelState",
            "MaterialDelCity",
            "MaterialDelPinNo",
            "MaterialDelContactNo",
            "MaterialDelFaxNo",
            "ContactPerson",
            "JuridictionCode",
            "Commissionurate",
            "TinVatNo",
            "Email",
            "EccNo",
            "Divn",
            "TinCstNo",
            "ContactNo",
            "RangeField",
            "PanNo",
            "TdsCode",
            "Remark",
        ]
        widgets = {
            "CustomerName": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Enter customer name",
                }
            ),
            "RegdAddress": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "rows": 3,
                    "placeholder": "Enter registered address",
                }
            ),
            "RegdCountry": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "hx-get": "/sales_distribution/get-states/",
                    "hx-target": "#id_RegdState",
                    "hx-trigger": "change",
                    "hx-include": "this",
                }
            ),
            "RegdState": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "hx-get": "/sales_distribution/get-cities/",
                    "hx-target": "#id_RegdCity",
                    "hx-trigger": "change",
                    "hx-include": "this",
                }
            ),
            "RegdCity": forms.Select(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                }
            ),
            "RegdPinNo": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "PIN Code",
                }
            ),
            "RegdContactNo": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Contact Number",
                }
            ),
            "RegdFaxNo": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Fax Number",
                }
            ),
            "Email": forms.EmailInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Email address",
                }
            ),
            "ContactPerson": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Contact person name",
                }
            ),
            "ContactNo": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "Contact Number",
                }
            ),
            "PanNo": forms.TextInput(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "placeholder": "PAN Number",
                }
            ),
            "Remark": forms.Textarea(
                attrs={
                    "class": "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
                    "rows": 3,
                    "placeholder": "Enter remarks",
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set required fields
        self.fields["CustomerName"].required = True

        # Populate dropdown choices
        self.fields["RegdCountry"].queryset = Country.objects.all()
        self.fields["RegdCountry"].empty_label = "Select Country"

    def clean_Email(self):
        """Validate email format."""
        email = self.cleaned_data.get("Email")
        if email and "@" not in email:
            raise forms.ValidationError("Please enter a valid email address.")
        return email
