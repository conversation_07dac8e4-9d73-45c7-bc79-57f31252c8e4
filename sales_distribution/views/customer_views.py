from django.views.generic import ListView, <PERSON><PERSON><PERSON>ie<PERSON>, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404, JsonResponse
from django.shortcuts import render
from django.template.loader import render_to_string
from ..models import Customer, Country, State, City
from ..forms import CustomerForm


def get_session_context(request):
    """
    Extracts session context values (CompId, FinYearId, SessionId) from the request.
    Returns default values if not found in session.
    """
    comp_id = request.session.get("CompId", 1)
    fin_year_id = request.session.get("FinYearId", 9)
    session_id = request.session.get("SessionId", "default_session")
    return comp_id, fin_year_id, session_id


# Customer Views
class CustomerListView(ListView):
    """Displays the main dashboard page for Customers."""

    model = Customer
    template_name = "sales_distribution/customer/customer_list.html"
    context_object_name = "customers"

    def get_queryset(self):
        """Filters customers based on session context."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        return Customer.get_filtered_customers(comp_id, fin_year_id)


class CustomerTablePartialView(ListView):
    """
    Renders only the customer table, intended for HTMX requests to refresh
    the list dynamically without a full page reload.
    """

    model = Customer
    template_name = "sales_distribution/customer/_customer_table.html"
    context_object_name = "customers"

    def get_queryset(self):
        """Filters customers based on session context."""
        comp_id, fin_year_id, _ = get_session_context(self.request)
        return Customer.get_filtered_customers(comp_id, fin_year_id)


class CustomerCreateView(CreateView):
    """Handles creation of new customers via a modal form."""

    model = Customer
    form_class = CustomerForm
    template_name = "sales_distribution/customer/_customer_form.html"
    success_url = reverse_lazy("customer_list")

    def form_valid(self, form):
        """Sets audit/context fields before saving the new customer."""
        comp_id, fin_year_id, session_id = get_session_context(self.request)

        # Generate CustomerId based on customer name
        customer_name = form.cleaned_data["CustomerName"]
        if customer_name:
            # Take first 3 characters of customer name and make uppercase
            prefix = customer_name[:3].upper()
            # Find the next available number for this prefix
            existing_customers = Customer.objects.filter(
                CustomerId__startswith=prefix, CompId=comp_id
            ).order_by("-CustomerId")

            if existing_customers.exists():
                # Extract the number part and increment
                last_id = existing_customers.first().CustomerId
                try:
                    last_num = int(last_id[3:])
                    next_num = last_num + 1
                except (ValueError, IndexError):
                    next_num = 1
            else:
                next_num = 1

            # Format as 3-digit number
            form.instance.CustomerId = f"{prefix}{next_num:03d}"

        # Assign audit/context fields from the session
        form.instance.CompId = comp_id
        form.instance.FinYearId = fin_year_id
        form.instance.SessionId = session_id

        response = super().form_valid(form)
        messages.success(self.request, "Customer added successfully.")

        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCustomerList"}
            )
        return response

    def form_invalid(self, form):
        """Handle form validation errors for HTMX requests."""
        if self.request.headers.get("HX-Request"):
            return render(
                self.request,
                self.template_name,
                {"form": form},
            )
        return super().form_invalid(form)


class CustomerUpdateView(UpdateView):
    """Handles updating existing customers via a modal form."""

    model = Customer
    form_class = CustomerForm
    template_name = "sales_distribution/customer/_customer_form.html"
    success_url = reverse_lazy("customer_list")

    def get_object(self, queryset=None):
        """Ensure the object retrieved belongs to the current session context."""
        obj = super().get_object(queryset)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        if obj.CompId != str(comp_id) or int(obj.FinYearId) > fin_year_id:
            raise Http404("Customer not found or not accessible.")
        return obj

    def form_valid(self, form):
        """Handle successful form submission for HTMX requests."""
        response = super().form_valid(form)
        messages.success(self.request, "Customer updated successfully.")

        if self.request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCustomerList"}
            )
        return response

    def form_invalid(self, form):
        """Handle form validation errors for HTMX requests."""
        if self.request.headers.get("HX-Request"):
            return render(
                self.request,
                self.template_name,
                {"form": form},
            )
        return super().form_invalid(form)


class CustomerDeleteView(DeleteView):
    """Handles deletion of customers via a modal confirmation."""

    model = Customer
    template_name = "sales_distribution/customer/_customer_confirm_delete.html"
    success_url = reverse_lazy("customer_list")

    def get_object(self, queryset=None):
        """Ensure the object retrieved belongs to the current session context."""
        obj = super().get_object(queryset)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        if obj.CompId != str(comp_id) or int(obj.FinYearId) > fin_year_id:
            raise Http404("Customer not found or not accessible.")
        return obj

    def delete(self, request, *args, **kwargs):
        """Handle deletion for HTMX requests."""
        response = super().delete(request, *args, **kwargs)
        messages.success(request, "Customer deleted successfully.")

        if request.headers.get("HX-Request"):
            return HttpResponse(
                status=204, headers={"HX-Trigger": "refreshCustomerList"}
            )
        return response


# HTMX Views for Cascading Dropdowns
class GetStatesView(View):
    """Returns states for a given country via HTMX."""

    def get(self, request):
        # Get country_id from the form field that triggered the request
        country_id = (
            request.GET.get("RegdCountry")
            or request.GET.get("WorkCountry")
            or request.GET.get("MaterialDelCountry")
        )

        states = (
            State.objects.filter(CountryId=country_id).order_by("StateName")
            if country_id
            else State.objects.none()
        )

        html = '<option value="">Select State</option>'
        for state in states:
            html += f'<option value="{state.SId}">{state.StateName}</option>'

        return HttpResponse(html)


class GetCitiesView(View):
    """Returns cities for a given state via HTMX."""

    def get(self, request):
        # Get state_id from the form field that triggered the request
        state_id = (
            request.GET.get("RegdState")
            or request.GET.get("WorkState")
            or request.GET.get("MaterialDelState")
        )

        cities = (
            City.objects.filter(StateId=state_id).order_by("CityName")
            if state_id
            else City.objects.none()
        )

        html = '<option value="">Select City</option>'
        for city in cities:
            html += f'<option value="{city.CityId}">{city.CityName}</option>'

        return HttpResponse(html)
