from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.core.exceptions import ValidationError
from .models import Category, SubCategory
from .forms import CategoryForm, SubCategoryForm
import datetime


class CategoryModelTest(TestCase):
    """
    Unit tests for the Category model - testing model methods without database operations.
    Since models are unmanaged (managed=False), we test model logic without DB operations.
    """

    def test_str_method(self):
        """Tests the __str__ method for correct string representation."""
        category = Category(CName="Test Category", Symbol="T")
        self.assertEqual(str(category), "Test Category (T)")

    def test_has_subcategory_display_property(self):
        """Tests the `has_subcategory_display` property for correct string conversion."""
        cat_yes = Category(HasSubCat="1")
        cat_no = Category(HasSubCat="0")
        self.assertEqual(cat_yes.has_subcategory_display, "Yes")
        self.assertEqual(cat_no.has_subcategory_display, "No")

    def test_symbol_uppercase_on_save(self):
        """Tests the Symbol is converted to uppercase in save method."""
        category = Category(CName="Test", Symbol="l", HasSubCat="0")
        # Simulate the save method logic without actually saving to DB
        if category.Symbol:
            category.Symbol = category.Symbol.upper()
        self.assertEqual(category.Symbol, "L")

    def test_has_sub_cat_boolean_to_string_conversion(self):
        """Tests that boolean values for HasSubCat are correctly converted to '1'/'0'."""
        cat_bool_true = Category(HasSubCat=True)
        cat_bool_false = Category(HasSubCat=False)

        # Simulate the save method logic
        if isinstance(cat_bool_true.HasSubCat, bool):
            cat_bool_true.HasSubCat = "1" if cat_bool_true.HasSubCat else "0"
        if isinstance(cat_bool_false.HasSubCat, bool):
            cat_bool_false.HasSubCat = "1" if cat_bool_false.HasSubCat else "0"

        self.assertEqual(cat_bool_true.HasSubCat, "1")
        self.assertEqual(cat_bool_false.HasSubCat, "0")


class SubCategoryModelTest(TestCase):
    """
    Unit tests for the SubCategory model - testing model methods without database operations.
    Since models are unmanaged (managed=False), we test model logic without DB operations.
    """

    def test_str_method(self):
        """Tests the __str__ method for correct string representation."""
        subcategory = SubCategory(SCName="Test SubCategory", SCSymbol="S")
        self.assertEqual(str(subcategory), "Test SubCategory (S)")

    def test_symbol_uppercase_on_save(self):
        """Tests the SCSymbol is converted to uppercase in save method."""
        subcategory = SubCategory(SCName="Test", SCSymbol="l")
        # Simulate the save method logic without actually saving to DB
        if subcategory.SCSymbol:
            subcategory.SCSymbol = subcategory.SCSymbol.upper()
        self.assertEqual(subcategory.SCSymbol, "L")


class CategoryFormTest(TestCase):
    """
    Unit tests for the CategoryForm.
    """

    def test_category_form_valid(self):
        """Tests that the form is valid with correct data."""
        form_data = {
            "CName": "New Test Category",
            "Symbol": "N",
            "has_sub_cat_checkbox": True,
        }
        form = CategoryForm(data=form_data)
        self.assertTrue(form.is_valid(), msg=form.errors.as_text())

        # Verify has_sub_cat_checkbox maps correctly to instance.HasSubCat
        instance = form.save(commit=False)
        self.assertEqual(instance.HasSubCat, "1")

    def test_category_form_invalid_empty_fields(self):
        """Tests form validation for required fields."""
        form_data = {
            "CName": "",  # Missing
            "Symbol": "",  # Missing
            "has_sub_cat_checkbox": False,
        }
        form = CategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("CName", form.errors)
        self.assertIn("Symbol", form.errors)

    def test_category_form_symbol_uppercase_conversion(self):
        """Tests that the form's clean_Symbol converts input to uppercase."""
        form_data = {
            "CName": "Valid Name",
            "Symbol": "a",  # Lowercase input
            "has_sub_cat_checkbox": False,
        }
        form = CategoryForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data["Symbol"], "A")  # Should be uppercase


class SubCategoryFormTest(TestCase):
    """
    Unit tests for the SubCategoryForm.
    """

    def test_subcategory_form_valid(self):
        """Tests that the form is valid with correct data."""
        form_data = {
            "CId": 1,  # Use a simple integer ID
            "SCName": "New Test SubCategory",
            "SCSymbol": "N",
        }
        form = SubCategoryForm(data=form_data)
        self.assertTrue(form.is_valid(), msg=form.errors.as_text())

    def test_subcategory_form_invalid_empty_fields(self):
        """Tests form validation for required fields."""
        form_data = {
            "CId": "",  # Missing
            "SCName": "",  # Missing
            "SCSymbol": "",  # Missing
        }
        form = SubCategoryForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("CId", form.errors)
        self.assertIn("SCName", form.errors)
        self.assertIn("SCSymbol", form.errors)

    def test_subcategory_form_symbol_uppercase_conversion(self):
        """Tests that the form's clean_SCSymbol converts input to uppercase."""
        form_data = {
            "CId": 1,  # Use a simple integer ID
            "SCName": "Valid Name",
            "SCSymbol": "a",  # Lowercase input
        }
        form = SubCategoryForm(data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data["SCSymbol"], "A")  # Should be uppercase


# Note: View tests are not included since the models are unmanaged (managed=False)
# and would require actual database tables to exist. In a production environment,
# these views would be tested against the actual database with existing tables.
